[{"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(auth)\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(auth)\\login\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\analytics\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\attendance\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\reports\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\students\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\api\\auth\\[...nextauth]\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\scanner\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\header.tsx": "13", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\sidebar.tsx": "14", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\providers\\session-provider.tsx": "15", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\providers\\theme-provider.tsx": "16", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\alert.tsx": "17", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\badge.tsx": "18", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\breadcrumb.tsx": "19", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\button.tsx": "20", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\card.tsx": "21", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\checkbox.tsx": "22", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\dialog.tsx": "23", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\dropdown-menu.tsx": "24", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\form.tsx": "25", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\input.tsx": "26", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\label.tsx": "27", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\navigation-menu.tsx": "28", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\select.tsx": "29", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\separator.tsx": "30", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\sheet.tsx": "31", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\sidebar.tsx": "32", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\skeleton.tsx": "33", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx": "34", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx": "35", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\textarea.tsx": "36", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tooltip.tsx": "37", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\auth\\config.ts": "38", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\auth\\types.ts": "39", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils.ts": "40", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\api\\scanner\\attendance\\route.ts": "41", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\api\\scanner\\lookup\\route.ts": "42", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\scanner\\layout.tsx": "43", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\attendance\\attendance-filters.tsx": "44", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\attendance\\attendance-grid.tsx": "45", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\dashboard\\attendance-charts.tsx": "46", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\dashboard\\attendance-stats.tsx": "47", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\dashboard\\notification-center.tsx": "48", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\dashboard\\weather-widget.tsx": "49", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\responsive-header.tsx": "50", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\attendance-marking.tsx": "51", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\batch-scanner.tsx": "52", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\feedback-system.tsx": "53", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\manual-entry-dialog.tsx": "54", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\qr-scanner.tsx": "55", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\settings-panel.tsx": "56", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\student-info-card.tsx": "57", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\sync-status-indicator.tsx": "58", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\attendance-statistics.tsx": "59", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\bulk-actions.tsx": "60", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\csv-import-dialog.tsx": "61", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\mobile-student-card.tsx": "62", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\pagination.tsx": "63", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\photo-upload.tsx": "64", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\qr-batch-generator.tsx": "65", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\qr-code-display.tsx": "66", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-filters.tsx": "67", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-profile-dialog.tsx": "68", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-profile-view.tsx": "69", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-registration-dialog.tsx": "70", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-registration-form.tsx": "71", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-status-manager.tsx": "72", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\students-table.tsx": "73", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\alert-dialog.tsx": "74", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\avatar.tsx": "75", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\error-boundary.tsx": "76", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\progress.tsx": "77", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\scroll-area.tsx": "78", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\slider.tsx": "79", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\switch.tsx": "80", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\data\\mock-data.ts": "81", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\data\\students-mock-data.ts": "82", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\types\\scanner.ts": "83", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\types\\student.ts": "84", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils\\export-utils.ts": "85", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils\\offline-manager.ts": "86", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils\\photo-management.ts": "87", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils\\qr-code.ts": "88", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\validations\\student.ts": "89"}, {"size": 167, "mtime": 1754072483529, "results": "90", "hashOfConfig": "91"}, {"size": 4190, "mtime": 1754072949289, "results": "92", "hashOfConfig": "91"}, {"size": 8626, "mtime": 1754072632421, "results": "93", "hashOfConfig": "91"}, {"size": 10613, "mtime": 1754080759088, "results": "94", "hashOfConfig": "91"}, {"size": 10328, "mtime": 1754080725572, "results": "95", "hashOfConfig": "91"}, {"size": 608, "mtime": 1754072499775, "results": "96", "hashOfConfig": "91"}, {"size": 6559, "mtime": 1754072599626, "results": "97", "hashOfConfig": "91"}, {"size": 12296, "mtime": 1754079316210, "results": "98", "hashOfConfig": "91"}, {"size": 84, "mtime": 1754072361090, "results": "99", "hashOfConfig": "91"}, {"size": 1236, "mtime": 1754078525172, "results": "100", "hashOfConfig": "91"}, {"size": 100, "mtime": 1754072263382, "results": "101", "hashOfConfig": "91"}, {"size": 14947, "mtime": 1754075693700, "results": "102", "hashOfConfig": "91"}, {"size": 3020, "mtime": 1754080559717, "results": "103", "hashOfConfig": "91"}, {"size": 2437, "mtime": 1754072440483, "results": "104", "hashOfConfig": "91"}, {"size": 275, "mtime": 1754072390628, "results": "105", "hashOfConfig": "91"}, {"size": 462, "mtime": 1754073045037, "results": "106", "hashOfConfig": "91"}, {"size": 1614, "mtime": 1754072172871, "results": "107", "hashOfConfig": "91"}, {"size": 1631, "mtime": 1754072172871, "results": "108", "hashOfConfig": "91"}, {"size": 2357, "mtime": 1754072173378, "results": "109", "hashOfConfig": "91"}, {"size": 2123, "mtime": 1754072172810, "results": "110", "hashOfConfig": "91"}, {"size": 1989, "mtime": 1754072172840, "results": "111", "hashOfConfig": "91"}, {"size": 1226, "mtime": 1754072173076, "results": "112", "hashOfConfig": "91"}, {"size": 3982, "mtime": 1754072172972, "results": "113", "hashOfConfig": "91"}, {"size": 8284, "mtime": 1754072172918, "results": "114", "hashOfConfig": "91"}, {"size": 3759, "mtime": 1754072173035, "results": "115", "hashOfConfig": "91"}, {"size": 967, "mtime": 1754072172841, "results": "116", "hashOfConfig": "91"}, {"size": 611, "mtime": 1754072173052, "results": "117", "hashOfConfig": "91"}, {"size": 6664, "mtime": 1754072173128, "results": "118", "hashOfConfig": "91"}, {"size": 6253, "mtime": 1754072172948, "results": "119", "hashOfConfig": "91"}, {"size": 699, "mtime": 1754072173311, "results": "120", "hashOfConfig": "91"}, {"size": 4090, "mtime": 1754072172991, "results": "121", "hashOfConfig": "91"}, {"size": 21633, "mtime": 1754072173303, "results": "122", "hashOfConfig": "91"}, {"size": 276, "mtime": 1754072173357, "results": "123", "hashOfConfig": "91"}, {"size": 2448, "mtime": 1754072172861, "results": "124", "hashOfConfig": "91"}, {"size": 1969, "mtime": 1754072173101, "results": "125", "hashOfConfig": "91"}, {"size": 759, "mtime": 1754072173062, "results": "126", "hashOfConfig": "91"}, {"size": 1891, "mtime": 1754072173338, "results": "127", "hashOfConfig": "91"}, {"size": 1545, "mtime": 1754072355037, "results": "128", "hashOfConfig": "91"}, {"size": 265, "mtime": 1754073083849, "results": "129", "hashOfConfig": "91"}, {"size": 166, "mtime": 1754072136186, "results": "130", "hashOfConfig": "91"}, {"size": 4585, "mtime": 1754075473502, "results": "131", "hashOfConfig": "91"}, {"size": 2484, "mtime": 1754075451041, "results": "132", "hashOfConfig": "91"}, {"size": 345, "mtime": 1754074636757, "results": "133", "hashOfConfig": "91"}, {"size": 6782, "mtime": 1754080258286, "results": "134", "hashOfConfig": "91"}, {"size": 10467, "mtime": 1754080291557, "results": "135", "hashOfConfig": "91"}, {"size": 8231, "mtime": 1754080772139, "results": "136", "hashOfConfig": "91"}, {"size": 6581, "mtime": 1754080034050, "results": "137", "hashOfConfig": "91"}, {"size": 11048, "mtime": 1754080421275, "results": "138", "hashOfConfig": "91"}, {"size": 6609, "mtime": 1754080067372, "results": "139", "hashOfConfig": "91"}, {"size": 7991, "mtime": 1754080462876, "results": "140", "hashOfConfig": "91"}, {"size": 9428, "mtime": 1754075016889, "results": "141", "hashOfConfig": "91"}, {"size": 11087, "mtime": 1754075166387, "results": "142", "hashOfConfig": "91"}, {"size": 8377, "mtime": 1754075068325, "results": "143", "hashOfConfig": "91"}, {"size": 9798, "mtime": 1754075113230, "results": "144", "hashOfConfig": "91"}, {"size": 9879, "mtime": 1754074873334, "results": "145", "hashOfConfig": "91"}, {"size": 11492, "mtime": 1754075252579, "results": "146", "hashOfConfig": "91"}, {"size": 7854, "mtime": 1754074912138, "results": "147", "hashOfConfig": "91"}, {"size": 7481, "mtime": 1754075385415, "results": "148", "hashOfConfig": "91"}, {"size": 10582, "mtime": 1754079097433, "results": "149", "hashOfConfig": "91"}, {"size": 11734, "mtime": 1754078353943, "results": "150", "hashOfConfig": "91"}, {"size": 16727, "mtime": 1754078599723, "results": "151", "hashOfConfig": "91"}, {"size": 8876, "mtime": 1754079223453, "results": "152", "hashOfConfig": "91"}, {"size": 6407, "mtime": 1754079356805, "results": "153", "hashOfConfig": "91"}, {"size": 10943, "mtime": 1754078917843, "results": "154", "hashOfConfig": "91"}, {"size": 17215, "mtime": 1754079612518, "results": "155", "hashOfConfig": "91"}, {"size": 10483, "mtime": 1754079550430, "results": "156", "hashOfConfig": "91"}, {"size": 13347, "mtime": 1754078269303, "results": "157", "hashOfConfig": "91"}, {"size": 4482, "mtime": 1754078219118, "results": "158", "hashOfConfig": "91"}, {"size": 13798, "mtime": 1754079586024, "results": "159", "hashOfConfig": "91"}, {"size": 4672, "mtime": 1754078094095, "results": "160", "hashOfConfig": "91"}, {"size": 30186, "mtime": 1754079377190, "results": "161", "hashOfConfig": "91"}, {"size": 13337, "mtime": 1754079011146, "results": "162", "hashOfConfig": "91"}, {"size": 14002, "mtime": 1754079267664, "results": "163", "hashOfConfig": "91"}, {"size": 4433, "mtime": 1754078487865, "results": "164", "hashOfConfig": "91"}, {"size": 1419, "mtime": 1754074971295, "results": "165", "hashOfConfig": "91"}, {"size": 6856, "mtime": 1754080632485, "results": "166", "hashOfConfig": "91"}, {"size": 791, "mtime": 1754075193285, "results": "167", "hashOfConfig": "91"}, {"size": 1656, "mtime": 1754075203972, "results": "168", "hashOfConfig": "91"}, {"size": 1091, "mtime": 1754075291170, "results": "169", "hashOfConfig": "91"}, {"size": 1153, "mtime": 1754075281661, "results": "170", "hashOfConfig": "91"}, {"size": 12426, "mtime": 1754079993698, "results": "171", "hashOfConfig": "91"}, {"size": 8597, "mtime": 1754077899748, "results": "172", "hashOfConfig": "91"}, {"size": 4602, "mtime": 1754077829343, "results": "173", "hashOfConfig": "91"}, {"size": 4126, "mtime": 1754077854221, "results": "174", "hashOfConfig": "91"}, {"size": 9777, "mtime": 1754080515053, "results": "175", "hashOfConfig": "91"}, {"size": 7332, "mtime": 1754075347770, "results": "176", "hashOfConfig": "91"}, {"size": 9808, "mtime": 1754078875698, "results": "177", "hashOfConfig": "91"}, {"size": 7788, "mtime": 1754078749625, "results": "178", "hashOfConfig": "91"}, {"size": 5813, "mtime": 1754077945333, "results": "179", "hashOfConfig": "91"}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1avohlr", {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(auth)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(auth)\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\attendance\\page.tsx", ["447", "448"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\dashboard\\page.tsx", ["449", "450", "451"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\reports\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\students\\page.tsx", ["452", "453"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\scanner\\page.tsx", ["454", "455", "456", "457", "458"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\providers\\session-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\providers\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\auth\\config.ts", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\auth\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\api\\scanner\\attendance\\route.ts", ["459", "460"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\api\\scanner\\lookup\\route.ts", ["461"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\scanner\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\attendance\\attendance-filters.tsx", ["462"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\attendance\\attendance-grid.tsx", ["463", "464", "465"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\dashboard\\attendance-charts.tsx", ["466"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\dashboard\\attendance-stats.tsx", ["467"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\dashboard\\notification-center.tsx", ["468", "469"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\dashboard\\weather-widget.tsx", ["470", "471", "472", "473", "474"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\responsive-header.tsx", ["475", "476"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\attendance-marking.tsx", ["477"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\batch-scanner.tsx", ["478"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\feedback-system.tsx", ["479", "480", "481"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\manual-entry-dialog.tsx", ["482", "483", "484"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\qr-scanner.tsx", ["485"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\settings-panel.tsx", ["486"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\student-info-card.tsx", ["487", "488"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\scanner\\sync-status-indicator.tsx", ["489"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\attendance-statistics.tsx", ["490", "491", "492"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\bulk-actions.tsx", ["493", "494", "495", "496", "497", "498", "499"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\csv-import-dialog.tsx", ["500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\mobile-student-card.tsx", ["512", "513"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\photo-upload.tsx", ["514"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\qr-batch-generator.tsx", ["515", "516", "517", "518", "519", "520"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\qr-code-display.tsx", ["521", "522", "523"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-filters.tsx", ["524", "525", "526", "527", "528", "529", "530"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-profile-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-profile-view.tsx", ["531", "532", "533", "534", "535", "536"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-registration-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-registration-form.tsx", ["537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\student-status-manager.tsx", ["549", "550", "551", "552", "553"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\students\\students-table.tsx", ["554", "555", "556"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\error-boundary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\data\\mock-data.ts", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\data\\students-mock-data.ts", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\types\\scanner.ts", ["557", "558", "559"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\types\\student.ts", ["560"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils\\export-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils\\offline-manager.ts", ["561"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils\\photo-management.ts", ["562"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils\\qr-code.ts", ["563", "564", "565", "566", "567"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\validations\\student.ts", ["568", "569"], [], {"ruleId": "570", "severity": 1, "message": "571", "line": 6, "column": 10, "nodeType": null, "messageId": "572", "endLine": 6, "endColumn": 15}, {"ruleId": "570", "severity": 1, "message": "573", "line": 12, "column": 30, "nodeType": null, "messageId": "572", "endLine": 12, "endColumn": 51}, {"ruleId": "570", "severity": 1, "message": "574", "line": 9, "column": 25, "nodeType": null, "messageId": "572", "endLine": 9, "endColumn": 45}, {"ruleId": "570", "severity": 1, "message": "575", "line": 14, "column": 3, "nodeType": null, "messageId": "572", "endLine": 14, "endColumn": 20}, {"ruleId": "576", "severity": 2, "message": "577", "line": 251, "column": 29, "nodeType": "578", "messageId": "579", "suggestions": "580"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 63, "column": 19, "nodeType": "583", "messageId": "584", "endLine": 63, "endColumn": 22, "suggestions": "585"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 64, "column": 19, "nodeType": "583", "messageId": "584", "endLine": 64, "endColumn": 22, "suggestions": "586"}, {"ruleId": "570", "severity": 1, "message": "587", "line": 4, "column": 29, "nodeType": null, "messageId": "572", "endLine": 4, "endColumn": 44}, {"ruleId": "570", "severity": 1, "message": "588", "line": 32, "column": 3, "nodeType": null, "messageId": "572", "endLine": 32, "endColumn": 22}, {"ruleId": "570", "severity": 1, "message": "589", "line": 35, "column": 57, "nodeType": null, "messageId": "572", "endLine": 35, "endColumn": 61}, {"ruleId": "570", "severity": 1, "message": "590", "line": 57, "column": 28, "nodeType": null, "messageId": "572", "endLine": 57, "endColumn": 47}, {"ruleId": "581", "severity": 2, "message": "582", "line": 149, "column": 87, "nodeType": "583", "messageId": "584", "endLine": 149, "endColumn": 90, "suggestions": "591"}, {"ruleId": "592", "severity": 2, "message": "593", "line": 7, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 7, "endColumn": 42, "fix": "596"}, {"ruleId": "570", "severity": 1, "message": "597", "line": 17, "column": 7, "nodeType": null, "messageId": "572", "endLine": 17, "endColumn": 13}, {"ruleId": "570", "severity": 1, "message": "598", "line": 33, "column": 11, "nodeType": null, "messageId": "572", "endLine": 33, "endColumn": 27}, {"ruleId": "581", "severity": 2, "message": "582", "line": 43, "column": 68, "nodeType": "583", "messageId": "584", "endLine": 43, "endColumn": 71, "suggestions": "599"}, {"ruleId": "570", "severity": 1, "message": "600", "line": 21, "column": 3, "nodeType": null, "messageId": "572", "endLine": 21, "endColumn": 11}, {"ruleId": "570", "severity": 1, "message": "601", "line": 22, "column": 3, "nodeType": null, "messageId": "572", "endLine": 22, "endColumn": 16}, {"ruleId": "570", "severity": 1, "message": "602", "line": 24, "column": 10, "nodeType": null, "messageId": "572", "endLine": 24, "endColumn": 16}, {"ruleId": "570", "severity": 1, "message": "571", "line": 21, "column": 10, "nodeType": null, "messageId": "572", "endLine": 21, "endColumn": 15}, {"ruleId": "570", "severity": 1, "message": "587", "line": 3, "column": 29, "nodeType": null, "messageId": "572", "endLine": 3, "endColumn": 44}, {"ruleId": "570", "severity": 1, "message": "603", "line": 15, "column": 3, "nodeType": null, "messageId": "572", "endLine": 15, "endColumn": 7}, {"ruleId": "581", "severity": 2, "message": "582", "line": 137, "column": 82, "nodeType": "583", "messageId": "584", "endLine": 137, "endColumn": 85, "suggestions": "604"}, {"ruleId": "570", "severity": 1, "message": "605", "line": 13, "column": 3, "nodeType": null, "messageId": "572", "endLine": 13, "endColumn": 14}, {"ruleId": "570", "severity": 1, "message": "606", "line": 64, "column": 10, "nodeType": null, "messageId": "572", "endLine": 64, "endColumn": 17}, {"ruleId": "570", "severity": 1, "message": "607", "line": 64, "column": 19, "nodeType": null, "messageId": "572", "endLine": 64, "endColumn": 29}, {"ruleId": "570", "severity": 1, "message": "608", "line": 65, "column": 10, "nodeType": null, "messageId": "572", "endLine": 65, "endColumn": 15}, {"ruleId": "570", "severity": 1, "message": "609", "line": 65, "column": 17, "nodeType": null, "messageId": "572", "endLine": 65, "endColumn": 25}, {"ruleId": "570", "severity": 1, "message": "610", "line": 6, "column": 30, "nodeType": null, "messageId": "572", "endLine": 6, "endColumn": 51}, {"ruleId": "570", "severity": 1, "message": "611", "line": 234, "column": 3, "nodeType": null, "messageId": "572", "endLine": 234, "endColumn": 11}, {"ruleId": "581", "severity": 2, "message": "582", "line": 57, "column": 20, "nodeType": "583", "messageId": "584", "endLine": 57, "endColumn": 23, "suggestions": "612"}, {"ruleId": "570", "severity": 1, "message": "613", "line": 3, "column": 20, "nodeType": null, "messageId": "572", "endLine": 3, "endColumn": 29}, {"ruleId": "581", "severity": 2, "message": "582", "line": 31, "column": 76, "nodeType": "583", "messageId": "584", "endLine": 31, "endColumn": 79, "suggestions": "614"}, {"ruleId": "615", "severity": 1, "message": "616", "line": 117, "column": 6, "nodeType": "617", "endLine": 117, "endColumn": 35, "suggestions": "618"}, {"ruleId": "615", "severity": 1, "message": "619", "line": 209, "column": 6, "nodeType": "617", "endLine": 209, "endColumn": 29, "suggestions": "620"}, {"ruleId": "570", "severity": 1, "message": "621", "line": 4, "column": 60, "nodeType": null, "messageId": "572", "endLine": 4, "endColumn": 73}, {"ruleId": "576", "severity": 2, "message": "622", "line": 160, "column": 49, "nodeType": "578", "messageId": "579", "suggestions": "623"}, {"ruleId": "576", "severity": 2, "message": "622", "line": 160, "column": 63, "nodeType": "578", "messageId": "579", "suggestions": "624"}, {"ruleId": "570", "severity": 1, "message": "625", "line": 7, "column": 40, "nodeType": null, "messageId": "572", "endLine": 7, "endColumn": 48}, {"ruleId": "581", "severity": 2, "message": "582", "line": 67, "column": 67, "nodeType": "583", "messageId": "584", "endLine": 67, "endColumn": 70, "suggestions": "626"}, {"ruleId": "570", "severity": 1, "message": "627", "line": 21, "column": 3, "nodeType": null, "messageId": "572", "endLine": 21, "endColumn": 24}, {"ruleId": "576", "severity": 2, "message": "577", "line": 142, "column": 58, "nodeType": "578", "messageId": "579", "suggestions": "628"}, {"ruleId": "570", "severity": 1, "message": "589", "line": 10, "column": 3, "nodeType": null, "messageId": "572", "endLine": 10, "endColumn": 7}, {"ruleId": "570", "severity": 1, "message": "571", "line": 4, "column": 10, "nodeType": null, "messageId": "572", "endLine": 4, "endColumn": 15}, {"ruleId": "570", "severity": 1, "message": "629", "line": 20, "column": 19, "nodeType": null, "messageId": "572", "endLine": 20, "endColumn": 34}, {"ruleId": "576", "severity": 2, "message": "577", "line": 241, "column": 27, "nodeType": "578", "messageId": "579", "suggestions": "630"}, {"ruleId": "570", "severity": 1, "message": "631", "line": 12, "column": 3, "nodeType": null, "messageId": "572", "endLine": 12, "endColumn": 9}, {"ruleId": "570", "severity": 1, "message": "632", "line": 80, "column": 14, "nodeType": null, "messageId": "572", "endLine": 80, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "632", "line": 97, "column": 14, "nodeType": null, "messageId": "572", "endLine": 97, "endColumn": 19}, {"ruleId": "581", "severity": 2, "message": "582", "line": 113, "column": 48, "nodeType": "583", "messageId": "584", "endLine": 113, "endColumn": 51, "suggestions": "633"}, {"ruleId": "570", "severity": 1, "message": "632", "line": 122, "column": 14, "nodeType": null, "messageId": "572", "endLine": 122, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "632", "line": 136, "column": 14, "nodeType": null, "messageId": "572", "endLine": 136, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "632", "line": 168, "column": 14, "nodeType": null, "messageId": "572", "endLine": 168, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "571", "line": 7, "column": 10, "nodeType": null, "messageId": "572", "endLine": 7, "endColumn": 15}, {"ruleId": "570", "severity": 1, "message": "634", "line": 10, "column": 10, "nodeType": null, "messageId": "572", "endLine": 10, "endColumn": 19}, {"ruleId": "581", "severity": 2, "message": "582", "line": 50, "column": 50, "nodeType": "583", "messageId": "584", "endLine": 50, "endColumn": 53, "suggestions": "635"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 116, "column": 20, "nodeType": "583", "messageId": "584", "endLine": 116, "endColumn": 23, "suggestions": "636"}, {"ruleId": "570", "severity": 1, "message": "632", "line": 125, "column": 14, "nodeType": null, "messageId": "572", "endLine": 125, "endColumn": 19}, {"ruleId": "581", "severity": 2, "message": "582", "line": 158, "column": 54, "nodeType": "583", "messageId": "584", "endLine": 158, "endColumn": 57, "suggestions": "637"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 159, "column": 29, "nodeType": "583", "messageId": "584", "endLine": 159, "endColumn": 32, "suggestions": "638"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 164, "column": 101, "nodeType": "583", "messageId": "584", "endLine": 164, "endColumn": 104, "suggestions": "639"}, {"ruleId": "570", "severity": 1, "message": "632", "line": 189, "column": 14, "nodeType": null, "messageId": "572", "endLine": 189, "endColumn": 19}, {"ruleId": "581", "severity": 2, "message": "582", "line": 344, "column": 59, "nodeType": "583", "messageId": "584", "endLine": 344, "endColumn": 62, "suggestions": "640"}, {"ruleId": "576", "severity": 2, "message": "622", "line": 428, "column": 50, "nodeType": "578", "messageId": "579", "suggestions": "641"}, {"ruleId": "576", "severity": 2, "message": "622", "line": 428, "column": 64, "nodeType": "578", "messageId": "579", "suggestions": "642"}, {"ruleId": "570", "severity": 1, "message": "643", "line": 17, "column": 3, "nodeType": null, "messageId": "572", "endLine": 17, "endColumn": 9}, {"ruleId": "570", "severity": 1, "message": "644", "line": 23, "column": 10, "nodeType": null, "messageId": "572", "endLine": 23, "endColumn": 35}, {"ruleId": "570", "severity": 1, "message": "632", "line": 149, "column": 14, "nodeType": null, "messageId": "572", "endLine": 149, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "645", "line": 20, "column": 3, "nodeType": null, "messageId": "572", "endLine": 20, "endColumn": 8}, {"ruleId": "570", "severity": 1, "message": "646", "line": 51, "column": 10, "nodeType": null, "messageId": "572", "endLine": 51, "endColumn": 22}, {"ruleId": "570", "severity": 1, "message": "632", "line": 116, "column": 14, "nodeType": null, "messageId": "572", "endLine": 116, "endColumn": 19}, {"ruleId": "581", "severity": 2, "message": "582", "line": 246, "column": 75, "nodeType": "583", "messageId": "584", "endLine": 246, "endColumn": 78, "suggestions": "647"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 270, "column": 73, "nodeType": "583", "messageId": "584", "endLine": 270, "endColumn": 76, "suggestions": "648"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 286, "column": 80, "nodeType": "583", "messageId": "584", "endLine": 286, "endColumn": 83, "suggestions": "649"}, {"ruleId": "570", "severity": 1, "message": "632", "line": 61, "column": 14, "nodeType": null, "messageId": "572", "endLine": 61, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "632", "line": 82, "column": 14, "nodeType": null, "messageId": "572", "endLine": 82, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "632", "line": 94, "column": 16, "nodeType": null, "messageId": "572", "endLine": 94, "endColumn": 21}, {"ruleId": "570", "severity": 1, "message": "650", "line": 7, "column": 10, "nodeType": null, "messageId": "572", "endLine": 7, "endColumn": 16}, {"ruleId": "570", "severity": 1, "message": "651", "line": 7, "column": 18, "nodeType": null, "messageId": "572", "endLine": 7, "endColumn": 31}, {"ruleId": "570", "severity": 1, "message": "652", "line": 7, "column": 33, "nodeType": null, "messageId": "572", "endLine": 7, "endColumn": 43}, {"ruleId": "570", "severity": 1, "message": "653", "line": 7, "column": 45, "nodeType": null, "messageId": "572", "endLine": 7, "endColumn": 58}, {"ruleId": "570", "severity": 1, "message": "654", "line": 7, "column": 60, "nodeType": null, "messageId": "572", "endLine": 7, "endColumn": 71}, {"ruleId": "581", "severity": 2, "message": "582", "line": 38, "column": 59, "nodeType": "583", "messageId": "584", "endLine": 38, "endColumn": 62, "suggestions": "655"}, {"ruleId": "570", "severity": 1, "message": "656", "line": 242, "column": 3, "nodeType": null, "messageId": "572", "endLine": 242, "endColumn": 18}, {"ruleId": "570", "severity": 1, "message": "587", "line": 4, "column": 29, "nodeType": null, "messageId": "572", "endLine": 4, "endColumn": 44}, {"ruleId": "570", "severity": 1, "message": "634", "line": 8, "column": 10, "nodeType": null, "messageId": "572", "endLine": 8, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "657", "line": 10, "column": 10, "nodeType": null, "messageId": "572", "endLine": 10, "endColumn": 18}, {"ruleId": "570", "severity": 1, "message": "658", "line": 21, "column": 3, "nodeType": null, "messageId": "572", "endLine": 21, "endColumn": 11}, {"ruleId": "570", "severity": 1, "message": "659", "line": 23, "column": 3, "nodeType": null, "messageId": "572", "endLine": 23, "endColumn": 13}, {"ruleId": "570", "severity": 1, "message": "660", "line": 24, "column": 3, "nodeType": null, "messageId": "572", "endLine": 24, "endColumn": 8}, {"ruleId": "570", "severity": 1, "message": "661", "line": 9, "column": 10, "nodeType": null, "messageId": "572", "endLine": 9, "endColumn": 15}, {"ruleId": "570", "severity": 1, "message": "571", "line": 14, "column": 10, "nodeType": null, "messageId": "572", "endLine": 14, "endColumn": 15}, {"ruleId": "570", "severity": 1, "message": "662", "line": 15, "column": 10, "nodeType": null, "messageId": "572", "endLine": 15, "endColumn": 16}, {"ruleId": "570", "severity": 1, "message": "663", "line": 15, "column": 18, "nodeType": null, "messageId": "572", "endLine": 15, "endColumn": 32}, {"ruleId": "570", "severity": 1, "message": "664", "line": 15, "column": 34, "nodeType": null, "messageId": "572", "endLine": 15, "endColumn": 45}, {"ruleId": "570", "severity": 1, "message": "665", "line": 16, "column": 16, "nodeType": null, "messageId": "572", "endLine": 16, "endColumn": 21}, {"ruleId": "570", "severity": 1, "message": "631", "line": 16, "column": 23, "nodeType": null, "messageId": "572", "endLine": 16, "endColumn": 29}, {"ruleId": "570", "severity": 1, "message": "666", "line": 16, "column": 52, "nodeType": null, "messageId": "572", "endLine": 16, "endColumn": 57}, {"ruleId": "570", "severity": 1, "message": "667", "line": 16, "column": 59, "nodeType": null, "messageId": "572", "endLine": 16, "endColumn": 63}, {"ruleId": "570", "severity": 1, "message": "668", "line": 54, "column": 9, "nodeType": null, "messageId": "572", "endLine": 54, "endColumn": 26}, {"ruleId": "576", "severity": 2, "message": "577", "line": 149, "column": 36, "nodeType": "578", "messageId": "579", "suggestions": "669"}, {"ruleId": "576", "severity": 2, "message": "577", "line": 281, "column": 36, "nodeType": "578", "messageId": "579", "suggestions": "670"}, {"ruleId": "570", "severity": 1, "message": "634", "line": 12, "column": 10, "nodeType": null, "messageId": "572", "endLine": 12, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "600", "line": 22, "column": 3, "nodeType": null, "messageId": "572", "endLine": 22, "endColumn": 11}, {"ruleId": "570", "severity": 1, "message": "671", "line": 26, "column": 10, "nodeType": null, "messageId": "572", "endLine": 26, "endColumn": 12}, {"ruleId": "570", "severity": 1, "message": "632", "line": 166, "column": 14, "nodeType": null, "messageId": "572", "endLine": 166, "endColumn": 19}, {"ruleId": "576", "severity": 2, "message": "577", "line": 283, "column": 64, "nodeType": "578", "messageId": "579", "suggestions": "672"}, {"ruleId": "570", "severity": 1, "message": "643", "line": 20, "column": 3, "nodeType": null, "messageId": "572", "endLine": 20, "endColumn": 9}, {"ruleId": "570", "severity": 1, "message": "673", "line": 51, "column": 10, "nodeType": null, "messageId": "572", "endLine": 51, "endColumn": 27}, {"ruleId": "570", "severity": 1, "message": "674", "line": 51, "column": 29, "nodeType": null, "messageId": "572", "endLine": 51, "endColumn": 49}, {"ruleId": "675", "severity": 2, "message": "676", "line": 219, "column": 18, "nodeType": "594", "messageId": "677", "endLine": 219, "endColumn": 39, "suggestions": "678"}, {"ruleId": "675", "severity": 2, "message": "676", "line": 220, "column": 18, "nodeType": "594", "messageId": "677", "endLine": 220, "endColumn": 36, "suggestions": "679"}, {"ruleId": "675", "severity": 2, "message": "676", "line": 221, "column": 18, "nodeType": "594", "messageId": "677", "endLine": 221, "endColumn": 30, "suggestions": "680"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 123, "column": 28, "nodeType": "583", "messageId": "584", "endLine": 123, "endColumn": 31, "suggestions": "681"}, {"ruleId": "581", "severity": 2, "message": "582", "line": 51, "column": 46, "nodeType": "583", "messageId": "584", "endLine": 51, "endColumn": 49, "suggestions": "682"}, {"ruleId": "570", "severity": 1, "message": "632", "line": 283, "column": 14, "nodeType": null, "messageId": "572", "endLine": 283, "endColumn": 19}, {"ruleId": "581", "severity": 2, "message": "582", "line": 55, "column": 62, "nodeType": "583", "messageId": "584", "endLine": 55, "endColumn": 65, "suggestions": "683"}, {"ruleId": "570", "severity": 1, "message": "632", "line": 72, "column": 14, "nodeType": null, "messageId": "572", "endLine": 72, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "684", "line": 110, "column": 11, "nodeType": null, "messageId": "572", "endLine": 110, "endColumn": 17}, {"ruleId": "581", "severity": 2, "message": "582", "line": 129, "column": 34, "nodeType": "583", "messageId": "584", "endLine": 129, "endColumn": 37, "suggestions": "685"}, {"ruleId": "570", "severity": 1, "message": "686", "line": 230, "column": 11, "nodeType": null, "messageId": "572", "endLine": 230, "endColumn": 19}, {"ruleId": "570", "severity": 1, "message": "687", "line": 13, "column": 7, "nodeType": null, "messageId": "572", "endLine": 13, "endColumn": 20}, {"ruleId": "570", "severity": 1, "message": "688", "line": 23, "column": 7, "nodeType": null, "messageId": "572", "endLine": 23, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'Badge' is defined but never used.", "unusedVar", "'mockAttendanceRecords' is defined but never used.", "'CompactWeatherWidget' is defined but never used.", "'MonthlyTrendChart' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["689", "690", "691", "692"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["693", "694"], ["695", "696"], "'CardDescription' is defined but never used.", "'findStudentByQRCode' is defined but never used.", "'Wifi' is defined but never used.", "'setAvailableCameras' is assigned a value but never used.", ["697", "698"], "prefer-const", "'attendanceRecords' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "699", "text": "700"}, "'reason' is assigned a value but never used.", "'attendanceRecord' is assigned a value but never used.", ["701", "702"], "'Calendar' is defined but never used.", "'MessageSquare' is defined but never used.", "'format' is defined but never used.", "'User' is defined but never used.", ["703", "704"], "'Thermometer' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'SystemHealthIndicator' is defined but never used.", "'children' is defined but never used.", ["705", "706"], "'useEffect' is defined but never used.", ["707", "708"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'playSound'. Either include it or remove the dependency array.", "ArrayExpression", ["709"], "React Hook useEffect has a missing dependency: 'handleDismiss'. Either include it or remove the dependency array.", ["710"], "'DialogTrigger' is defined but never used.", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["711", "712", "713", "714"], ["715", "716", "717", "718"], "'Settings' is defined but never used.", ["719", "720"], "'showAttendanceHistory' is assigned a value but never used.", ["721", "722", "723", "724"], "'AttendanceStats' is defined but never used.", ["725", "726", "727", "728"], "'Upload' is defined but never used.", "'error' is defined but never used.", ["729", "730"], "'Separator' is defined but never used.", ["731", "732"], ["733", "734"], ["735", "736"], ["737", "738"], ["739", "740"], ["741", "742"], ["743", "744", "745", "746"], ["747", "748", "749", "750"], "'MapPin' is defined but never used.", "'StudentRegistrationDialog' is defined but never used.", "'Users' is defined but never used.", "'isGenerating' is assigned a value but never used.", ["751", "752"], ["753", "754"], ["755", "756"], "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", ["757", "758"], "'onFiltersChange' is defined but never used.", "'Progress' is defined but never used.", "'Download' is defined but never used.", "'TrendingUp' is defined but never used.", "'Clock' is defined but never used.", "'Label' is defined but never used.", "'Avatar' is defined but never used.", "'AvatarFallback' is defined but never used.", "'AvatarImage' is defined but never used.", "'Minus' is defined but never used.", "'Phone' is defined but never used.", "'Mail' is defined but never used.", "'handlePhotoChange' is assigned a value but never used.", ["759", "760", "761", "762"], ["763", "764", "765", "766"], "'cn' is defined but never used.", ["767", "768", "769", "770"], "'showProfileDialog' is assigned a value but never used.", "'setShowProfileDialog' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["771"], ["772"], ["773"], ["774", "775"], ["776", "777"], ["778", "779"], "'qrSize' is assigned a value but never used.", ["780", "781"], "'qrString' is assigned a value but never used.", "'addressSchema' is assigned a value but never used.", "'guardianSchema' is assigned a value but never used.", {"messageId": "782", "data": "783", "fix": "784", "desc": "785"}, {"messageId": "782", "data": "786", "fix": "787", "desc": "788"}, {"messageId": "782", "data": "789", "fix": "790", "desc": "791"}, {"messageId": "782", "data": "792", "fix": "793", "desc": "794"}, {"messageId": "795", "fix": "796", "desc": "797"}, {"messageId": "798", "fix": "799", "desc": "800"}, {"messageId": "795", "fix": "801", "desc": "797"}, {"messageId": "798", "fix": "802", "desc": "800"}, {"messageId": "795", "fix": "803", "desc": "797"}, {"messageId": "798", "fix": "804", "desc": "800"}, [330, 376], "const attendanceRecords: AttendanceRecord[] = []", {"messageId": "795", "fix": "805", "desc": "797"}, {"messageId": "798", "fix": "806", "desc": "800"}, {"messageId": "795", "fix": "807", "desc": "797"}, {"messageId": "798", "fix": "808", "desc": "800"}, {"messageId": "795", "fix": "809", "desc": "797"}, {"messageId": "798", "fix": "810", "desc": "800"}, {"messageId": "795", "fix": "811", "desc": "797"}, {"messageId": "798", "fix": "812", "desc": "800"}, {"desc": "813", "fix": "814"}, {"desc": "815", "fix": "816"}, {"messageId": "782", "data": "817", "fix": "818", "desc": "819"}, {"messageId": "782", "data": "820", "fix": "821", "desc": "822"}, {"messageId": "782", "data": "823", "fix": "824", "desc": "825"}, {"messageId": "782", "data": "826", "fix": "827", "desc": "828"}, {"messageId": "782", "data": "829", "fix": "830", "desc": "819"}, {"messageId": "782", "data": "831", "fix": "832", "desc": "822"}, {"messageId": "782", "data": "833", "fix": "834", "desc": "825"}, {"messageId": "782", "data": "835", "fix": "836", "desc": "828"}, {"messageId": "795", "fix": "837", "desc": "797"}, {"messageId": "798", "fix": "838", "desc": "800"}, {"messageId": "782", "data": "839", "fix": "840", "desc": "785"}, {"messageId": "782", "data": "841", "fix": "842", "desc": "788"}, {"messageId": "782", "data": "843", "fix": "844", "desc": "791"}, {"messageId": "782", "data": "845", "fix": "846", "desc": "794"}, {"messageId": "782", "data": "847", "fix": "848", "desc": "785"}, {"messageId": "782", "data": "849", "fix": "850", "desc": "788"}, {"messageId": "782", "data": "851", "fix": "852", "desc": "791"}, {"messageId": "782", "data": "853", "fix": "854", "desc": "794"}, {"messageId": "795", "fix": "855", "desc": "797"}, {"messageId": "798", "fix": "856", "desc": "800"}, {"messageId": "795", "fix": "857", "desc": "797"}, {"messageId": "798", "fix": "858", "desc": "800"}, {"messageId": "795", "fix": "859", "desc": "797"}, {"messageId": "798", "fix": "860", "desc": "800"}, {"messageId": "795", "fix": "861", "desc": "797"}, {"messageId": "798", "fix": "862", "desc": "800"}, {"messageId": "795", "fix": "863", "desc": "797"}, {"messageId": "798", "fix": "864", "desc": "800"}, {"messageId": "795", "fix": "865", "desc": "797"}, {"messageId": "798", "fix": "866", "desc": "800"}, {"messageId": "795", "fix": "867", "desc": "797"}, {"messageId": "798", "fix": "868", "desc": "800"}, {"messageId": "782", "data": "869", "fix": "870", "desc": "819"}, {"messageId": "782", "data": "871", "fix": "872", "desc": "822"}, {"messageId": "782", "data": "873", "fix": "874", "desc": "825"}, {"messageId": "782", "data": "875", "fix": "876", "desc": "828"}, {"messageId": "782", "data": "877", "fix": "878", "desc": "819"}, {"messageId": "782", "data": "879", "fix": "880", "desc": "822"}, {"messageId": "782", "data": "881", "fix": "882", "desc": "825"}, {"messageId": "782", "data": "883", "fix": "884", "desc": "828"}, {"messageId": "795", "fix": "885", "desc": "797"}, {"messageId": "798", "fix": "886", "desc": "800"}, {"messageId": "795", "fix": "887", "desc": "797"}, {"messageId": "798", "fix": "888", "desc": "800"}, {"messageId": "795", "fix": "889", "desc": "797"}, {"messageId": "798", "fix": "890", "desc": "800"}, {"messageId": "795", "fix": "891", "desc": "797"}, {"messageId": "798", "fix": "892", "desc": "800"}, {"messageId": "782", "data": "893", "fix": "894", "desc": "785"}, {"messageId": "782", "data": "895", "fix": "896", "desc": "788"}, {"messageId": "782", "data": "897", "fix": "898", "desc": "791"}, {"messageId": "782", "data": "899", "fix": "900", "desc": "794"}, {"messageId": "782", "data": "901", "fix": "902", "desc": "785"}, {"messageId": "782", "data": "903", "fix": "904", "desc": "788"}, {"messageId": "782", "data": "905", "fix": "906", "desc": "791"}, {"messageId": "782", "data": "907", "fix": "908", "desc": "794"}, {"messageId": "782", "data": "909", "fix": "910", "desc": "785"}, {"messageId": "782", "data": "911", "fix": "912", "desc": "788"}, {"messageId": "782", "data": "913", "fix": "914", "desc": "791"}, {"messageId": "782", "data": "915", "fix": "916", "desc": "794"}, {"messageId": "917", "fix": "918", "desc": "919"}, {"messageId": "917", "fix": "920", "desc": "919"}, {"messageId": "917", "fix": "921", "desc": "919"}, {"messageId": "795", "fix": "922", "desc": "797"}, {"messageId": "798", "fix": "923", "desc": "800"}, {"messageId": "795", "fix": "924", "desc": "797"}, {"messageId": "798", "fix": "925", "desc": "800"}, {"messageId": "795", "fix": "926", "desc": "797"}, {"messageId": "798", "fix": "927", "desc": "800"}, {"messageId": "795", "fix": "928", "desc": "797"}, {"messageId": "798", "fix": "929", "desc": "800"}, "replaceWithAlt", {"alt": "930"}, {"range": "931", "text": "932"}, "Replace with `&apos;`.", {"alt": "933"}, {"range": "934", "text": "935"}, "Replace with `&lsquo;`.", {"alt": "936"}, {"range": "937", "text": "938"}, "Replace with `&#39;`.", {"alt": "939"}, {"range": "940", "text": "941"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "942", "text": "943"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "944", "text": "945"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "946", "text": "943"}, {"range": "947", "text": "945"}, {"range": "948", "text": "943"}, {"range": "949", "text": "945"}, {"range": "950", "text": "943"}, {"range": "951", "text": "945"}, {"range": "952", "text": "943"}, {"range": "953", "text": "945"}, {"range": "954", "text": "943"}, {"range": "955", "text": "945"}, {"range": "956", "text": "943"}, {"range": "957", "text": "945"}, "Update the dependencies array to be: [notifications, audioEnabled, playSound]", {"range": "958", "text": "959"}, "Update the dependencies array to be: [handleDismiss, notification.duration]", {"range": "960", "text": "961"}, {"alt": "962"}, {"range": "963", "text": "964"}, "Replace with `&quot;`.", {"alt": "965"}, {"range": "966", "text": "967"}, "Replace with `&ldquo;`.", {"alt": "968"}, {"range": "969", "text": "970"}, "Replace with `&#34;`.", {"alt": "971"}, {"range": "972", "text": "973"}, "Replace with `&rdquo;`.", {"alt": "962"}, {"range": "974", "text": "962"}, {"alt": "965"}, {"range": "975", "text": "965"}, {"alt": "968"}, {"range": "976", "text": "968"}, {"alt": "971"}, {"range": "977", "text": "971"}, {"range": "978", "text": "943"}, {"range": "979", "text": "945"}, {"alt": "930"}, {"range": "980", "text": "981"}, {"alt": "933"}, {"range": "982", "text": "983"}, {"alt": "936"}, {"range": "984", "text": "985"}, {"alt": "939"}, {"range": "986", "text": "987"}, {"alt": "930"}, {"range": "988", "text": "989"}, {"alt": "933"}, {"range": "990", "text": "991"}, {"alt": "936"}, {"range": "992", "text": "993"}, {"alt": "939"}, {"range": "994", "text": "995"}, {"range": "996", "text": "943"}, {"range": "997", "text": "945"}, {"range": "998", "text": "943"}, {"range": "999", "text": "945"}, {"range": "1000", "text": "943"}, {"range": "1001", "text": "945"}, {"range": "1002", "text": "943"}, {"range": "1003", "text": "945"}, {"range": "1004", "text": "943"}, {"range": "1005", "text": "945"}, {"range": "1006", "text": "943"}, {"range": "1007", "text": "945"}, {"range": "1008", "text": "943"}, {"range": "1009", "text": "945"}, {"alt": "962"}, {"range": "1010", "text": "1011"}, {"alt": "965"}, {"range": "1012", "text": "1013"}, {"alt": "968"}, {"range": "1014", "text": "1015"}, {"alt": "971"}, {"range": "1016", "text": "1017"}, {"alt": "962"}, {"range": "1018", "text": "1019"}, {"alt": "965"}, {"range": "1020", "text": "1021"}, {"alt": "968"}, {"range": "1022", "text": "1023"}, {"alt": "971"}, {"range": "1024", "text": "1025"}, {"range": "1026", "text": "943"}, {"range": "1027", "text": "945"}, {"range": "1028", "text": "943"}, {"range": "1029", "text": "945"}, {"range": "1030", "text": "943"}, {"range": "1031", "text": "945"}, {"range": "1032", "text": "943"}, {"range": "1033", "text": "945"}, {"alt": "930"}, {"range": "1034", "text": "1035"}, {"alt": "933"}, {"range": "1036", "text": "1037"}, {"alt": "936"}, {"range": "1038", "text": "1039"}, {"alt": "939"}, {"range": "1040", "text": "1041"}, {"alt": "930"}, {"range": "1042", "text": "1043"}, {"alt": "933"}, {"range": "1044", "text": "1045"}, {"alt": "936"}, {"range": "1046", "text": "1047"}, {"alt": "939"}, {"range": "1048", "text": "1049"}, {"alt": "930"}, {"range": "1050", "text": "1051"}, {"alt": "933"}, {"range": "1052", "text": "1053"}, {"alt": "936"}, {"range": "1054", "text": "1055"}, {"alt": "939"}, {"range": "1056", "text": "1057"}, "replaceEmptyInterfaceWithSuper", {"range": "1058", "text": "1059"}, "Replace empty interface with a type alias.", {"range": "1060", "text": "1061"}, {"range": "1062", "text": "1063"}, {"range": "1064", "text": "943"}, {"range": "1065", "text": "945"}, {"range": "1066", "text": "943"}, {"range": "1067", "text": "945"}, {"range": "1068", "text": "943"}, {"range": "1069", "text": "945"}, {"range": "1070", "text": "943"}, {"range": "1071", "text": "945"}, "&apos;", [9805, 9856], "\n                Export Today&apos;s Data\n              ", "&lsquo;", [9805, 9856], "\n                Export Today&lsquo;s Data\n              ", "&#39;", [9805, 9856], "\n                Export Today&#39;s Data\n              ", "&rsquo;", [9805, 9856], "\n                Export Today&rsquo;s Data\n              ", [2664, 2667], "unknown", [2664, 2667], "never", [2686, 2689], [2686, 2689], [4939, 4942], [4939, 4942], [1175, 1178], [1175, 1178], [3613, 3616], [3613, 3616], [1496, 1499], [1496, 1499], [1010, 1013], [1010, 1013], [3924, 3953], "[notifications, audioEnabled, playSound]", [6673, 6696], "[handleDismiss, notification.duration]", "&quot;", [6268, 6296], "No students found matching &quot;", "&ldquo;", [6268, 6296], "No students found matching &ldquo;", "&#34;", [6268, 6296], "No students found matching &#34;", "&rdquo;", [6268, 6296], "No students found matching &rdquo;", [6309, 6310], [6309, 6310], [6309, 6310], [6309, 6310], [1663, 1666], [1663, 1666], [4666, 4684], "Today&apos;s Attendance", [4666, 4684], "Today&lsquo;s Attendance", [4666, 4684], "Today&#39;s Attendance", [4666, 4684], "Today&rsquo;s Attendance", [8817, 8982], "\n              This student&apos;s attendance rate is below the minimum requirement of 75%. \n              Consider reaching out to the student and guardian.\n            ", [8817, 8982], "\n              This student&lsquo;s attendance rate is below the minimum requirement of 75%. \n              Consider reaching out to the student and guardian.\n            ", [8817, 8982], "\n              This student&#39;s attendance rate is below the minimum requirement of 75%. \n              Consider reaching out to the student and guardian.\n            ", [8817, 8982], "\n              This student&rsquo;s attendance rate is below the minimum requirement of 75%. \n              Consider reaching out to the student and guardian.\n            ", [3808, 3811], [3808, 3811], [1559, 1562], [1559, 1562], [3325, 3328], [3325, 3328], [4661, 4664], [4661, 4664], [4694, 4697], [4694, 4697], [5038, 5041], [5038, 5041], [12382, 12385], [12382, 12385], [16227, 16236], ", Field &quot;", [16227, 16236], ", Field &ldquo;", [16227, 16236], ", Field &#34;", [16227, 16236], ", Field &rdquo;", [16249, 16252], "&quot;: ", [16249, 16252], "&ldquo;: ", [16249, 16252], "&#34;: ", [16249, 16252], "&rdquo;: ", [8524, 8527], [8524, 8527], [9662, 9665], [9662, 9665], [10525, 10528], [10525, 10528], [1353, 1356], [1353, 1356], [5595, 5671], "\n                  Enter the student&apos;s personal information\n                ", [5595, 5671], "\n                  Enter the student&lsquo;s personal information\n                ", [5595, 5671], "\n                  Enter the student&#39;s personal information\n                ", [5595, 5671], "\n                  Enter the student&rsquo;s personal information\n                ", [10696, 10768], "\n                  Enter the student&apos;s academic details\n                ", [10696, 10768], "\n                  Enter the student&lsquo;s academic details\n                ", [10696, 10768], "\n                  Enter the student&#39;s academic details\n                ", [10696, 10768], "\n                  Enter the student&rsquo;s academic details\n                ", [10032, 10125], "\n                    This reason will be recorded in the student&apos;s history\n                  ", [10032, 10125], "\n                    This reason will be recorded in the student&lsquo;s history\n                  ", [10032, 10125], "\n                    This reason will be recorded in the student&#39;s history\n                  ", [10032, 10125], "\n                    This reason will be recorded in the student&rsquo;s history\n                  ", [4396, 4459], "type StudentLookupResponse = ApiResponse<Student>", [4467, 4536], "type AttendanceResponse = ApiResponse<AttendanceRecord>", [4544, 4601], "type ScanResponse = ApiResponse<ScanResult>", [2691, 2694], [2691, 2694], [1472, 1475], [1472, 1475], [1627, 1630], [1627, 1630], [3646, 3649], [3646, 3649]]