"use strict";exports.id=657,exports.ids=[657],exports.modules={643:(a,b,c)=>{c.d(b,{M4:()=>v,il:()=>u,nn:()=>t,tp:()=>s});var d=c(60687),e=c(48482),f=c(61855),g=c(85168),h=c(27747),i=c(19598),j=c(23812),k=c(15202),l=c(93464),m=c(2041),n=c(41887),o=c(56651),p=c(56988),q=c(25679),r=c(55192);function s({data:a}){return(0,d.jsxs)(r.Zp,{children:[(0,d.jsxs)(r.aR,{children:[(0,d.jsx)(r.ZB,{children:"Weekly Attendance Trend"}),(0,d.jsx)(r.<PERSON>,{children:"Daily attendance patterns for this week"})]}),(0,d.jsx)(r.<PERSON>,{children:(0,d.jsx)(e.u,{width:"100%",height:300,children:(0,d.jsxs)(f.Q,{data:a,children:[(0,d.jsx)(g.d,{strokeDasharray:"3 3"}),(0,d.jsx)(h.W,{dataKey:"day"}),(0,d.jsx)(i.h,{}),(0,d.jsx)(j.m,{}),(0,d.jsx)(k.s,{}),(0,d.jsx)(l.Gk,{type:"monotone",dataKey:"present",stackId:"1",stroke:"#22c55e",fill:"#22c55e",fillOpacity:.8}),(0,d.jsx)(l.Gk,{type:"monotone",dataKey:"late",stackId:"1",stroke:"#f59e0b",fill:"#f59e0b",fillOpacity:.8}),(0,d.jsx)(l.Gk,{type:"monotone",dataKey:"absent",stackId:"1",stroke:"#ef4444",fill:"#ef4444",fillOpacity:.8})]})})})]})}function t({data:a}){return(0,d.jsxs)(r.Zp,{children:[(0,d.jsxs)(r.aR,{children:[(0,d.jsx)(r.ZB,{children:"Grade Level Breakdown"}),(0,d.jsx)(r.BT,{children:"Attendance by grade level"})]}),(0,d.jsx)(r.Wu,{children:(0,d.jsx)(e.u,{width:"100%",height:300,children:(0,d.jsxs)(m.E,{data:a,children:[(0,d.jsx)(g.d,{strokeDasharray:"3 3"}),(0,d.jsx)(h.W,{dataKey:"grade"}),(0,d.jsx)(i.h,{}),(0,d.jsx)(j.m,{}),(0,d.jsx)(k.s,{}),(0,d.jsx)(n.y,{dataKey:"present",fill:"#22c55e",name:"Present"}),(0,d.jsx)(n.y,{dataKey:"late",fill:"#f59e0b",name:"Late"}),(0,d.jsx)(n.y,{dataKey:"absent",fill:"#ef4444",name:"Absent"})]})})})]})}function u({present:a,late:b,absent:c}){let f=[{name:"Present",value:a,color:"#22c55e"},{name:"Late",value:b,color:"#f59e0b"},{name:"Absent",value:c,color:"#ef4444"}],g=a+b+c;return(0,d.jsxs)(r.Zp,{children:[(0,d.jsxs)(r.aR,{children:[(0,d.jsx)(r.ZB,{children:"Today's Attendance Distribution"}),(0,d.jsx)(r.BT,{children:"Current attendance breakdown"})]}),(0,d.jsx)(r.Wu,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(e.u,{width:"60%",height:200,children:(0,d.jsxs)(o.r,{children:[(0,d.jsx)(p.F,{data:f,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:f.map((a,b)=>(0,d.jsx)(q.f,{fill:a.color},`cell-${b}`))}),(0,d.jsx)(j.m,{})]})}),(0,d.jsx)("div",{className:"space-y-2",children:f.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:a.color}}),(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsxs)("div",{className:"text-muted-foreground",children:[a.value," (",(a.value/g*100).toFixed(1),"%)"]})]})]},b))})]})})]})}function v({data:a}){let b=Array.from({length:10},(a,b)=>b+7),c=Math.max(...a.map(a=>a.count));return(0,d.jsxs)(r.Zp,{children:[(0,d.jsxs)(r.aR,{children:[(0,d.jsx)(r.ZB,{children:"Attendance Heat Map"}),(0,d.jsx)(r.BT,{children:"Student check-in patterns by day and hour"})]}),(0,d.jsxs)(r.Wu,{children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex space-x-1",children:[(0,d.jsx)("div",{className:"w-12"}),b.map(a=>(0,d.jsx)("div",{className:"w-8 text-xs text-center text-muted-foreground",children:a},a))]}),["Mon","Tue","Wed","Thu","Fri"].map(e=>(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("div",{className:"w-12 text-xs text-muted-foreground",children:e}),b.map(b=>{let f=((b,c)=>{let d=a.find(a=>a.day===b&&a.hour===c);return d?d.count:0})(e,b);return(0,d.jsx)("div",{className:"w-8 h-6 rounded-sm border border-border",style:{backgroundColor:`rgba(34, 197, 94, ${f/c})`},title:`${e} ${b}:00 - ${f} students`},`${e}-${b}`)})]},e))]}),(0,d.jsxs)("div",{className:"flex items-center justify-between mt-4 text-xs text-muted-foreground",children:[(0,d.jsx)("span",{children:"Less"}),(0,d.jsx)("div",{className:"flex space-x-1",children:[0,.2,.4,.6,.8,1].map(a=>(0,d.jsx)("div",{className:"w-3 h-3 rounded-sm border border-border",style:{backgroundColor:`rgba(34, 197, 94, ${a})`}},a))}),(0,d.jsx)("span",{children:"More"})]})]})]})}},15096:(a,b,c)=>{c.d(b,{IK:()=>e,Kq:()=>h,Lp:()=>i,Y1:()=>f,Z9:()=>d,o:()=>j});let d=[{id:"STU001",name:"Maria Cristina Santos",email:"<EMAIL>",course:"Junior High School",year:"1st Year",section:"Grade 7-A",grade:"7",status:"Active",photo:"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU001_2025"},{id:"STU002",name:"Juan Carlos Dela Cruz",email:"<EMAIL>",course:"Junior High School",year:"1st Year",section:"Grade 7-B",grade:"7",status:"Active",photo:"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU002_2025"},{id:"STU003",name:"Ana Marie Reyes",email:"<EMAIL>",course:"Junior High School",year:"1st Year",section:"Grade 7-A",grade:"7",status:"Active",photo:"https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU003_2025"},{id:"STU004",name:"Jose Miguel Rodriguez",email:"<EMAIL>",course:"Junior High School",year:"2nd Year",section:"Grade 8-A",grade:"8",status:"Active",photo:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU004_2025"},{id:"STU005",name:"Princess Mae Garcia",email:"<EMAIL>",course:"Junior High School",year:"2nd Year",section:"Grade 8-B",grade:"8",status:"Active",photo:"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU005_2025"},{id:"STU006",name:"Mark Anthony Villanueva",email:"<EMAIL>",course:"Junior High School",year:"3rd Year",section:"Grade 9-A",grade:"9",status:"Active",photo:"https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU006_2025"},{id:"STU007",name:"Angelica Mae Torres",email:"<EMAIL>",course:"Junior High School",year:"3rd Year",section:"Grade 9-B",grade:"9",status:"Active",photo:"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU007_2025"},{id:"STU008",name:"Christian Paul Mendoza",email:"<EMAIL>",course:"Junior High School",year:"4th Year",section:"Grade 10-A",grade:"10",status:"Active",photo:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU008_2025"},{id:"STU009",name:"Kimberly Rose Flores",email:"<EMAIL>",course:"Junior High School",year:"4th Year",section:"Grade 10-B",grade:"10",status:"Active",photo:"https://images.unsplash.com/photo-*************-53994a69daeb?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU009_2025"},{id:"STU010",name:"John Michael Cruz",email:"<EMAIL>",course:"Information and Communications Technology",year:"1st Year Senior High",section:"ICT 11-A",grade:"11",status:"Active",photo:"https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU010_2025"},{id:"STU011",name:"Mary Grace Aquino",email:"<EMAIL>",course:"Accountancy, Business and Management",year:"1st Year Senior High",section:"ABM 11-A",grade:"11",status:"Active",photo:"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU011_2025"},{id:"STU012",name:"Ryan James Bautista",email:"<EMAIL>",course:"Information and Communications Technology",year:"2nd Year Senior High",section:"ICT 12-A",grade:"12",status:"Active",photo:"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU012_2025"},{id:"STU013",name:"Sarah Jane Morales",email:"<EMAIL>",course:"Humanities and Social Sciences",year:"2nd Year Senior High",section:"HUMSS 12-A",grade:"12",status:"Active",photo:"https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU013_2025"}],e=[{id:"SUBJ001",name:"Programming Fundamentals",code:"IT101",instructor:"Prof. Martinez",schedule:[{day:"Monday",startTime:"08:00",endTime:"10:00"},{day:"Wednesday",startTime:"08:00",endTime:"10:00"},{day:"Friday",startTime:"08:00",endTime:"10:00"}]},{id:"SUBJ002",name:"Database Management",code:"IT201",instructor:"Prof. Rodriguez",schedule:[{day:"Tuesday",startTime:"10:00",endTime:"12:00"},{day:"Thursday",startTime:"10:00",endTime:"12:00"}]},{id:"SUBJ003",name:"Web Development",code:"IT301",instructor:"Prof. Santos",schedule:[{day:"Monday",startTime:"13:00",endTime:"15:00"},{day:"Wednesday",startTime:"13:00",endTime:"15:00"}]},{id:"SUBJ004",name:"Data Structures",code:"CS201",instructor:"Prof. Reyes",schedule:[{day:"Tuesday",startTime:"08:00",endTime:"10:00"},{day:"Thursday",startTime:"08:00",endTime:"10:00"}]},{id:"SUBJ005",name:"Software Engineering",code:"CS301",instructor:"Prof. Cruz",schedule:[{day:"Monday",startTime:"15:00",endTime:"17:00"},{day:"Friday",startTime:"15:00",endTime:"17:00"}]}],f=[{id:"PERIOD001",name:"1st Period",startTime:"08:00",endTime:"10:00",type:"morning"},{id:"PERIOD002",name:"2nd Period",startTime:"10:00",endTime:"12:00",type:"morning"},{id:"PERIOD003",name:"3rd Period",startTime:"13:00",endTime:"15:00",type:"afternoon"},{id:"PERIOD004",name:"4th Period",startTime:"15:00",endTime:"17:00",type:"afternoon"},{id:"PERIOD005",name:"Evening Class",startTime:"18:00",endTime:"20:00",type:"evening"}],g=[{id:"ATT001",studentId:"STU001",studentName:"Maria Cristina Santos",course:"Junior High School",checkIn:"7:45 AM",checkOut:"4:30 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",timestamp:new Date(new Date().setHours(7,45,0))},{id:"ATT002",studentId:"STU002",studentName:"Juan Carlos Dela Cruz",course:"Junior High School",checkIn:"7:50 AM",checkOut:"4:25 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",timestamp:new Date(new Date().setHours(7,50,0))},{id:"ATT003",studentId:"STU003",studentName:"Ana Marie Reyes",course:"Junior High School",checkIn:"8:15 AM",checkOut:"4:35 PM",date:new Date().toISOString().split("T")[0],status:"Late",type:"gate",timestamp:new Date(new Date().setHours(8,15,0))},{id:"ATT004",studentId:"STU004",studentName:"Jose Miguel Rodriguez",course:"Junior High School",date:new Date().toISOString().split("T")[0],status:"Absent",type:"subject",subject:"Mathematics",period:"1st Period",timestamp:new Date(new Date().setHours(8,0,0))},{id:"ATT005",studentId:"STU005",studentName:"Princess Mae Garcia",course:"Junior High School",checkIn:"7:55 AM",checkOut:"4:20 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",timestamp:new Date(new Date().setHours(7,55,0))},{id:"ATT006",studentId:"STU010",studentName:"John Michael Cruz",course:"Information and Communications Technology",checkIn:"7:40 AM",checkOut:"5:00 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",timestamp:new Date(new Date().setHours(7,40,0))},{id:"ATT007",studentId:"STU012",studentName:"Ryan James Bautista",course:"Information and Communications Technology",checkIn:"8:10 AM",checkOut:"5:05 PM",date:new Date().toISOString().split("T")[0],status:"Late",type:"gate",timestamp:new Date(new Date().setHours(8,10,0))}],h={totalStudents:1234,presentToday:1105,lateToday:23,absentToday:106,attendanceRate:89.5,weeklyTrend:[{day:"Mon",present:1150,late:15,absent:69},{day:"Tue",present:1120,late:28,absent:86},{day:"Wed",present:1105,late:23,absent:106},{day:"Thu",present:1140,late:18,absent:76},{day:"Fri",present:1095,late:35,absent:104}],gradeBreakdown:[{grade:"7",total:180,present:165,late:3,absent:12},{grade:"8",total:175,present:158,late:4,absent:13},{grade:"9",total:170,present:155,late:2,absent:13},{grade:"10",total:165,present:148,late:5,absent:12},{grade:"11",total:272,present:245,late:6,absent:21},{grade:"12",total:272,present:234,late:3,absent:35}]},i=[{id:"ACT001",type:"scan",studentName:"Maria Cristina Santos",action:"Check In",time:"2 minutes ago",status:"success"},{id:"ACT002",type:"scan",studentName:"Juan Carlos Dela Cruz",action:"Check Out",time:"5 minutes ago",status:"success"},{id:"ACT003",type:"alert",studentName:"Jose Miguel Rodriguez",action:"Marked Absent",time:"15 minutes ago",status:"warning"},{id:"ACT004",type:"scan",studentName:"Princess Mae Garcia",action:"Late Arrival",time:"25 minutes ago",status:"warning"}];function j(a){let b=new Date().toISOString().split("T")[0];return g.find(c=>c.studentId===a&&c.date===b)}},41031:(a,b,c)=>{c.d(b,{C8:()=>o,iS:()=>p,nz:()=>n});var d=c(60687),e=c(55192),f=c(59821),g=c(86287),h=c(41312),i=c(40228),j=c(48730),k=c(25541),l=c(12640),m=c(96241);function n({totalStudents:a,presentToday:b,lateToday:c,absentToday:f,attendanceRate:n,className:o}){let p=[{title:"Total Students",value:a.toLocaleString(),icon:h.A,description:"Enrolled students",color:"text-blue-600",bgColor:"bg-blue-50",change:"+2.5%",trend:"up"},{title:"Present Today",value:b.toLocaleString(),icon:i.A,description:`${n}% attendance rate`,color:"text-green-600",bgColor:"bg-green-50",change:"+1.2%",trend:"up"},{title:"Late Arrivals",value:c.toString(),icon:j.A,description:"Students arrived late",color:"text-yellow-600",bgColor:"bg-yellow-50",change:"-0.8%",trend:"down"},{title:"Absent Today",value:f.toString(),icon:h.A,description:`${(f/a*100).toFixed(1)}% of total`,color:"text-red-600",bgColor:"bg-red-50",change:"+0.3%",trend:"up"}];return(0,d.jsx)("div",{className:(0,m.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-4",o),children:p.map((a,b)=>{let c=a.icon,f="up"===a.trend?k.A:l.A;return(0,d.jsxs)(e.Zp,{className:"relative overflow-hidden",children:[(0,d.jsxs)(e.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.ZB,{className:"text-sm font-medium text-muted-foreground",children:a.title}),(0,d.jsx)("div",{className:(0,m.cn)("p-2 rounded-lg",a.bgColor),children:(0,d.jsx)(c,{className:(0,m.cn)("h-4 w-4",a.color)})})]}),(0,d.jsxs)(e.Wu,{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:a.value}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:a.description})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(f,{className:(0,m.cn)("h-3 w-3","up"===a.trend?"text-green-500":"text-red-500")}),(0,d.jsx)("span",{className:(0,m.cn)("text-xs font-medium","up"===a.trend?"text-green-500":"text-red-500"),children:a.change})]})]}),1===b&&(0,d.jsx)("div",{className:"mt-3",children:(0,d.jsx)(g.k,{value:n,className:"h-2"})})]})]},b)})})}function o({value:a,max:b,size:c=120,strokeWidth:e=8,className:f,children:g}){let h=(c-e)/2,i=2*h*Math.PI,j=a/b*100,k=i-j/100*i;return(0,d.jsxs)("div",{className:(0,m.cn)("relative inline-flex items-center justify-center",f),children:[(0,d.jsxs)("svg",{width:c,height:c,className:"transform -rotate-90",children:[(0,d.jsx)("circle",{cx:c/2,cy:c/2,r:h,stroke:"currentColor",strokeWidth:e,fill:"none",className:"text-muted-foreground/20"}),(0,d.jsx)("circle",{cx:c/2,cy:c/2,r:h,stroke:"currentColor",strokeWidth:e,fill:"none",strokeDasharray:i,strokeDashoffset:k,className:"text-primary transition-all duration-300 ease-in-out",strokeLinecap:"round"})]}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:g||(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"text-2xl font-bold",children:[j.toFixed(0),"%"]}),(0,d.jsxs)("div",{className:"text-xs text-muted-foreground",children:[a,"/",b]})]})})]})}function p({value:a,label:b,trend:c="stable",className:e}){return(0,d.jsxs)("div",{className:(0,m.cn)("text-center",e),children:[(0,d.jsx)("div",{className:"text-3xl font-bold tabular-nums",children:a.toLocaleString()}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:b}),"stable"!==c&&(0,d.jsxs)("div",{className:"flex items-center justify-center mt-1",children:["up"===c?(0,d.jsx)(k.A,{className:"h-3 w-3 text-green-500 mr-1"}):(0,d.jsx)(l.A,{className:"h-3 w-3 text-red-500 mr-1"}),(0,d.jsx)(f.E,{variant:"up"===c?"default":"destructive",className:"text-xs",children:"up"===c?"↑":"↓"})]})]})}},85910:(a,b,c)=>{c.d(b,{Tabs:()=>g,TabsContent:()=>j,TabsList:()=>h,TabsTrigger:()=>i});var d=c(60687);c(43210);var e=c(55146),f=c(96241);function g({className:a,...b}){return(0,d.jsx)(e.bL,{"data-slot":"tabs",className:(0,f.cn)("flex flex-col gap-2",a),...b})}function h({className:a,...b}){return(0,d.jsx)(e.B8,{"data-slot":"tabs-list",className:(0,f.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...b})}function i({className:a,...b}){return(0,d.jsx)(e.l9,{"data-slot":"tabs-trigger",className:(0,f.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...b})}function j({className:a,...b}){return(0,d.jsx)(e.UC,{"data-slot":"tabs-content",className:(0,f.cn)("flex-1 outline-none",a),...b})}},86287:(a,b,c)=>{c.d(b,{k:()=>h});var d=c(60687),e=c(43210),f=c(25177),g=c(96241);let h=e.forwardRef(({className:a,value:b,...c},e)=>(0,d.jsx)(f.bL,{ref:e,className:(0,g.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...c,children:(0,d.jsx)(f.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(b||0)}%)`}})}));h.displayName=f.bL.displayName}};