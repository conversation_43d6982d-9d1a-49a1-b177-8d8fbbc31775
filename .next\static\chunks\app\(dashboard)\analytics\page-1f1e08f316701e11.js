(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[817],{34964:(e,t,r)=>{"use strict";r.d(t,{Tabs:()=>i,TabsContent:()=>u,TabsList:()=>s,TabsTrigger:()=>l});var n=r(95155);r(12115);var a=r(60704),o=r(53999);function i(e){let{className:t,...r}=e;return(0,n.jsx)(a.bL,{"data-slot":"tabs",className:(0,o.cn)("flex flex-col gap-2",t),...r})}function s(e){let{className:t,...r}=e;return(0,n.jsx)(a.B8,{"data-slot":"tabs-list",className:(0,o.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)(a.l9,{"data-slot":"tabs-trigger",className:(0,o.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...r})}function u(e){let{className:t,...r}=e;return(0,n.jsx)(a.UC,{"data-slot":"tabs-content",className:(0,o.cn)("flex-1 outline-none",t),...r})}},49333:(e,t,r)=>{Promise.resolve().then(r.bind(r,34964))},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var n=r(52596),a=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}},60704:(e,t,r)=>{"use strict";r.d(t,{B8:()=>k,UC:()=>E,bL:()=>A,l9:()=>D});var n=r(12115),a=r(85185),o=r(46081),i=r(89196),s=r(28905),l=r(63655),u=r(94315),c=r(5845),d=r(61285),f=r(95155),v="Tabs",[b,p]=(0,o.A)(v,[i.RG]),m=(0,i.RG)(),[g,h]=b(v),x=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:o,orientation:i="horizontal",dir:s,activationMode:b="automatic",...p}=e,m=(0,u.jH)(s),[h,x]=(0,c.i)({prop:n,onChange:a,defaultProp:null!=o?o:"",caller:v});return(0,f.jsx)(g,{scope:r,baseId:(0,d.B)(),value:h,onValueChange:x,orientation:i,dir:m,activationMode:b,children:(0,f.jsx)(l.sG.div,{dir:m,"data-orientation":i,...p,ref:t})})});x.displayName=v;var w="TabsList",y=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,o=h(w,r),s=m(r);return(0,f.jsx)(i.bL,{asChild:!0,...s,orientation:o.orientation,dir:o.dir,loop:n,children:(0,f.jsx)(l.sG.div,{role:"tablist","aria-orientation":o.orientation,...a,ref:t})})});y.displayName=w;var j="TabsTrigger",R=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...s}=e,u=h(j,r),c=m(r),d=F(u.baseId,n),v=I(u.baseId,n),b=n===u.value;return(0,f.jsx)(i.q7,{asChild:!0,...c,focusable:!o,active:b,children:(0,f.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":b,"aria-controls":v,"data-state":b?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:d,...s,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(n)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(n)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;b||o||!e||u.onValueChange(n)})})})});R.displayName=j;var T="TabsContent",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:o,children:i,...u}=e,c=h(T,r),d=F(c.baseId,a),v=I(c.baseId,a),b=a===c.value,p=n.useRef(b);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(s.C,{present:o||b,children:r=>{let{present:n}=r;return(0,f.jsx)(l.sG.div,{"data-state":b?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:v,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&i})}})});function F(e,t){return"".concat(e,"-trigger-").concat(t)}function I(e,t){return"".concat(e,"-content-").concat(t)}C.displayName=T;var A=x,k=y,D=R,E=C},89196:(e,t,r)=>{"use strict";r.d(t,{RG:()=>y,bL:()=>D,q7:()=>E});var n=r(12115),a=r(85185),o=r(37328),i=r(6101),s=r(46081),l=r(61285),u=r(63655),c=r(39033),d=r(5845),f=r(94315),v=r(95155),b="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[g,h,x]=(0,o.N)(m),[w,y]=(0,s.A)(m,[x]),[j,R]=w(m),T=n.forwardRef((e,t)=>(0,v.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(C,{...e,ref:t})})}));T.displayName=m;var C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:s=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:w,onEntryFocus:y,preventScrollOnEntryFocus:R=!1,...T}=e,C=n.useRef(null),F=(0,i.s)(t,C),I=(0,f.jH)(l),[A,D]=(0,d.i)({prop:g,defaultProp:null!=x?x:null,onChange:w,caller:m}),[E,G]=n.useState(!1),N=(0,c.c)(y),_=h(r),L=n.useRef(!1),[K,S]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(b,N),()=>e.removeEventListener(b,N)},[N]),(0,v.jsx)(j,{scope:r,orientation:o,dir:I,loop:s,currentTabStopId:A,onItemFocus:n.useCallback(e=>D(e),[D]),onItemShiftTab:n.useCallback(()=>G(!0),[]),onFocusableItemAdd:n.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>S(e=>e-1),[]),children:(0,v.jsx)(u.sG.div,{tabIndex:E||0===K?-1:0,"data-orientation":o,...T,ref:F,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(b,p);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);k([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),R)}}L.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>G(!1))})})}),F="RovingFocusGroupItem",I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:s,children:c,...d}=e,f=(0,l.B)(),b=s||f,p=R(F,r),m=p.currentTabStopId===b,x=h(r),{onFocusableItemAdd:w,onFocusableItemRemove:y,currentTabStopId:j}=p;return n.useEffect(()=>{if(o)return w(),()=>y()},[o,w,y]),(0,v.jsx)(g.ItemSlot,{scope:r,id:b,focusable:o,active:i,children:(0,v.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...d,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o?p.onItemFocus(b):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>p.onItemFocus(b)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return A[a]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>k(r))}}),children:"function"==typeof c?c({isCurrentTabStop:m,hasTabStop:null!=j}):c})})});I.displayName=F;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function k(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var D=T,E=I}},e=>{e.O(0,[803,550,441,964,358],()=>e(e.s=49333)),_N_E=e.O()}]);