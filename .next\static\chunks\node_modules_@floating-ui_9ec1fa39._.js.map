{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40floating-ui/utils/dist/floating-ui.utils.mjs"], "sourcesContent": ["/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nconst yAxisSides = /*#__PURE__*/new Set(['top', 'bottom']);\nfunction getSideAxis(placement) {\n  return yAxisSides.has(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nconst lrPlacement = ['left', 'right'];\nconst rlPlacement = ['right', 'left'];\nconst tbPlacement = ['top', 'bottom'];\nconst btPlacement = ['bottom', 'top'];\nfunction getSideList(side, isStart, rtl) {\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rlPlacement : lrPlacement;\n      return isStart ? lrPlacement : rlPlacement;\n    case 'left':\n    case 'right':\n      return isStart ? tbPlacement : btPlacement;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;AAED,MAAM,QAAQ;IAAC;IAAO;IAAS;IAAU;CAAO;AAChD,MAAM,aAAa;IAAC;IAAS;CAAM;AACnC,MAAM,aAAa,WAAW,GAAE,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,IAAI,MAAM,CAAC,MAAM,OAAO,MAAM,UAAU,CAAC,EAAE,EAAE,OAAO,MAAM,UAAU,CAAC,EAAE,GAAG,EAAE;AACxI,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,eAAe,CAAA,IAAK,CAAC;QACzB,GAAG;QACH,GAAG;IACL,CAAC;AACD,MAAM,kBAAkB;IACtB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;AACP;AACA,MAAM,uBAAuB;IAC3B,OAAO;IACP,KAAK;AACP;AACA,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG;IAC9B,OAAO,IAAI,OAAO,IAAI,OAAO;AAC/B;AACA,SAAS,SAAS,KAAK,EAAE,KAAK;IAC5B,OAAO,OAAO,UAAU,aAAa,MAAM,SAAS;AACtD;AACA,SAAS,QAAQ,SAAS;IACxB,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC;AACA,SAAS,aAAa,SAAS;IAC7B,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC;AACA,SAAS,gBAAgB,IAAI;IAC3B,OAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,IAAI;IACzB,OAAO,SAAS,MAAM,WAAW;AACnC;AACA,MAAM,aAAa,WAAW,GAAE,IAAI,IAAI;IAAC;IAAO;CAAS;AACzD,SAAS,YAAY,SAAS;IAC5B,OAAO,WAAW,GAAG,CAAC,QAAQ,cAAc,MAAM;AACpD;AACA,SAAS,iBAAiB,SAAS;IACjC,OAAO,gBAAgB,YAAY;AACrC;AACA,SAAS,kBAAkB,SAAS,EAAE,KAAK,EAAE,GAAG;IAC9C,IAAI,QAAQ,KAAK,GAAG;QAClB,MAAM;IACR;IACA,MAAM,YAAY,aAAa;IAC/B,MAAM,gBAAgB,iBAAiB;IACvC,MAAM,SAAS,cAAc;IAC7B,IAAI,oBAAoB,kBAAkB,MAAM,cAAc,CAAC,MAAM,QAAQ,OAAO,IAAI,UAAU,SAAS,cAAc,UAAU,WAAW;IAC9I,IAAI,MAAM,SAAS,CAAC,OAAO,GAAG,MAAM,QAAQ,CAAC,OAAO,EAAE;QACpD,oBAAoB,qBAAqB;IAC3C;IACA,OAAO;QAAC;QAAmB,qBAAqB;KAAmB;AACrE;AACA,SAAS,sBAAsB,SAAS;IACtC,MAAM,oBAAoB,qBAAqB;IAC/C,OAAO;QAAC,8BAA8B;QAAY;QAAmB,8BAA8B;KAAmB;AACxH;AACA,SAAS,8BAA8B,SAAS;IAC9C,OAAO,UAAU,OAAO,CAAC,cAAc,CAAA,YAAa,oBAAoB,CAAC,UAAU;AACrF;AACA,MAAM,cAAc;IAAC;IAAQ;CAAQ;AACrC,MAAM,cAAc;IAAC;IAAS;CAAO;AACrC,MAAM,cAAc;IAAC;IAAO;CAAS;AACrC,MAAM,cAAc;IAAC;IAAU;CAAM;AACrC,SAAS,YAAY,IAAI,EAAE,OAAO,EAAE,GAAG;IACrC,OAAQ;QACN,KAAK;QACL,KAAK;YACH,IAAI,KAAK,OAAO,UAAU,cAAc;YACxC,OAAO,UAAU,cAAc;QACjC,KAAK;QACL,KAAK;YACH,OAAO,UAAU,cAAc;QACjC;YACE,OAAO,EAAE;IACb;AACF;AACA,SAAS,0BAA0B,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG;IACzE,MAAM,YAAY,aAAa;IAC/B,IAAI,OAAO,YAAY,QAAQ,YAAY,cAAc,SAAS;IAClE,IAAI,WAAW;QACb,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,OAAO,MAAM;QACrC,IAAI,eAAe;YACjB,OAAO,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC;QAC9B;IACF;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,SAAS;IACrC,OAAO,UAAU,OAAO,CAAC,0BAA0B,CAAA,OAAQ,eAAe,CAAC,KAAK;AAClF;AACA,SAAS,oBAAoB,OAAO;IAClC,OAAO;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;QACN,GAAG,OAAO;IACZ;AACF;AACA,SAAS,iBAAiB,OAAO;IAC/B,OAAO,OAAO,YAAY,WAAW,oBAAoB,WAAW;QAClE,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;IACR;AACF;AACA,SAAS,iBAAiB,IAAI;IAC5B,MAAM,EACJ,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACP,GAAG;IACJ,OAAO;QACL;QACA;QACA,KAAK;QACL,MAAM;QACN,OAAO,IAAI;QACX,QAAQ,IAAI;QACZ;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40floating-ui/utils/dist/floating-ui.utils.dom.mjs"], "sourcesContent": ["function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nconst invalidOverflowDisplayValues = /*#__PURE__*/new Set(['inline', 'contents']);\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !invalidOverflowDisplayValues.has(display);\n}\nconst tableElements = /*#__PURE__*/new Set(['table', 'td', 'th']);\nfunction isTableElement(element) {\n  return tableElements.has(getNodeName(element));\n}\nconst topLayerSelectors = [':popover-open', ':modal'];\nfunction isTopLayer(element) {\n  return topLayerSelectors.some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (_e) {\n      return false;\n    }\n  });\n}\nconst transformProperties = ['transform', 'translate', 'scale', 'rotate', 'perspective'];\nconst willChangeValues = ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'];\nconst containValues = ['paint', 'layout', 'strict', 'content'];\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return transformProperties.some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || willChangeValues.some(value => (css.willChange || '').includes(value)) || containValues.some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nconst lastTraversableNodeNames = /*#__PURE__*/new Set(['html', 'body', '#document']);\nfunction isLastTraversableNode(node) {\n  return lastTraversableNodeNames.has(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS;IACP,OAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,OAAO,OAAO;QAChB,OAAO,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,WAAW;IAC1C;IACA,wEAAwE;IACxE,sDAAsD;IACtD,yDAAyD;IACzD,OAAO;AACT;AACA,SAAS,UAAU,IAAI;IACrB,IAAI;IACJ,OAAO,CAAC,QAAQ,QAAQ,CAAC,sBAAsB,KAAK,aAAa,KAAK,OAAO,KAAK,IAAI,oBAAoB,WAAW,KAAK;AAC5H;AACA,SAAS,mBAAmB,IAAI;IAC9B,IAAI;IACJ,OAAO,CAAC,OAAO,CAAC,OAAO,QAAQ,KAAK,aAAa,GAAG,KAAK,QAAQ,KAAK,OAAO,QAAQ,KAAK,OAAO,KAAK,IAAI,KAAK,eAAe;AAChI;AACA,SAAS,OAAO,KAAK;IACnB,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,OAAO,iBAAiB,QAAQ,iBAAiB,UAAU,OAAO,IAAI;AACxE;AACA,SAAS,UAAU,KAAK;IACtB,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,OAAO,iBAAiB,WAAW,iBAAiB,UAAU,OAAO,OAAO;AAC9E;AACA,SAAS,cAAc,KAAK;IAC1B,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,OAAO,iBAAiB,eAAe,iBAAiB,UAAU,OAAO,WAAW;AACtF;AACA,SAAS,aAAa,KAAK;IACzB,IAAI,CAAC,eAAe,OAAO,eAAe,aAAa;QACrD,OAAO;IACT;IACA,OAAO,iBAAiB,cAAc,iBAAiB,UAAU,OAAO,UAAU;AACpF;AACA,MAAM,+BAA+B,WAAW,GAAE,IAAI,IAAI;IAAC;IAAU;CAAW;AAChF,SAAS,kBAAkB,OAAO;IAChC,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,SAAS,EACT,OAAO,EACR,GAAG,iBAAiB;IACrB,OAAO,kCAAkC,IAAI,CAAC,WAAW,YAAY,cAAc,CAAC,6BAA6B,GAAG,CAAC;AACvH;AACA,MAAM,gBAAgB,WAAW,GAAE,IAAI,IAAI;IAAC;IAAS;IAAM;CAAK;AAChE,SAAS,eAAe,OAAO;IAC7B,OAAO,cAAc,GAAG,CAAC,YAAY;AACvC;AACA,MAAM,oBAAoB;IAAC;IAAiB;CAAS;AACrD,SAAS,WAAW,OAAO;IACzB,OAAO,kBAAkB,IAAI,CAAC,CAAA;QAC5B,IAAI;YACF,OAAO,QAAQ,OAAO,CAAC;QACzB,EAAE,OAAO,IAAI;YACX,OAAO;QACT;IACF;AACF;AACA,MAAM,sBAAsB;IAAC;IAAa;IAAa;IAAS;IAAU;CAAc;AACxF,MAAM,mBAAmB;IAAC;IAAa;IAAa;IAAS;IAAU;IAAe;CAAS;AAC/F,MAAM,gBAAgB;IAAC;IAAS;IAAU;IAAU;CAAU;AAC9D,SAAS,kBAAkB,YAAY;IACrC,MAAM,SAAS;IACf,MAAM,MAAM,UAAU,gBAAgB,iBAAiB,gBAAgB;IAEvE,qGAAqG;IACrG,mEAAmE;IACnE,OAAO,oBAAoB,IAAI,CAAC,CAAA,QAAS,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,KAAK,SAAS,UAAU,CAAC,IAAI,aAAa,GAAG,IAAI,aAAa,KAAK,WAAW,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,cAAc,GAAG,IAAI,cAAc,KAAK,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,MAAM,GAAG,IAAI,MAAM,KAAK,SAAS,KAAK,KAAK,iBAAiB,IAAI,CAAC,CAAA,QAAS,CAAC,IAAI,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,WAAW,cAAc,IAAI,CAAC,CAAA,QAAS,CAAC,IAAI,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC;AACna;AACA,SAAS,mBAAmB,OAAO;IACjC,IAAI,cAAc,cAAc;IAChC,MAAO,cAAc,gBAAgB,CAAC,sBAAsB,aAAc;QACxE,IAAI,kBAAkB,cAAc;YAClC,OAAO;QACT,OAAO,IAAI,WAAW,cAAc;YAClC,OAAO;QACT;QACA,cAAc,cAAc;IAC9B;IACA,OAAO;AACT;AACA,SAAS;IACP,IAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,QAAQ,EAAE,OAAO;IACxD,OAAO,IAAI,QAAQ,CAAC,2BAA2B;AACjD;AACA,MAAM,2BAA2B,WAAW,GAAE,IAAI,IAAI;IAAC;IAAQ;IAAQ;CAAY;AACnF,SAAS,sBAAsB,IAAI;IACjC,OAAO,yBAAyB,GAAG,CAAC,YAAY;AAClD;AACA,SAAS,iBAAiB,OAAO;IAC/B,OAAO,UAAU,SAAS,gBAAgB,CAAC;AAC7C;AACA,SAAS,cAAc,OAAO;IAC5B,IAAI,UAAU,UAAU;QACtB,OAAO;YACL,YAAY,QAAQ,UAAU;YAC9B,WAAW,QAAQ,SAAS;QAC9B;IACF;IACA,OAAO;QACL,YAAY,QAAQ,OAAO;QAC3B,WAAW,QAAQ,OAAO;IAC5B;AACF;AACA,SAAS,cAAc,IAAI;IACzB,IAAI,YAAY,UAAU,QAAQ;QAChC,OAAO;IACT;IACA,MAAM,SACN,4DAA4D;IAC5D,KAAK,YAAY,IACjB,wBAAwB;IACxB,KAAK,UAAU,IACf,uBAAuB;IACvB,aAAa,SAAS,KAAK,IAAI,IAC/B,YAAY;IACZ,mBAAmB;IACnB,OAAO,aAAa,UAAU,OAAO,IAAI,GAAG;AAC9C;AACA,SAAS,2BAA2B,IAAI;IACtC,MAAM,aAAa,cAAc;IACjC,IAAI,sBAAsB,aAAa;QACrC,OAAO,KAAK,aAAa,GAAG,KAAK,aAAa,CAAC,IAAI,GAAG,KAAK,IAAI;IACjE;IACA,IAAI,cAAc,eAAe,kBAAkB,aAAa;QAC9D,OAAO;IACT;IACA,OAAO,2BAA2B;AACpC;AACA,SAAS,qBAAqB,IAAI,EAAE,IAAI,EAAE,eAAe;IACvD,IAAI;IACJ,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO,EAAE;IACX;IACA,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IACA,MAAM,qBAAqB,2BAA2B;IACtD,MAAM,SAAS,uBAAuB,CAAC,CAAC,uBAAuB,KAAK,aAAa,KAAK,OAAO,KAAK,IAAI,qBAAqB,IAAI;IAC/H,MAAM,MAAM,UAAU;IACtB,IAAI,QAAQ;QACV,MAAM,eAAe,gBAAgB;QACrC,OAAO,KAAK,MAAM,CAAC,KAAK,IAAI,cAAc,IAAI,EAAE,EAAE,kBAAkB,sBAAsB,qBAAqB,EAAE,EAAE,gBAAgB,kBAAkB,qBAAqB,gBAAgB,EAAE;IAC9L;IACA,OAAO,KAAK,MAAM,CAAC,oBAAoB,qBAAqB,oBAAoB,EAAE,EAAE;AACtF;AACA,SAAS,gBAAgB,GAAG;IAC1B,OAAO,IAAI,MAAM,IAAI,OAAO,cAAc,CAAC,IAAI,MAAM,IAAI,IAAI,YAAY,GAAG;AAC9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40floating-ui/core/dist/floating-ui.core.mjs"], "sourcesContent": ["import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          if (!ignoreCrossAxisOverflow ||\n          // We leave the current main axis only if every placement on that axis\n          // overflows the main axis.\n          overflowsData.every(d => getSideAxis(d.placement) === initialSideAxis ? d.overflows[0] > 0 : true)) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nconst originSides = /*#__PURE__*/new Set(['left', 'top']);\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = originSides.has(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = originSides.has(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGA,SAAS,2BAA2B,IAAI,EAAE,SAAS,EAAE,GAAG;IACtD,IAAI,EACF,SAAS,EACT,QAAQ,EACT,GAAG;IACJ,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM,gBAAgB,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE;IACvC,MAAM,cAAc,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;IAClC,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;IACrB,MAAM,aAAa,aAAa;IAChC,MAAM,UAAU,UAAU,CAAC,GAAG,UAAU,KAAK,GAAG,IAAI,SAAS,KAAK,GAAG;IACrE,MAAM,UAAU,UAAU,CAAC,GAAG,UAAU,MAAM,GAAG,IAAI,SAAS,MAAM,GAAG;IACvE,MAAM,cAAc,SAAS,CAAC,YAAY,GAAG,IAAI,QAAQ,CAAC,YAAY,GAAG;IACzE,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,SAAS;gBACP,GAAG;gBACH,GAAG,UAAU,CAAC,GAAG,SAAS,MAAM;YAClC;YACA;QACF,KAAK;YACH,SAAS;gBACP,GAAG;gBACH,GAAG,UAAU,CAAC,GAAG,UAAU,MAAM;YACnC;YACA;QACF,KAAK;YACH,SAAS;gBACP,GAAG,UAAU,CAAC,GAAG,UAAU,KAAK;gBAChC,GAAG;YACL;YACA;QACF,KAAK;YACH,SAAS;gBACP,GAAG,UAAU,CAAC,GAAG,SAAS,KAAK;gBAC/B,GAAG;YACL;YACA;QACF;YACE,SAAS;gBACP,GAAG,UAAU,CAAC;gBACd,GAAG,UAAU,CAAC;YAChB;IACJ;IACA,OAAQ,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;QACnB,KAAK;YACH,MAAM,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,aAAa,CAAC,IAAI,CAAC;YAClE;QACF,KAAK;YACH,MAAM,CAAC,cAAc,IAAI,cAAc,CAAC,OAAO,aAAa,CAAC,IAAI,CAAC;YAClE;IACJ;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,MAAM,kBAAkB,OAAO,WAAW,UAAU;IAClD,MAAM,EACJ,YAAY,QAAQ,EACpB,WAAW,UAAU,EACrB,aAAa,EAAE,EACf,QAAQ,EACT,GAAG;IACJ,MAAM,kBAAkB,WAAW,MAAM,CAAC;IAC1C,MAAM,MAAM,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS;IAC7E,IAAI,QAAQ,MAAM,SAAS,eAAe,CAAC;QACzC;QACA;QACA;IACF;IACA,IAAI,EACF,CAAC,EACD,CAAC,EACF,GAAG,2BAA2B,OAAO,WAAW;IACjD,IAAI,oBAAoB;IACxB,IAAI,iBAAiB,CAAC;IACtB,IAAI,aAAa;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;QAC/C,MAAM,EACJ,IAAI,EACJ,EAAE,EACH,GAAG,eAAe,CAAC,EAAE;QACtB,MAAM,EACJ,GAAG,KAAK,EACR,GAAG,KAAK,EACR,IAAI,EACJ,KAAK,EACN,GAAG,MAAM,GAAG;YACX;YACA;YACA,kBAAkB;YAClB,WAAW;YACX;YACA;YACA;YACA;YACA,UAAU;gBACR;gBACA;YACF;QACF;QACA,IAAI,SAAS,OAAO,QAAQ;QAC5B,IAAI,SAAS,OAAO,QAAQ;QAC5B,iBAAiB;YACf,GAAG,cAAc;YACjB,CAAC,KAAK,EAAE;gBACN,GAAG,cAAc,CAAC,KAAK;gBACvB,GAAG,IAAI;YACT;QACF;QACA,IAAI,SAAS,cAAc,IAAI;YAC7B;YACA,IAAI,OAAO,UAAU,UAAU;gBAC7B,IAAI,MAAM,SAAS,EAAE;oBACnB,oBAAoB,MAAM,SAAS;gBACrC;gBACA,IAAI,MAAM,KAAK,EAAE;oBACf,QAAQ,MAAM,KAAK,KAAK,OAAO,MAAM,SAAS,eAAe,CAAC;wBAC5D;wBACA;wBACA;oBACF,KAAK,MAAM,KAAK;gBAClB;gBACA,CAAC,EACC,CAAC,EACD,CAAC,EACF,GAAG,2BAA2B,OAAO,mBAAmB,IAAI;YAC/D;YACA,IAAI,CAAC;QACP;IACF;IACA,OAAO;QACL;QACA;QACA,WAAW;QACX;QACA;IACF;AACF;AAEA;;;;;;;CAOC,GACD,eAAe,eAAe,KAAK,EAAE,OAAO;IAC1C,IAAI;IACJ,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,CAAC,EACD,CAAC,EACD,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,WAAW,mBAAmB,EAC9B,eAAe,UAAU,EACzB,iBAAiB,UAAU,EAC3B,cAAc,KAAK,EACnB,UAAU,CAAC,EACZ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IACtB,MAAM,gBAAgB,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE;IACvC,MAAM,aAAa,mBAAmB,aAAa,cAAc;IACjE,MAAM,UAAU,QAAQ,CAAC,cAAc,aAAa,eAAe;IACnE,MAAM,qBAAqB,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,SAAS,eAAe,CAAC;QACzE,SAAS,CAAC,CAAC,wBAAwB,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,KAAK,IAAI,SAAS,SAAS,CAAC,QAAQ,CAAC,KAAK,OAAO,wBAAwB,IAAI,IAAI,UAAU,QAAQ,cAAc,IAAK,MAAM,CAAC,SAAS,kBAAkB,IAAI,OAAO,KAAK,IAAI,SAAS,kBAAkB,CAAC,SAAS,QAAQ,CAAC;QACjS;QACA;QACA;IACF;IACA,MAAM,OAAO,mBAAmB,aAAa;QAC3C;QACA;QACA,OAAO,MAAM,QAAQ,CAAC,KAAK;QAC3B,QAAQ,MAAM,QAAQ,CAAC,MAAM;IAC/B,IAAI,MAAM,SAAS;IACnB,MAAM,eAAe,MAAM,CAAC,SAAS,eAAe,IAAI,OAAO,KAAK,IAAI,SAAS,eAAe,CAAC,SAAS,QAAQ,CAAC;IACnH,MAAM,cAAc,AAAC,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,KAAK,IAAI,SAAS,SAAS,CAAC,aAAa,IAAK,AAAC,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,KAAK,IAAI,SAAS,QAAQ,CAAC,aAAa,KAAM;QACvL,GAAG;QACH,GAAG;IACL,IAAI;QACF,GAAG;QACH,GAAG;IACL;IACA,MAAM,oBAAoB,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,qDAAqD,GAAG,MAAM,SAAS,qDAAqD,CAAC;QAC/K;QACA;QACA;QACA;IACF,KAAK;IACL,OAAO;QACL,KAAK,CAAC,mBAAmB,GAAG,GAAG,kBAAkB,GAAG,GAAG,cAAc,GAAG,IAAI,YAAY,CAAC;QACzF,QAAQ,CAAC,kBAAkB,MAAM,GAAG,mBAAmB,MAAM,GAAG,cAAc,MAAM,IAAI,YAAY,CAAC;QACrG,MAAM,CAAC,mBAAmB,IAAI,GAAG,kBAAkB,IAAI,GAAG,cAAc,IAAI,IAAI,YAAY,CAAC;QAC7F,OAAO,CAAC,kBAAkB,KAAK,GAAG,mBAAmB,KAAK,GAAG,cAAc,KAAK,IAAI,YAAY,CAAC;IACnG;AACF;AAEA;;;;CAIC,GACD,MAAM,QAAQ,CAAA,UAAW,CAAC;QACxB,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EACT,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,cAAc,EACf,GAAG;YACJ,4DAA4D;YAC5D,MAAM,EACJ,OAAO,EACP,UAAU,CAAC,EACZ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,UAAU,CAAC;YACjC,IAAI,WAAW,MAAM;gBACnB,OAAO,CAAC;YACV;YACA,MAAM,gBAAgB,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE;YACvC,MAAM,SAAS;gBACb;gBACA;YACF;YACA,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE;YAC9B,MAAM,SAAS,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;YAC7B,MAAM,kBAAkB,MAAM,SAAS,aAAa,CAAC;YACrD,MAAM,UAAU,SAAS;YACzB,MAAM,UAAU,UAAU,QAAQ;YAClC,MAAM,UAAU,UAAU,WAAW;YACrC,MAAM,aAAa,UAAU,iBAAiB;YAC9C,MAAM,UAAU,MAAM,SAAS,CAAC,OAAO,GAAG,MAAM,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,QAAQ,CAAC,OAAO;YACvG,MAAM,YAAY,MAAM,CAAC,KAAK,GAAG,MAAM,SAAS,CAAC,KAAK;YACtD,MAAM,oBAAoB,MAAM,CAAC,SAAS,eAAe,IAAI,OAAO,KAAK,IAAI,SAAS,eAAe,CAAC,QAAQ;YAC9G,IAAI,aAAa,oBAAoB,iBAAiB,CAAC,WAAW,GAAG;YAErE,0DAA0D;YAC1D,IAAI,CAAC,cAAc,CAAE,MAAM,CAAC,SAAS,SAAS,IAAI,OAAO,KAAK,IAAI,SAAS,SAAS,CAAC,kBAAkB,GAAI;gBACzG,aAAa,SAAS,QAAQ,CAAC,WAAW,IAAI,MAAM,QAAQ,CAAC,OAAO;YACtE;YACA,MAAM,oBAAoB,UAAU,IAAI,YAAY;YAEpD,0EAA0E;YAC1E,uDAAuD;YACvD,MAAM,yBAAyB,aAAa,IAAI,eAAe,CAAC,OAAO,GAAG,IAAI;YAC9E,MAAM,aAAa,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,aAAa,CAAC,QAAQ,EAAE;YAC/C,MAAM,aAAa,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,aAAa,CAAC,QAAQ,EAAE;YAE/C,0EAA0E;YAC1E,kDAAkD;YAClD,MAAM,QAAQ;YACd,MAAM,MAAM,aAAa,eAAe,CAAC,OAAO,GAAG;YACnD,MAAM,SAAS,aAAa,IAAI,eAAe,CAAC,OAAO,GAAG,IAAI;YAC9D,MAAM,SAAS,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,OAAO,QAAQ;YAEpC,yEAAyE;YACzE,yEAAyE;YACzE,yEAAyE;YACzE,iDAAiD;YACjD,MAAM,kBAAkB,CAAC,eAAe,KAAK,IAAI,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE,cAAc,QAAQ,WAAW,UAAU,MAAM,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,QAAQ,aAAa,UAAU,IAAI,eAAe,CAAC,OAAO,GAAG,IAAI;YAClN,MAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAAS,MAAM;YAC3F,OAAO;gBACL,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG;gBACvB,MAAM;oBACJ,CAAC,KAAK,EAAE;oBACR,cAAc,SAAS,SAAS;oBAChC,GAAI,mBAAmB;wBACrB;oBACF,CAAC;gBACH;gBACA,OAAO;YACT;QACF;IACF,CAAC;AAED,SAAS,iBAAiB,SAAS,EAAE,aAAa,EAAE,iBAAiB;IACnE,MAAM,qCAAqC,YAAY;WAAI,kBAAkB,MAAM,CAAC,CAAA,YAAa,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE,eAAe;WAAe,kBAAkB,MAAM,CAAC,CAAA,YAAa,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE,eAAe;KAAW,GAAG,kBAAkB,MAAM,CAAC,CAAA,YAAa,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,eAAe;IACzR,OAAO,mCAAmC,MAAM,CAAC,CAAA;QAC/C,IAAI,WAAW;YACb,OAAO,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE,eAAe,aAAa,CAAC,gBAAgB,CAAA,GAAA,gLAAA,CAAA,gCAA6B,AAAD,EAAE,eAAe,YAAY,KAAK;QACjI;QACA,OAAO;IACT;AACF;AACA;;;;;CAKC,GACD,MAAM,gBAAgB,SAAU,OAAO;IACrC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,IAAI,uBAAuB,wBAAwB;YACnD,MAAM,EACJ,KAAK,EACL,cAAc,EACd,SAAS,EACT,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,MAAM,EACJ,YAAY,KAAK,EACjB,SAAS,EACT,oBAAoB,gLAAA,CAAA,aAAU,EAC9B,gBAAgB,IAAI,EACpB,GAAG,uBACJ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,eAAe,cAAc,aAAa,sBAAsB,gLAAA,CAAA,aAAU,GAAG,iBAAiB,aAAa,MAAM,eAAe,qBAAqB;YAC3J,MAAM,WAAW,MAAM,eAAe,OAAO;YAC7C,MAAM,eAAe,CAAC,CAAC,wBAAwB,eAAe,aAAa,KAAK,OAAO,KAAK,IAAI,sBAAsB,KAAK,KAAK;YAChI,MAAM,mBAAmB,YAAY,CAAC,aAAa;YACnD,IAAI,oBAAoB,MAAM;gBAC5B,OAAO,CAAC;YACV;YACA,MAAM,iBAAiB,CAAA,GAAA,gLAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB,OAAO,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS,QAAQ,CAAC;YAE5I,mDAAmD;YACnD,IAAI,cAAc,kBAAkB;gBAClC,OAAO;oBACL,OAAO;wBACL,WAAW,YAAY,CAAC,EAAE;oBAC5B;gBACF;YACF;YACA,MAAM,mBAAmB;gBAAC,QAAQ,CAAC,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;gBAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;aAAC;YACxH,MAAM,eAAe;mBAAK,CAAC,CAAC,yBAAyB,eAAe,aAAa,KAAK,OAAO,KAAK,IAAI,uBAAuB,SAAS,KAAK,EAAE;gBAAG;oBAC9I,WAAW;oBACX,WAAW;gBACb;aAAE;YACF,MAAM,gBAAgB,YAAY,CAAC,eAAe,EAAE;YAEpD,sCAAsC;YACtC,IAAI,eAAe;gBACjB,OAAO;oBACL,MAAM;wBACJ,OAAO,eAAe;wBACtB,WAAW;oBACb;oBACA,OAAO;wBACL,WAAW;oBACb;gBACF;YACF;YACA,MAAM,8BAA8B,aAAa,GAAG,CAAC,CAAA;gBACnD,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE,EAAE,SAAS;gBAC1C,OAAO;oBAAC,EAAE,SAAS;oBAAE,aAAa,YAClC,oDAAoD;oBACpD,EAAE,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,GAAG,KACpD,2BAA2B;oBAC3B,EAAE,SAAS,CAAC,EAAE;oBAAE,EAAE,SAAS;iBAAC;YAC9B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC7B,MAAM,8BAA8B,4BAA4B,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GACvF,+DAA+D;gBAC/D,QAAQ;gBACR,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE,CAAC,CAAC,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAC5C,MAAM,iBAAiB,CAAC,CAAC,wBAAwB,2BAA2B,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,qBAAqB,CAAC,EAAE,KAAK,2BAA2B,CAAC,EAAE,CAAC,EAAE;YAClK,IAAI,mBAAmB,WAAW;gBAChC,OAAO;oBACL,MAAM;wBACJ,OAAO,eAAe;wBACtB,WAAW;oBACb;oBACA,OAAO;wBACL,WAAW;oBACb;gBACF;YACF;YACA,OAAO,CAAC;QACV;IACF;AACF;AAEA;;;;;CAKC,GACD,MAAM,OAAO,SAAU,OAAO;IAC5B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,IAAI,uBAAuB;YAC3B,MAAM,EACJ,SAAS,EACT,cAAc,EACd,KAAK,EACL,gBAAgB,EAChB,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,MAAM,EACJ,UAAU,gBAAgB,IAAI,EAC9B,WAAW,iBAAiB,IAAI,EAChC,oBAAoB,2BAA2B,EAC/C,mBAAmB,SAAS,EAC5B,4BAA4B,MAAM,EAClC,gBAAgB,IAAI,EACpB,GAAG,uBACJ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YAEtB,sEAAsE;YACtE,0EAA0E;YAC1E,QAAQ;YACR,iFAAiF;YACjF,IAAI,CAAC,wBAAwB,eAAe,KAAK,KAAK,QAAQ,sBAAsB,eAAe,EAAE;gBACnG,OAAO,CAAC;YACV;YACA,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;YACrB,MAAM,kBAAkB,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE;YACpC,MAAM,kBAAkB,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB;YACtD,MAAM,MAAM,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS,QAAQ,CAAC;YACtF,MAAM,qBAAqB,+BAA+B,CAAC,mBAAmB,CAAC,gBAAgB;gBAAC,CAAA,GAAA,gLAAA,CAAA,uBAAoB,AAAD,EAAE;aAAkB,GAAG,CAAA,GAAA,gLAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB;YACjL,MAAM,+BAA+B,8BAA8B;YACnE,IAAI,CAAC,+BAA+B,8BAA8B;gBAChE,mBAAmB,IAAI,IAAI,CAAA,GAAA,gLAAA,CAAA,4BAAyB,AAAD,EAAE,kBAAkB,eAAe,2BAA2B;YACnH;YACA,MAAM,aAAa;gBAAC;mBAAqB;aAAmB;YAC5D,MAAM,WAAW,MAAM,eAAe,OAAO;YAC7C,MAAM,YAAY,EAAE;YACpB,IAAI,gBAAgB,CAAC,CAAC,uBAAuB,eAAe,IAAI,KAAK,OAAO,KAAK,IAAI,qBAAqB,SAAS,KAAK,EAAE;YAC1H,IAAI,eAAe;gBACjB,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK;YAC/B;YACA,IAAI,gBAAgB;gBAClB,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,OAAO;gBAClD,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD;YACA,gBAAgB;mBAAI;gBAAe;oBACjC;oBACA;gBACF;aAAE;YAEF,oCAAoC;YACpC,IAAI,CAAC,UAAU,KAAK,CAAC,CAAA,OAAQ,QAAQ,IAAI;gBACvC,IAAI,uBAAuB;gBAC3B,MAAM,YAAY,CAAC,CAAC,CAAC,wBAAwB,eAAe,IAAI,KAAK,OAAO,KAAK,IAAI,sBAAsB,KAAK,KAAK,CAAC,IAAI;gBAC1H,MAAM,gBAAgB,UAAU,CAAC,UAAU;gBAC3C,IAAI,eAAe;oBACjB,MAAM,0BAA0B,mBAAmB,cAAc,oBAAoB,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB;oBAClH,IAAI,CAAC,2BACL,sEAAsE;oBACtE,2BAA2B;oBAC3B,cAAc,KAAK,CAAC,CAAA,IAAK,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,EAAE,SAAS,MAAM,kBAAkB,EAAE,SAAS,CAAC,EAAE,GAAG,IAAI,OAAO;wBAClG,+CAA+C;wBAC/C,OAAO;4BACL,MAAM;gCACJ,OAAO;gCACP,WAAW;4BACb;4BACA,OAAO;gCACL,WAAW;4BACb;wBACF;oBACF;gBACF;gBAEA,wEAAwE;gBACxE,yEAAyE;gBACzE,IAAI,iBAAiB,CAAC,wBAAwB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,sBAAsB,SAAS;gBAEnM,sBAAsB;gBACtB,IAAI,CAAC,gBAAgB;oBACnB,OAAQ;wBACN,KAAK;4BACH;gCACE,IAAI;gCACJ,MAAM,YAAY,CAAC,yBAAyB,cAAc,MAAM,CAAC,CAAA;oCAC/D,IAAI,8BAA8B;wCAChC,MAAM,kBAAkB,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,EAAE,SAAS;wCAC/C,OAAO,oBAAoB,mBAC3B,uDAAuD;wCACvD,6CAA6C;wCAC7C,oBAAoB;oCACtB;oCACA,OAAO;gCACT,GAAG,GAAG,CAAC,CAAA,IAAK;wCAAC,EAAE,SAAS;wCAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAA,WAAY,WAAW,GAAG,MAAM,CAAC,CAAC,KAAK,WAAa,MAAM,UAAU;qCAAG,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,sBAAsB,CAAC,EAAE;gCAClM,IAAI,WAAW;oCACb,iBAAiB;gCACnB;gCACA;4BACF;wBACF,KAAK;4BACH,iBAAiB;4BACjB;oBACJ;gBACF;gBACA,IAAI,cAAc,gBAAgB;oBAChC,OAAO;wBACL,OAAO;4BACL,WAAW;wBACb;oBACF;gBACF;YACF;YACA,OAAO,CAAC;QACV;IACF;AACF;AAEA,SAAS,eAAe,QAAQ,EAAE,IAAI;IACpC,OAAO;QACL,KAAK,SAAS,GAAG,GAAG,KAAK,MAAM;QAC/B,OAAO,SAAS,KAAK,GAAG,KAAK,KAAK;QAClC,QAAQ,SAAS,MAAM,GAAG,KAAK,MAAM;QACrC,MAAM,SAAS,IAAI,GAAG,KAAK,KAAK;IAClC;AACF;AACA,SAAS,sBAAsB,QAAQ;IACrC,OAAO,gLAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,QAAQ,CAAC,KAAK,IAAI;AAC9C;AACA;;;;CAIC,GACD,MAAM,OAAO,SAAU,OAAO;IAC5B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,KAAK,EACN,GAAG;YACJ,MAAM,EACJ,WAAW,iBAAiB,EAC5B,GAAG,uBACJ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,OAAQ;gBACN,KAAK;oBACH;wBACE,MAAM,WAAW,MAAM,eAAe,OAAO;4BAC3C,GAAG,qBAAqB;4BACxB,gBAAgB;wBAClB;wBACA,MAAM,UAAU,eAAe,UAAU,MAAM,SAAS;wBACxD,OAAO;4BACL,MAAM;gCACJ,wBAAwB;gCACxB,iBAAiB,sBAAsB;4BACzC;wBACF;oBACF;gBACF,KAAK;oBACH;wBACE,MAAM,WAAW,MAAM,eAAe,OAAO;4BAC3C,GAAG,qBAAqB;4BACxB,aAAa;wBACf;wBACA,MAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;wBACvD,OAAO;4BACL,MAAM;gCACJ,gBAAgB;gCAChB,SAAS,sBAAsB;4BACjC;wBACF;oBACF;gBACF;oBACE;wBACE,OAAO,CAAC;oBACV;YACJ;QACF;IACF;AACF;AAEA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,KAAK,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC/C,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,KAAK,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;IAC9C,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,KAAK,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAChD,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,KAAK,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IACjD,OAAO;QACL,GAAG;QACH,GAAG;QACH,OAAO,OAAO;QACd,QAAQ,OAAO;IACjB;AACF;AACA,SAAS,eAAe,KAAK;IAC3B,MAAM,cAAc,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC;IAC1D,MAAM,SAAS,EAAE;IACjB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,MAAM,OAAO,WAAW,CAAC,EAAE;QAC3B,IAAI,CAAC,YAAY,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,GAAG;YAC1D,OAAO,IAAI,CAAC;gBAAC;aAAK;QACpB,OAAO;YACL,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC;QACjC;QACA,WAAW;IACb;IACA,OAAO,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;AAC7D;AACA;;;;CAIC,GACD,MAAM,SAAS,SAAU,OAAO;IAC9B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,gEAAgE;YAChE,qEAAqE;YACrE,2CAA2C;YAC3C,MAAM,EACJ,UAAU,CAAC,EACX,CAAC,EACD,CAAC,EACF,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,oBAAoB,MAAM,IAAI,CAAC,AAAC,MAAM,CAAC,SAAS,cAAc,IAAI,OAAO,KAAK,IAAI,SAAS,cAAc,CAAC,SAAS,SAAS,CAAC,KAAM,EAAE;YAC3I,MAAM,cAAc,eAAe;YACnC,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;YAClD,MAAM,gBAAgB,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE;YACvC,SAAS;gBACP,8CAA8C;gBAC9C,IAAI,YAAY,MAAM,KAAK,KAAK,WAAW,CAAC,EAAE,CAAC,IAAI,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;oBACpG,0DAA0D;oBAC1D,OAAO,YAAY,IAAI,CAAC,CAAA,OAAQ,IAAI,KAAK,IAAI,GAAG,cAAc,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,cAAc,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,cAAc,GAAG,IAAI,IAAI,KAAK,MAAM,GAAG,cAAc,MAAM,KAAK;gBAC/L;gBAEA,uCAAuC;gBACvC,IAAI,YAAY,MAAM,IAAI,GAAG;oBAC3B,IAAI,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,eAAe,KAAK;wBAClC,MAAM,YAAY,WAAW,CAAC,EAAE;wBAChC,MAAM,WAAW,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;wBACpD,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,eAAe;wBACrC,MAAM,MAAM,UAAU,GAAG;wBACzB,MAAM,SAAS,SAAS,MAAM;wBAC9B,MAAM,OAAO,QAAQ,UAAU,IAAI,GAAG,SAAS,IAAI;wBACnD,MAAM,QAAQ,QAAQ,UAAU,KAAK,GAAG,SAAS,KAAK;wBACtD,MAAM,QAAQ,QAAQ;wBACtB,MAAM,SAAS,SAAS;wBACxB,OAAO;4BACL;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,GAAG;4BACH,GAAG;wBACL;oBACF;oBACA,MAAM,aAAa,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,eAAe;oBAC1C,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,KAAK,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;oBAC1D,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,KAAK,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;oBACxD,MAAM,eAAe,YAAY,MAAM,CAAC,CAAA,OAAQ,aAAa,KAAK,IAAI,KAAK,UAAU,KAAK,KAAK,KAAK;oBACpG,MAAM,MAAM,YAAY,CAAC,EAAE,CAAC,GAAG;oBAC/B,MAAM,SAAS,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,MAAM;oBAC3D,MAAM,OAAO;oBACb,MAAM,QAAQ;oBACd,MAAM,QAAQ,QAAQ;oBACtB,MAAM,SAAS,SAAS;oBACxB,OAAO;wBACL;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA,GAAG;wBACH,GAAG;oBACL;gBACF;gBACA,OAAO;YACT;YACA,MAAM,aAAa,MAAM,SAAS,eAAe,CAAC;gBAChD,WAAW;oBACT;gBACF;gBACA,UAAU,SAAS,QAAQ;gBAC3B;YACF;YACA,IAAI,MAAM,SAAS,CAAC,CAAC,KAAK,WAAW,SAAS,CAAC,CAAC,IAAI,MAAM,SAAS,CAAC,CAAC,KAAK,WAAW,SAAS,CAAC,CAAC,IAAI,MAAM,SAAS,CAAC,KAAK,KAAK,WAAW,SAAS,CAAC,KAAK,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,WAAW,SAAS,CAAC,MAAM,EAAE;gBAClN,OAAO;oBACL,OAAO;wBACL,OAAO;oBACT;gBACF;YACF;YACA,OAAO,CAAC;QACV;IACF;AACF;AAEA,MAAM,cAAc,WAAW,GAAE,IAAI,IAAI;IAAC;IAAQ;CAAM;AAExD,sEAAsE;AACtE,aAAa;AAEb,eAAe,qBAAqB,KAAK,EAAE,OAAO;IAChD,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,QAAQ,EACT,GAAG;IACJ,MAAM,MAAM,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS,QAAQ,CAAC;IACtF,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;IACrB,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IAC/B,MAAM,aAAa,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,eAAe;IAC9C,MAAM,gBAAgB,YAAY,GAAG,CAAC,QAAQ,CAAC,IAAI;IACnD,MAAM,iBAAiB,OAAO,aAAa,CAAC,IAAI;IAChD,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IAEnC,wCAAwC;IACxC,IAAI,EACF,QAAQ,EACR,SAAS,EACT,aAAa,EACd,GAAG,OAAO,aAAa,WAAW;QACjC,UAAU;QACV,WAAW;QACX,eAAe;IACjB,IAAI;QACF,UAAU,SAAS,QAAQ,IAAI;QAC/B,WAAW,SAAS,SAAS,IAAI;QACjC,eAAe,SAAS,aAAa;IACvC;IACA,IAAI,aAAa,OAAO,kBAAkB,UAAU;QAClD,YAAY,cAAc,QAAQ,gBAAgB,CAAC,IAAI;IACzD;IACA,OAAO,aAAa;QAClB,GAAG,YAAY;QACf,GAAG,WAAW;IAChB,IAAI;QACF,GAAG,WAAW;QACd,GAAG,YAAY;IACjB;AACF;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,SAAU,OAAO;IAC9B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,IAAI,uBAAuB;YAC3B,MAAM,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EACT,cAAc,EACf,GAAG;YACJ,MAAM,aAAa,MAAM,qBAAqB,OAAO;YAErD,wEAAwE;YACxE,4DAA4D;YAC5D,IAAI,cAAc,CAAC,CAAC,wBAAwB,eAAe,MAAM,KAAK,OAAO,KAAK,IAAI,sBAAsB,SAAS,KAAK,CAAC,wBAAwB,eAAe,KAAK,KAAK,QAAQ,sBAAsB,eAAe,EAAE;gBACzN,OAAO,CAAC;YACV;YACA,OAAO;gBACL,GAAG,IAAI,WAAW,CAAC;gBACnB,GAAG,IAAI,WAAW,CAAC;gBACnB,MAAM;oBACJ,GAAG,UAAU;oBACb;gBACF;YACF;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,MAAM,QAAQ,SAAU,OAAO;IAC7B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EACV,GAAG;YACJ,MAAM,EACJ,UAAU,gBAAgB,IAAI,EAC9B,WAAW,iBAAiB,KAAK,EACjC,UAAU;gBACR,IAAI,CAAA;oBACF,IAAI,EACF,CAAC,EACD,CAAC,EACF,GAAG;oBACJ,OAAO;wBACL;wBACA;oBACF;gBACF;YACF,CAAC,EACD,GAAG,uBACJ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,SAAS;gBACb;gBACA;YACF;YACA,MAAM,WAAW,MAAM,eAAe,OAAO;YAC7C,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;YACtC,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,kBAAe,AAAD,EAAE;YACjC,IAAI,gBAAgB,MAAM,CAAC,SAAS;YACpC,IAAI,iBAAiB,MAAM,CAAC,UAAU;YACtC,IAAI,eAAe;gBACjB,MAAM,UAAU,aAAa,MAAM,QAAQ;gBAC3C,MAAM,UAAU,aAAa,MAAM,WAAW;gBAC9C,MAAM,MAAM,gBAAgB,QAAQ,CAAC,QAAQ;gBAC7C,MAAM,MAAM,gBAAgB,QAAQ,CAAC,QAAQ;gBAC7C,gBAAgB,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,eAAe;YAC5C;YACA,IAAI,gBAAgB;gBAClB,MAAM,UAAU,cAAc,MAAM,QAAQ;gBAC5C,MAAM,UAAU,cAAc,MAAM,WAAW;gBAC/C,MAAM,MAAM,iBAAiB,QAAQ,CAAC,QAAQ;gBAC9C,MAAM,MAAM,iBAAiB,QAAQ,CAAC,QAAQ;gBAC9C,iBAAiB,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,gBAAgB;YAC9C;YACA,MAAM,gBAAgB,QAAQ,EAAE,CAAC;gBAC/B,GAAG,KAAK;gBACR,CAAC,SAAS,EAAE;gBACZ,CAAC,UAAU,EAAE;YACf;YACA,OAAO;gBACL,GAAG,aAAa;gBAChB,MAAM;oBACJ,GAAG,cAAc,CAAC,GAAG;oBACrB,GAAG,cAAc,CAAC,GAAG;oBACrB,SAAS;wBACP,CAAC,SAAS,EAAE;wBACZ,CAAC,UAAU,EAAE;oBACf;gBACF;YACF;QACF;IACF;AACF;AACA;;CAEC,GACD,MAAM,aAAa,SAAU,OAAO;IAClC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL;QACA,IAAG,KAAK;YACN,MAAM,EACJ,CAAC,EACD,CAAC,EACD,SAAS,EACT,KAAK,EACL,cAAc,EACf,GAAG;YACJ,MAAM,EACJ,SAAS,CAAC,EACV,UAAU,gBAAgB,IAAI,EAC9B,WAAW,iBAAiB,IAAI,EACjC,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,SAAS;gBACb;gBACA;YACF;YACA,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE;YAC9B,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,kBAAe,AAAD,EAAE;YACjC,IAAI,gBAAgB,MAAM,CAAC,SAAS;YACpC,IAAI,iBAAiB,MAAM,CAAC,UAAU;YACtC,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACnC,MAAM,iBAAiB,OAAO,cAAc,WAAW;gBACrD,UAAU;gBACV,WAAW;YACb,IAAI;gBACF,UAAU;gBACV,WAAW;gBACX,GAAG,SAAS;YACd;YACA,IAAI,eAAe;gBACjB,MAAM,MAAM,aAAa,MAAM,WAAW;gBAC1C,MAAM,WAAW,MAAM,SAAS,CAAC,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,GAAG,eAAe,QAAQ;gBAC1F,MAAM,WAAW,MAAM,SAAS,CAAC,SAAS,GAAG,MAAM,SAAS,CAAC,IAAI,GAAG,eAAe,QAAQ;gBAC3F,IAAI,gBAAgB,UAAU;oBAC5B,gBAAgB;gBAClB,OAAO,IAAI,gBAAgB,UAAU;oBACnC,gBAAgB;gBAClB;YACF;YACA,IAAI,gBAAgB;gBAClB,IAAI,uBAAuB;gBAC3B,MAAM,MAAM,aAAa,MAAM,UAAU;gBACzC,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;gBAC7C,MAAM,WAAW,MAAM,SAAS,CAAC,UAAU,GAAG,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC,wBAAwB,eAAe,MAAM,KAAK,OAAO,KAAK,IAAI,qBAAqB,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,eAAe,SAAS;gBAClP,MAAM,WAAW,MAAM,SAAS,CAAC,UAAU,GAAG,MAAM,SAAS,CAAC,IAAI,GAAG,CAAC,eAAe,IAAI,CAAC,CAAC,yBAAyB,eAAe,MAAM,KAAK,OAAO,KAAK,IAAI,sBAAsB,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,eAAe,eAAe,SAAS,GAAG,CAAC;gBACrP,IAAI,iBAAiB,UAAU;oBAC7B,iBAAiB;gBACnB,OAAO,IAAI,iBAAiB,UAAU;oBACpC,iBAAiB;gBACnB;YACF;YACA,OAAO;gBACL,CAAC,SAAS,EAAE;gBACZ,CAAC,UAAU,EAAE;YACf;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,MAAM,OAAO,SAAU,OAAO;IAC5B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,OAAO;QACL,MAAM;QACN;QACA,MAAM,IAAG,KAAK;YACZ,IAAI,uBAAuB;YAC3B,MAAM,EACJ,SAAS,EACT,KAAK,EACL,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,MAAM,EACJ,QAAQ,KAAO,CAAC,EAChB,GAAG,uBACJ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACtB,MAAM,WAAW,MAAM,eAAe,OAAO;YAC7C,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;YACrB,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;YAC/B,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,eAAe;YAC3C,MAAM,EACJ,KAAK,EACL,MAAM,EACP,GAAG,MAAM,QAAQ;YAClB,IAAI;YACJ,IAAI;YACJ,IAAI,SAAS,SAAS,SAAS,UAAU;gBACvC,aAAa;gBACb,YAAY,cAAc,CAAC,AAAC,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAK,UAAU,KAAK,IAAI,SAAS;YACzI,OAAO;gBACL,YAAY;gBACZ,aAAa,cAAc,QAAQ,QAAQ;YAC7C;YACA,MAAM,wBAAwB,SAAS,SAAS,GAAG,GAAG,SAAS,MAAM;YACrE,MAAM,uBAAuB,QAAQ,SAAS,IAAI,GAAG,SAAS,KAAK;YACnE,MAAM,0BAA0B,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,SAAS,QAAQ,CAAC,WAAW,EAAE;YACnE,MAAM,yBAAyB,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,QAAQ,CAAC,UAAU,EAAE;YAChE,MAAM,UAAU,CAAC,MAAM,cAAc,CAAC,KAAK;YAC3C,IAAI,kBAAkB;YACtB,IAAI,iBAAiB;YACrB,IAAI,CAAC,wBAAwB,MAAM,cAAc,CAAC,KAAK,KAAK,QAAQ,sBAAsB,OAAO,CAAC,CAAC,EAAE;gBACnG,iBAAiB;YACnB;YACA,IAAI,CAAC,yBAAyB,MAAM,cAAc,CAAC,KAAK,KAAK,QAAQ,uBAAuB,OAAO,CAAC,CAAC,EAAE;gBACrG,kBAAkB;YACpB;YACA,IAAI,WAAW,CAAC,WAAW;gBACzB,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,SAAS,IAAI,EAAE;gBAChC,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,SAAS,KAAK,EAAE;gBACjC,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,SAAS,GAAG,EAAE;gBAC/B,MAAM,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,SAAS,MAAM,EAAE;gBAClC,IAAI,SAAS;oBACX,iBAAiB,QAAQ,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,SAAS,IAAI,EAAE,SAAS,KAAK,CAAC;gBAC3G,OAAO;oBACL,kBAAkB,SAAS,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,SAAS,GAAG,EAAE,SAAS,MAAM,CAAC;gBAC7G;YACF;YACA,MAAM,MAAM;gBACV,GAAG,KAAK;gBACR;gBACA;YACF;YACA,MAAM,iBAAiB,MAAM,SAAS,aAAa,CAAC,SAAS,QAAQ;YACrE,IAAI,UAAU,eAAe,KAAK,IAAI,WAAW,eAAe,MAAM,EAAE;gBACtE,OAAO;oBACL,OAAO;wBACL,OAAO;oBACT;gBACF;YACF;YACA,OAAO,CAAC;QACV;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40floating-ui/dom/dist/floating-ui.dom.mjs"], "sourcesContent": ["import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nconst absoluteOrFixed = /*#__PURE__*/new Set(['absolute', 'fixed']);\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && absoluteOrFixed.has(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAEA;;;;;AAGA,SAAS,iBAAiB,OAAO;IAC/B,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE;IAC7B,yEAAyE;IACzE,yEAAyE;IACzE,IAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;IACrC,IAAI,SAAS,WAAW,IAAI,MAAM,KAAK;IACvC,MAAM,YAAY,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;IAChC,MAAM,cAAc,YAAY,QAAQ,WAAW,GAAG;IACtD,MAAM,eAAe,YAAY,QAAQ,YAAY,GAAG;IACxD,MAAM,iBAAiB,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,WAAW,eAAe,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,YAAY;IACzE,IAAI,gBAAgB;QAClB,QAAQ;QACR,SAAS;IACX;IACA,OAAO;QACL;QACA;QACA,GAAG;IACL;AACF;AAEA,SAAS,cAAc,OAAO;IAC5B,OAAO,CAAC,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,WAAW,QAAQ,cAAc,GAAG;AACxD;AAEA,SAAS,SAAS,OAAO;IACvB,MAAM,aAAa,cAAc;IACjC,IAAI,CAAC,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,aAAa;QAC9B,OAAO,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IACtB;IACA,MAAM,OAAO,WAAW,qBAAqB;IAC7C,MAAM,EACJ,KAAK,EACL,MAAM,EACN,CAAC,EACF,GAAG,iBAAiB;IACrB,IAAI,IAAI,CAAC,IAAI,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI;IAC/C,IAAI,IAAI,CAAC,IAAI,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI;IAEjD,mDAAmD;IAEnD,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ,CAAC,IAAI;QAC7B,IAAI;IACN;IACA,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ,CAAC,IAAI;QAC7B,IAAI;IACN;IACA,OAAO;QACL;QACA;IACF;AACF;AAEA,MAAM,YAAY,WAAW,GAAE,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;AAC5C,SAAS,iBAAiB,OAAO;IAC/B,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE;IACtB,IAAI,CAAC,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,OAAO,CAAC,IAAI,cAAc,EAAE;QACtC,OAAO;IACT;IACA,OAAO;QACL,GAAG,IAAI,cAAc,CAAC,UAAU;QAChC,GAAG,IAAI,cAAc,CAAC,SAAS;IACjC;AACF;AACA,SAAS,uBAAuB,OAAO,EAAE,OAAO,EAAE,oBAAoB;IACpE,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IACA,IAAI,CAAC,wBAAwB,WAAW,yBAAyB,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QACnF,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,YAAY;IACjF,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IACA,MAAM,aAAa,QAAQ,qBAAqB;IAChD,MAAM,aAAa,cAAc;IACjC,IAAI,QAAQ,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IACzB,IAAI,cAAc;QAChB,IAAI,cAAc;YAChB,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;gBAC3B,QAAQ,SAAS;YACnB;QACF,OAAO;YACL,QAAQ,SAAS;QACnB;IACF;IACA,MAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,gBAAgB,iBAAiB,cAAc,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IACtI,IAAI,IAAI,CAAC,WAAW,IAAI,GAAG,cAAc,CAAC,IAAI,MAAM,CAAC;IACrD,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,cAAc,CAAC,IAAI,MAAM,CAAC;IACpD,IAAI,QAAQ,WAAW,KAAK,GAAG,MAAM,CAAC;IACtC,IAAI,SAAS,WAAW,MAAM,GAAG,MAAM,CAAC;IACxC,IAAI,YAAY;QACd,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE;QACtB,MAAM,YAAY,gBAAgB,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QACtF,IAAI,aAAa;QACjB,IAAI,gBAAgB,CAAA,GAAA,uLAAA,CAAA,kBAAe,AAAD,EAAE;QACpC,MAAO,iBAAiB,gBAAgB,cAAc,WAAY;YAChE,MAAM,cAAc,SAAS;YAC7B,MAAM,aAAa,cAAc,qBAAqB;YACtD,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE;YAC7B,MAAM,OAAO,WAAW,IAAI,GAAG,CAAC,cAAc,UAAU,GAAG,WAAW,IAAI,WAAW,CAAC,IAAI,YAAY,CAAC;YACvG,MAAM,MAAM,WAAW,GAAG,GAAG,CAAC,cAAc,SAAS,GAAG,WAAW,IAAI,UAAU,CAAC,IAAI,YAAY,CAAC;YACnG,KAAK,YAAY,CAAC;YAClB,KAAK,YAAY,CAAC;YAClB,SAAS,YAAY,CAAC;YACtB,UAAU,YAAY,CAAC;YACvB,KAAK;YACL,KAAK;YACL,aAAa,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE;YACvB,gBAAgB,CAAA,GAAA,uLAAA,CAAA,kBAAe,AAAD,EAAE;QAClC;IACF;IACA,OAAO,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB;QACA;QACA;QACA;IACF;AACF;AAEA,yEAAyE;AACzE,qBAAqB;AACrB,SAAS,oBAAoB,OAAO,EAAE,IAAI;IACxC,MAAM,aAAa,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,UAAU;IACpD,IAAI,CAAC,MAAM;QACT,OAAO,sBAAsB,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI,GAAG;IACnE;IACA,OAAO,KAAK,IAAI,GAAG;AACrB;AAEA,SAAS,cAAc,eAAe,EAAE,MAAM,EAAE,gBAAgB;IAC9D,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB;IACrB;IACA,MAAM,WAAW,gBAAgB,qBAAqB;IACtD,MAAM,IAAI,SAAS,IAAI,GAAG,OAAO,UAAU,GAAG,CAAC,mBAAmB,IAClE,wBAAwB;IACxB,oBAAoB,iBAAiB,SAAS;IAC9C,MAAM,IAAI,SAAS,GAAG,GAAG,OAAO,SAAS;IACzC,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,sDAAsD,IAAI;IACjE,IAAI,EACF,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,QAAQ,EACT,GAAG;IACJ,MAAM,UAAU,aAAa;IAC7B,MAAM,kBAAkB,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC3C,MAAM,WAAW,WAAW,CAAA,GAAA,uLAAA,CAAA,aAAU,AAAD,EAAE,SAAS,QAAQ,IAAI;IAC5D,IAAI,iBAAiB,mBAAmB,YAAY,SAAS;QAC3D,OAAO;IACT;IACA,IAAI,SAAS;QACX,YAAY;QACZ,WAAW;IACb;IACA,IAAI,QAAQ,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IACzB,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IAC7B,MAAM,0BAA0B,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;IAC9C,IAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;QACnE,IAAI,CAAA,GAAA,uLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB,UAAU,CAAA,GAAA,uLAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB;YAC9E,SAAS,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;QACzB;QACA,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;YAC/B,MAAM,aAAa,sBAAsB;YACzC,QAAQ,SAAS;YACjB,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,UAAU;YAClD,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,SAAS;QACnD;IACF;IACA,MAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,QAAQ,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IACzI,OAAO;QACL,OAAO,KAAK,KAAK,GAAG,MAAM,CAAC;QAC3B,QAAQ,KAAK,MAAM,GAAG,MAAM,CAAC;QAC7B,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,OAAO,UAAU,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;QAC5E,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,OAAO,SAAS,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;IAC7E;AACF;AAEA,SAAS,eAAe,OAAO;IAC7B,OAAO,MAAM,IAAI,CAAC,QAAQ,cAAc;AAC1C;AAEA,+EAA+E;AAC/E,uEAAuE;AACvE,SAAS,gBAAgB,OAAO;IAC9B,MAAM,OAAO,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE;IAChC,MAAM,SAAS,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;IAC7B,MAAM,OAAO,QAAQ,aAAa,CAAC,IAAI;IACvC,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW;IACxF,MAAM,SAAS,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY;IAC7F,IAAI,IAAI,CAAC,OAAO,UAAU,GAAG,oBAAoB;IACjD,MAAM,IAAI,CAAC,OAAO,SAAS;IAC3B,IAAI,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,SAAS,KAAK,OAAO;QAC9C,KAAK,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,KAAK,WAAW,EAAE,KAAK,WAAW,IAAI;IACjD;IACA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,gBAAgB,OAAO,EAAE,QAAQ;IACxC,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE;IACtB,MAAM,OAAO,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE;IAChC,MAAM,iBAAiB,IAAI,cAAc;IACzC,IAAI,QAAQ,KAAK,WAAW;IAC5B,IAAI,SAAS,KAAK,YAAY;IAC9B,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,gBAAgB;QAClB,QAAQ,eAAe,KAAK;QAC5B,SAAS,eAAe,MAAM;QAC9B,MAAM,sBAAsB,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD;QACnC,IAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;YACvE,IAAI,eAAe,UAAU;YAC7B,IAAI,eAAe,SAAS;QAC9B;IACF;IACA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA,MAAM,kBAAkB,WAAW,GAAE,IAAI,IAAI;IAAC;IAAY;CAAQ;AAClE,oEAAoE;AACpE,SAAS,2BAA2B,OAAO,EAAE,QAAQ;IACnD,MAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa;IACrE,MAAM,MAAM,WAAW,GAAG,GAAG,QAAQ,SAAS;IAC9C,MAAM,OAAO,WAAW,IAAI,GAAG,QAAQ,UAAU;IACjD,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,SAAS,WAAW,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IACxE,MAAM,QAAQ,QAAQ,WAAW,GAAG,MAAM,CAAC;IAC3C,MAAM,SAAS,QAAQ,YAAY,GAAG,MAAM,CAAC;IAC7C,MAAM,IAAI,OAAO,MAAM,CAAC;IACxB,MAAM,IAAI,MAAM,MAAM,CAAC;IACvB,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AACA,SAAS,kCAAkC,OAAO,EAAE,gBAAgB,EAAE,QAAQ;IAC5E,IAAI;IACJ,IAAI,qBAAqB,YAAY;QACnC,OAAO,gBAAgB,SAAS;IAClC,OAAO,IAAI,qBAAqB,YAAY;QAC1C,OAAO,gBAAgB,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5C,OAAO,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB;QACtC,OAAO,2BAA2B,kBAAkB;IACtD,OAAO;QACL,MAAM,gBAAgB,iBAAiB;QACvC,OAAO;YACL,GAAG,iBAAiB,CAAC,GAAG,cAAc,CAAC;YACvC,GAAG,iBAAiB,CAAC,GAAG,cAAc,CAAC;YACvC,OAAO,iBAAiB,KAAK;YAC7B,QAAQ,iBAAiB,MAAM;QACjC;IACF;IACA,OAAO,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE;AAC1B;AACA,SAAS,yBAAyB,OAAO,EAAE,QAAQ;IACjD,MAAM,aAAa,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;IACjC,IAAI,eAAe,YAAY,CAAC,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAA,GAAA,uLAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa;QAC1F,OAAO;IACT;IACA,OAAO,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,QAAQ,KAAK,WAAW,yBAAyB,YAAY;AACnG;AAEA,4EAA4E;AAC5E,2EAA2E;AAC3E,oCAAoC;AACpC,SAAS,4BAA4B,OAAO,EAAE,KAAK;IACjD,MAAM,eAAe,MAAM,GAAG,CAAC;IAC/B,IAAI,cAAc;QAChB,OAAO;IACT;IACA,IAAI,SAAS,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,EAAE,EAAE,OAAO,MAAM,CAAC,CAAA,KAAM,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAA,GAAA,uLAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;IACxG,IAAI,sCAAsC;IAC1C,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,QAAQ,KAAK;IAC9D,IAAI,cAAc,iBAAiB,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;IAE5D,qGAAqG;IACrG,MAAO,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,CAAC,CAAA,GAAA,uLAAA,CAAA,wBAAqB,AAAD,EAAE,aAAc;QACpE,MAAM,gBAAgB,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE;QACvC,MAAM,0BAA0B,CAAA,GAAA,uLAAA,CAAA,oBAAiB,AAAD,EAAE;QAClD,IAAI,CAAC,2BAA2B,cAAc,QAAQ,KAAK,SAAS;YAClE,sCAAsC;QACxC;QACA,MAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,QAAQ,KAAK,YAAY,CAAC,CAAC,uCAAuC,gBAAgB,GAAG,CAAC,oCAAoC,QAAQ,KAAK,CAAA,GAAA,uLAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,CAAC,2BAA2B,yBAAyB,SAAS;QACnY,IAAI,uBAAuB;YACzB,8BAA8B;YAC9B,SAAS,OAAO,MAAM,CAAC,CAAA,WAAY,aAAa;QAClD,OAAO;YACL,mDAAmD;YACnD,sCAAsC;QACxC;QACA,cAAc,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;IAC9B;IACA,MAAM,GAAG,CAAC,SAAS;IACnB,OAAO;AACT;AAEA,4EAA4E;AAC5E,sBAAsB;AACtB,SAAS,gBAAgB,IAAI;IAC3B,IAAI,EACF,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,QAAQ,EACT,GAAG;IACJ,MAAM,2BAA2B,aAAa,sBAAsB,CAAA,GAAA,uLAAA,CAAA,aAAU,AAAD,EAAE,WAAW,EAAE,GAAG,4BAA4B,SAAS,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC;IACzJ,MAAM,oBAAoB;WAAI;QAA0B;KAAa;IACrE,MAAM,wBAAwB,iBAAiB,CAAC,EAAE;IAClD,MAAM,eAAe,kBAAkB,MAAM,CAAC,CAAC,SAAS;QACtD,MAAM,OAAO,kCAAkC,SAAS,kBAAkB;QAC1E,QAAQ,GAAG,GAAG,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,KAAK,GAAG,EAAE,QAAQ,GAAG;QACvC,QAAQ,KAAK,GAAG,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KAAK,EAAE,QAAQ,KAAK;QAC7C,QAAQ,MAAM,GAAG,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,KAAK,MAAM,EAAE,QAAQ,MAAM;QAChD,QAAQ,IAAI,GAAG,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,KAAK,IAAI,EAAE,QAAQ,IAAI;QAC1C,OAAO;IACT,GAAG,kCAAkC,SAAS,uBAAuB;IACrE,OAAO;QACL,OAAO,aAAa,KAAK,GAAG,aAAa,IAAI;QAC7C,QAAQ,aAAa,MAAM,GAAG,aAAa,GAAG;QAC9C,GAAG,aAAa,IAAI;QACpB,GAAG,aAAa,GAAG;IACrB;AACF;AAEA,SAAS,cAAc,OAAO;IAC5B,MAAM,EACJ,KAAK,EACL,MAAM,EACP,GAAG,iBAAiB;IACrB,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,8BAA8B,OAAO,EAAE,YAAY,EAAE,QAAQ;IACpE,MAAM,0BAA0B,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;IAC9C,MAAM,kBAAkB,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC3C,MAAM,UAAU,aAAa;IAC7B,MAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS;IAC3D,IAAI,SAAS;QACX,YAAY;QACZ,WAAW;IACb;IACA,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IAE7B,sEAAsE;IACtE,uEAAuE;IACvE,SAAS;QACP,QAAQ,CAAC,GAAG,oBAAoB;IAClC;IACA,IAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;QACnE,IAAI,CAAA,GAAA,uLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB,UAAU,CAAA,GAAA,uLAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB;YAC9E,SAAS,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;QACzB;QACA,IAAI,yBAAyB;YAC3B,MAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS;YACtE,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,UAAU;YAClD,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa,SAAS;QACnD,OAAO,IAAI,iBAAiB;YAC1B;QACF;IACF;IACA,IAAI,WAAW,CAAC,2BAA2B,iBAAiB;QAC1D;IACF;IACA,MAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,UAAU,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE;IACnI,MAAM,IAAI,KAAK,IAAI,GAAG,OAAO,UAAU,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;IAClE,MAAM,IAAI,KAAK,GAAG,GAAG,OAAO,SAAS,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;IAChE,OAAO;QACL;QACA;QACA,OAAO,KAAK,KAAK;QACjB,QAAQ,KAAK,MAAM;IACrB;AACF;AAEA,SAAS,mBAAmB,OAAO;IACjC,OAAO,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,QAAQ,KAAK;AAChD;AAEA,SAAS,oBAAoB,OAAO,EAAE,QAAQ;IAC5C,IAAI,CAAC,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,QAAQ,KAAK,SAAS;QAC7E,OAAO;IACT;IACA,IAAI,UAAU;QACZ,OAAO,SAAS;IAClB;IACA,IAAI,kBAAkB,QAAQ,YAAY;IAE1C,6EAA6E;IAC7E,6EAA6E;IAC7E,4EAA4E;IAC5E,cAAc;IACd,IAAI,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACnD,kBAAkB,gBAAgB,aAAa,CAAC,IAAI;IACtD;IACA,OAAO;AACT;AAEA,yEAAyE;AACzE,kDAAkD;AAClD,SAAS,gBAAgB,OAAO,EAAE,QAAQ;IACxC,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE;IACtB,IAAI,CAAA,GAAA,uLAAA,CAAA,aAAU,AAAD,EAAE,UAAU;QACvB,OAAO;IACT;IACA,IAAI,CAAC,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;QAC3B,IAAI,kBAAkB,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;QACpC,MAAO,mBAAmB,CAAC,CAAA,GAAA,uLAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAkB;YACjE,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,CAAC,mBAAmB,kBAAkB;gBACtE,OAAO;YACT;YACA,kBAAkB,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;QAClC;QACA,OAAO;IACT;IACA,IAAI,eAAe,oBAAoB,SAAS;IAChD,MAAO,gBAAgB,CAAA,GAAA,uLAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,mBAAmB,cAAe;QACvF,eAAe,oBAAoB,cAAc;IACnD;IACA,IAAI,gBAAgB,CAAA,GAAA,uLAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,mBAAmB,iBAAiB,CAAC,CAAA,GAAA,uLAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;QAC/H,OAAO;IACT;IACA,OAAO,gBAAgB,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY;AACxD;AAEA,MAAM,kBAAkB,eAAgB,IAAI;IAC1C,MAAM,oBAAoB,IAAI,CAAC,eAAe,IAAI;IAClD,MAAM,kBAAkB,IAAI,CAAC,aAAa;IAC1C,MAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;IAC9D,OAAO;QACL,WAAW,8BAA8B,KAAK,SAAS,EAAE,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;QAC9G,UAAU;YACR,GAAG;YACH,GAAG;YACH,OAAO,mBAAmB,KAAK;YAC/B,QAAQ,mBAAmB,MAAM;QACnC;IACF;AACF;AAEA,SAAS,MAAM,OAAO;IACpB,OAAO,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,SAAS,KAAK;AACjD;AAEA,MAAM,WAAW;IACf;IACA,oBAAA,uLAAA,CAAA,qBAAkB;IAClB;IACA;IACA;IACA;IACA;IACA;IACA,WAAA,uLAAA,CAAA,YAAS;IACT;AACF;AAEA,SAAS,cAAc,CAAC,EAAE,CAAC;IACzB,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM;AACnF;AAEA,yCAAyC;AACzC,SAAS,YAAY,OAAO,EAAE,MAAM;IAClC,IAAI,KAAK;IACT,IAAI;IACJ,MAAM,OAAO,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE;IAChC,SAAS;QACP,IAAI;QACJ,aAAa;QACb,CAAC,MAAM,EAAE,KAAK,QAAQ,IAAI,UAAU;QACpC,KAAK;IACP;IACA,SAAS,QAAQ,IAAI,EAAE,SAAS;QAC9B,IAAI,SAAS,KAAK,GAAG;YACnB,OAAO;QACT;QACA,IAAI,cAAc,KAAK,GAAG;YACxB,YAAY;QACd;QACA;QACA,MAAM,2BAA2B,QAAQ,qBAAqB;QAC9D,MAAM,EACJ,IAAI,EACJ,GAAG,EACH,KAAK,EACL,MAAM,EACP,GAAG;QACJ,IAAI,CAAC,MAAM;YACT;QACF;QACA,IAAI,CAAC,SAAS,CAAC,QAAQ;YACrB;QACF;QACA,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE;QACvB,MAAM,aAAa,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,WAAW,GAAG,CAAC,OAAO,KAAK;QACzD,MAAM,cAAc,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,YAAY,GAAG,CAAC,MAAM,MAAM;QAC3D,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE;QACxB,MAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;QACjG,MAAM,UAAU;YACd;YACA,WAAW,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,GAAG,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,GAAG,eAAe;QAC1C;QACA,IAAI,gBAAgB;QACpB,SAAS,cAAc,OAAO;YAC5B,MAAM,QAAQ,OAAO,CAAC,EAAE,CAAC,iBAAiB;YAC1C,IAAI,UAAU,WAAW;gBACvB,IAAI,CAAC,eAAe;oBAClB,OAAO;gBACT;gBACA,IAAI,CAAC,OAAO;oBACV,oEAAoE;oBACpE,0CAA0C;oBAC1C,YAAY,WAAW;wBACrB,QAAQ,OAAO;oBACjB,GAAG;gBACL,OAAO;oBACL,QAAQ,OAAO;gBACjB;YACF;YACA,IAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,qBAAqB,KAAK;gBAC5F,iEAAiE;gBACjE,uEAAuE;gBACvE,wEAAwE;gBACxE,oEAAoE;gBACpE,oEAAoE;gBACpE,uEAAuE;gBACvE,uDAAuD;gBACvD;YACF;YACA,gBAAgB;QAClB;QAEA,0EAA0E;QAC1E,SAAS;QACT,IAAI;YACF,KAAK,IAAI,qBAAqB,eAAe;gBAC3C,GAAG,OAAO;gBACV,mBAAmB;gBACnB,MAAM,KAAK,aAAa;YAC1B;QACF,EAAE,OAAO,IAAI;YACX,KAAK,IAAI,qBAAqB,eAAe;QAC/C;QACA,GAAG,OAAO,CAAC;IACb;IACA,QAAQ;IACR,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;IACtD,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,gBAAgB,OAAO,mBAAmB,UAAU,EACpD,cAAc,OAAO,yBAAyB,UAAU,EACxD,iBAAiB,KAAK,EACvB,GAAG;IACJ,MAAM,cAAc,cAAc;IAClC,MAAM,YAAY,kBAAkB,iBAAiB;WAAK,cAAc,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe,EAAE;WAAM,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE;KAAU,GAAG,EAAE;IACxJ,UAAU,OAAO,CAAC,CAAA;QAChB,kBAAkB,SAAS,gBAAgB,CAAC,UAAU,QAAQ;YAC5D,SAAS;QACX;QACA,kBAAkB,SAAS,gBAAgB,CAAC,UAAU;IACxD;IACA,MAAM,YAAY,eAAe,cAAc,YAAY,aAAa,UAAU;IAClF,IAAI,iBAAiB,CAAC;IACtB,IAAI,iBAAiB;IACrB,IAAI,eAAe;QACjB,iBAAiB,IAAI,eAAe,CAAA;YAClC,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,cAAc,WAAW,MAAM,KAAK,eAAe,gBAAgB;gBACrE,yDAAyD;gBACzD,yDAAyD;gBACzD,eAAe,SAAS,CAAC;gBACzB,qBAAqB;gBACrB,iBAAiB,sBAAsB;oBACrC,IAAI;oBACJ,CAAC,kBAAkB,cAAc,KAAK,QAAQ,gBAAgB,OAAO,CAAC;gBACxE;YACF;YACA;QACF;QACA,IAAI,eAAe,CAAC,gBAAgB;YAClC,eAAe,OAAO,CAAC;QACzB;QACA,eAAe,OAAO,CAAC;IACzB;IACA,IAAI;IACJ,IAAI,cAAc,iBAAiB,sBAAsB,aAAa;IACtE,IAAI,gBAAgB;QAClB;IACF;IACA,SAAS;QACP,MAAM,cAAc,sBAAsB;QAC1C,IAAI,eAAe,CAAC,cAAc,aAAa,cAAc;YAC3D;QACF;QACA,cAAc;QACd,UAAU,sBAAsB;IAClC;IACA;IACA,OAAO;QACL,IAAI;QACJ,UAAU,OAAO,CAAC,CAAA;YAChB,kBAAkB,SAAS,mBAAmB,CAAC,UAAU;YACzD,kBAAkB,SAAS,mBAAmB,CAAC,UAAU;QAC3D;QACA,aAAa,QAAQ;QACrB,CAAC,mBAAmB,cAAc,KAAK,QAAQ,iBAAiB,UAAU;QAC1E,iBAAiB;QACjB,IAAI,gBAAgB;YAClB,qBAAqB;QACvB;IACF;AACF;AAEA;;;;;;;CAOC,GACD,MAAM,iBAAiB,8LAAA,CAAA,iBAAgB;AAEvC;;;;;;CAMC,GACD,MAAM,SAAS,8LAAA,CAAA,SAAQ;AAEvB;;;;;CAKC,GACD,MAAM,gBAAgB,8LAAA,CAAA,gBAAe;AAErC;;;;CAIC,GACD,MAAM,QAAQ,8LAAA,CAAA,QAAO;AAErB;;;;;CAKC,GACD,MAAM,OAAO,8LAAA,CAAA,OAAM;AAEnB;;;;;CAKC,GACD,MAAM,OAAO,8LAAA,CAAA,OAAM;AAEnB;;;;CAIC,GACD,MAAM,OAAO,8LAAA,CAAA,OAAM;AAEnB;;;;CAIC,GACD,MAAM,QAAQ,8LAAA,CAAA,QAAO;AAErB;;;;CAIC,GACD,MAAM,SAAS,8LAAA,CAAA,SAAQ;AAEvB;;CAEC,GACD,MAAM,aAAa,8LAAA,CAAA,aAAY;AAE/B;;;CAGC,GACD,MAAM,kBAAkB,CAAC,WAAW,UAAU;IAC5C,2EAA2E;IAC3E,wEAAwE;IACxE,6EAA6E;IAC7E,MAAM,QAAQ,IAAI;IAClB,MAAM,gBAAgB;QACpB;QACA,GAAG,OAAO;IACZ;IACA,MAAM,oBAAoB;QACxB,GAAG,cAAc,QAAQ;QACzB,IAAI;IACN;IACA,OAAO,CAAA,GAAA,8LAAA,CAAA,kBAAiB,AAAD,EAAE,WAAW,UAAU;QAC5C,GAAG,aAAa;QAChB,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/node_modules/%40floating-ui/react-dom/dist/floating-ui.react-dom.mjs"], "sourcesContent": ["import { computePosition, arrow as arrow$2, autoPlacement as autoPlacement$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar isClient = typeof document !== 'undefined';\n\nvar noop = function noop() {};\nvar index = isClient ? useLayoutEffect : noop;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAEA;AAEA;;;;;;AAEA,IAAI,WAAW,OAAO,aAAa;AAEnC,IAAI,OAAO,SAAS,QAAQ;AAC5B,IAAI,QAAQ,WAAW,6JAAA,CAAA,kBAAe,GAAG;AAEzC,gFAAgF;AAChF,YAAY;AACZ,SAAS,UAAU,CAAC,EAAE,CAAC;IACrB,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IACA,IAAI,OAAO,MAAM,OAAO,GAAG;QACzB,OAAO;IACT;IACA,IAAI,OAAO,MAAM,cAAc,EAAE,QAAQ,OAAO,EAAE,QAAQ,IAAI;QAC5D,OAAO;IACT;IACA,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK,KAAK,OAAO,MAAM,UAAU;QACnC,IAAI,MAAM,OAAO,CAAC,IAAI;YACpB,SAAS,EAAE,MAAM;YACjB,IAAI,WAAW,EAAE,MAAM,EAAE,OAAO;YAChC,IAAK,IAAI,QAAQ,QAAQ,GAAI;gBAC3B,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG;oBAC1B,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,OAAO,OAAO,IAAI,CAAC;QACnB,SAAS,KAAK,MAAM;QACpB,IAAI,WAAW,OAAO,IAAI,CAAC,GAAG,MAAM,EAAE;YACpC,OAAO;QACT;QACA,IAAK,IAAI,QAAQ,QAAQ,GAAI;YAC3B,IAAI,CAAC,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG;gBACvC,OAAO;YACT;QACF;QACA,IAAK,IAAI,QAAQ,QAAQ,GAAI;YAC3B,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAI,QAAQ,YAAY,EAAE,QAAQ,EAAE;gBAClC;YACF;YACA,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,GAAG;gBAC9B,OAAO;YACT;QACF;QACA,OAAO;IACT;IACA,OAAO,MAAM,KAAK,MAAM;AAC1B;AAEA,SAAS,OAAO,OAAO;IACrB,IAAI,OAAO,WAAW,aAAa;QACjC,OAAO;IACT;IACA,MAAM,MAAM,QAAQ,aAAa,CAAC,WAAW,IAAI;IACjD,OAAO,IAAI,gBAAgB,IAAI;AACjC;AAEA,SAAS,WAAW,OAAO,EAAE,KAAK;IAChC,MAAM,MAAM,OAAO;IACnB,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO;AACnC;AAEA,SAAS,aAAa,KAAK;IACzB,MAAM,MAAM,6JAAA,CAAA,SAAY,CAAC;IACzB,MAAM;QACJ,IAAI,OAAO,GAAG;IAChB;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,YAAY,OAAO;IAC1B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,YAAY,QAAQ,EACpB,WAAW,UAAU,EACrB,aAAa,EAAE,EACf,QAAQ,EACR,UAAU,EACR,WAAW,iBAAiB,EAC5B,UAAU,gBAAgB,EAC3B,GAAG,CAAC,CAAC,EACN,YAAY,IAAI,EAChB,oBAAoB,EACpB,IAAI,EACL,GAAG;IACJ,MAAM,CAAC,MAAM,QAAQ,GAAG,6JAAA,CAAA,WAAc,CAAC;QACrC,GAAG;QACH,GAAG;QACH;QACA;QACA,gBAAgB,CAAC;QACjB,cAAc;IAChB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,6JAAA,CAAA,WAAc,CAAC;IAC/D,IAAI,CAAC,UAAU,kBAAkB,aAAa;QAC5C,oBAAoB;IACtB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,WAAc,CAAC;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,WAAc,CAAC;IACjD,MAAM,eAAe,6JAAA,CAAA,cAAiB;iDAAC,CAAA;YACrC,IAAI,SAAS,aAAa,OAAO,EAAE;gBACjC,aAAa,OAAO,GAAG;gBACvB,cAAc;YAChB;QACF;gDAAG,EAAE;IACL,MAAM,cAAc,6JAAA,CAAA,cAAiB;gDAAC,CAAA;YACpC,IAAI,SAAS,YAAY,OAAO,EAAE;gBAChC,YAAY,OAAO,GAAG;gBACtB,aAAa;YACf;QACF;+CAAG,EAAE;IACL,MAAM,cAAc,qBAAqB;IACzC,MAAM,aAAa,oBAAoB;IACvC,MAAM,eAAe,6JAAA,CAAA,SAAY,CAAC;IAClC,MAAM,cAAc,6JAAA,CAAA,SAAY,CAAC;IACjC,MAAM,UAAU,6JAAA,CAAA,SAAY,CAAC;IAC7B,MAAM,0BAA0B,wBAAwB;IACxD,MAAM,0BAA0B,aAAa;IAC7C,MAAM,cAAc,aAAa;IACjC,MAAM,UAAU,aAAa;IAC7B,MAAM,SAAS,6JAAA,CAAA,cAAiB;2CAAC;YAC/B,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,YAAY,OAAO,EAAE;gBACjD;YACF;YACA,MAAM,SAAS;gBACb;gBACA;gBACA,YAAY;YACd;YACA,IAAI,YAAY,OAAO,EAAE;gBACvB,OAAO,QAAQ,GAAG,YAAY,OAAO;YACvC;YACA,CAAA,GAAA,4LAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,OAAO,EAAE,YAAY,OAAO,EAAE,QAAQ,IAAI;mDAAC,CAAA;oBACtE,MAAM,WAAW;wBACf,GAAG,IAAI;wBACP,sEAAsE;wBACtE,gEAAgE;wBAChE,mEAAmE;wBACnE,kEAAkE;wBAClE,cAAc,QAAQ,OAAO,KAAK;oBACpC;oBACA,IAAI,aAAa,OAAO,IAAI,CAAC,UAAU,QAAQ,OAAO,EAAE,WAAW;wBACjE,QAAQ,OAAO,GAAG;wBAClB,oKAAA,CAAA,YAAkB;+DAAC;gCACjB,QAAQ;4BACV;;oBACF;gBACF;;QACF;0CAAG;QAAC;QAAkB;QAAW;QAAU;QAAa;KAAQ;IAChE,MAAM;QACJ,IAAI,SAAS,SAAS,QAAQ,OAAO,CAAC,YAAY,EAAE;YAClD,QAAQ,OAAO,CAAC,YAAY,GAAG;YAC/B,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,cAAc;gBAChB,CAAC;QACH;IACF,GAAG;QAAC;KAAK;IACT,MAAM,eAAe,6JAAA,CAAA,SAAY,CAAC;IAClC,MAAM;QACJ,aAAa,OAAO,GAAG;QACvB,OAAO;YACL,aAAa,OAAO,GAAG;QACzB;IACF,GAAG,EAAE;IACL,MAAM;QACJ,IAAI,aAAa,aAAa,OAAO,GAAG;QACxC,IAAI,YAAY,YAAY,OAAO,GAAG;QACtC,IAAI,eAAe,YAAY;YAC7B,IAAI,wBAAwB,OAAO,EAAE;gBACnC,OAAO,wBAAwB,OAAO,CAAC,aAAa,YAAY;YAClE;YACA;QACF;IACF,GAAG;QAAC;QAAa;QAAY;QAAQ;QAAyB;KAAwB;IACtF,MAAM,OAAO,6JAAA,CAAA,UAAa;qCAAC,IAAM,CAAC;gBAChC,WAAW;gBACX,UAAU;gBACV;gBACA;YACF,CAAC;oCAAG;QAAC;QAAc;KAAY;IAC/B,MAAM,WAAW,6JAAA,CAAA,UAAa;yCAAC,IAAM,CAAC;gBACpC,WAAW;gBACX,UAAU;YACZ,CAAC;wCAAG;QAAC;QAAa;KAAW;IAC7B,MAAM,iBAAiB,6JAAA,CAAA,UAAa;+CAAC;YACnC,MAAM,gBAAgB;gBACpB,UAAU;gBACV,MAAM;gBACN,KAAK;YACP;YACA,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,OAAO;YACT;YACA,MAAM,IAAI,WAAW,SAAS,QAAQ,EAAE,KAAK,CAAC;YAC9C,MAAM,IAAI,WAAW,SAAS,QAAQ,EAAE,KAAK,CAAC;YAC9C,IAAI,WAAW;gBACb,OAAO;oBACL,GAAG,aAAa;oBAChB,WAAW,eAAe,IAAI,SAAS,IAAI;oBAC3C,GAAI,OAAO,SAAS,QAAQ,KAAK,OAAO;wBACtC,YAAY;oBACd,CAAC;gBACH;YACF;YACA,OAAO;gBACL,UAAU;gBACV,MAAM;gBACN,KAAK;YACP;QACF;8CAAG;QAAC;QAAU;QAAW,SAAS,QAAQ;QAAE,KAAK,CAAC;QAAE,KAAK,CAAC;KAAC;IAC3D,OAAO,6JAAA,CAAA,UAAa;+BAAC,IAAM,CAAC;gBAC1B,GAAG,IAAI;gBACP;gBACA;gBACA;gBACA;YACF,CAAC;8BAAG;QAAC;QAAM;QAAQ;QAAM;QAAU;KAAe;AACpD;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAA;IACd,SAAS,MAAM,KAAK;QAClB,OAAO,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,OAAO;IACvC;IACA,OAAO;QACL,MAAM;QACN;QACA,IAAG,KAAK;YACN,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG,OAAO,YAAY,aAAa,QAAQ,SAAS;YACrD,IAAI,WAAW,MAAM,UAAU;gBAC7B,IAAI,QAAQ,OAAO,IAAI,MAAM;oBAC3B,OAAO,CAAA,GAAA,4LAAA,CAAA,QAAO,AAAD,EAAE;wBACb,SAAS,QAAQ,OAAO;wBACxB;oBACF,GAAG,EAAE,CAAC;gBACR;gBACA,OAAO,CAAC;YACV;YACA,IAAI,SAAS;gBACX,OAAO,CAAA,GAAA,4LAAA,CAAA,QAAO,AAAD,EAAE;oBACb;oBACA;gBACF,GAAG,EAAE,CAAC;YACR;YACA,OAAO,CAAC;QACV;IACF;AACF;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,CAAC,SAAS,OAAS,CAAC;QACjC,GAAG,CAAA,GAAA,4LAAA,CAAA,SAAQ,AAAD,EAAE,QAAQ;QACpB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;CAIC,GACD,MAAM,QAAQ,CAAC,SAAS,OAAS,CAAC;QAChC,GAAG,CAAA,GAAA,4LAAA,CAAA,QAAO,AAAD,EAAE,QAAQ;QACnB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;CAEC,GACD,MAAM,aAAa,CAAC,SAAS,OAAS,CAAC;QACrC,GAAG,CAAA,GAAA,4LAAA,CAAA,aAAY,AAAD,EAAE,QAAQ;QACxB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;;CAKC,GACD,MAAM,OAAO,CAAC,SAAS,OAAS,CAAC;QAC/B,GAAG,CAAA,GAAA,4LAAA,CAAA,OAAM,AAAD,EAAE,QAAQ;QAClB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;;CAKC,GACD,MAAM,OAAO,CAAC,SAAS,OAAS,CAAC;QAC/B,GAAG,CAAA,GAAA,4LAAA,CAAA,OAAM,AAAD,EAAE,QAAQ;QAClB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC,SAAS,OAAS,CAAC;QACxC,GAAG,CAAA,GAAA,4LAAA,CAAA,gBAAe,AAAD,EAAE,QAAQ;QAC3B,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;CAIC,GACD,MAAM,OAAO,CAAC,SAAS,OAAS,CAAC;QAC/B,GAAG,CAAA,GAAA,4LAAA,CAAA,OAAM,AAAD,EAAE,QAAQ;QAClB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;CAIC,GACD,MAAM,SAAS,CAAC,SAAS,OAAS,CAAC;QACjC,GAAG,CAAA,GAAA,4LAAA,CAAA,SAAQ,AAAD,EAAE,QAAQ;QACpB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC;AAED;;;;;CAKC,GACD,MAAM,QAAQ,CAAC,SAAS,OAAS,CAAC;QAChC,GAAG,QAAQ,QAAQ;QACnB,SAAS;YAAC;YAAS;SAAK;IAC1B,CAAC", "ignoreList": [0], "debugId": null}}]}