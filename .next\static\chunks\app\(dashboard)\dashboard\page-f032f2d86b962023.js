(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[337],{61735:(e,s,a)=>{Promise.resolve().then(a.bind(a,91633))},91633:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(95155),l=a(12115),n=a(88482),i=a(97168),d=a(88145),r=a(34964),c=a(24623),x=a(62098),m=a(50589),u=a(26386),o=a(42851),h=a(48691),j=a(7574),v=a(92657),N=a(98611),y=a(53999);let f={location:"Tanauan, Leyte",temperature:28,condition:"Partly Cloudy",humidity:75,windSpeed:12,visibility:10,pressure:1013,feelsLike:32,uvIndex:7,icon:"partly-cloudy",lastUpdated:new Date().toLocaleTimeString()},p={sunny:x.A,"partly-cloudy":m.A,cloudy:m.A,rainy:u.A,stormy:u.A,snowy:o.A,default:x.A};function g(e){var s,a;let{className:i}=e,[r,c]=(0,l.useState)(f),[m,u]=(0,l.useState)(!1),[o,g]=(0,l.useState)(null);(0,l.useEffect)(()=>{let e=setInterval(()=>{c(e=>({...e,temperature:Math.round(26+6*Math.random()),humidity:Math.round(65+20*Math.random()),windSpeed:Math.round(8+8*Math.random()),lastUpdated:new Date().toLocaleTimeString()}))},18e5);return()=>clearInterval(e)},[]);let w=p[r.icon]||p.default;return(0,t.jsxs)(n.Zp,{className:(0,y.cn)("",i),children:[(0,t.jsx)(n.aR,{className:"pb-3",children:(0,t.jsxs)(n.ZB,{className:"text-lg flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Weather"}),(0,t.jsx)(d.E,{variant:"outline",className:"text-xs",children:r.location})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 rounded-lg bg-blue-50",children:(0,t.jsx)(w,{className:"h-8 w-8 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:(0,y.cn)("text-3xl font-bold",(s=r.temperature)>=30?"text-red-500":s>=25?"text-orange-500":s>=20?"text-yellow-500":"text-blue-500"),children:[r.temperature,"\xb0C"]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:r.condition})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Feels like"}),(0,t.jsxs)("div",{className:"text-lg font-semibold",children:[r.feelsLike,"\xb0C"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium",children:[r.humidity,"%"]}),(0,t.jsx)("div",{className:"text-muted-foreground",children:"Humidity"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium",children:[r.windSpeed," km/h"]}),(0,t.jsx)("div",{className:"text-muted-foreground",children:"Wind"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium",children:[r.visibility," km"]}),(0,t.jsx)("div",{className:"text-muted-foreground",children:"Visibility"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-purple-500"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium",children:[r.pressure," hPa"]}),(0,t.jsx)("div",{className:"text-muted-foreground",children:"Pressure"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg bg-muted/50",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-yellow-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"UV Index"})]}),(0,t.jsx)(d.E,{variant:"outline",className:(0,y.cn)("font-bold",(a=r.uvIndex)>=8?"text-red-500":a>=6?"text-orange-500":a>=3?"text-yellow-500":"text-green-500"),children:r.uvIndex})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground text-center pt-2 border-t",children:["Last updated: ",r.lastUpdated]})]})]})}var w=a(13977),b=a(74324),T=a(53904),S=a(97939),A=a(23861),E=a(79397),M=a(1243),k=a(91788),q=a(81304),K=a(381),R=a(13319);function C(){let[e,s]=(0,l.useState)(new Date),[a,x]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=setInterval(()=>{s(new Date)},1e3);return()=>clearInterval(e)},[]);let m=async()=>{x(!0),await new Promise(e=>setTimeout(e,1e3)),x(!1)};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Welcome to QRSAMS - Real-time Student Attendance Monitoring"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-2xl font-bold tabular-nums",children:(0,R.GP)(e,"HH:mm:ss")}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,R.GP)(e,"EEEE, MMMM d, yyyy")})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:m,disabled:a,children:[(0,t.jsx)(T.A,{className:"mr-2 h-4 w-4 ".concat(a?"animate-spin":"")}),"Refresh"]}),(0,t.jsxs)(i.$,{size:"sm",children:[(0,t.jsx)(S.A,{className:"mr-2 h-4 w-4"}),"Open Scanner"]})]})]})]}),(0,t.jsx)(c.nz,{totalStudents:b.Kq.totalStudents,presentToday:b.Kq.presentToday,lateToday:b.Kq.lateToday,absentToday:b.Kq.absentToday,attendanceRate:b.Kq.attendanceRate}),(0,t.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,t.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,t.jsxs)(r.Tabs,{defaultValue:"overview",className:"space-y-4",children:[(0,t.jsxs)(r.TabsList,{className:"grid w-full grid-cols-4",children:[(0,t.jsx)(r.TabsTrigger,{value:"overview",children:"Overview"}),(0,t.jsx)(r.TabsTrigger,{value:"trends",children:"Trends"}),(0,t.jsx)(r.TabsTrigger,{value:"grades",children:"Grades"}),(0,t.jsx)(r.TabsTrigger,{value:"patterns",children:"Patterns"})]}),(0,t.jsx)(r.TabsContent,{value:"overview",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsx)(w.il,{present:b.Kq.presentToday,late:b.Kq.lateToday,absent:b.Kq.absentToday}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"Real-time Counters"}),(0,t.jsx)(n.BT,{children:"Live attendance tracking"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsx)(c.iS,{value:b.Kq.presentToday,label:"Present",trend:"up"}),(0,t.jsx)(c.iS,{value:b.Kq.lateToday,label:"Late",trend:"down"})]}),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(c.C8,{value:b.Kq.presentToday+b.Kq.lateToday,max:b.Kq.totalStudents,size:120})})]})]})]})}),(0,t.jsx)(r.TabsContent,{value:"trends",children:(0,t.jsx)(w.tp,{data:b.Kq.weeklyTrend})}),(0,t.jsx)(r.TabsContent,{value:"grades",children:(0,t.jsx)(w.nn,{data:b.Kq.gradeBreakdown})}),(0,t.jsx)(r.TabsContent,{value:"patterns",children:(0,t.jsx)(w.M4,{data:[{day:"Mon",hour:7,count:45},{day:"Mon",hour:8,count:120},{day:"Tue",hour:7,count:38},{day:"Tue",hour:8,count:95},{day:"Wed",hour:7,count:52},{day:"Wed",hour:8,count:110},{day:"Thu",hour:7,count:41},{day:"Thu",hour:8,count:88},{day:"Fri",hour:7,count:35},{day:"Fri",hour:8,count:75}]})})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(g,{}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.ZB,{className:"text-lg",children:"Recent Activity"}),(0,t.jsx)(n.BT,{children:"Latest scans and alerts"})]}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[b.Lp.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("success"===e.status?"bg-green-500":"warning"===e.status?"bg-yellow-500":"bg-red-500")}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium truncate",children:e.studentName}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.action," • ",e.time]})]}),(0,t.jsx)(d.E,{variant:"success"===e.status?"default":"warning"===e.status?"secondary":"destructive",className:"text-xs",children:"scan"===e.type?(0,t.jsx)(E.A,{className:"h-3 w-3"}):(0,t.jsx)(M.A,{className:"h-3 w-3"})})]},e.id)),(0,t.jsx)(i.$,{variant:"outline",size:"sm",className:"w-full",children:"View All Activity"})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"text-lg",children:"System Health"}),(0,t.jsx)(n.BT,{children:"Real-time system status"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Scanner Status"}),(0,t.jsx)(d.E,{variant:"default",children:"Online"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Database"}),(0,t.jsx)(d.E,{variant:"default",children:"Connected"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"SMS Gateway"}),(0,t.jsx)(d.E,{variant:"secondary",children:"Pending"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Last Sync"}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:"2 min ago"})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"text-lg",children:"Quick Actions"})}),(0,t.jsxs)(n.Wu,{className:"space-y-2",children:[(0,t.jsxs)(i.$,{className:"w-full justify-start",variant:"outline",children:[(0,t.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"Export Today's Data"]}),(0,t.jsxs)(i.$,{className:"w-full justify-start",variant:"outline",children:[(0,t.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Print Daily Report"]}),(0,t.jsxs)(i.$,{className:"w-full justify-start",variant:"outline",children:[(0,t.jsx)(K.A,{className:"mr-2 h-4 w-4"}),"System Settings"]})]})]})]})]})]})}}},e=>{e.O(0,[803,550,319,110,911,902,498,441,964,358],()=>e(e.s=61735)),_N_E=e.O()}]);