(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{34964:(e,s,t)=>{"use strict";t.d(s,{Tabs:()=>l,TabsContent:()=>c,TabsList:()=>i,TabsTrigger:()=>d});var a=t(95155);t(12115);var n=t(60704),r=t(53999);function l(e){let{className:s,...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"tabs",className:(0,r.cn)("flex flex-col gap-2",s),...t})}function i(e){let{className:s,...t}=e;return(0,a.jsx)(n.B8,{"data-slot":"tabs-list",className:(0,r.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"tabs-trigger",className:(0,r.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)(n.UC,{"data-slot":"tabs-content",className:(0,r.cn)("flex-1 outline-none",s),...t})}},41085:(e,s,t)=>{"use strict";t.d(s,{CG:()=>d,Fm:()=>x,Qs:()=>h,cj:()=>i,h:()=>m,qp:()=>u});var a=t(95155);t(12115);var n=t(15452),r=t(54416),l=t(53999);function i(e){let{...s}=e;return(0,a.jsx)(n.bL,{"data-slot":"sheet",...s})}function d(e){let{...s}=e;return(0,a.jsx)(n.l9,{"data-slot":"sheet-trigger",...s})}function c(e){let{...s}=e;return(0,a.jsx)(n.ZL,{"data-slot":"sheet-portal",...s})}function o(e){let{className:s,...t}=e;return(0,a.jsx)(n.hJ,{"data-slot":"sheet-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...t})}function m(e){let{className:s,children:t,side:i="right",...d}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(o,{}),(0,a.jsxs)(n.UC,{"data-slot":"sheet-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===i&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===i&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===i&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===i&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",s),...d,children:[t,(0,a.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(r.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,l.cn)("flex flex-col gap-1.5 p-4",s),...t})}function u(e){let{className:s,...t}=e;return(0,a.jsx)(n.hE,{"data-slot":"sheet-title",className:(0,l.cn)("text-foreground font-semibold",s),...t})}function h(e){let{className:s,...t}=e;return(0,a.jsx)(n.VY,{"data-slot":"sheet-description",className:(0,l.cn)("text-muted-foreground text-sm",s),...t})}},41397:(e,s,t)=>{"use strict";t.d(s,{k:()=>i});var a=t(95155),n=t(12115),r=t(55863),l=t(53999);let i=n.forwardRef((e,s)=>{let{className:t,value:n,...i}=e;return(0,a.jsx)(r.bL,{ref:s,className:(0,l.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...i,children:(0,a.jsx)(r.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(n||0),"%)")}})})});i.displayName=r.bL.displayName},49026:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>i,TN:()=>d});var a=t(95155);t(12115);var n=t(74466),r=t(53999);let l=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,r.cn)(l({variant:t}),s),...n})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,r.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...t})}},53999:(e,s,t)=>{"use strict";t.d(s,{cn:()=>r});var a=t(52596),n=t(39688);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.QP)((0,a.$)(s))}},55909:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>sv});var a=t(95155),n=t(12115),r=t(88482),l=t(97168),i=t(88145),d=t(76037),c=t(84616),o=t(29869),m=t(97939),x=t(91788),u=t(17580),h=t(55670),j=t(9446),p=t(33109);let g=e=>[e.firstName,e.middleName,e.lastName].filter(Boolean).join(" "),f=["7","8","9","10","11","12"],v=["Father","Mother","Guardian","Grandparent","Sibling","Other"],N=["Male","Female"];var b=t(89852),y=t(82714),w=t(76981),C=t(5196),S=t(53999);function A(e){let{className:s,...t}=e;return(0,a.jsx)(w.bL,{"data-slot":"checkbox",className:(0,S.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...t,children:(0,a.jsx)(w.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(C.A,{className:"size-3.5"})})})}var k=t(41085),R=t(47924),D=t(70257),z=t(66932),I=t(54416);function P(e){var s,t,d,c,o;let{filters:m,onFiltersChange:x,availableGrades:u,availableSections:h,availableCourses:j,availableYears:p,className:g}=e,[f,v]=(0,n.useState)(!1),N=(e,s)=>{x({...m,[e]:s})},y=(e,s)=>{let t=m[e]||[],a=t.includes(s)?t.filter(e=>e!==s):[...t,s];N(e,a.length>0?a:void 0)},w=()=>{x({})},C=()=>{var e,s,t,a,n;let r=0;return m.search&&r++,(null==(e=m.grade)?void 0:e.length)&&r++,(null==(s=m.section)?void 0:s.length)&&r++,(null==(t=m.status)?void 0:t.length)&&r++,(null==(a=m.course)?void 0:a.length)&&r++,(null==(n=m.year)?void 0:n.length)&&r++,r},A=C()>0,P=["Active","Inactive","Transferred","Graduated"];return(0,a.jsxs)("div",{className:(0,S.cn)("space-y-4",g),children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(R.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{placeholder:"Search students by name, ID, email, or guardian...",value:m.search||"",onChange:e=>N("search",e.target.value||void 0),className:"pl-10"})]}),(0,a.jsxs)(k.cj,{open:f,onOpenChange:v,children:[(0,a.jsx)(k.CG,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"outline",className:"md:hidden",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Filters",A&&(0,a.jsx)(i.E,{variant:"secondary",className:"ml-2 h-5 w-5 p-0 text-xs",children:C()})]})}),(0,a.jsxs)(k.h,{side:"right",className:"w-80",children:[(0,a.jsxs)(k.Fm,{children:[(0,a.jsx)(k.qp,{children:"Filter Students"}),(0,a.jsx)(k.Qs,{children:"Apply filters to narrow down the student list"})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(T,{filters:m,onFiltersChange:x,availableGrades:u,availableSections:h,availableCourses:j,availableYears:p,statusOptions:P,onClearFilters:w})})]})]}),(0,a.jsxs)(l.$,{variant:"outline",className:"hidden md:flex",onClick:()=>v(!f),children:[(0,a.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Filters",A&&(0,a.jsx)(i.E,{variant:"secondary",className:"ml-2 h-5 w-5 p-0 text-xs",children:C()})]}),A&&(0,a.jsxs)(l.$,{variant:"ghost",onClick:w,children:[(0,a.jsx)(I.A,{className:"h-4 w-4 mr-2"}),"Clear"]})]}),f&&(0,a.jsxs)(r.Zp,{className:"hidden md:block",children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{className:"text-lg",children:"Filter Options"}),(0,a.jsx)(r.BT,{children:"Select multiple options to filter the student list"})]}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)(F,{filters:m,onFiltersChange:x,availableGrades:u,availableSections:h,availableCourses:j,availableYears:p,statusOptions:P,toggleArrayFilter:y})})]}),A&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[m.search&&(0,a.jsxs)(i.E,{variant:"secondary",className:"gap-1",children:["Search: ",m.search,(0,a.jsx)(I.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>N("search",void 0)})]}),null==(s=m.grade)?void 0:s.map(e=>(0,a.jsxs)(i.E,{variant:"secondary",className:"gap-1",children:["Grade ",e,(0,a.jsx)(I.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>y("grade",e)})]},e)),null==(t=m.section)?void 0:t.map(e=>(0,a.jsxs)(i.E,{variant:"secondary",className:"gap-1",children:["Section ",e,(0,a.jsx)(I.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>y("section",e)})]},e)),null==(d=m.status)?void 0:d.map(e=>(0,a.jsxs)(i.E,{variant:"secondary",className:"gap-1",children:[e,(0,a.jsx)(I.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>y("status",e)})]},e)),null==(c=m.course)?void 0:c.map(e=>(0,a.jsxs)(i.E,{variant:"secondary",className:"gap-1",children:[e,(0,a.jsx)(I.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>y("course",e)})]},e)),null==(o=m.year)?void 0:o.map(e=>(0,a.jsxs)(i.E,{variant:"secondary",className:"gap-1",children:[e,(0,a.jsx)(I.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>y("year",e)})]},e))]})]})}function F(e){let{filters:s,onFiltersChange:t,availableGrades:n,availableSections:r,availableCourses:l,availableYears:i,statusOptions:d,toggleArrayFilter:c}=e;return(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Grade Level"}),(0,a.jsx)("div",{className:"space-y-2",children:n.map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A,{id:"grade-".concat(e),checked:(null==(t=s.grade)?void 0:t.includes(e))||!1,onCheckedChange:()=>c("grade",e)}),(0,a.jsxs)(y.J,{htmlFor:"grade-".concat(e),className:"text-sm",children:["Grade ",e]})]},e)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Section"}),(0,a.jsx)("div",{className:"space-y-2",children:r.map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A,{id:"section-".concat(e),checked:(null==(t=s.section)?void 0:t.includes(e))||!1,onCheckedChange:()=>c("section",e)}),(0,a.jsxs)(y.J,{htmlFor:"section-".concat(e),className:"text-sm",children:["Section ",e]})]},e)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Status"}),(0,a.jsx)("div",{className:"space-y-2",children:d.map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A,{id:"status-".concat(e),checked:(null==(t=s.status)?void 0:t.includes(e))||!1,onCheckedChange:()=>c("status",e)}),(0,a.jsx)(y.J,{htmlFor:"status-".concat(e),className:"text-sm",children:e})]},e)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Course/Track"}),(0,a.jsx)("div",{className:"space-y-2",children:l.map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A,{id:"course-".concat(e),checked:(null==(t=s.course)?void 0:t.includes(e))||!1,onCheckedChange:()=>c("course",e)}),(0,a.jsx)(y.J,{htmlFor:"course-".concat(e),className:"text-sm",children:e})]},e)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Year Level"}),(0,a.jsx)("div",{className:"space-y-2",children:i.map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A,{id:"year-".concat(e),checked:(null==(t=s.year)?void 0:t.includes(e))||!1,onCheckedChange:()=>c("year",e)}),(0,a.jsx)(y.J,{htmlFor:"year-".concat(e),className:"text-sm",children:e})]},e)})})]})]})}function T(e){let{filters:s,onFiltersChange:t,availableGrades:n,availableSections:r,availableCourses:i,availableYears:c,statusOptions:o,onClearFilters:m}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(F,{filters:s,onFiltersChange:t,availableGrades:n,availableSections:r,availableCourses:i,availableYears:c,statusOptions:o,toggleArrayFilter:(e,a)=>{let n=s[e]||[],r=n.includes(a)?n.filter(e=>e!==a):[...n,a];t({...s,[e]:r.length>0?r:void 0})}}),(0,a.jsx)(d.w,{}),(0,a.jsxs)(l.$,{variant:"outline",onClick:m,className:"w-full",children:[(0,a.jsx)(I.A,{className:"h-4 w-4 mr-2"}),"Clear All Filters"]})]})}var B=t(88524),E=t(69663),U=t(48698);function G(e){let{...s}=e;return(0,a.jsx)(U.bL,{"data-slot":"dropdown-menu",...s})}function L(e){let{...s}=e;return(0,a.jsx)(U.l9,{"data-slot":"dropdown-menu-trigger",...s})}function Q(e){let{className:s,sideOffset:t=4,...n}=e;return(0,a.jsx)(U.ZL,{children:(0,a.jsx)(U.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,S.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",s),...n})})}function M(e){let{className:s,inset:t,variant:n="default",...r}=e;return(0,a.jsx)(U.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:(0,S.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...r})}function Y(e){let{className:s,...t}=e;return(0,a.jsx)(U.wv,{"data-slot":"dropdown-menu-separator",className:(0,S.cn)("bg-border -mx-1 my-1 h-px",s),...t})}var Z=t(47863),O=t(66474),q=t(19420),$=t(28883),_=t(92657),V=t(5623),W=t(13717),J=t(62525),H=t(99840),K=t(34964),X=t(40646),ee=t(85339),es=t(54861),et=t(87949),ea=t(71007),en=t(69074),er=t(81304),el=t(4516),ei=t(49026),ed=t(53904),ec=t(24357),eo=t(1243);class em{static getInstance(){return em.instance||(em.instance=new em),em.instance}generateQRData(e){return{studentId:e.id,name:g(e),grade:e.grade,section:e.section,validUntil:this.getValidUntilDate()}}generateQRString(e){let s=this.generateQRData(e);return JSON.stringify({id:s.studentId,name:s.name,grade:s.grade,section:s.section,school:"Tanauan School of Arts and Trade",type:"student_id",validUntil:s.validUntil,generated:new Date().toISOString()})}generateQRCodeId(e){let s=Date.now(),t=new Date().getFullYear();return"QR_".concat(e.id,"_").concat(t,"_").concat(s.toString().slice(-6))}getValidUntilDate(){let e=new Date;return e.setFullYear(e.getFullYear()+1),e.toISOString().split("T")[0]}validateQRData(e){try{let s=JSON.parse(e);if(!s.id||!s.name||!s.type)return{valid:!1,error:"Missing required fields"};if("student_id"!==s.type)return{valid:!1,error:"Invalid QR code type"};if(s.validUntil&&new Date(s.validUntil)<new Date)return{valid:!1,error:"QR code has expired"};return{valid:!0,data:s}}catch(e){return{valid:!1,error:"Invalid QR code format"}}}}class ex{static getInstance(){return ex.instance||(ex.instance=new ex),ex.instance}getQRSize(e){switch(e){case"small":return 96;case"medium":default:return 144;case"large":return 192}}generatePrintLayout(e,s){let t=em.getInstance();this.getQRSize(s.size);let a=e.map(e=>({student:e,qrData:t.generateQRString(e),qrId:t.generateQRCodeId(e)})),n=this.layoutQRCodes(a,s);return{sheets:n,totalCodes:a.length,totalSheets:n.length,options:s}}layoutQRCodes(e,s){let t=[],a=s.codesPerSheet;for(let n=0;n<e.length;n+=a){let r=e.slice(n,n+a);t.push({id:"sheet_".concat(Math.floor(n/a)+1),codes:r,paperSize:s.paperSize})}return t}generatePrintCSS(e){let s=this.getQRSize(e.size);return"\n      @media print {\n        @page {\n          size: ".concat(e.paperSize,";\n          margin: 0.5in;\n        }\n        \n        .qr-sheet {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(").concat(s+20,"px, 1fr));\n          gap: ").concat(10,"px;\n          page-break-after: always;\n        }\n        \n        .qr-code-item {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          text-align: center;\n          padding: ").concat(10,"px;\n          border: 1px solid #ddd;\n          border-radius: 4px;\n        }\n        \n        .qr-code-image {\n          width: ").concat(s,"px;\n          height: ").concat(s,"px;\n          margin-bottom: 8px;\n        }\n        \n        .student-info {\n          font-size: 10px;\n          line-height: 1.2;\n        }\n        \n        .student-name {\n          font-weight: bold;\n          margin-bottom: 2px;\n        }\n        \n        .student-details {\n          color: #666;\n        }\n      }\n    ")}}class eu{static getInstance(){return eu.instance||(eu.instance=new eu),eu.instance}async generateForStudent(e){let s=em.getInstance();s.generateQRString(e);let t=s.generateQRCodeId(e);return await new Promise(e=>setTimeout(e,100)),t}async generateBatch(e,s){let t=[];for(let a=0;a<e.length;a++){let n=await this.generateForStudent(e[a]);t.push(n),s&&s((a+1)/e.length*100)}return t}async regenerateForStudent(e){return this.generateForStudent(e)}validateAndDecode(e){let s=em.getInstance().validateQRData(e);return s.valid&&s.data?{valid:!0,studentId:s.data.id}:{valid:!1,error:s.error}}}let eh=em.getInstance();ex.getInstance();let ej=eu.getInstance();var ep=t(56671);function eg(e){let{student:s,onQRGenerated:t,onQRRegenerated:d,className:c}=e,[o,u]=(0,n.useState)(!1),[h,j]=(0,n.useState)(null),[p,f]=(0,n.useState)(s.qrCode||null);(0,n.useEffect)(()=>{s.qrCode&&j(eh.generateQRString(s))},[s]);let v=async()=>{u(!0);try{let e=await ej.generateForStudent(s),a=eh.generateQRString(s);f(e),j(a),t&&t(e),ep.o.success("QR code generated successfully!")}catch(e){ep.o.error("Failed to generate QR code")}finally{u(!1)}},N=async()=>{u(!0);try{let e=await ej.regenerateForStudent(s),t=eh.generateQRString(s);f(e),j(t),d&&d(e),ep.o.success("QR code regenerated successfully!")}catch(e){ep.o.error("Failed to regenerate QR code")}finally{u(!1)}},b=async()=>{if(h)try{await navigator.clipboard.writeText(h),ep.o.success("QR code data copied to clipboard")}catch(e){ep.o.error("Failed to copy QR code data")}},y=g(s);return(0,a.jsxs)(r.Zp,{className:(0,S.cn)("w-full",c),children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Student QR Code"]}),(0,a.jsxs)(r.BT,{children:["QR code for ",y," (",s.id,")"]})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("div",{className:"w-48 h-48 bg-white border-2 border-gray-200 rounded-lg flex items-center justify-center",children:p?(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"h-24 w-24 mx-auto mb-2 text-muted-foreground"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"QR Code Preview"}),(0,a.jsx)("p",{className:"text-xs font-mono break-all px-2",children:p})]}):(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(m.A,{className:"h-12 w-12 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"No QR Code Generated"})]})}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:N,disabled:o,children:[o?(0,a.jsx)(ed.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,a.jsx)(ed.A,{className:"h-4 w-4 mr-2"}),"Regenerate"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{p&&(ep.o.success("QR code download started"),console.log("Downloading QR code:",p))},children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{if(!p)return;let e=window.open("","_blank");if(e){let t=g(s);e.document.write("\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <title>QR Code - ".concat(t,'</title>\n          <style>\n            body {\n              font-family: Arial, sans-serif;\n              display: flex;\n              justify-content: center;\n              align-items: center;\n              min-height: 100vh;\n              margin: 0;\n              background: white;\n            }\n            .qr-container {\n              text-align: center;\n              padding: 20px;\n              border: 2px solid #ddd;\n              border-radius: 8px;\n            }\n            .qr-placeholder {\n              width: 200px;\n              height: 200px;\n              border: 2px dashed #ccc;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              margin: 20px auto;\n              font-size: 14px;\n              color: #666;\n            }\n            .student-info {\n              margin-top: 15px;\n            }\n            .student-name {\n              font-size: 18px;\n              font-weight: bold;\n              margin-bottom: 5px;\n            }\n            .student-details {\n              font-size: 14px;\n              color: #666;\n              line-height: 1.4;\n            }\n            @media print {\n              body { margin: 0; }\n              .qr-container { border: none; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class="qr-container">\n            <div class="qr-placeholder">\n              QR Code<br/>\n              ').concat(p,'\n            </div>\n            <div class="student-info">\n              <div class="student-name">').concat(t,'</div>\n              <div class="student-details">\n                Student ID: ').concat(s.id,"<br/>\n                Grade ").concat(s.grade).concat(s.section?" - Section ".concat(s.section):"","<br/>\n                ").concat(s.course," - ").concat(s.year,"<br/>\n                Tanauan School of Arts and Trade\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      ")),e.document.close(),e.print()}},children:[(0,a.jsx)(er.A,{className:"h-4 w-4 mr-2"}),"Print"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:b,children:[(0,a.jsx)(ec.A,{className:"h-4 w-4 mr-2"}),"Copy Data"]})]}):(0,a.jsx)(l.$,{onClick:v,disabled:o,className:"min-w-[120px]",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Generate QR"]})})})]}),p&&(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"QR Code ID:"}),(0,a.jsx)("span",{className:"font-mono text-xs break-all",children:p}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Student ID:"}),(0,a.jsx)("span",{className:"font-mono",children:s.id}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Student Name:"}),(0,a.jsx)("span",{children:y}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Grade & Section:"}),(0,a.jsxs)("span",{children:["Grade ",s.grade,s.section?" - ".concat(s.section):""]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Course:"}),(0,a.jsx)("span",{children:s.course}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Status:"}),(0,a.jsx)(i.E,{variant:"Active"===s.status?"default":"secondary",children:s.status})]})}),(0,a.jsxs)(ei.Fc,{children:[(0,a.jsx)(eo.A,{className:"h-4 w-4"}),(0,a.jsxs)(ei.TN,{children:[(0,a.jsx)("strong",{children:"Usage Instructions:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"Use this QR code for quick attendance marking"}),(0,a.jsx)("li",{children:"Scan with the QRSAMS mobile app or scanner"}),(0,a.jsx)("li",{children:"Print and laminate for durability"}),(0,a.jsx)("li",{children:"Keep the QR code secure and report if lost"}),(0,a.jsx)("li",{children:"QR codes are valid for one academic year"})]})]})]}),h&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(X.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-green-600",children:"QR code is valid and ready to use"})]})]})]})}var ef=t(41397),ev=t(14186),eN=t(68500),eb=t(72713);function ey(e){var s;let{student:t,showDetailed:n=!1,showActions:i=!1,className:c}=e,o=t.attendanceStats;if(!o)return(0,a.jsx)(r.Zp,{className:(0,S.cn)("w-full",c),children:(0,a.jsxs)(r.Wu,{className:"text-center py-8",children:[(0,a.jsx)(ev.A,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No Attendance Data"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Attendance statistics will appear here once the student starts attending classes."})]})});let m=(s=o.attendanceRate)>=95?{grade:"Excellent",color:"text-green-600",icon:(0,a.jsx)(X.A,{className:"h-4 w-4 text-green-600"})}:s>=85?{grade:"Good",color:"text-blue-600",icon:(0,a.jsx)(X.A,{className:"h-4 w-4 text-blue-600"})}:s>=75?{grade:"Fair",color:"text-yellow-600",icon:(0,a.jsx)(ee.A,{className:"h-4 w-4 text-yellow-600"})}:{grade:"Poor",color:"text-red-600",icon:(0,a.jsx)(es.A,{className:"h-4 w-4 text-red-600"})};return(0,a.jsxs)(r.Zp,{className:(0,S.cn)("w-full",c),children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eb.A,{className:"h-5 w-5"}),"Attendance Statistics",i&&(0,a.jsxs)("div",{className:"ml-auto flex gap-2",children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"View Details"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,a.jsx)(r.BT,{children:"Attendance summary for the current academic period"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsxs)("span",{className:"text-3xl font-bold",children:[o.attendanceRate.toFixed(1),"%"]}),"up"==(o.attendanceRate>=85?"up":"down")?(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(eN.A,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[m.icon,(0,a.jsxs)("span",{className:(0,S.cn)("font-medium",m.color),children:[m.grade," Attendance"]})]}),(0,a.jsx)(ef.k,{value:o.attendanceRate,className:"w-full max-w-xs mx-auto"})]}),(0,a.jsx)(d.w,{}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center space-y-1",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:o.presentDays}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Present"}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(o.presentDays/o.totalDays*100).toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"text-center space-y-1",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:o.lateDays}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Late"}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(o.lateDays/o.totalDays*100).toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"text-center space-y-1",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:o.absentDays}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Absent"}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(o.absentDays/o.totalDays*100).toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"text-center space-y-1",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:o.totalDays}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Days"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"School Days"})]})]}),n&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.w,{}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium",children:"Detailed Breakdown"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(X.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Present Days"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-bold",children:o.presentDays}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(o.presentDays/o.totalDays*100).toFixed(1),"%"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-yellow-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4 text-yellow-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Late Arrivals"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-bold",children:o.lateDays}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(o.lateDays/o.totalDays*100).toFixed(1),"%"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(es.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Absent Days"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-bold",children:o.absentDays}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(o.absentDays/o.totalDays*100).toFixed(1),"%"]})]})]})]})]})]}),o.lastAttendance&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.w,{}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(en.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Last attendance: ",new Date(o.lastAttendance).toLocaleDateString()]})]})]}),o.attendanceRate<75&&(0,a.jsxs)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-800",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:"Low Attendance Alert"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:"This student's attendance rate is below the minimum requirement of 75%. Consider reaching out to the student and guardian."})]}),o.absentDays>=5&&(0,a.jsxs)("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-yellow-800",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:"Frequent Absences"})]}),(0,a.jsxs)("p",{className:"text-sm text-yellow-700 mt-1",children:["This student has been absent for ",o.absentDays," days. Monitor attendance closely and consider intervention."]})]})]})]})}function ew(e){var s;let{student:t,className:n}=e,r=t.attendanceStats;return r?(0,a.jsxs)("div",{className:(0,S.cn)("flex items-center gap-1",n),children:[(s=r.attendanceRate)>=95?(0,a.jsx)(X.A,{className:"h-3 w-3 text-green-500"}):s>=85?(0,a.jsx)(X.A,{className:"h-3 w-3 text-blue-500"}):s>=75?(0,a.jsx)(ee.A,{className:"h-3 w-3 text-yellow-500"}):(0,a.jsx)(es.A,{className:"h-3 w-3 text-red-500"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[r.attendanceRate.toFixed(1),"%"]})]}):(0,a.jsx)("div",{className:(0,S.cn)("text-center",n),children:(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:"No data"})})}function eC(e){var s;let{student:t,onEdit:d,onGenerateQR:c,onPrintQR:o,className:x}=e,[h,j]=(0,n.useState)("overview"),p=g(t);return(0,a.jsxs)("div",{className:(0,S.cn)("space-y-6",x),children:[(0,a.jsx)(r.Zp,{children:(0,a.jsx)(r.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-6",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center md:items-start gap-4",children:[(0,a.jsxs)(E.eu,{className:"h-32 w-32 border-4 border-border",children:[(0,a.jsx)(E.BK,{src:t.photo,alt:p,className:"object-cover"}),(0,a.jsx)(E.q5,{className:"text-2xl font-bold bg-primary/10",children:p.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]}),(0,a.jsx)("div",{className:"text-center md:text-left",children:(0,a.jsx)(i.E,{variant:"Active"===t.status?"default":"secondary",className:(0,S.cn)("text-xs",(e=>{switch(e){case"Active":return"bg-green-500";case"Inactive":return"bg-yellow-500";case"Transferred":return"bg-blue-500";case"Graduated":return"bg-purple-500";default:return"bg-gray-500"}})(t.status)),children:t.status})})]}),(0,a.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:p}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground",children:t.email}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["ID: ",t.id]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-sm",children:["Grade ",t.grade," - ",t.section?"Section ".concat(t.section):"No Section"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-sm",children:[t.course," - ",t.year]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(en.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-sm",children:["Enrolled: ",new Date(t.enrollmentDate).toLocaleDateString()]})]}),t.attendanceStats&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(s=t.attendanceStats.attendanceRate)>=95?(0,a.jsx)(X.A,{className:"h-4 w-4 text-green-500"}):s>=85?(0,a.jsx)(ee.A,{className:"h-4 w-4 text-yellow-500"}):(0,a.jsx)(es.A,{className:"h-4 w-4 text-red-500"}),(0,a.jsxs)("span",{className:"text-sm",children:["Attendance: ",t.attendanceStats.attendanceRate.toFixed(1),"%"]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[d&&(0,a.jsxs)(l.$,{onClick:d,variant:"outline",children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),c&&(0,a.jsxs)(l.$,{onClick:c,variant:"outline",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Generate QR"]}),o&&(0,a.jsxs)(l.$,{onClick:o,variant:"outline",children:[(0,a.jsx)(er.A,{className:"h-4 w-4 mr-2"}),"Print QR"]})]})]})})}),(0,a.jsxs)(K.Tabs,{value:h,onValueChange:j,children:[(0,a.jsxs)(K.TabsList,{className:"grid w-full grid-cols-4",children:[(0,a.jsx)(K.TabsTrigger,{value:"overview",children:"Overview"}),(0,a.jsx)(K.TabsTrigger,{value:"contacts",children:"Contacts"}),(0,a.jsx)(K.TabsTrigger,{value:"attendance",children:"Attendance"}),(0,a.jsx)(K.TabsTrigger,{value:"qr-code",children:"QR Code"})]}),(0,a.jsxs)(K.TabsContent,{value:"overview",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Personal Information"]})}),(0,a.jsx)(r.Wu,{className:"space-y-3",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"First Name:"}),(0,a.jsx)("span",{children:t.firstName}),t.middleName&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Middle Name:"}),(0,a.jsx)("span",{children:t.middleName})]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Last Name:"}),(0,a.jsx)("span",{children:t.lastName}),t.dateOfBirth&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Date of Birth:"}),(0,a.jsx)("span",{children:new Date(t.dateOfBirth).toLocaleDateString()})]}),t.gender&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Gender:"}),(0,a.jsx)("span",{children:t.gender})]})]})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-5 w-5"}),"Academic Information"]})}),(0,a.jsx)(r.Wu,{className:"space-y-3",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Grade Level:"}),(0,a.jsxs)("span",{children:["Grade ",t.grade]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Section:"}),(0,a.jsx)("span",{children:t.section||"Not assigned"}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Course/Track:"}),(0,a.jsx)("span",{children:t.course}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Year Level:"}),(0,a.jsx)("span",{children:t.year}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Status:"}),(0,a.jsx)(i.E,{variant:"Active"===t.status?"default":"secondary",children:t.status})]})})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(el.A,{className:"h-5 w-5"}),"Address Information"]})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{children:t.address.street}),(0,a.jsxs)("p",{children:["Barangay ",t.address.barangay]}),(0,a.jsxs)("p",{children:[t.address.city,", ",t.address.province," ",t.address.zipCode]}),(0,a.jsx)("p",{children:t.address.country})]})})]})]}),(0,a.jsxs)(K.TabsContent,{value:"contacts",className:"space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Guardian Information"]})}),(0,a.jsxs)(r.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:t.guardian.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:t.guardian.relationship})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:t.guardian.phone})]}),t.guardian.email&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)($.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:t.guardian.email})]})]})]}),t.guardian.address&&(0,a.jsxs)("div",{className:"pt-2 border-t",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Address:"}),(0,a.jsx)("p",{className:"text-sm",children:t.guardian.address})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Emergency Contacts"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:t.emergencyContacts.map((e,s)=>(0,a.jsx)("div",{className:"p-4 border rounded-lg",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.relationship})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:e.phone})]}),e.address&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(el.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm",children:e.address})]})]})]})},s))})})]})]}),(0,a.jsx)(K.TabsContent,{value:"attendance",className:"space-y-6",children:(0,a.jsx)(ey,{student:t,showDetailed:!0,showActions:!0})}),(0,a.jsx)(K.TabsContent,{value:"qr-code",className:"space-y-6",children:(0,a.jsx)(eg,{student:t,onQRGenerated:c,onQRRegenerated:c})})]})]})}var eS=t(62177),eA=t(90221),ek=t(95784),eR=t(99474),eD=t(99708);let ez=eS.Op,eI=n.createContext({}),eP=e=>{let{...s}=e;return(0,a.jsx)(eI.Provider,{value:{name:s.name},children:(0,a.jsx)(eS.xI,{...s})})},eF=()=>{let e=n.useContext(eI),s=n.useContext(eT),{getFieldState:t}=(0,eS.xW)(),a=(0,eS.lN)({name:e.name}),r=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...r}},eT=n.createContext({});function eB(e){let{className:s,...t}=e,r=n.useId();return(0,a.jsx)(eT.Provider,{value:{id:r},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,S.cn)("grid gap-2",s),...t})})}function eE(e){let{className:s,...t}=e,{error:n,formItemId:r}=eF();return(0,a.jsx)(y.J,{"data-slot":"form-label","data-error":!!n,className:(0,S.cn)("data-[error=true]:text-destructive",s),htmlFor:r,...t})}function eU(e){let{...s}=e,{error:t,formItemId:n,formDescriptionId:r,formMessageId:l}=eF();return(0,a.jsx)(eD.DX,{"data-slot":"form-control",id:n,"aria-describedby":t?"".concat(r," ").concat(l):"".concat(r),"aria-invalid":!!t,...s})}function eG(e){let{className:s,...t}=e,{formDescriptionId:n}=eF();return(0,a.jsx)("p",{"data-slot":"form-description",id:n,className:(0,S.cn)("text-muted-foreground text-sm",s),...t})}function eL(e){var s;let{className:t,...n}=e,{error:r,formMessageId:l}=eF(),i=r?String(null!=(s=null==r?void 0:r.message)?s:""):n.children;return i?(0,a.jsx)("p",{"data-slot":"form-message",id:l,className:(0,S.cn)("text-destructive text-sm",t),...n,children:i}):null}var eQ=t(68309);let eM=eQ.Ik({name:eQ.Yj().min(2,"Name must be at least 2 characters"),phone:eQ.Yj().min(10,"Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/,"Invalid phone number format"),relationship:eQ.Yj().min(1,"Relationship is required"),address:eQ.Yj().optional()});eQ.Ik({street:eQ.Yj().min(5,"Street address must be at least 5 characters"),barangay:eQ.Yj().min(2,"Barangay is required"),city:eQ.Yj().min(2,"City is required"),province:eQ.Yj().min(2,"Province is required"),zipCode:eQ.Yj().min(4,"ZIP code must be at least 4 characters").max(10,"ZIP code must be at most 10 characters"),country:eQ.Yj().default("Philippines")}),eQ.Ik({name:eQ.Yj().min(2,"Guardian name must be at least 2 characters"),phone:eQ.Yj().min(10,"Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/,"Invalid phone number format"),email:eQ.Yj().email("Invalid email format").optional().or(eQ.eu("")),relationship:eQ.k5(v,{errorMap:()=>({message:"Please select a valid relationship"})}),address:eQ.Yj().optional()});let eY=eQ.Ik({id:eQ.Yj().min(12,"DepEd ID must be 12 digits").max(12,"DepEd ID must be 12 digits").regex(/^\d{12}$/,"DepEd ID must contain only numbers"),firstName:eQ.Yj().min(2,"First name must be at least 2 characters").max(50,"First name must be at most 50 characters"),middleName:eQ.Yj().max(50,"Middle name must be at most 50 characters").optional(),lastName:eQ.Yj().min(2,"Last name must be at least 2 characters").max(50,"Last name must be at most 50 characters"),email:eQ.Yj().email("Invalid email format"),dateOfBirth:eQ.Yj().optional(),gender:eQ.k5(N).optional(),course:eQ.Yj().min(2,"Course is required"),year:eQ.Yj().min(1,"Year level is required"),section:eQ.Yj().optional(),grade:eQ.k5(f,{errorMap:()=>({message:"Please select a valid grade level"})}),guardianName:eQ.Yj().min(2,"Guardian name must be at least 2 characters"),guardianPhone:eQ.Yj().min(10,"Phone number must be at least 10 digits").regex(/^[\+]?[0-9\s\-\(\)]+$/,"Invalid phone number format"),guardianEmail:eQ.Yj().email("Invalid email format").optional().or(eQ.eu("")),guardianRelationship:eQ.k5(v,{errorMap:()=>({message:"Please select a valid relationship"})}),guardianAddress:eQ.Yj().optional(),emergencyContacts:eQ.YO(eM).min(1,"At least one emergency contact is required").max(3,"Maximum 3 emergency contacts allowed"),street:eQ.Yj().min(5,"Street address must be at least 5 characters"),barangay:eQ.Yj().min(2,"Barangay is required"),city:eQ.Yj().min(2,"City is required"),province:eQ.Yj().min(2,"Province is required"),zipCode:eQ.Yj().min(4,"ZIP code must be at least 4 characters").max(10,"ZIP code must be at most 10 characters"),country:eQ.Yj().default("Philippines"),photo:eQ.bz().optional()});eY.partial().extend({id:eQ.Yj().min(12,"DepEd ID must be 12 digits").max(12,"DepEd ID must be 12 digits").regex(/^\d{12}$/,"DepEd ID must contain only numbers")}),eQ.Ik({id:eQ.Yj().min(12,"DepEd ID must be 12 digits").max(12,"DepEd ID must be 12 digits").regex(/^\d{12}$/,"DepEd ID must contain only numbers"),firstName:eQ.Yj().min(2,"First name is required"),lastName:eQ.Yj().min(2,"Last name is required"),email:eQ.Yj().email("Invalid email format"),grade:eQ.k5(f),course:eQ.Yj().min(2,"Course is required"),year:eQ.Yj().min(1,"Year level is required"),guardianName:eQ.Yj().min(2,"Guardian name is required"),guardianPhone:eQ.Yj().min(10,"Guardian phone is required").regex(/^[\+]?[0-9\s\-\(\)]+$/,"Invalid phone number format"),guardianRelationship:eQ.k5(v)}),eQ.YO(eY);var eZ=t(84355),eO=t(27213),eq=t(51154);class e${static getInstance(){return e$.instance||(e$.instance=new e$),e$.instance}validatePhoto(e){var s,t;let a={valid:!0,warnings:[]};return["image/jpeg","image/jpg","image/png","image/webp"].includes(e.type)?e.size>0xa00000?{valid:!1,error:"File size too large. Please upload an image smaller than 10MB."}:(e.size>5242880&&(null==(s=a.warnings)||s.push("Large file size detected. Consider compressing the image.")),e.name.length>100&&(null==(t=a.warnings)||t.push("File name is very long. It will be shortened during upload.")),a):{valid:!1,error:"Invalid file type. Please upload a JPEG, PNG, or WebP image."}}async getImageDimensions(e){return new Promise((s,t)=>{let a=new Image,n=URL.createObjectURL(e);a.onload=()=>{URL.revokeObjectURL(n),s({width:a.width,height:a.height})},a.onerror=()=>{URL.revokeObjectURL(n),t(Error("Failed to load image"))},a.src=n})}async compressImage(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t={...this.defaultOptions,...s};return new Promise(async(s,a)=>{try{let n=document.createElement("canvas"),r=n.getContext("2d");if(!r)return void a(Error("Canvas context not available"));let l=new Image,i=URL.createObjectURL(e);l.onload=()=>{URL.revokeObjectURL(i);let{width:d,height:c}=l,o=d/c;d>t.maxWidth&&(c=(d=t.maxWidth)/o),c>t.maxHeight&&(d=(c=t.maxHeight)*o),n.width=d,n.height=c,r.drawImage(l,0,0,d,c),n.toBlob(n=>{if(!n)return void a(Error("Failed to compress image"));let r=new File([n],this.generateFileName(e.name,t.format),{type:"image/".concat(t.format)}),l={originalName:e.name,size:r.size,type:r.type,dimensions:{width:d,height:c},lastModified:Date.now()};s({file:r,metadata:l})},"image/".concat(t.format),t.quality)},l.onerror=()=>{URL.revokeObjectURL(i),a(Error("Failed to load image for compression"))},l.src=i}catch(e){a(e)}})}generateFileName(e,s){let t=Date.now(),a=Math.random().toString(36).substring(2,8),n=e.split(".")[0].substring(0,20);return"".concat(n,"_").concat(t,"_").concat(a,".").concat(s)}createPreviewUrl(e){return URL.createObjectURL(e)}revokePreviewUrl(e){URL.revokeObjectURL(e)}async processPhotoForUpload(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=this.validatePhoto(e);if(!t.valid)throw Error(t.error);let a=await this.getImageDimensions(e),n=e,r={originalName:e.name,size:e.size,type:e.type,dimensions:a,lastModified:e.lastModified};if(e.size>1048576||a.width>1200||a.height>1200||Object.keys(s).length>0){let t=await this.compressImage(e,s);n=t.file,r=t.metadata}let l=this.createPreviewUrl(n);return{processedFile:n,metadata:r,previewUrl:l,validationResult:t}}async uploadPhoto(e,s){await new Promise(e=>setTimeout(e,1e3));let t=Date.now();return"https://storage.qrsams.edu.ph/students/".concat(s,"/photo_").concat(t,".jpg")}async deletePhoto(e){await new Promise(e=>setTimeout(e,500)),console.log("Photo deleted:",e)}getPhotoInfo(e){try{let s=e.split("/"),t=s[s.length-1],a=s[s.length-2],n=t.match(/_(\d+)\./),r=n?parseInt(n[1]):void 0;return{studentId:a,timestamp:r,filename:t}}catch(e){return{}}}async batchProcessPhotos(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=[];for(let a of e)try{let e=await this.processPhotoForUpload(a,s);t.push({originalFile:a,...e})}catch(e){t.push({originalFile:a,processedFile:a,metadata:{originalName:a.name,size:a.size,type:a.type,dimensions:{width:0,height:0},lastModified:a.lastModified},previewUrl:"",validationResult:{valid:!1,error:e instanceof Error?e.message:"Unknown error"},error:e instanceof Error?e.message:"Unknown error"})}return t}constructor(){this.defaultOptions={maxWidth:800,maxHeight:800,quality:.8,format:"jpeg"}}}let e_=e$.getInstance();function eV(e){let{currentPhoto:s,onPhotoChange:t,onPhotoUploaded:d,studentId:c,studentName:m,disabled:x=!1,className:u}=e,[h,j]=(0,n.useState)(null),[p,g]=(0,n.useState)(s||null),[f,v]=(0,n.useState)(!1),[N,b]=(0,n.useState)(0),[w,C]=(0,n.useState)(null),[A,k]=(0,n.useState)(null),[R,D]=(0,n.useState)(!1),z=(0,n.useRef)(null),P=(0,n.useCallback)(async e=>{try{var s;let a=await e_.processPhotoForUpload(e,{maxWidth:800,maxHeight:800,quality:.8,format:"jpeg"});j(a.processedFile),g(a.previewUrl),C(a.validationResult),k(a.metadata),t&&t(a.processedFile),(null==(s=a.validationResult.warnings)?void 0:s.length)&&a.validationResult.warnings.forEach(e=>{ep.o.warning(e)})}catch(s){let e=s instanceof Error?s.message:"Failed to process photo";ep.o.error(e),C({valid:!1,error:e})}},[t]),F=async()=>{if(h&&c){v(!0),b(0);try{let e=setInterval(()=>{b(s=>s>=90?(clearInterval(e),90):s+10)},200),s=await e_.uploadPhoto(h,c);clearInterval(e),b(100),d&&d(s),ep.o.success("Photo uploaded successfully!"),setTimeout(()=>{b(0),v(!1)},1e3)}catch(e){v(!1),b(0),ep.o.error("Failed to upload photo")}}};return(0,a.jsxs)(r.Zp,{className:(0,S.cn)("w-full",u),children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eZ.A,{className:"h-5 w-5"}),"Student Photo"]}),(0,a.jsx)(r.BT,{children:"Upload a clear photo of the student for identification purposes"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsxs)(E.eu,{className:"h-32 w-32 border-4 border-border",children:[(0,a.jsx)(E.BK,{src:p||void 0,alt:m||"Student photo"}),(0,a.jsx)(E.q5,{className:"text-2xl",children:m?m.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2):(0,a.jsx)(ea.A,{className:"h-12 w-12"})})]}),w&&(0,a.jsx)("div",{className:"text-center",children:w.valid?(0,a.jsxs)(i.E,{variant:"default",className:"gap-1",children:[(0,a.jsx)(X.A,{className:"h-3 w-3"}),"Valid Photo"]}):(0,a.jsxs)(i.E,{variant:"destructive",className:"gap-1",children:[(0,a.jsx)(eo.A,{className:"h-3 w-3"}),"Invalid Photo"]})})]}),(0,a.jsxs)("div",{className:(0,S.cn)("border-2 border-dashed rounded-lg p-6 text-center transition-colors",R?"border-primary bg-primary/5":"border-muted-foreground/25",x&&"opacity-50 pointer-events-none"),onDragOver:e=>{e.preventDefault(),D(!0)},onDragLeave:e=>{e.preventDefault(),D(!1)},onDrop:e=>{e.preventDefault(),D(!1);let s=e.dataTransfer.files[0];s&&s.type.startsWith("image/")?P(s):ep.o.error("Please drop a valid image file")},children:[(0,a.jsx)(eO.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-lg font-medium",children:h?"Photo Selected":"Drop your photo here"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"or click to browse files"})]}),(0,a.jsx)("input",{ref:z,type:"file",accept:"image/*",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];t&&P(t)},className:"hidden",disabled:x}),(0,a.jsxs)(l.$,{onClick:()=>{var e;return null==(e=z.current)?void 0:e.click()},className:"mt-4",disabled:x,children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Select Photo"]})]}),A&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Photo Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"File Size:"}),(0,a.jsxs)("span",{children:[(A.size/1024).toFixed(1)," KB"]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Dimensions:"}),(0,a.jsxs)("span",{children:[A.dimensions.width," \xd7 ",A.dimensions.height]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Format:"}),(0,a.jsx)("span",{children:A.type.split("/")[1].toUpperCase()})]})]}),f&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:"Uploading photo..."}),(0,a.jsxs)("span",{children:[N,"%"]})]}),(0,a.jsx)(ef.k,{value:N,className:"w-full"})]}),w&&!w.valid&&(0,a.jsxs)(ei.Fc,{variant:"destructive",children:[(0,a.jsx)(eo.A,{className:"h-4 w-4"}),(0,a.jsx)(ei.TN,{children:w.error})]}),(0,a.jsxs)("div",{className:"flex justify-center gap-2",children:[h&&!f&&(0,a.jsxs)(a.Fragment,{children:[c&&(0,a.jsxs)(l.$,{onClick:F,disabled:!(null==w?void 0:w.valid),children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Upload Photo"]}),(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>{p&&p!==s&&e_.revokePreviewUrl(p),j(null),g(s||null),C(null),k(null),t&&t(null),z.current&&(z.current.value="")},children:[(0,a.jsx)(I.A,{className:"mr-2 h-4 w-4"}),"Remove"]})]}),p&&!h&&(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>{g(null),t&&t(null)},children:[(0,a.jsx)(I.A,{className:"mr-2 h-4 w-4"}),"Remove Current Photo"]}),f&&(0,a.jsxs)(l.$,{disabled:!0,children:[(0,a.jsx)(eq.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uploading..."]})]}),(0,a.jsxs)(ei.Fc,{children:[(0,a.jsx)(eo.A,{className:"h-4 w-4"}),(0,a.jsxs)(ei.TN,{children:[(0,a.jsx)("strong",{children:"Photo Guidelines:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"Use a clear, recent photo of the student"}),(0,a.jsx)("li",{children:"Face should be clearly visible and well-lit"}),(0,a.jsx)("li",{children:"Avoid sunglasses, hats, or face coverings"}),(0,a.jsx)("li",{children:"Recommended size: 300\xd7300 pixels or larger"}),(0,a.jsx)("li",{children:"Supported formats: JPEG, PNG, WebP (max 10MB)"})]})]})]})]})]})}function eW(e){let{onSubmit:s,onCancel:t,initialData:i,isLoading:o=!1,mode:m="create"}=e,[x,h]=(0,n.useState)(null),[j,p]=(0,n.useState)(1),g=(0,eS.mN)({resolver:(0,eA.u)(eY),defaultValues:{country:"Philippines",province:"Batangas",city:"Tanauan City",emergencyContacts:[{name:"",phone:"",relationship:"",address:""}],...i}}),{fields:y,append:w,remove:C}=(0,eS.jz)({control:g.control,name:"emergencyContacts"}),A=async e=>{try{await s(e)}catch(e){console.error("Form submission error:",e)}},k=[{number:1,title:"Basic Information",icon:ea.A},{number:2,title:"Academic Details",icon:et.A},{number:3,title:"Guardian & Contacts",icon:u.A},{number:4,title:"Address & Photo",icon:el.A}];return(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,a.jsx)(r.Zp,{children:(0,a.jsx)(r.Wu,{className:"pt-6",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:k.map((e,s)=>{let t=e.icon,n=j===e.number,r=j>e.number;return(0,a.jsxs)("div",{className:"flex items-center flex-1",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center flex-1",children:[(0,a.jsx)("div",{className:(0,S.cn)("flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 transition-colors",n?"border-primary bg-primary text-primary-foreground":r?"border-green-500 bg-green-500 text-white":"border-muted-foreground bg-background"),children:(0,a.jsx)(t,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),(0,a.jsx)("div",{className:"mt-2 text-center",children:(0,a.jsxs)("p",{className:(0,S.cn)("text-xs sm:text-sm font-medium",n?"text-primary":r?"text-green-600":"text-muted-foreground"),children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:e.title}),(0,a.jsx)("span",{className:"sm:hidden",children:e.number})]})})]}),s<k.length-1&&(0,a.jsx)("div",{className:(0,S.cn)("w-4 sm:w-12 h-0.5 mx-2 sm:mx-4 transition-colors",r?"bg-green-500":"bg-muted")})]},e.number)})})})}),(0,a.jsx)(ez,{...g,children:(0,a.jsxs)("form",{onSubmit:g.handleSubmit(A),className:"space-y-6",children:[1===j&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Basic Information"]}),(0,a.jsx)(r.BT,{children:"Enter the student's personal information"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"id",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"DepEd ID *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"123456789012",...s,maxLength:12})}),(0,a.jsx)(eG,{children:"12-digit DepEd ID number"}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"email",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Email Address *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{type:"email",placeholder:"<EMAIL>",...s})}),(0,a.jsx)(eL,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"firstName",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"First Name *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Juan",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"middleName",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Middle Name"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Santos",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"lastName",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Last Name *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Dela Cruz",...s})}),(0,a.jsx)(eL,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"dateOfBirth",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Date of Birth"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{type:"date",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"gender",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Gender"}),(0,a.jsxs)(ek.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,a.jsx)(eU,{children:(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{placeholder:"Select gender"})})}),(0,a.jsx)(ek.gC,{children:N.map(e=>(0,a.jsx)(ek.eb,{value:e,children:e},e))})]}),(0,a.jsx)(eL,{})]})}})]})]})]}),2===j&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-5 w-5"}),"Academic Information"]}),(0,a.jsx)(r.BT,{children:"Enter the student's academic details"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"grade",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Grade Level *"}),(0,a.jsxs)(ek.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,a.jsx)(eU,{children:(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{placeholder:"Select grade level"})})}),(0,a.jsx)(ek.gC,{children:f.map(e=>(0,a.jsxs)(ek.eb,{value:e,children:["Grade ",e]},e))})]}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"section",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Section"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"A, B, C, etc.",...s})}),(0,a.jsx)(eL,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"course",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Course/Track *"}),(0,a.jsxs)(ek.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,a.jsx)(eU,{children:(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{placeholder:"Select course"})})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:"Information Technology",children:"Information Technology"}),(0,a.jsx)(ek.eb,{value:"Computer Science",children:"Computer Science"}),(0,a.jsx)(ek.eb,{value:"STEM",children:"STEM"}),(0,a.jsx)(ek.eb,{value:"ABM",children:"ABM"}),(0,a.jsx)(ek.eb,{value:"HUMSS",children:"HUMSS"}),(0,a.jsx)(ek.eb,{value:"GAS",children:"GAS"})]})]}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"year",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Year Level *"}),(0,a.jsxs)(ek.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,a.jsx)(eU,{children:(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{placeholder:"Select year level"})})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:"1st Year",children:"1st Year"}),(0,a.jsx)(ek.eb,{value:"2nd Year",children:"2nd Year"}),(0,a.jsx)(ek.eb,{value:"3rd Year",children:"3rd Year"}),(0,a.jsx)(ek.eb,{value:"4th Year",children:"4th Year"})]})]}),(0,a.jsx)(eL,{})]})}})]})]})]}),3===j&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Guardian & Emergency Contacts"]}),(0,a.jsx)(r.BT,{children:"Enter guardian and emergency contact information"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Guardian Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"guardianName",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Guardian Name *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Full name",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"guardianRelationship",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Relationship *"}),(0,a.jsxs)(ek.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,a.jsx)(eU,{children:(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{placeholder:"Select relationship"})})}),(0,a.jsx)(ek.gC,{children:v.map(e=>(0,a.jsx)(ek.eb,{value:e,children:e},e))})]}),(0,a.jsx)(eL,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"guardianPhone",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Phone Number *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"+63 ************",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"guardianEmail",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Email Address"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{type:"email",placeholder:"<EMAIL>",...s})}),(0,a.jsx)(eL,{})]})}})]}),(0,a.jsx)(eP,{control:g.control,name:"guardianAddress",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Guardian Address"}),(0,a.jsx)(eU,{children:(0,a.jsx)(eR.T,{placeholder:"Complete address",...s})}),(0,a.jsx)(eL,{})]})}})]}),(0,a.jsx)(d.w,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Emergency Contacts"}),(0,a.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>w({name:"",phone:"",relationship:"",address:""}),disabled:y.length>=3,children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Add Contact"]})]}),y.map((e,s)=>(0,a.jsxs)(r.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h4",{className:"font-medium",children:["Emergency Contact ",s+1]}),y.length>1&&(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>C(s),children:(0,a.jsx)(I.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"emergencyContacts.".concat(s,".name"),render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Name *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Full name",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"emergencyContacts.".concat(s,".phone"),render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Phone Number *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"+63 ************",...s})}),(0,a.jsx)(eL,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[(0,a.jsx)(eP,{control:g.control,name:"emergencyContacts.".concat(s,".relationship"),render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Relationship *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"e.g., Uncle, Aunt, Friend",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"emergencyContacts.".concat(s,".address"),render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Address"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Complete address",...s})}),(0,a.jsx)(eL,{})]})}})]})]},e.id))]})]})]}),4===j&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(el.A,{className:"h-5 w-5"}),"Address & Photo"]}),(0,a.jsx)(r.BT,{children:"Enter address information and upload student photo"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Address Information"}),(0,a.jsx)(eP,{control:g.control,name:"street",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Street Address *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"House/Lot No., Street Name",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"barangay",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Barangay *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Barangay name",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"city",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"City/Municipality *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"City or Municipality",...s})}),(0,a.jsx)(eL,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(eP,{control:g.control,name:"province",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Province *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Province",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"zipCode",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"ZIP Code *"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"4232",...s})}),(0,a.jsx)(eL,{})]})}}),(0,a.jsx)(eP,{control:g.control,name:"country",render:e=>{let{field:s}=e;return(0,a.jsxs)(eB,{children:[(0,a.jsx)(eE,{children:"Country"}),(0,a.jsx)(eU,{children:(0,a.jsx)(b.p,{placeholder:"Philippines",...s})}),(0,a.jsx)(eL,{})]})}})]})]}),(0,a.jsx)(d.w,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Student Photo"}),(0,a.jsx)(eV,{currentPhoto:x||void 0,onPhotoChange:e=>{if(e instanceof File){let s=new FileReader;s.onloadend=()=>{h(s.result)},s.readAsDataURL(e),g.setValue("photo",e)}else null===e?(h(null),g.setValue("photo",void 0)):(h(e),g.setValue("photo",e))},studentName:"".concat(g.watch("firstName")," ").concat(g.watch("lastName")),disabled:o})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>{j>1&&p(j-1)},disabled:1===j,children:"Previous"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[t&&(0,a.jsx)(l.$,{type:"button",variant:"ghost",onClick:t,children:"Cancel"}),j<4?(0,a.jsx)(l.$,{type:"button",onClick:()=>{j<4&&p(j+1)},children:"Next"}):(0,a.jsx)(l.$,{type:"submit",disabled:o,children:o?"Saving...":"edit"===m?"Update Student":"Register Student"})]})]})]})})]})}function eJ(e){let{trigger:s,onStudentCreated:t,initialData:r,mode:i="create",open:d,onOpenChange:o}=e,[m,x]=(0,n.useState)(!1),[u,h]=(0,n.useState)(!1),j=e=>{o?o(e):x(e)},p=async e=>{h(!0);try{let s={id:e.id,firstName:e.firstName,middleName:e.middleName,lastName:e.lastName,email:e.email,course:e.course,year:e.year,section:e.section,grade:e.grade,status:"Active",photo:"string"==typeof e.photo?e.photo:void 0,qrCode:"QR_".concat(e.id,"_2025"),dateOfBirth:e.dateOfBirth,gender:e.gender,guardian:{name:e.guardianName,phone:e.guardianPhone,email:e.guardianEmail,relationship:e.guardianRelationship,address:e.guardianAddress},emergencyContacts:e.emergencyContacts,address:{street:e.street,barangay:e.barangay,city:e.city,province:e.province,zipCode:e.zipCode,country:e.country||"Philippines"},enrollmentDate:new Date().toISOString().split("T")[0],lastUpdated:new Date().toISOString()};await new Promise(e=>setTimeout(e,1e3)),ep.o.success("edit"===i?"Student information updated successfully!":"Student registered successfully!"),t&&t(s),j(!1)}catch(e){console.error("Error saving student:",e),ep.o.error("Failed to save student information. Please try again.")}finally{h(!1)}};return(0,a.jsxs)(H.lG,{open:void 0!==d?d:m,onOpenChange:j,children:[s&&(0,a.jsx)(H.zM,{asChild:!0,children:s}),!s&&(0,a.jsx)(H.zM,{asChild:!0,children:(0,a.jsxs)(l.$,{children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Add Student"]})}),(0,a.jsxs)(H.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(H.c7,{children:[(0,a.jsx)(H.L3,{children:"edit"===i?"Edit Student Information":"Register New Student"}),(0,a.jsx)(H.rr,{children:"edit"===i?"Update the student's information below.":"Fill in the student's information to register them in the system."})]}),(0,a.jsx)(eW,{onSubmit:p,onCancel:()=>{j(!1)},initialData:r,isLoading:u,mode:i})]})]})}function eH(e){let{student:s,trigger:t,open:r,onOpenChange:i,onStudentUpdated:d}=e,[c,o]=(0,n.useState)(!1),[m,x]=(0,n.useState)(!1),u=g(s);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(H.lG,{open:void 0!==r?r:c,onOpenChange:e=>{i?i(e):o(e)},children:[t&&(0,a.jsx)(H.zM,{asChild:!0,children:t}),!t&&(0,a.jsx)(H.zM,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"View"]})}),(0,a.jsxs)(H.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(H.c7,{children:[(0,a.jsxs)(H.L3,{children:["Student Profile - ",u]}),(0,a.jsx)(H.rr,{children:"Complete student information and attendance details"})]}),(0,a.jsx)(eC,{student:s,onEdit:()=>{x(!0)},onGenerateQR:()=>{ep.o.success("QR code generated successfully!"),console.log("Generating QR code for student:",s.id)},onPrintQR:()=>{ep.o.success("QR code sent to printer!"),console.log("Printing QR code for student:",s.id)}})]})]}),(0,a.jsx)(eJ,{mode:"edit",open:m,onOpenChange:x,initialData:{id:s.id,firstName:s.firstName,middleName:s.middleName,lastName:s.lastName,email:s.email,dateOfBirth:s.dateOfBirth,gender:s.gender,course:s.course,year:s.year,section:s.section,grade:s.grade,guardianName:s.guardian.name,guardianPhone:s.guardian.phone,guardianEmail:s.guardian.email,guardianRelationship:s.guardian.relationship,guardianAddress:s.guardian.address,emergencyContacts:s.emergencyContacts,street:s.address.street,barangay:s.address.barangay,city:s.address.city,province:s.address.province,zipCode:s.address.zipCode,country:s.address.country,photo:s.photo},onStudentCreated:e=>{d&&d(e),x(!1),ep.o.success("Student information updated successfully!")}})]})}var eK=t(75494),eX=t(57434);function e0(e){let{student:s,onStatusChanged:t,trigger:d,open:c,onOpenChange:o}=e,[m,x]=(0,n.useState)(!1),[u,p]=(0,n.useState)(s.status),[f,v]=(0,n.useState)(""),[N,b]=(0,n.useState)(!1),w=e=>{o?o(e):x(e),e||(p(s.status),v(""),b(!1))},C=[{from:"Active",to:"Inactive",allowed:!0,requiresReason:!0,warning:"Student will be marked as temporarily inactive"},{from:"Active",to:"Transferred",allowed:!0,requiresReason:!0,warning:"Student will be removed from active enrollment"},{from:"Active",to:"Graduated",allowed:!0,requiresReason:!1,warning:"Student will be marked as graduated"},{from:"Inactive",to:"Active",allowed:!0,requiresReason:!0,warning:"Student will be reactivated"},{from:"Inactive",to:"Transferred",allowed:!0,requiresReason:!0,warning:"Student will be marked as transferred"},{from:"Transferred",to:"Active",allowed:!0,requiresReason:!0,warning:"Student will be re-enrolled"},{from:"Graduated",to:"Active",allowed:!1,requiresReason:!1,warning:"Cannot reactivate graduated students"}],S=()=>C.find(e=>e.from===s.status&&e.to===u),A=e=>{switch(e){case"Active":return(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"});case"Inactive":return(0,a.jsx)(j.A,{className:"h-4 w-4 text-yellow-600"});case"Transferred":return(0,a.jsx)(eK.A,{className:"h-4 w-4 text-blue-600"});case"Graduated":return(0,a.jsx)(et.A,{className:"h-4 w-4 text-purple-600"});default:return(0,a.jsx)(ev.A,{className:"h-4 w-4"})}},k=async()=>{let e=S();if(!(null==e?void 0:e.allowed))return void ep.o.error("Invalid status transition");if(e.requiresReason&&!f.trim())return void ep.o.error("Please provide a reason for this status change");b(!0);try{await new Promise(e=>setTimeout(e,1e3));let e={...s,status:u,lastUpdated:new Date().toISOString()};t&&t(e,u,f.trim()||void 0),ep.o.success("Student status updated to ".concat(u)),w(!1)}catch(e){ep.o.error("Failed to update student status")}finally{b(!1)}},R=g(s),D=S(),z=C.filter(e=>e.from===s.status&&e.allowed);return(0,a.jsxs)(H.lG,{open:void 0!==c?c:m,onOpenChange:w,children:[d&&(0,a.jsx)(H.zM,{asChild:!0,children:d}),!d&&(0,a.jsx)(H.zM,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[A(s.status),(0,a.jsx)("span",{className:"ml-2",children:"Manage Status"})]})}),(0,a.jsxs)(H.Cf,{className:"max-w-2xl",children:[(0,a.jsxs)(H.c7,{children:[(0,a.jsxs)(H.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"Manage Student Status"]}),(0,a.jsxs)(H.rr,{children:["Update the enrollment status for ",R]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{className:"text-lg",children:"Current Status"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[A(s.status),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.E,{className:(e=>{switch(e){case"Active":return"bg-green-500 text-white";case"Inactive":return"bg-yellow-500 text-white";case"Transferred":return"bg-blue-500 text-white";case"Graduated":return"bg-purple-500 text-white";default:return"bg-gray-500 text-white"}})(s.status),children:s.status}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Since ",new Date(s.lastUpdated).toLocaleDateString()]})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:(e=>{switch(e){case"Active":return"Student is currently enrolled and attending classes";case"Inactive":return"Student is temporarily not attending (e.g., medical leave, suspension)";case"Transferred":return"Student has transferred to another school or program";case"Graduated":return"Student has completed their studies and graduated";default:return"Unknown status"}})(s.status)})]})]})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{className:"text-lg",children:"Change Status"}),(0,a.jsx)(r.BT,{children:"Select a new status for the student"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y.J,{children:"New Status"}),(0,a.jsxs)(ek.l6,{value:u,onValueChange:e=>p(e),children:[(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:s.status,disabled:!0,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[A(s.status),s.status," (Current)"]})}),z.map(e=>(0,a.jsx)(ek.eb,{value:e.to,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[A(e.to),e.to]})},e.to))]})]})]}),D&&u!==s.status&&(0,a.jsxs)(ei.Fc,{children:[(0,a.jsx)(eo.A,{className:"h-4 w-4"}),(0,a.jsx)(ei.TN,{children:D.warning})]}),(null==D?void 0:D.requiresReason)&&u!==s.status&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(y.J,{children:"Reason for Status Change *"}),(0,a.jsx)(eR.T,{placeholder:"Please provide a reason for this status change...",value:f,onChange:e=>v(e.target.value),rows:3}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This reason will be recorded in the student's history"})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"text-lg flex items-center gap-2",children:[(0,a.jsx)(eX.A,{className:"h-4 w-4"}),"Status History"]})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted rounded-lg",children:[A(s.status),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:s.status}),(0,a.jsx)(i.E,{variant:"outline",className:"text-xs",children:"Current"})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Since ",new Date(s.lastUpdated).toLocaleDateString()]})]})]}),u!==s.status&&(null==D?void 0:D.allowed)&&(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 border-2 border-dashed border-primary/50 rounded-lg",children:[A(u),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:u}),(0,a.jsx)(i.E,{variant:"outline",className:"text-xs",children:"Pending"})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Will be effective ",new Date().toLocaleDateString()]}),f&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Reason: ",f]})]})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>w(!1),children:"Cancel"}),(0,a.jsx)(l.$,{onClick:k,disabled:u===s.status||!(null==D?void 0:D.allowed)||(null==D?void 0:D.requiresReason)&&!f.trim()||N,children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ev.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(X.A,{className:"mr-2 h-4 w-4"}),"Update Status"]})})]})]})]})]})}function e1(e){let{student:s,isSelected:t,onSelectionChange:n,onStudentUpdated:d,onStudentDeleted:c,className:o}=e,x=g(s);return(0,a.jsx)(r.Zp,{className:(0,S.cn)("w-full",t&&"ring-2 ring-primary",o),children:(0,a.jsx)(r.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(A,{checked:t,onCheckedChange:n,className:"mt-1","aria-label":"Select ".concat(x)}),(0,a.jsxs)(E.eu,{className:"h-12 w-12 flex-shrink-0",children:[(0,a.jsx)(E.BK,{src:s.photo,alt:x}),(0,a.jsx)(E.q5,{className:"text-sm",children:x.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-2",children:[(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-base truncate",children:x}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground truncate",children:s.email})]}),(0,a.jsxs)(G,{children:[(0,a.jsx)(L,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 flex-shrink-0",children:(0,a.jsx)(V.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(Q,{align:"end",children:[(0,a.jsx)(eH,{student:s,onStudentUpdated:d,trigger:(0,a.jsxs)(M,{onSelect:e=>e.preventDefault(),children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"View Profile"]})}),(0,a.jsxs)(M,{children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"Edit Student"]}),(0,a.jsxs)(M,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Generate QR Code"]}),(0,a.jsx)(Y,{}),(0,a.jsx)(e0,{student:s,onStatusChanged:e=>null==d?void 0:d(e),trigger:(0,a.jsxs)(M,{onSelect:e=>e.preventDefault(),children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"Manage Status"]})}),(0,a.jsx)(Y,{}),(0,a.jsxs)(M,{className:"text-destructive",onClick:()=>null==c?void 0:c(s.id),children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Delete Student"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(ea.A,{className:"h-3 w-3 text-muted-foreground flex-shrink-0"}),(0,a.jsx)("span",{className:"font-mono text-xs truncate",children:s.id})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(et.A,{className:"h-3 w-3 text-muted-foreground flex-shrink-0"}),(0,a.jsxs)("span",{className:"text-xs truncate",children:["Grade ",s.grade]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(q.A,{className:"h-3 w-3 text-muted-foreground flex-shrink-0"}),(0,a.jsx)("span",{className:"text-xs truncate",children:s.guardian.phone})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)($.A,{className:"h-3 w-3 text-muted-foreground flex-shrink-0"}),(0,a.jsx)("span",{className:"text-xs truncate",children:s.guardian.email||"No email"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(i.E,{variant:"outline",className:"text-xs",children:s.course}),(0,a.jsx)(i.E,{variant:"outline",className:"text-xs",children:s.year}),s.section&&(0,a.jsxs)(i.E,{variant:"secondary",className:"text-xs",children:["Section ",s.section]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(i.E,{variant:"Active"===s.status?"default":"secondary",className:(0,S.cn)("text-xs",(e=>{switch(e){case"Active":return"bg-green-500";case"Inactive":return"bg-yellow-500";case"Transferred":return"bg-blue-500";case"Graduated":return"bg-purple-500";default:return"bg-gray-500"}})(s.status)),children:s.status}),(0,a.jsx)(ew,{student:s})]})]})]})})})}function e2(e){let{students:s,selectedStudents:t,onSelectionChange:n,onStudentUpdated:r,onStudentDeleted:l,className:i}=e;return 0===s.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(ea.A,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No students found"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No students match your current search and filter criteria."})]}):(0,a.jsx)("div",{className:(0,S.cn)("space-y-3",i),children:s.map(e=>(0,a.jsx)(e1,{student:e,isSelected:t.includes(e.id),onSelectionChange:s=>{var a;return a=e.id,void(s?n([...t,a]):n(t.filter(e=>e!==a)))},onStudentUpdated:r,onStudentDeleted:l},e.id))})}function e4(e){let{students:s,selectedStudents:t,onSelectionChange:r,sortConfig:d,onSortChange:c,onStudentUpdated:o,onStudentDeleted:x,className:u}=e,[h,j]=(0,n.useState)(null),[p,f]=(0,n.useState)(null),v=e=>{let{field:s,children:t}=e,n=d.field===s,r=d.direction;return(0,a.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-auto p-0 font-medium hover:bg-transparent",onClick:()=>(e=>{let s=d.field===e&&"asc"===d.direction?"desc":"asc";c({field:e,direction:s})})(s),children:(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[t,n?"asc"===r?(0,a.jsx)(Z.A,{className:"h-4 w-4"}):(0,a.jsx)(O.A,{className:"h-4 w-4"}):(0,a.jsx)("div",{className:"h-4 w-4"})]})})},N=s.length>0&&t.length===s.length,b=t.length>0&&t.length<s.length;return(0,a.jsxs)("div",{className:(0,S.cn)("space-y-4",u),children:[(0,a.jsx)("div",{className:"hidden lg:block",children:(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(B.XI,{children:[(0,a.jsx)(B.A0,{children:(0,a.jsxs)(B.Hj,{children:[(0,a.jsx)(B.nd,{className:"w-12",children:(0,a.jsx)(A,{checked:N,onCheckedChange:e=>{e?r(s.map(e=>e.id)):r([])},"aria-label":"Select all students",className:(0,S.cn)(b&&"data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground opacity-50")})}),(0,a.jsx)(B.nd,{className:"w-16",children:"Photo"}),(0,a.jsx)(B.nd,{children:(0,a.jsx)(v,{field:"name",children:"Name"})}),(0,a.jsx)(B.nd,{children:(0,a.jsx)(v,{field:"id",children:"Student ID"})}),(0,a.jsx)(B.nd,{children:(0,a.jsx)(v,{field:"grade",children:"Grade"})}),(0,a.jsx)(B.nd,{children:"Section"}),(0,a.jsx)(B.nd,{children:(0,a.jsx)(v,{field:"course",children:"Course"})}),(0,a.jsx)(B.nd,{children:"Contact"}),(0,a.jsx)(B.nd,{children:(0,a.jsx)(v,{field:"status",children:"Status"})}),(0,a.jsx)(B.nd,{children:"Attendance"}),(0,a.jsx)(B.nd,{className:"text-right",children:"Actions"})]})}),(0,a.jsx)(B.BF,{children:0===s.length?(0,a.jsx)(B.Hj,{children:(0,a.jsx)(B.nA,{colSpan:11,className:"text-center py-8 text-muted-foreground",children:"No students found matching your criteria"})}):s.map(e=>{let s=g(e),n=t.includes(e.id);return(0,a.jsxs)(B.Hj,{className:(0,S.cn)(n&&"bg-muted/50"),children:[(0,a.jsx)(B.nA,{children:(0,a.jsx)(A,{checked:n,onCheckedChange:s=>{var a;return a=e.id,void(s?r([...t,a]):r(t.filter(e=>e!==a)))},"aria-label":"Select ".concat(s)})}),(0,a.jsx)(B.nA,{children:(0,a.jsxs)(E.eu,{className:"h-10 w-10",children:[(0,a.jsx)(E.BK,{src:e.photo,alt:s}),(0,a.jsx)(E.q5,{className:"text-xs",children:s.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]})}),(0,a.jsx)(B.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:s}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:e.email})]})}),(0,a.jsx)(B.nA,{className:"font-mono text-sm",children:e.id}),(0,a.jsx)(B.nA,{children:(0,a.jsxs)(i.E,{variant:"outline",children:["Grade ",e.grade]})}),(0,a.jsx)(B.nA,{children:e.section?(0,a.jsx)(i.E,{variant:"secondary",children:e.section}):(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:"No section"})}),(0,a.jsx)(B.nA,{children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{children:e.course}),(0,a.jsx)("div",{className:"text-muted-foreground",children:e.year})]})}),(0,a.jsx)(B.nA,{children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,a.jsx)(q.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:e.guardian.phone})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,a.jsx)($.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"truncate max-w-[120px]",children:e.guardian.email||"No email"})]})]})}),(0,a.jsx)(B.nA,{children:(0,a.jsx)(i.E,{variant:"Active"===e.status?"default":"secondary",className:(0,S.cn)("text-xs",(e=>{switch(e){case"Active":return"bg-green-500";case"Inactive":return"bg-yellow-500";case"Transferred":return"bg-blue-500";case"Graduated":return"bg-purple-500";default:return"bg-gray-500"}})(e.status)),children:e.status})}),(0,a.jsx)(B.nA,{children:(0,a.jsx)(ew,{student:e})}),(0,a.jsx)(B.nA,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center justify-end gap-2",children:[(0,a.jsx)(eH,{student:e,onStudentUpdated:o,trigger:(0,a.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(_.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(G,{children:[(0,a.jsx)(L,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(V.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(Q,{align:"end",children:[(0,a.jsxs)(M,{onClick:()=>f(e),children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"Edit Student"]}),(0,a.jsxs)(M,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Generate QR Code"]}),(0,a.jsx)(Y,{}),(0,a.jsx)(e0,{student:e,onStatusChanged:e=>null==o?void 0:o(e),trigger:(0,a.jsxs)(M,{onSelect:e=>e.preventDefault(),children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"Manage Status"]})}),(0,a.jsx)(Y,{}),(0,a.jsxs)(M,{className:"text-destructive",onClick:()=>null==x?void 0:x(e.id),children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Delete Student"]})]})]})]})})]},e.id)})})]})})}),(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)(e2,{students:s,selectedStudents:t,onSelectionChange:r,onStudentUpdated:o,onStudentDeleted:x})}),p&&(0,a.jsx)(eJ,{mode:"edit",open:!!p,onOpenChange:e=>!e&&f(null),initialData:{id:p.id,firstName:p.firstName,middleName:p.middleName,lastName:p.lastName,email:p.email,dateOfBirth:p.dateOfBirth,gender:p.gender,course:p.course,year:p.year,section:p.section,grade:p.grade,guardianName:p.guardian.name,guardianPhone:p.guardian.phone,guardianEmail:p.guardian.email,guardianRelationship:p.guardian.relationship,guardianAddress:p.guardian.address,emergencyContacts:p.emergencyContacts,street:p.address.street,barangay:p.address.barangay,city:p.address.city,province:p.address.province,zipCode:p.address.zipCode,country:p.address.country,photo:p.photo},onStudentCreated:e=>{null==o||o(e),f(null)}})]})}var e5=t(17649);let e3=e5.bL,e9=e5.l9,e6=e5.ZL,e8=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(e5.hJ,{className:(0,S.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...n,ref:s})});e8.displayName=e5.hJ.displayName;let e7=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsxs)(e6,{children:[(0,a.jsx)(e8,{}),(0,a.jsx)(e5.UC,{ref:s,className:(0,S.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...n})]})});e7.displayName=e5.UC.displayName;let se=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,S.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...t})};se.displayName="AlertDialogHeader";let ss=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,S.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};ss.displayName="AlertDialogFooter";let st=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(e5.hE,{ref:s,className:(0,S.cn)("text-lg font-semibold",t),...n})});st.displayName=e5.hE.displayName;let sa=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(e5.VY,{ref:s,className:(0,S.cn)("text-sm text-muted-foreground",t),...n})});sa.displayName=e5.VY.displayName;let sn=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(e5.rc,{ref:s,className:(0,S.cn)((0,l.r)(),t),...n})});sn.displayName=e5.rc.displayName;let sr=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(e5.ZD,{ref:s,className:(0,S.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",t),...n})});function sl(e){let{selectedStudents:s,students:t,onClearSelection:d,onStudentsUpdated:c,className:o}=e,[h,j]=(0,n.useState)(!1),[p,g]=(0,n.useState)(!1),[f,v]=(0,n.useState)(!1),[N,b]=(0,n.useState)(""),y=t.filter(e=>s.includes(e.id)),w=async()=>{j(!0);try{await new Promise(e=>setTimeout(e,1e3));let e=["ID,Name,Email,Grade,Section,Course,Year,Status,Guardian Name,Guardian Phone",...y.map(e=>[e.id,'"'.concat(e.firstName," ").concat(e.middleName||""," ").concat(e.lastName,'".trim()'),e.email,e.grade,e.section||"",'"'.concat(e.course,'"'),'"'.concat(e.year,'"'),e.status,'"'.concat(e.guardian.name,'"'),e.guardian.phone].join(","))].join("\n"),t=new Blob([e],{type:"text/csv"}),a=window.URL.createObjectURL(t),n=document.createElement("a");n.href=a,n.download="students_export_".concat(new Date().toISOString().split("T")[0],".csv"),n.click(),window.URL.revokeObjectURL(a),ep.o.success("Exported ".concat(s.length," students to CSV"))}catch(e){ep.o.error("Failed to export students")}finally{j(!1)}},C=async()=>{g(!0);try{await new Promise(e=>setTimeout(e,2e3)),ep.o.success("Generated QR codes for ".concat(s.length," students")),console.log("Generating QR codes for students:",s)}catch(e){ep.o.error("Failed to generate QR codes")}finally{g(!1)}},S=async()=>{if(N)try{await new Promise(e=>setTimeout(e,1e3));let e=t.map(e=>s.includes(e.id)?{...e,status:N}:e);null==c||c(e),ep.o.success("Updated status for ".concat(s.length," students")),v(!1),b(""),d()}catch(e){ep.o.error("Failed to update student status")}},A=async()=>{try{await new Promise(e=>setTimeout(e,1e3));let e=t.filter(e=>!s.includes(e.id));null==c||c(e),ep.o.success("Deleted ".concat(s.length," students")),d()}catch(e){ep.o.error("Failed to delete students")}},k=async()=>{try{let e=["Student ID,Student Name,Guardian Name,Relationship,Phone,Email,Address",...y.map(e=>[e.id,'"'.concat(e.firstName," ").concat(e.middleName||""," ").concat(e.lastName,'".trim()'),'"'.concat(e.guardian.name,'"'),e.guardian.relationship,e.guardian.phone,e.guardian.email||"",'"'.concat(e.guardian.address||"",'"')].join(","))].join("\n"),t=new Blob([e],{type:"text/csv"}),a=window.URL.createObjectURL(t),n=document.createElement("a");n.href=a,n.download="guardian_contacts_".concat(new Date().toISOString().split("T")[0],".csv"),n.click(),window.URL.revokeObjectURL(a),ep.o.success("Exported guardian contacts for ".concat(s.length," students"))}catch(e){ep.o.error("Failed to export guardian contacts")}};return 0===s.length?null:(0,a.jsxs)(r.Zp,{className:o,children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Bulk Actions",(0,a.jsxs)(i.E,{variant:"secondary",children:[s.length," selected"]})]}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:d,children:(0,a.jsx)(I.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)(r.BT,{children:["Perform actions on ",s.length," selected student",1!==s.length?"s":""]})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:w,disabled:h,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),h?"Exporting...":"Export CSV"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:k,children:[(0,a.jsx)(eX.A,{className:"h-4 w-4 mr-2"}),"Export Contacts"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:C,disabled:p,children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),p?"Generating...":"Generate QR Codes"]}),(0,a.jsxs)(H.lG,{open:f,onOpenChange:v,children:[(0,a.jsx)(H.zM,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(X.A,{className:"h-4 w-4 mr-2"}),"Update Status"]})}),(0,a.jsxs)(H.Cf,{children:[(0,a.jsxs)(H.c7,{children:[(0,a.jsx)(H.L3,{children:"Update Student Status"}),(0,a.jsxs)(H.rr,{children:["Change the status for ",s.length," selected student",1!==s.length?"s":""]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(ek.l6,{value:N,onValueChange:b,children:[(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{placeholder:"Select new status"})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:"Active",children:"Active"}),(0,a.jsx)(ek.eb,{value:"Inactive",children:"Inactive"}),(0,a.jsx)(ek.eb,{value:"Transferred",children:"Transferred"}),(0,a.jsx)(ek.eb,{value:"Graduated",children:"Graduated"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>v(!1),children:"Cancel"}),(0,a.jsx)(l.$,{onClick:S,disabled:!N,children:"Update Status"})]})]})]})]}),(0,a.jsxs)(e3,{children:[(0,a.jsx)(e9,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"text-destructive hover:text-destructive",children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Delete"]})}),(0,a.jsxs)(e7,{children:[(0,a.jsxs)(se,{children:[(0,a.jsxs)(st,{className:"flex items-center gap-2",children:[(0,a.jsx)(eo.A,{className:"h-5 w-5 text-destructive"}),"Delete Students"]}),(0,a.jsxs)(sa,{children:["Are you sure you want to delete ",s.length," student",1!==s.length?"s":"","? This action cannot be undone and will permanently remove all student data including:",(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"Personal information"}),(0,a.jsx)("li",{children:"Academic records"}),(0,a.jsx)("li",{children:"Attendance history"}),(0,a.jsx)("li",{children:"Guardian and emergency contacts"})]})]})]}),(0,a.jsxs)(ss,{children:[(0,a.jsx)(sr,{children:"Cancel"}),(0,a.jsx)(sn,{onClick:A,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete Students"})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Selected Students:"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1 max-h-20 overflow-y-auto",children:[y.slice(0,10).map(e=>(0,a.jsxs)(i.E,{variant:"secondary",className:"text-xs",children:[e.firstName," ",e.lastName]},e.id)),y.length>10&&(0,a.jsxs)(i.E,{variant:"secondary",className:"text-xs",children:["+",y.length-10," more"]})]})]})]})]})}sr.displayName=e5.ZD.displayName;var si=t(52278),sd=t(42355),sc=t(13052),so=t(12767);function sm(e){let{pagination:s,onPaginationChange:t,className:n}=e,{page:r,pageSize:i,total:d}=s,c=Math.ceil(d/i),o=(r-1)*i+1,m=Math.min(r*i,d),x=e=>{e>=1&&e<=c&&t({...s,page:e})};return 0===d?null:(0,a.jsxs)("div",{className:(0,S.cn)("flex flex-col sm:flex-row items-center justify-between gap-4",n),children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsxs)("span",{className:"text-center sm:text-left",children:["Showing ",o," to ",m," of ",d," students"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Show"}),(0,a.jsxs)(ek.l6,{value:i.toString(),onValueChange:e=>{let a=parseInt(e),n=Math.min(r,Math.ceil(d/a));t({...s,pageSize:a,page:Math.max(1,n)})},children:[(0,a.jsx)(ek.bq,{className:"w-16 h-8",children:(0,a.jsx)(ek.yv,{})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:"10",children:"10"}),(0,a.jsx)(ek.eb,{value:"20",children:"20"}),(0,a.jsx)(ek.eb,{value:"50",children:"50"}),(0,a.jsx)(ek.eb,{value:"100",children:"100"})]})]}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"per page"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>x(1),disabled:1===r,className:"hidden sm:flex h-8 w-8 p-0",children:(0,a.jsx)(si.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>x(r-1),disabled:1===r,className:"h-8 w-8 p-0",children:(0,a.jsx)(sd.A,{className:"h-4 w-4"})}),(0,a.jsx)("div",{className:"flex items-center gap-1",children:(()=>{let e=[],s=[];for(let s=Math.max(2,r-2);s<=Math.min(c-1,r+2);s++)e.push(s);return r-2>2?s.push(1,"..."):s.push(1),s.push(...e),r+2<c-1?s.push("...",c):c>1&&s.push(c),s})().map((e,s)=>{if("..."===e)return(0,a.jsx)("span",{className:"px-1 sm:px-2 text-muted-foreground text-sm",children:"..."},"dots-".concat(s));let t=e===r;return(0,a.jsx)(l.$,{variant:t?"default":"outline",size:"sm",onClick:()=>x(e),className:"h-8 w-8 p-0 text-sm",children:e},e)})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>x(r+1),disabled:r===c,className:"h-8 w-8 p-0",children:(0,a.jsx)(sc.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>x(c),disabled:r===c,className:"hidden sm:flex h-8 w-8 p-0",children:(0,a.jsx)(so.A,{className:"h-4 w-4"})})]})]})}var sx=t(64261);function su(e){let{trigger:s,onStudentsImported:t,open:i,onOpenChange:d}=e,[c,m]=(0,n.useState)(!1),[h,j]=(0,n.useState)(0),[p,g]=(0,n.useState)(!1),[f,v]=(0,n.useState)(null),[N,b]=(0,n.useState)(null),[y,w]=(0,n.useState)([]),C=(0,n.useRef)(null),S=[{id:"upload",title:"Upload CSV File",description:"Select and upload your student data CSV file",completed:!1},{id:"preview",title:"Preview Data",description:"Review the data before importing",completed:!1},{id:"import",title:"Import Students",description:"Process and import student records",completed:!1},{id:"complete",title:"Complete",description:"Import completed successfully",completed:!1}],A=e=>{d?d(e):m(e),e||(j(0),b(null),w([]),v(null),g(!1))},k=async e=>{try{let s=(await e.text()).split("\n").filter(e=>e.trim()),t=s[0].split(",").map(e=>e.trim().replace(/"/g,"")),a=s.slice(1,6).map(e=>{let s=e.split(",").map(e=>e.trim().replace(/"/g,"")),a={};return t.forEach((e,t)=>{a[e]=s[t]||""}),a});w(a),j(1)}catch(e){ep.o.error("Failed to parse CSV file")}},R=async()=>{if(N){g(!0),j(2);try{await new Promise(e=>setTimeout(e,2e3));let e={success:!0,imported:y.length,failed:0,errors:[]},s=y.map((e,s)=>({id:e.id||"IMP".concat(Date.now()).concat(s),firstName:e.firstName||e["First Name"]||"Unknown",middleName:e.middleName||e["Middle Name"],lastName:e.lastName||e["Last Name"]||"Student",email:e.email||e.Email||"student".concat(s,"@tanauan.edu.ph"),course:e.course||e.Course||"General",year:e.year||e.Year||"1st Year",section:e.section||e.Section,grade:e.grade||e.Grade||"7",status:"Active",guardian:{name:e.guardianName||e["Guardian Name"]||"Unknown Guardian",phone:e.guardianPhone||e["Guardian Phone"]||"09000000000",email:e.guardianEmail||e["Guardian Email"],relationship:e.guardianRelationship||e["Guardian Relationship"]||"Guardian",address:e.guardianAddress||e["Guardian Address"]},emergencyContacts:[],address:{street:e.street||e.Street||"Unknown Street",barangay:e.barangay||e.Barangay||"Unknown Barangay",city:e.city||e.City||"Tanauan City",province:e.province||e.Province||"Batangas",zipCode:e.zipCode||e["ZIP Code"]||"4232",country:"Philippines"},enrollmentDate:new Date().toISOString().split("T")[0],lastUpdated:new Date().toISOString(),qrCode:"QR_".concat(e.id||"IMP".concat(Date.now()).concat(s),"_2025")}));v(e),j(3),t&&t(s),ep.o.success("Successfully imported ".concat(e.imported," students"))}catch(e){ep.o.error("Failed to import students"),v({success:!1,imported:0,failed:y.length,errors:[{row:1,field:"general",message:"Import failed"}]})}finally{g(!1)}}};return(0,a.jsxs)(H.lG,{open:void 0!==i?i:c,onOpenChange:A,children:[s&&(0,a.jsx)(H.zM,{asChild:!0,children:s}),!s&&(0,a.jsx)(H.zM,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"outline",children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Import CSV"]})}),(0,a.jsxs)(H.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(H.c7,{children:[(0,a.jsxs)(H.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(sx.A,{className:"h-5 w-5"}),"Import Students from CSV"]}),(0,a.jsx)(H.rr,{children:"Upload a CSV file to import multiple student records at once"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:S.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ".concat(s<=h?"border-primary bg-primary text-primary-foreground":"border-muted-foreground bg-background"),children:s<h?(0,a.jsx)(X.A,{className:"h-4 w-4"}):(0,a.jsx)("span",{className:"text-sm",children:s+1})}),s<S.length-1&&(0,a.jsx)("div",{className:"w-12 h-0.5 mx-2 transition-colors ".concat(s<h?"bg-primary":"bg-muted")})]},e.id))}),0===h&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Upload CSV File"}),(0,a.jsx)(r.BT,{children:"Select a CSV file containing student data to import"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center",children:[(0,a.jsx)(eX.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-lg font-medium",children:"Drop your CSV file here"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"or click to browse"})]}),(0,a.jsx)("input",{ref:C,type:"file",accept:".csv",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];t&&"text/csv"===t.type?(b(t),k(t)):ep.o.error("Please select a valid CSV file")},className:"hidden"}),(0,a.jsxs)(l.$,{onClick:()=>{var e;return null==(e=C.current)?void 0:e.click()},className:"mt-4",children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Select CSV File"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Need a template? Download our sample CSV file"}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{let e=new Blob(["id,firstName,middleName,lastName,email,grade,section,course,year,guardianName,guardianPhone,guardianEmail,guardianRelationship,street,barangay,city,province,zipCode\n123456789001,John,Michael,Doe,<EMAIL>,11,A,Information Technology,3rd Year,Robert Doe,09123456789,<EMAIL>,Father,123 Main St,Poblacion,Tanauan City,Batangas,4232\n123456789002,Jane,Marie,Smith,<EMAIL>,10,B,Computer Science,2nd Year,Patricia Smith,09123456790,<EMAIL>,Mother,456 Oak Ave,San Jose,Tanauan City,Batangas,4232"],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="student_import_template.csv",t.click(),window.URL.revokeObjectURL(s)},children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Download Template"]})]}),N&&(0,a.jsxs)(ei.Fc,{children:[(0,a.jsx)(X.A,{className:"h-4 w-4"}),(0,a.jsxs)(ei.TN,{children:["File selected: ",N.name," (",(N.size/1024).toFixed(1)," KB)"]})]})]})]}),1===h&&y.length>0&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Preview Data"}),(0,a.jsx)(r.BT,{children:"Review the first few rows of your data before importing"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse border border-border",children:[(0,a.jsx)("thead",{children:(0,a.jsx)("tr",{className:"bg-muted",children:Object.keys(y[0]).map(e=>(0,a.jsx)("th",{className:"border border-border p-2 text-left text-sm font-medium",children:e},e))})}),(0,a.jsx)("tbody",{children:y.map((e,s)=>(0,a.jsx)("tr",{children:Object.values(e).map((e,s)=>(0,a.jsx)("td",{className:"border border-border p-2 text-sm",children:e||"-"},s))},s))})]})}),(0,a.jsxs)("div",{className:"flex justify-between mt-4",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>j(0),children:"Back"}),(0,a.jsxs)(l.$,{onClick:R,children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Import ",y.length," Students"]})]})]})]}),2===h&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Importing Students"}),(0,a.jsx)(r.BT,{children:"Please wait while we process your student data"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"Processing student records..."}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"This may take a few moments"})]}),(0,a.jsx)(ef.k,{value:p?50:100,className:"w-full"})]})]}),3===h&&f&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[f.success?(0,a.jsx)(X.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(es.A,{className:"h-5 w-5 text-red-600"}),"Import ",f.success?"Completed":"Failed"]}),(0,a.jsx)(r.BT,{children:f.success?"Your student data has been successfully imported":"There were errors during the import process"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:f.imported}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Imported"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:f.failed}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Failed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:f.imported+f.failed}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total"})]})]}),f.errors.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-destructive",children:"Errors:"}),f.errors.map((e,s)=>(0,a.jsxs)(ei.Fc,{variant:"destructive",children:[(0,a.jsx)(eo.A,{className:"h-4 w-4"}),(0,a.jsxs)(ei.TN,{children:["Row ",e.row,', Field "',e.field,'": ',e.message]})]},s))]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(l.$,{onClick:()=>A(!1),children:"Close"})})]})]})]})]})]})}var sh=t(381),sj=t(42118),sp=t(54653);function sg(e){let{students:s,selectedStudents:t=[],trigger:c,open:o,onOpenChange:u}=e,[h,j]=(0,n.useState)(!1),[p,f]=(0,n.useState)(!1),[v,N]=(0,n.useState)(0),[b,w]=(0,n.useState)("select"),[C,S]=(0,n.useState)(t),[k,R]=(0,n.useState)({format:"sheet",size:"medium",includeStudentInfo:!0,includeSchoolLogo:!0,codesPerSheet:12,paperSize:"A4"}),D=e=>{u?u(e):j(e),e||(w("select"),f(!1),N(0))},z=s.filter(e=>C.includes(e.id)),I=async()=>{if(0===z.length)return void ep.o.error("Please select at least one student");f(!0),w("generate"),N(0);try{for(let e=0;e<=100;e+=10)N(e),await new Promise(e=>setTimeout(e,200));w("complete"),ep.o.success("Generated QR codes for ".concat(z.length," students"))}catch(e){ep.o.error("Failed to generate QR codes"),w("options")}finally{f(!1)}};return(0,a.jsxs)(H.lG,{open:void 0!==o?o:h,onOpenChange:D,children:[c&&(0,a.jsx)(H.zM,{asChild:!0,children:c}),!c&&(0,a.jsx)(H.zM,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"outline",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Generate QR Codes"]})}),(0,a.jsxs)(H.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(H.c7,{children:[(0,a.jsxs)(H.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Batch QR Code Generator"]}),(0,a.jsx)(H.rr,{children:"Generate QR codes for multiple students at once"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:["select"===b&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{children:"Select Students"}),(0,a.jsxs)(i.E,{variant:"secondary",children:[C.length," selected"]})]}),(0,a.jsx)(r.BT,{children:"Choose which students to generate QR codes for"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A,{id:"select-all",checked:C.length===s.length,onCheckedChange:e=>{e?S(s.map(e=>e.id)):S([])}}),(0,a.jsxs)(y.J,{htmlFor:"select-all",className:"font-medium",children:["Select All Students (",s.length,")"]})]}),(0,a.jsx)(d.w,{}),(0,a.jsx)("div",{className:"max-h-60 overflow-y-auto space-y-2",children:s.map(e=>{let s=g(e),t=C.includes(e.id);return(0,a.jsxs)("div",{className:"flex items-center space-x-2 p-2 rounded hover:bg-muted",children:[(0,a.jsx)(A,{id:"student-".concat(e.id),checked:t,onCheckedChange:s=>{var t;return t=e.id,void(s?S(e=>[...e,t]):S(e=>e.filter(e=>e!==t)))}}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(y.J,{htmlFor:"student-".concat(e.id),className:"font-medium cursor-pointer",children:s}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e.id," • Grade ",e.grade," • ",e.course]})]}),(0,a.jsx)(i.E,{variant:"Active"===e.status?"default":"secondary",children:e.status})]},e.id)})}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(l.$,{onClick:()=>w("options"),disabled:0===C.length,children:"Next: Configure Options"})})]})]}),"options"===b&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(sh.A,{className:"h-5 w-5"}),"Generation Options"]}),(0,a.jsx)(r.BT,{children:"Configure how the QR codes should be generated"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Output Format"}),(0,a.jsxs)(ek.l6,{value:k.format,onValueChange:e=>R(s=>({...s,format:e})),children:[(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:"individual",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(sj.A,{className:"h-4 w-4"}),"Individual Files"]})}),(0,a.jsx)(ek.eb,{value:"sheet",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(sp.A,{className:"h-4 w-4"}),"Print Sheet"]})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"QR Code Size"}),(0,a.jsxs)(ek.l6,{value:k.size,onValueChange:e=>R(s=>({...s,size:e})),children:[(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:"small",children:"Small (1 inch)"}),(0,a.jsx)(ek.eb,{value:"medium",children:"Medium (1.5 inches)"}),(0,a.jsx)(ek.eb,{value:"large",children:"Large (2 inches)"})]})]})]}),"sheet"===k.format&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Paper Size"}),(0,a.jsxs)(ek.l6,{value:k.paperSize,onValueChange:e=>R(s=>({...s,paperSize:e})),children:[(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:"A4",children:"A4 (210 \xd7 297 mm)"}),(0,a.jsx)(ek.eb,{value:"Letter",children:"Letter (8.5 \xd7 11 in)"})]})]})]}),"sheet"===k.format&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Codes per Sheet"}),(0,a.jsxs)(ek.l6,{value:k.codesPerSheet.toString(),onValueChange:e=>R(s=>({...s,codesPerSheet:parseInt(e)})),children:[(0,a.jsx)(ek.bq,{children:(0,a.jsx)(ek.yv,{})}),(0,a.jsxs)(ek.gC,{children:[(0,a.jsx)(ek.eb,{value:"6",children:"6 codes per sheet"}),(0,a.jsx)(ek.eb,{value:"12",children:"12 codes per sheet"}),(0,a.jsx)(ek.eb,{value:"20",children:"20 codes per sheet"}),(0,a.jsx)(ek.eb,{value:"30",children:"30 codes per sheet"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.J,{className:"text-sm font-medium",children:"Additional Options"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A,{id:"include-info",checked:k.includeStudentInfo,onCheckedChange:e=>R(s=>({...s,includeStudentInfo:e}))}),(0,a.jsx)(y.J,{htmlFor:"include-info",children:"Include student name and ID"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A,{id:"include-logo",checked:k.includeSchoolLogo,onCheckedChange:e=>R(s=>({...s,includeSchoolLogo:e}))}),(0,a.jsx)(y.J,{htmlFor:"include-logo",children:"Include school logo"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>w("select"),children:"Back"}),(0,a.jsxs)(l.$,{onClick:I,children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Generate ",z.length," QR Codes"]})]})]})]}),"generate"===b&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Generating QR Codes"}),(0,a.jsxs)(r.BT,{children:["Please wait while we generate QR codes for ",z.length," students"]})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"Generating QR codes..."}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Processing ",z.length," student records"]})]}),(0,a.jsx)(ef.k,{value:v,className:"w-full"}),(0,a.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[v,"% complete"]})]})]}),"complete"===b&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(X.A,{className:"h-5 w-5 text-green-600"}),"QR Codes Generated Successfully"]}),(0,a.jsxs)(r.BT,{children:[z.length," QR codes have been generated and are ready for download or printing"]})]}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:z.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"QR Codes Generated"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"sheet"===k.format?Math.ceil(z.length/k.codesPerSheet):z.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"sheet"===k.format?"Print Sheets":"Individual Files"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:k.size}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Size"})]})]}),(0,a.jsx)(d.w,{}),(0,a.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,a.jsxs)(l.$,{onClick:()=>{ep.o.success("QR codes downloaded successfully"),console.log("Downloading QR codes for:",z.map(e=>e.id))},children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Download Files"]}),(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>{ep.o.success("QR codes sent to printer"),console.log("Printing QR codes for:",z.map(e=>e.id))},children:[(0,a.jsx)(er.A,{className:"mr-2 h-4 w-4"}),"Print Now"]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(l.$,{variant:"ghost",onClick:()=>D(!1),children:"Close"})})]})]})]})]})]})}let sf=[{id:"123456789001",firstName:"John",middleName:"Michael",lastName:"Doe",email:"<EMAIL>",course:"Information Technology",year:"3rd Year",section:"IT-3A",grade:"11",status:"Active",photo:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",qrCode:"QR_123456789001_2025",dateOfBirth:"2007-05-15",gender:"Male",guardian:{name:"Robert Doe",phone:"+63 ************",email:"<EMAIL>",relationship:"Father",address:"123 Main St, Tanauan City"},emergencyContacts:[{name:"Mary Doe",phone:"+63 ************",relationship:"Mother",address:"123 Main St, Tanauan City"},{name:"James Doe",phone:"+63 ************",relationship:"Uncle"}],address:{street:"123 Main Street",barangay:"Poblacion",city:"Tanauan City",province:"Batangas",zipCode:"4232",country:"Philippines"},enrollmentDate:"2023-08-15",lastUpdated:"2025-01-15",attendanceStats:{totalDays:120,presentDays:110,lateDays:8,absentDays:2,attendanceRate:91.7,lastAttendance:"2025-01-15"}},{id:"123456789002",firstName:"Jane",middleName:"Marie",lastName:"Smith",email:"<EMAIL>",course:"Computer Science",year:"2nd Year",section:"CS-2B",grade:"10",status:"Active",photo:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",qrCode:"QR_123456789002_2025",dateOfBirth:"2008-03-22",gender:"Female",guardian:{name:"Patricia Smith",phone:"+63 ************",email:"<EMAIL>",relationship:"Mother",address:"456 Oak Ave, Tanauan City"},emergencyContacts:[{name:"David Smith",phone:"+63 ************",relationship:"Father",address:"456 Oak Ave, Tanauan City"}],address:{street:"456 Oak Avenue",barangay:"San Jose",city:"Tanauan City",province:"Batangas",zipCode:"4232",country:"Philippines"},enrollmentDate:"2023-08-15",lastUpdated:"2025-01-14",attendanceStats:{totalDays:120,presentDays:115,lateDays:3,absentDays:2,attendanceRate:95.8,lastAttendance:"2025-01-14"}},{id:"123456789003",firstName:"Mike",lastName:"Johnson",email:"<EMAIL>",course:"Information Technology",year:"1st Year",section:"IT-1C",grade:"9",status:"Active",photo:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",qrCode:"QR_123456789003_2025",dateOfBirth:"2009-11-08",gender:"Male",guardian:{name:"Linda Johnson",phone:"+63 ************",email:"<EMAIL>",relationship:"Mother",address:"789 Pine St, Tanauan City"},emergencyContacts:[{name:"Mark Johnson",phone:"+63 ************",relationship:"Father"},{name:"Susan Johnson",phone:"+63 ************",relationship:"Grandmother",address:"321 Elm St, Tanauan City"}],address:{street:"789 Pine Street",barangay:"Natatas",city:"Tanauan City",province:"Batangas",zipCode:"4232",country:"Philippines"},enrollmentDate:"2024-08-15",lastUpdated:"2025-01-13",attendanceStats:{totalDays:120,presentDays:105,lateDays:12,absentDays:3,attendanceRate:87.5,lastAttendance:"2025-01-13"}},{id:"123456789004",firstName:"Sarah",middleName:"Grace",lastName:"Wilson",email:"<EMAIL>",course:"Computer Science",year:"4th Year",section:"CS-4A",grade:"12",status:"Active",photo:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",qrCode:"QR_123456789004_2025",dateOfBirth:"2006-09-12",gender:"Female",guardian:{name:"Thomas Wilson",phone:"+63 ************",email:"<EMAIL>",relationship:"Father",address:"654 Maple Dr, Tanauan City"},emergencyContacts:[{name:"Helen Wilson",phone:"+63 ************",relationship:"Mother",address:"654 Maple Dr, Tanauan City"}],address:{street:"654 Maple Drive",barangay:"Sambat",city:"Tanauan City",province:"Batangas",zipCode:"4232",country:"Philippines"},enrollmentDate:"2021-08-15",lastUpdated:"2025-01-12",attendanceStats:{totalDays:120,presentDays:118,lateDays:1,absentDays:1,attendanceRate:98.3,lastAttendance:"2025-01-12"}},{id:"123456789005",firstName:"Alex",lastName:"Rodriguez",email:"<EMAIL>",course:"Information Technology",year:"2nd Year",section:"IT-2A",grade:"10",status:"Inactive",photo:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",qrCode:"QR_123456789005_2025",dateOfBirth:"2008-07-25",gender:"Male",guardian:{name:"Maria Rodriguez",phone:"+63 ************",email:"<EMAIL>",relationship:"Mother",address:"987 Cedar Ln, Tanauan City"},emergencyContacts:[{name:"Carlos Rodriguez",phone:"+63 ************",relationship:"Father"}],address:{street:"987 Cedar Lane",barangay:"Ulango",city:"Tanauan City",province:"Batangas",zipCode:"4232",country:"Philippines"},enrollmentDate:"2023-08-15",lastUpdated:"2024-12-15",attendanceStats:{totalDays:120,presentDays:85,lateDays:15,absentDays:20,attendanceRate:70.8,lastAttendance:"2024-12-10"}}];function sv(){let[e,s]=(0,n.useState)(sf),[t,f]=(0,n.useState)([]),[v,N]=(0,n.useState)({}),[b,y]=(0,n.useState)({field:"name",direction:"asc"}),[w,C]=(0,n.useState)({page:1,pageSize:20,total:0}),[S,A]=(0,n.useState)(!1),k=(0,n.useMemo)(()=>{var s,t,a,n,r;let l=e;return v.search&&(l=(e=>{if(!e.trim())return sf;let s=e.toLowerCase();return sf.filter(e=>{var t;return g(e).toLowerCase().includes(s)||e.id.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)||e.course.toLowerCase().includes(s)||(null==(t=e.section)?void 0:t.toLowerCase().includes(s))||e.guardian.name.toLowerCase().includes(s)})})(v.search)),(null==(s=v.grade)?void 0:s.length)&&(l=l.filter(e=>v.grade.includes(e.grade))),(null==(t=v.section)?void 0:t.length)&&(l=l.filter(e=>e.section&&v.section.includes(e.section))),(null==(a=v.status)?void 0:a.length)&&(l=l.filter(e=>v.status.includes(e.status))),(null==(n=v.course)?void 0:n.length)&&(l=l.filter(e=>v.course.includes(e.course))),(null==(r=v.year)?void 0:r.length)&&(l=l.filter(e=>v.year.includes(e.year))),[...l].sort((e,s)=>{let t,a;return("name"===b.field?(t=g(e).toLowerCase(),a=g(s).toLowerCase()):(t=e[b.field],a=s[b.field]),t<a)?"asc"===b.direction?-1:1:t>a?"asc"===b.direction?1:-1:0})},[e,v,b]),R=(0,n.useMemo)(()=>{let e=(w.page-1)*w.pageSize,s=e+w.pageSize;return k.slice(e,s)},[k,w.page,w.pageSize]);(0,n.useEffect)(()=>{C(e=>({...e,total:k.length,page:1}))},[k.length]);let D=(0,n.useMemo)(()=>{let s=e.length,t=e.filter(e=>"Active"===e.status).length;return{total:s,active:t,inactive:e.filter(e=>"Inactive"===e.status).length,avgAttendance:e.filter(e=>e.attendanceStats).reduce((e,s)=>{var t;return e+((null==(t=s.attendanceStats)?void 0:t.attendanceRate)||0)},0)/e.filter(e=>e.attendanceStats).length||0}},[e]);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold tracking-tight",children:"Students"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage student records and information"})]}),(0,a.jsx)(eJ,{open:S,onOpenChange:A,onStudentCreated:e=>{s(s=>[...s,e]),ep.o.success("Student registered successfully!")},trigger:(0,a.jsxs)(l.$,{className:"w-full sm:w-auto",children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Add Student"]})})]}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row gap-2",children:(0,a.jsxs)("div",{className:"flex flex-1 gap-2",children:[(0,a.jsx)(su,{onStudentsImported:e=>{s(s=>[...s,...e]),ep.o.success("Imported ".concat(e.length," students"))},trigger:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Import CSV"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Import"})]})}),(0,a.jsx)(sg,{students:e,trigger:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Generate QR"}),(0,a.jsx)("span",{className:"sm:hidden",children:"QR"})]})}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Export All"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Export"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-xs sm:text-sm font-medium",children:"Total Students"}),(0,a.jsx)(u.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-xl sm:text-2xl font-bold",children:D.total}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Registered in system"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Registered"})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-xs sm:text-sm font-medium",children:"Active Students"}),(0,a.jsx)(h.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-green-600",children:D.active}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Currently enrolled"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Enrolled"})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-xs sm:text-sm font-medium",children:"Inactive Students"}),(0,a.jsx)(j.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-xl sm:text-2xl font-bold text-yellow-600",children:D.inactive}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Not currently active"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Inactive"})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-xs sm:text-sm font-medium",children:"Avg. Attendance"}),(0,a.jsx)(p.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)("div",{className:"text-xl sm:text-2xl font-bold",children:[D.avgAttendance.toFixed(1),"%"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Overall attendance rate"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Attendance"})]})]})]})]}),(0,a.jsx)(P,{filters:v,onFiltersChange:N,availableGrades:[...new Set(sf.map(e=>e.grade))].sort(),availableSections:[...new Set(sf.map(e=>e.section).filter(Boolean))].sort(),availableCourses:[...new Set(sf.map(e=>e.course))].sort(),availableYears:[...new Set(sf.map(e=>e.year))].sort()}),t.length>0&&(0,a.jsx)(sl,{selectedStudents:t,students:e,onClearSelection:()=>f([]),onStudentsUpdated:e=>{s(e),f([])}}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{children:"Student Directory"}),(0,a.jsxs)(i.E,{variant:"secondary",children:[k.length," student",1!==k.length?"s":""]})]}),(0,a.jsx)(r.BT,{children:v.search||Object.keys(v).length>1?"Filtered results from ".concat(e.length," total students"):"Complete list of registered students"})]}),(0,a.jsxs)(r.Wu,{className:"p-0",children:[(0,a.jsx)(e4,{students:R,selectedStudents:t,onSelectionChange:f,sortConfig:b,onSortChange:y,onStudentUpdated:e=>{s(s=>s.map(s=>s.id===e.id?e:s)),ep.o.success("Student updated successfully!")},onStudentDeleted:e=>{s(s=>s.filter(s=>s.id!==e)),f(s=>s.filter(s=>s!==e)),ep.o.success("Student deleted successfully!")}}),k.length>w.pageSize&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.w,{}),(0,a.jsx)("div",{className:"p-4",children:(0,a.jsx)(sm,{pagination:w,onPaginationChange:C})})]})]})]})]})}},57403:(e,s,t)=>{Promise.resolve().then(t.bind(t,55909))},69663:(e,s,t)=>{"use strict";t.d(s,{BK:()=>d,eu:()=>i,q5:()=>c});var a=t(95155),n=t(12115),r=t(54011),l=t(53999);let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.bL,{ref:s,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...n})});i.displayName=r.bL.displayName;let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r._V,{ref:s,className:(0,l.cn)("aspect-square h-full w-full",t),...n})});d.displayName=r._V.displayName;let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.H4,{ref:s,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...n})});c.displayName=r.H4.displayName},76037:(e,s,t)=>{"use strict";t.d(s,{w:()=>l});var a=t(95155);t(12115);var n=t(87489),r=t(53999);function l(e){let{className:s,orientation:t="horizontal",decorative:l=!0,...i}=e;return(0,a.jsx)(n.b,{"data-slot":"separator",decorative:l,orientation:t,className:(0,r.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",s),...i})}},82714:(e,s,t)=>{"use strict";t.d(s,{J:()=>l});var a=t(95155);t(12115);var n=t(40968),r=t(53999);function l(e){let{className:s,...t}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},88145:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(95155);t(12115);var n=t(99708),r=t(74466),l=t(53999);let i=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,asChild:r=!1,...d}=e,c=r?n.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(i({variant:t}),s),...d})}},88482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>r,aR:()=>l});var a=t(95155);t(12115);var n=t(53999);function r(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",s),...t})}},88524:(e,s,t)=>{"use strict";t.d(s,{A0:()=>l,BF:()=>i,Hj:()=>d,XI:()=>r,nA:()=>o,nd:()=>c});var a=t(95155);t(12115);var n=t(53999);function r(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",s),...t})})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",s),...t})}function i(e){let{className:s,...t}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...t})}},89852:(e,s,t)=>{"use strict";t.d(s,{p:()=>r});var a=t(95155);t(12115);var n=t(53999);function r(e){let{className:s,type:t,...r}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...r})}},95784:(e,s,t)=>{"use strict";t.d(s,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>c,yv:()=>o});var a=t(95155);t(12115);var n=t(22918),r=t(66474),l=t(5196),i=t(47863),d=t(53999);function c(e){let{...s}=e;return(0,a.jsx)(n.bL,{"data-slot":"select",...s})}function o(e){let{...s}=e;return(0,a.jsx)(n.WT,{"data-slot":"select-value",...s})}function m(e){let{className:s,size:t="default",children:l,...i}=e;return(0,a.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...i,children:[l,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:s,children:t,position:r="popper",...l}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:r,...l,children:[(0,a.jsx)(h,{}),(0,a.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(j,{})]})})}function u(e){let{className:s,children:t,...r}=e;return(0,a.jsxs)(n.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"size-4"})})}),(0,a.jsx)(n.p4,{children:t})]})}function h(e){let{className:s,...t}=e;return(0,a.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}function j(e){let{className:s,...t}=e;return(0,a.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(r.A,{className:"size-4"})})}},97168:(e,s,t)=>{"use strict";t.d(s,{$:()=>d,r:()=>i});var a=t(95155);t(12115);var n=t(99708),r=t(74466),l=t(53999);let i=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:r,asChild:d=!1,...c}=e,o=d?n.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,l.cn)(i({variant:t,size:r,className:s})),...c})}},99474:(e,s,t)=>{"use strict";t.d(s,{T:()=>r});var a=t(95155);t(12115);var n=t(53999);function r(e){let{className:s,...t}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...t})}},99840:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>m,L3:()=>u,c7:()=>x,lG:()=>i,rr:()=>h,zM:()=>d});var a=t(95155);t(12115);var n=t(15452),r=t(54416),l=t(53999);function i(e){let{...s}=e;return(0,a.jsx)(n.bL,{"data-slot":"dialog",...s})}function d(e){let{...s}=e;return(0,a.jsx)(n.l9,{"data-slot":"dialog-trigger",...s})}function c(e){let{...s}=e;return(0,a.jsx)(n.ZL,{"data-slot":"dialog-portal",...s})}function o(e){let{className:s,...t}=e;return(0,a.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...t})}function m(e){let{className:s,children:t,showCloseButton:i=!0,...d}=e;return(0,a.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,a.jsx)(o,{}),(0,a.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...d,children:[t,i&&(0,a.jsxs)(n.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(r.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",s),...t})}function u(e){let{className:s,...t}=e;return(0,a.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",s),...t})}function h(e){let{className:s,...t}=e;return(0,a.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",s),...t})}}},e=>{e.O(0,[803,550,986,679,671,190,441,964,358],()=>e(e.s=57403)),_N_E=e.O()}]);