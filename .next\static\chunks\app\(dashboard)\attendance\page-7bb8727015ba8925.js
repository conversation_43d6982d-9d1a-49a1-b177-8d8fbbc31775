(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[16],{5040:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},33091:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>O});var s=a(95155),n=a(12115),l=a(88482),r=a(97168),d=a(34964),c=a(89852),i=a(82714),o=a(95784),u=a(88145),h=a(69074),x=a(13319);function m(e){var t,a,d;let{onFiltersChange:m,className:p}=e,[g,v]=(0,n.useState)({dateRange:{from:new Date,to:new Date},grade:"all",section:"all",course:"all",status:"all",searchQuery:""}),j=(e,t)=>{let a={...g,[e]:t};v(a),m(a)},b=[{value:"all",label:"All Grades"},{value:"7",label:"Grade 7"},{value:"8",label:"Grade 8"},{value:"9",label:"Grade 9"},{value:"10",label:"Grade 10"},{value:"11",label:"Grade 11"},{value:"12",label:"Grade 12"}],f=[{value:"all",label:"All Courses"},{value:"junior-high",label:"Junior High School"},{value:"ict",label:"Information and Communications Technology"},{value:"abm",label:"Accountancy, Business and Management"},{value:"humss",label:"Humanities and Social Sciences"},{value:"stem",label:"Science, Technology, Engineering and Mathematics"}],y=[{value:"all",label:"All Status"},{value:"present",label:"Present"},{value:"late",label:"Late"},{value:"absent",label:"Absent"}];return(0,s.jsxs)(l.Zp,{className:p,children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{className:"text-lg",children:"Filters"})}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"search",children:"Search Student"}),(0,s.jsx)(c.p,{id:"search",placeholder:"Search by name or ID...",value:g.searchQuery,onChange:e=>j("searchQuery",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Date Range"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(r.$,{variant:"outline",size:"sm",className:"justify-start text-left font-normal",children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),(0,x.GP)(g.dateRange.from,"MMM dd, yyyy")]}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"to"}),(0,s.jsxs)(r.$,{variant:"outline",size:"sm",className:"justify-start text-left font-normal",children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),(0,x.GP)(g.dateRange.to,"MMM dd, yyyy")]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Grade Level"}),(0,s.jsxs)(o.l6,{value:g.grade,onValueChange:e=>j("grade",e),children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{placeholder:"Select grade"})}),(0,s.jsx)(o.gC,{children:b.map(e=>(0,s.jsx)(o.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Course/Track"}),(0,s.jsxs)(o.l6,{value:g.course,onValueChange:e=>j("course",e),children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{placeholder:"Select course"})}),(0,s.jsx)(o.gC,{children:f.map(e=>(0,s.jsx)(o.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Attendance Status"}),(0,s.jsxs)(o.l6,{value:g.status,onValueChange:e=>j("status",e),children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{placeholder:"Select status"})}),(0,s.jsx)(o.gC,{children:y.map(e=>(0,s.jsx)(o.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Active Filters"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:["all"!==g.grade&&(0,s.jsxs)(u.E,{variant:"secondary",children:["Grade: ",null==(t=b.find(e=>e.value===g.grade))?void 0:t.label]}),"all"!==g.course&&(0,s.jsxs)(u.E,{variant:"secondary",children:["Course: ",null==(a=f.find(e=>e.value===g.course))?void 0:a.label]}),"all"!==g.status&&(0,s.jsxs)(u.E,{variant:"secondary",children:["Status: ",null==(d=y.find(e=>e.value===g.status))?void 0:d.label]}),g.searchQuery&&(0,s.jsxs)(u.E,{variant:"secondary",children:["Search: ",g.searchQuery]})]})]}),(0,s.jsx)(r.$,{variant:"outline",onClick:()=>{let e={dateRange:{from:new Date,to:new Date},grade:"all",section:"all",course:"all",status:"all",searchQuery:""};v(e),m(e)},className:"w-full",children:"Clear All Filters"})]})]})}var p=a(88524),g=a(69663),v=a(99840),j=a(99474),b=a(40646),f=a(14186),y=a(54861),N=a(85339),w=a(71007),k=a(13717);function A(e){let{records:t,onUpdateRecord:a,className:d}=e,[h,x]=(0,n.useState)(null),[m,A]=(0,n.useState)({status:"",checkIn:"",checkOut:"",reason:""}),S=()=>{h&&(a(h.id,{status:m.status,checkIn:m.checkIn||void 0,checkOut:m.checkOut||void 0,reason:m.reason||void 0}),x(null))};return(0,s.jsxs)(l.Zp,{className:d,children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:"Attendance Records"}),(0,s.jsxs)(u.E,{variant:"outline",children:[t.length," records"]})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(p.XI,{children:[(0,s.jsx)(p.A0,{children:(0,s.jsxs)(p.Hj,{children:[(0,s.jsx)(p.nd,{children:"Student"}),(0,s.jsx)(p.nd,{children:"Grade/Section"}),(0,s.jsx)(p.nd,{children:"Check In"}),(0,s.jsx)(p.nd,{children:"Check Out"}),(0,s.jsx)(p.nd,{children:"Status"}),(0,s.jsx)(p.nd,{children:"Subject/Period"}),(0,s.jsx)(p.nd,{className:"text-right",children:"Actions"})]})}),(0,s.jsx)(p.BF,{children:t.map(e=>{var t;return(0,s.jsxs)(p.Hj,{children:[(0,s.jsx)(p.nA,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)(g.eu,{className:"h-8 w-8",children:[(0,s.jsx)(g.BK,{src:e.photo,alt:e.studentName}),(0,s.jsx)(g.q5,{children:(0,s.jsx)(w.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.studentName}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:e.studentId})]})]})}),(0,s.jsx)(p.nA,{children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-medium",children:["Grade ",e.grade]}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:e.section})]})}),(0,s.jsx)(p.nA,{children:(0,s.jsx)("div",{className:"flex items-center space-x-2",children:e.checkIn?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{children:e.checkIn})]}):(0,s.jsx)("span",{className:"text-muted-foreground",children:"-"})})}),(0,s.jsx)(p.nA,{children:(0,s.jsx)("div",{className:"flex items-center space-x-2",children:e.checkOut?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{children:e.checkOut})]}):(0,s.jsx)("span",{className:"text-muted-foreground",children:"-"})})}),(0,s.jsx)(p.nA,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(e=>{switch(e){case"Present":return(0,s.jsx)(b.A,{className:"h-4 w-4 text-green-500"});case"Late":return(0,s.jsx)(f.A,{className:"h-4 w-4 text-yellow-500"});case"Absent":return(0,s.jsx)(y.A,{className:"h-4 w-4 text-red-500"});default:return(0,s.jsx)(N.A,{className:"h-4 w-4 text-gray-500"})}})(e.status),(t=e.status,(0,s.jsx)(u.E,{variant:"Present"===t?"default":"Late"===t?"secondary":"destructive",children:t}))]})}),(0,s.jsx)(p.nA,{children:e.subject?(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.subject}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:e.period})]}):(0,s.jsx)(u.E,{variant:"outline",children:"Gate Entry"})}),(0,s.jsx)(p.nA,{className:"text-right",children:(0,s.jsxs)(v.lG,{children:[(0,s.jsx)(v.zM,{asChild:!0,children:(0,s.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>{x(e),A({status:e.status,checkIn:e.checkIn||"",checkOut:e.checkOut||"",reason:e.reason||""})},children:(0,s.jsx)(k.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(v.Cf,{children:[(0,s.jsxs)(v.c7,{children:[(0,s.jsx)(v.L3,{children:"Edit Attendance Record"}),(0,s.jsxs)(v.rr,{children:["Update attendance information for ",e.studentName]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Status"}),(0,s.jsxs)(o.l6,{value:m.status,onValueChange:e=>A({...m,status:e}),children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{})}),(0,s.jsxs)(o.gC,{children:[(0,s.jsx)(o.eb,{value:"Present",children:"Present"}),(0,s.jsx)(o.eb,{value:"Late",children:"Late"}),(0,s.jsx)(o.eb,{value:"Absent",children:"Absent"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Check In Time"}),(0,s.jsx)(c.p,{type:"time",value:m.checkIn,onChange:e=>A({...m,checkIn:e.target.value})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Check Out Time"}),(0,s.jsx)(c.p,{type:"time",value:m.checkOut,onChange:e=>A({...m,checkOut:e.target.value})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{children:"Reason/Notes"}),(0,s.jsx)(j.T,{placeholder:"Enter reason for absence or late arrival...",value:m.reason,onChange:e=>A({...m,reason:e.target.value})})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(r.$,{variant:"outline",onClick:()=>x(null),children:"Cancel"}),(0,s.jsx)(r.$,{onClick:S,children:"Save Changes"})]})]})]})]})})]},e.id)})})]})})})]})}var S=a(24623),C=a(13977),T=a(74324),M=a(91788),R=a(81304),z=a(97939),I=a(17580),L=a(5040),G=a(1243);let P=[{id:"ATT001",studentId:"STU001",studentName:"Maria Cristina Santos",course:"Junior High School",grade:"7",section:"Grade 7-A",checkIn:"7:45 AM",checkOut:"4:30 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",photo:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"},{id:"ATT002",studentId:"STU002",studentName:"Juan Carlos Dela Cruz",course:"Junior High School",grade:"7",section:"Grade 7-B",checkIn:"7:50 AM",checkOut:"4:25 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",photo:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"},{id:"ATT003",studentId:"STU003",studentName:"Ana Marie Reyes",course:"Junior High School",grade:"7",section:"Grade 7-A",checkIn:"8:15 AM",checkOut:"4:35 PM",date:new Date().toISOString().split("T")[0],status:"Late",type:"gate",reason:"Traffic delay",photo:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"},{id:"ATT004",studentId:"STU004",studentName:"Jose Miguel Rodriguez",course:"Junior High School",grade:"8",section:"Grade 8-A",date:new Date().toISOString().split("T")[0],status:"Absent",type:"subject",subject:"Mathematics",period:"1st Period",reason:"Sick leave",photo:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"}];function O(){let[e,t]=(0,n.useState)(P),[a,c]=(0,n.useState)(P),[i,o]=(0,n.useState)({dateRange:{from:new Date,to:new Date},grade:"all",section:"all",course:"all",status:"all",searchQuery:""});return(0,n.useEffect)(()=>{let t=e;i.searchQuery&&(t=t.filter(e=>e.studentName.toLowerCase().includes(i.searchQuery.toLowerCase())||e.studentId.toLowerCase().includes(i.searchQuery.toLowerCase()))),"all"!==i.grade&&(t=t.filter(e=>e.grade===i.grade)),"all"!==i.status&&(t=t.filter(e=>e.status.toLowerCase()===i.status)),c(t)},[i,e]),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Attendance Management"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Monitor, track, and manage student attendance records"})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(r.$,{variant:"outline",onClick:()=>{var e;e="attendance-report-".concat((0,x.GP)(new Date,"yyyy-MM-dd"),".csv"),function(e,t,a){let s=new Blob([e],{type:a}),n=URL.createObjectURL(s),l=document.createElement("a");l.href=n,l.download=t,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(n)}(["Student ID,Student Name,Grade,Section,Course,Date,Check In,Check Out,Status,Type,Subject,Period,Reason",...a.map(e=>[e.studentId,'"'.concat(e.studentName,'"'),e.grade,'"'.concat(e.section,'"'),'"'.concat(e.course,'"'),e.date,e.checkIn||"",e.checkOut||"",e.status,e.type,e.subject||"",e.period||"",e.reason?'"'.concat(e.reason,'"'):""].join(","))].join("\n"),e||"attendance-report-".concat((0,x.GP)(new Date,"yyyy-MM-dd"),".csv"),"text/csv")},children:[(0,s.jsx)(M.A,{className:"mr-2 h-4 w-4"}),"Export Data"]}),(0,s.jsxs)(r.$,{variant:"outline",onClick:()=>{!function(e,t){let a=function(e,t){let a=(0,x.GP)(t,"yyyy-MM-dd"),s=e.filter(e=>e.date===a),n={date:(0,x.GP)(t,"MMMM d, yyyy"),totalStudents:s.length,present:s.filter(e=>"Present"===e.status).length,late:s.filter(e=>"Late"===e.status).length,absent:s.filter(e=>"Absent"===e.status).length,attendanceRate:0};return n.totalStudents>0&&(n.attendanceRate=(n.present+n.late)/n.totalStudents*100),{summary:n,gradeBreakdown:s.reduce((e,t)=>{let a=t.grade;return e[a]||(e[a]={total:0,present:0,late:0,absent:0}),e[a].total++,e[a][t.status.toLowerCase()]++,e},{}),records:s}}(e,t),s="\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Daily Attendance Report - ".concat(a.summary.date,'</title>\n      <style>\n        body { \n          font-family: Arial, sans-serif; \n          margin: 20px; \n          color: #333;\n        }\n        .header { \n          text-align: center; \n          margin-bottom: 30px;\n          border-bottom: 2px solid #333;\n          padding-bottom: 20px;\n        }\n        .school-name {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        .report-title {\n          font-size: 18px;\n          color: #666;\n        }\n        .summary { \n          display: grid; \n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 20px;\n          margin-bottom: 30px;\n        }\n        .summary-card {\n          border: 1px solid #ddd;\n          padding: 15px;\n          border-radius: 8px;\n          text-align: center;\n        }\n        .summary-value {\n          font-size: 32px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        .summary-label {\n          color: #666;\n          font-size: 14px;\n        }\n        .present { color: #22c55e; }\n        .late { color: #f59e0b; }\n        .absent { color: #ef4444; }\n        .rate { color: #3b82f6; }\n        \n        .grade-breakdown {\n          margin-bottom: 30px;\n        }\n        .grade-breakdown h3 {\n          margin-bottom: 15px;\n          color: #333;\n        }\n        .grade-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n        }\n        .grade-table th,\n        .grade-table td {\n          border: 1px solid #ddd;\n          padding: 8px;\n          text-align: center;\n        }\n        .grade-table th {\n          background-color: #f5f5f5;\n          font-weight: bold;\n        }\n        \n        .records-table {\n          width: 100%;\n          border-collapse: collapse;\n          font-size: 12px;\n        }\n        .records-table th,\n        .records-table td {\n          border: 1px solid #ddd;\n          padding: 6px;\n          text-align: left;\n        }\n        .records-table th {\n          background-color: #f5f5f5;\n          font-weight: bold;\n        }\n        \n        .footer {\n          margin-top: 40px;\n          text-align: center;\n          color: #666;\n          font-size: 12px;\n          border-top: 1px solid #ddd;\n          padding-top: 20px;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class="header">\n        <div class="school-name">QRSAMS - Tanauan National High School</div>\n        <div class="report-title">Daily Attendance Report</div>\n        <div style="margin-top: 10px; font-size: 16px;">').concat(a.summary.date,'</div>\n      </div>\n      \n      <div class="summary">\n        <div class="summary-card">\n          <div class="summary-value">').concat(a.summary.totalStudents,'</div>\n          <div class="summary-label">Total Students</div>\n        </div>\n        <div class="summary-card">\n          <div class="summary-value present">').concat(a.summary.present,'</div>\n          <div class="summary-label">Present</div>\n        </div>\n        <div class="summary-card">\n          <div class="summary-value late">').concat(a.summary.late,'</div>\n          <div class="summary-label">Late</div>\n        </div>\n        <div class="summary-card">\n          <div class="summary-value absent">').concat(a.summary.absent,'</div>\n          <div class="summary-label">Absent</div>\n        </div>\n        <div class="summary-card">\n          <div class="summary-value rate">').concat(a.summary.attendanceRate.toFixed(1),'%</div>\n          <div class="summary-label">Attendance Rate</div>\n        </div>\n      </div>\n      \n      <div class="grade-breakdown">\n        <h3>Grade Level Breakdown</h3>\n        <table class="grade-table">\n          <thead>\n            <tr>\n              <th>Grade</th>\n              <th>Total</th>\n              <th>Present</th>\n              <th>Late</th>\n              <th>Absent</th>\n              <th>Rate</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat(Object.entries(a.gradeBreakdown).map(e=>{let[t,a]=e;return"\n              <tr>\n                <td>Grade ".concat(t,"</td>\n                <td>").concat(a.total,'</td>\n                <td class="present">').concat(a.present,'</td>\n                <td class="late">').concat(a.late,'</td>\n                <td class="absent">').concat(a.absent,"</td>\n                <td>").concat(((a.present+a.late)/a.total*100).toFixed(1),"%</td>\n              </tr>\n            ")}).join(""),'\n          </tbody>\n        </table>\n      </div>\n      \n      <div>\n        <h3>Detailed Records</h3>\n        <table class="records-table">\n          <thead>\n            <tr>\n              <th>Student ID</th>\n              <th>Name</th>\n              <th>Grade</th>\n              <th>Section</th>\n              <th>Check In</th>\n              <th>Check Out</th>\n              <th>Status</th>\n              <th>Notes</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat(a.records.map(e=>"\n              <tr>\n                <td>".concat(e.studentId,"</td>\n                <td>").concat(e.studentName,"</td>\n                <td>").concat(e.grade,"</td>\n                <td>").concat(e.section,"</td>\n                <td>").concat(e.checkIn||"-","</td>\n                <td>").concat(e.checkOut||"-",'</td>\n                <td class="').concat(e.status.toLowerCase(),'">').concat(e.status,"</td>\n                <td>").concat(e.reason||"-","</td>\n              </tr>\n            ")).join(""),'\n          </tbody>\n        </table>\n      </div>\n      \n      <div class="footer">\n        Generated on ').concat((0,x.GP)(new Date,"MMMM d, yyyy 'at' h:mm a")," by QRSAMS\n      </div>\n    </body>\n    </html>\n  "),n=window.open("","_blank");n&&(n.document.write(s),n.document.close(),n.focus(),n.print(),n.close())}(a,new Date)},children:[(0,s.jsx)(R.A,{className:"mr-2 h-4 w-4"}),"Print Report"]}),(0,s.jsxs)(r.$,{children:[(0,s.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Open Scanner"]})]})]}),(0,s.jsx)(S.nz,{totalStudents:T.Kq.totalStudents,presentToday:T.Kq.presentToday,lateToday:T.Kq.lateToday,absentToday:T.Kq.absentToday,attendanceRate:T.Kq.attendanceRate}),(0,s.jsxs)("div",{className:"grid gap-6 lg:grid-cols-4",children:[(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(m,{onFiltersChange:e=>{o(e)}})}),(0,s.jsx)("div",{className:"lg:col-span-3 space-y-6",children:(0,s.jsxs)(d.Tabs,{defaultValue:"daily",className:"space-y-4",children:[(0,s.jsxs)(d.TabsList,{className:"grid w-full grid-cols-4",children:[(0,s.jsx)(d.TabsTrigger,{value:"daily",children:"Daily View"}),(0,s.jsx)(d.TabsTrigger,{value:"subject",children:"By Subject"}),(0,s.jsx)(d.TabsTrigger,{value:"analytics",children:"Analytics"}),(0,s.jsx)(d.TabsTrigger,{value:"reports",children:"Reports"})]}),(0,s.jsx)(d.TabsContent,{value:"daily",className:"space-y-4",children:(0,s.jsx)(A,{records:a,onUpdateRecord:(e,a)=>{t(t=>t.map(t=>t.id===e?{...t,...a}:t))}})}),(0,s.jsx)(d.TabsContent,{value:"subject",className:"space-y-4",children:(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"Subject-wise Attendance"}),(0,s.jsx)(l.BT,{children:"Track attendance by subject and period"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:["Mathematics","Science","English","Filipino","Social Studies","PE"].map(e=>(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{className:"pb-2",children:(0,s.jsx)(l.ZB,{className:"text-base",children:e})}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{children:"Present:"}),(0,s.jsx)("span",{className:"font-medium text-green-600",children:"85%"})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{children:"Late:"}),(0,s.jsx)("span",{className:"font-medium text-yellow-600",children:"8%"})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{children:"Absent:"}),(0,s.jsx)("span",{className:"font-medium text-red-600",children:"7%"})]})]})})]},e))})})]})}),(0,s.jsx)(d.TabsContent,{value:"analytics",className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsx)(C.tp,{data:T.Kq.weeklyTrend}),(0,s.jsx)(C.nn,{data:T.Kq.gradeBreakdown})]})}),(0,s.jsx)(d.TabsContent,{value:"reports",className:"space-y-4",children:(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{children:"Generate Reports"}),(0,s.jsx)(l.BT,{children:"Create and download attendance reports"})]}),(0,s.jsx)(l.Wu,{className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsxs)(r.$,{variant:"outline",className:"h-20 flex-col",children:[(0,s.jsx)(h.A,{className:"h-6 w-6 mb-2"}),"Daily Report"]}),(0,s.jsxs)(r.$,{variant:"outline",className:"h-20 flex-col",children:[(0,s.jsx)(I.A,{className:"h-6 w-6 mb-2"}),"Student Summary"]}),(0,s.jsxs)(r.$,{variant:"outline",className:"h-20 flex-col",children:[(0,s.jsx)(L.A,{className:"h-6 w-6 mb-2"}),"Subject Report"]}),(0,s.jsxs)(r.$,{variant:"outline",className:"h-20 flex-col",children:[(0,s.jsx)(G.A,{className:"h-6 w-6 mb-2"}),"Absence Report"]})]})})]})})]})})]})]})}},69663:(e,t,a)=>{"use strict";a.d(t,{BK:()=>c,eu:()=>d,q5:()=>i});var s=a(95155),n=a(12115),l=a(54011),r=a(53999);let d=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l.bL,{ref:t,className:(0,r.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...n})});d.displayName=l.bL.displayName;let c=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l._V,{ref:t,className:(0,r.cn)("aspect-square h-full w-full",a),...n})});c.displayName=l._V.displayName;let i=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l.H4,{ref:t,className:(0,r.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...n})});i.displayName=l.H4.displayName},82714:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var s=a(95155);a(12115);var n=a(40968),l=a(53999);function r(e){let{className:t,...a}=e;return(0,s.jsx)(n.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},83315:(e,t,a)=>{Promise.resolve().then(a.bind(a,33091))},88524:(e,t,a)=>{"use strict";a.d(t,{A0:()=>r,BF:()=>d,Hj:()=>c,XI:()=>l,nA:()=>o,nd:()=>i});var s=a(95155);a(12115);var n=a(53999);function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",t),...a})})}function r(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},89852:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var s=a(95155);a(12115);var n=a(53999);function l(e){let{className:t,type:a,...l}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...l})}},95784:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>x,gC:()=>h,l6:()=>i,yv:()=>o});var s=a(95155);a(12115);var n=a(22918),l=a(66474),r=a(5196),d=a(47863),c=a(53999);function i(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"select",...t})}function o(e){let{...t}=e;return(0,s.jsx)(n.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:r,...d}=e;return(0,s.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":a,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d,children:[r,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:a,position:l="popper",...r}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...r,children:[(0,s.jsx)(m,{}),(0,s.jsx)(n.LM,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(p,{})]})})}function x(e){let{className:t,children:a,...l}=e;return(0,s.jsxs)(n.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(r.A,{className:"size-4"})})}),(0,s.jsx)(n.p4,{children:a})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(d.A,{className:"size-4"})})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}},99474:(e,t,a)=>{"use strict";a.d(t,{T:()=>l});var s=a(95155);a(12115);var n=a(53999);function l(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},99840:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,L3:()=>x,c7:()=>h,lG:()=>d,rr:()=>m,zM:()=>c});var s=a(95155);a(12115);var n=a(15452),l=a(54416),r=a(53999);function d(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"dialog",...t})}function c(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"dialog-trigger",...t})}function i(e){let{...t}=e;return(0,s.jsx)(n.ZL,{"data-slot":"dialog-portal",...t})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,showCloseButton:d=!0,...c}=e;return(0,s.jsxs)(i,{"data-slot":"dialog-portal",children:[(0,s.jsx)(o,{}),(0,s.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...c,children:[a,d&&(0,s.jsxs)(n.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}}},e=>{e.O(0,[803,550,986,679,319,110,902,498,441,964,358],()=>e(e.s=83315)),_N_E=e.O()}]);