"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[498],{13977:(e,t,s)=>{s.d(t,{M4:()=>y,il:()=>v,nn:()=>b,tp:()=>p});var a=s(95155),n=s(83540),r=s(99445),l=s(94754),c=s(96025),i=s(52071),d=s(27086),o=s(64683),x=s(41213),u=s(3401),m=s(78882),h=s(90170),j=s(18357),f=s(54811),g=s(88482);function p(e){let{data:t}=e;return(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{children:[(0,a.jsx)(g.<PERSON><PERSON>,{children:"Weekly Attendance Trend"}),(0,a.jsx)(g.<PERSON>,{children:"Daily attendance patterns for this week"})]}),(0,a.jsx)(g.<PERSON>,{children:(0,a.jsx)(n.u,{width:"100%",height:300,children:(0,a.jsxs)(r.Q,{data:t,children:[(0,a.jsx)(l.d,{strokeDasharray:"3 3"}),(0,a.jsx)(c.W,{dataKey:"day"}),(0,a.jsx)(i.h,{}),(0,a.jsx)(d.m,{}),(0,a.jsx)(o.s,{}),(0,a.jsx)(x.Gk,{type:"monotone",dataKey:"present",stackId:"1",stroke:"#22c55e",fill:"#22c55e",fillOpacity:.8}),(0,a.jsx)(x.Gk,{type:"monotone",dataKey:"late",stackId:"1",stroke:"#f59e0b",fill:"#f59e0b",fillOpacity:.8}),(0,a.jsx)(x.Gk,{type:"monotone",dataKey:"absent",stackId:"1",stroke:"#ef4444",fill:"#ef4444",fillOpacity:.8})]})})})]})}function b(e){let{data:t}=e;return(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{children:[(0,a.jsx)(g.ZB,{children:"Grade Level Breakdown"}),(0,a.jsx)(g.BT,{children:"Attendance by grade level"})]}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)(n.u,{width:"100%",height:300,children:(0,a.jsxs)(u.E,{data:t,children:[(0,a.jsx)(l.d,{strokeDasharray:"3 3"}),(0,a.jsx)(c.W,{dataKey:"grade"}),(0,a.jsx)(i.h,{}),(0,a.jsx)(d.m,{}),(0,a.jsx)(o.s,{}),(0,a.jsx)(m.y,{dataKey:"present",fill:"#22c55e",name:"Present"}),(0,a.jsx)(m.y,{dataKey:"late",fill:"#f59e0b",name:"Late"}),(0,a.jsx)(m.y,{dataKey:"absent",fill:"#ef4444",name:"Absent"})]})})})]})}function v(e){let{present:t,late:s,absent:r}=e,l=[{name:"Present",value:t,color:"#22c55e"},{name:"Late",value:s,color:"#f59e0b"},{name:"Absent",value:r,color:"#ef4444"}],c=t+s+r;return(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{children:[(0,a.jsx)(g.ZB,{children:"Today's Attendance Distribution"}),(0,a.jsx)(g.BT,{children:"Current attendance breakdown"})]}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(n.u,{width:"60%",height:200,children:(0,a.jsxs)(h.r,{children:[(0,a.jsx)(j.F,{data:l,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:l.map((e,t)=>(0,a.jsx)(f.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(d.m,{})]})}),(0,a.jsx)("div",{className:"space-y-2",children:l.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-muted-foreground",children:[e.value," (",(e.value/c*100).toFixed(1),"%)"]})]})]},t))})]})})]})}function y(e){let{data:t}=e,s=Array.from({length:10},(e,t)=>t+7),n=Math.max(...t.map(e=>e.count));return(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{children:[(0,a.jsx)(g.ZB,{children:"Attendance Heat Map"}),(0,a.jsx)(g.BT,{children:"Student check-in patterns by day and hour"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-12"}),s.map(e=>(0,a.jsx)("div",{className:"w-8 text-xs text-center text-muted-foreground",children:e},e))]}),["Mon","Tue","Wed","Thu","Fri"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-12 text-xs text-muted-foreground",children:e}),s.map(s=>{let r=((e,s)=>{let a=t.find(t=>t.day===e&&t.hour===s);return a?a.count:0})(e,s),l=r/n;return(0,a.jsx)("div",{className:"w-8 h-6 rounded-sm border border-border",style:{backgroundColor:"rgba(34, 197, 94, ".concat(l,")")},title:"".concat(e," ").concat(s,":00 - ").concat(r," students")},"".concat(e,"-").concat(s))})]},e))]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4 text-xs text-muted-foreground",children:[(0,a.jsx)("span",{children:"Less"}),(0,a.jsx)("div",{className:"flex space-x-1",children:[0,.2,.4,.6,.8,1].map(e=>(0,a.jsx)("div",{className:"w-3 h-3 rounded-sm border border-border",style:{backgroundColor:"rgba(34, 197, 94, ".concat(e,")")}},e))}),(0,a.jsx)("span",{children:"More"})]})]})]})}},24623:(e,t,s)=>{s.d(t,{C8:()=>h,iS:()=>j,nz:()=>m});var a=s(95155),n=s(88482),r=s(88145),l=s(41397),c=s(17580),i=s(69074),d=s(14186),o=s(33109),x=s(68500),u=s(53999);function m(e){let{totalStudents:t,presentToday:s,lateToday:r,absentToday:m,attendanceRate:h,className:j}=e,f=[{title:"Total Students",value:t.toLocaleString(),icon:c.A,description:"Enrolled students",color:"text-blue-600",bgColor:"bg-blue-50",change:"+2.5%",trend:"up"},{title:"Present Today",value:s.toLocaleString(),icon:i.A,description:"".concat(h,"% attendance rate"),color:"text-green-600",bgColor:"bg-green-50",change:"+1.2%",trend:"up"},{title:"Late Arrivals",value:r.toString(),icon:d.A,description:"Students arrived late",color:"text-yellow-600",bgColor:"bg-yellow-50",change:"-0.8%",trend:"down"},{title:"Absent Today",value:m.toString(),icon:c.A,description:"".concat((m/t*100).toFixed(1),"% of total"),color:"text-red-600",bgColor:"bg-red-50",change:"+0.3%",trend:"up"}];return(0,a.jsx)("div",{className:(0,u.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-4",j),children:f.map((e,t)=>{let s=e.icon,r="up"===e.trend?o.A:x.A;return(0,a.jsxs)(n.Zp,{className:"relative overflow-hidden",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium text-muted-foreground",children:e.title}),(0,a.jsx)("div",{className:(0,u.cn)("p-2 rounded-lg",e.bgColor),children:(0,a.jsx)(s,{className:(0,u.cn)("h-4 w-4",e.color)})})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.description})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(r,{className:(0,u.cn)("h-3 w-3","up"===e.trend?"text-green-500":"text-red-500")}),(0,a.jsx)("span",{className:(0,u.cn)("text-xs font-medium","up"===e.trend?"text-green-500":"text-red-500"),children:e.change})]})]}),1===t&&(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(l.k,{value:h,className:"h-2"})})]})]},t)})})}function h(e){let{value:t,max:s,size:n=120,strokeWidth:r=8,className:l,children:c}=e,i=(n-r)/2,d=2*i*Math.PI,o=t/s*100,x=d-o/100*d;return(0,a.jsxs)("div",{className:(0,u.cn)("relative inline-flex items-center justify-center",l),children:[(0,a.jsxs)("svg",{width:n,height:n,className:"transform -rotate-90",children:[(0,a.jsx)("circle",{cx:n/2,cy:n/2,r:i,stroke:"currentColor",strokeWidth:r,fill:"none",className:"text-muted-foreground/20"}),(0,a.jsx)("circle",{cx:n/2,cy:n/2,r:i,stroke:"currentColor",strokeWidth:r,fill:"none",strokeDasharray:d,strokeDashoffset:x,className:"text-primary transition-all duration-300 ease-in-out",strokeLinecap:"round"})]}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:c||(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[o.toFixed(0),"%"]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[t,"/",s]})]})})]})}function j(e){let{value:t,label:s,trend:n="stable",className:l}=e;return(0,a.jsxs)("div",{className:(0,u.cn)("text-center",l),children:[(0,a.jsx)("div",{className:"text-3xl font-bold tabular-nums",children:t.toLocaleString()}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:s}),"stable"!==n&&(0,a.jsxs)("div",{className:"flex items-center justify-center mt-1",children:["up"===n?(0,a.jsx)(o.A,{className:"h-3 w-3 text-green-500 mr-1"}):(0,a.jsx)(x.A,{className:"h-3 w-3 text-red-500 mr-1"}),(0,a.jsx)(r.E,{variant:"up"===n?"default":"destructive",className:"text-xs",children:"up"===n?"↑":"↓"})]})]})}},34964:(e,t,s)=>{s.d(t,{Tabs:()=>l,TabsContent:()=>d,TabsList:()=>c,TabsTrigger:()=>i});var a=s(95155);s(12115);var n=s(60704),r=s(53999);function l(e){let{className:t,...s}=e;return(0,a.jsx)(n.bL,{"data-slot":"tabs",className:(0,r.cn)("flex flex-col gap-2",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(n.B8,{"data-slot":"tabs-list",className:(0,r.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)(n.l9,{"data-slot":"tabs-trigger",className:(0,r.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)(n.UC,{"data-slot":"tabs-content",className:(0,r.cn)("flex-1 outline-none",t),...s})}}}]);