(()=>{var a={};a.id=890,a.ids=[890],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26162:(a,b,c)=>{"use strict";c.d(b,{jP:()=>h,mp:()=>i,o:()=>l,vQ:()=>k,w0:()=>j});let d=[{id:"STU001",name:"<PERSON>",email:"<EMAIL>",course:"Junior High School",year:"1st Year",section:"Grade 7-A",grade:"7",status:"Active",photo:"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU001_2025"},{id:"STU002",name:"Juan <PERSON> Dela Cruz",email:"<EMAIL>",course:"Junior High School",year:"1st Year",section:"Grade 7-B",grade:"7",status:"Active",photo:"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU002_2025"},{id:"STU003",name:"Ana Marie Reyes",email:"<EMAIL>",course:"Junior High School",year:"1st Year",section:"Grade 7-A",grade:"7",status:"Active",photo:"https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU003_2025"},{id:"STU004",name:"Jose Miguel Rodriguez",email:"<EMAIL>",course:"Junior High School",year:"2nd Year",section:"Grade 8-A",grade:"8",status:"Active",photo:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU004_2025"},{id:"STU005",name:"Princess Mae Garcia",email:"<EMAIL>",course:"Junior High School",year:"2nd Year",section:"Grade 8-B",grade:"8",status:"Active",photo:"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU005_2025"},{id:"STU006",name:"Mark Anthony Villanueva",email:"<EMAIL>",course:"Junior High School",year:"3rd Year",section:"Grade 9-A",grade:"9",status:"Active",photo:"https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU006_2025"},{id:"STU007",name:"Angelica Mae Torres",email:"<EMAIL>",course:"Junior High School",year:"3rd Year",section:"Grade 9-B",grade:"9",status:"Active",photo:"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU007_2025"},{id:"STU008",name:"Christian Paul Mendoza",email:"<EMAIL>",course:"Junior High School",year:"4th Year",section:"Grade 10-A",grade:"10",status:"Active",photo:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU008_2025"},{id:"STU009",name:"Kimberly Rose Flores",email:"<EMAIL>",course:"Junior High School",year:"4th Year",section:"Grade 10-B",grade:"10",status:"Active",photo:"https://images.unsplash.com/photo-*************-53994a69daeb?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU009_2025"},{id:"STU010",name:"John Michael Cruz",email:"<EMAIL>",course:"Information and Communications Technology",year:"1st Year Senior High",section:"ICT 11-A",grade:"11",status:"Active",photo:"https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU010_2025"},{id:"STU011",name:"Mary Grace Aquino",email:"<EMAIL>",course:"Accountancy, Business and Management",year:"1st Year Senior High",section:"ABM 11-A",grade:"11",status:"Active",photo:"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU011_2025"},{id:"STU012",name:"Ryan James Bautista",email:"<EMAIL>",course:"Information and Communications Technology",year:"2nd Year Senior High",section:"ICT 12-A",grade:"12",status:"Active",photo:"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU012_2025"},{id:"STU013",name:"Sarah Jane Morales",email:"<EMAIL>",course:"Humanities and Social Sciences",year:"2nd Year Senior High",section:"HUMSS 12-A",grade:"12",status:"Active",photo:"https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face",qrCode:"QR_STU013_2025"}],e=[{id:"SUBJ001",name:"Programming Fundamentals",code:"IT101",instructor:"Prof. Martinez",schedule:[{day:"Monday",startTime:"08:00",endTime:"10:00"},{day:"Wednesday",startTime:"08:00",endTime:"10:00"},{day:"Friday",startTime:"08:00",endTime:"10:00"}]},{id:"SUBJ002",name:"Database Management",code:"IT201",instructor:"Prof. Rodriguez",schedule:[{day:"Tuesday",startTime:"10:00",endTime:"12:00"},{day:"Thursday",startTime:"10:00",endTime:"12:00"}]},{id:"SUBJ003",name:"Web Development",code:"IT301",instructor:"Prof. Santos",schedule:[{day:"Monday",startTime:"13:00",endTime:"15:00"},{day:"Wednesday",startTime:"13:00",endTime:"15:00"}]},{id:"SUBJ004",name:"Data Structures",code:"CS201",instructor:"Prof. Reyes",schedule:[{day:"Tuesday",startTime:"08:00",endTime:"10:00"},{day:"Thursday",startTime:"08:00",endTime:"10:00"}]},{id:"SUBJ005",name:"Software Engineering",code:"CS301",instructor:"Prof. Cruz",schedule:[{day:"Monday",startTime:"15:00",endTime:"17:00"},{day:"Friday",startTime:"15:00",endTime:"17:00"}]}],f=[{id:"PERIOD001",name:"1st Period",startTime:"08:00",endTime:"10:00",type:"morning"},{id:"PERIOD002",name:"2nd Period",startTime:"10:00",endTime:"12:00",type:"morning"},{id:"PERIOD003",name:"3rd Period",startTime:"13:00",endTime:"15:00",type:"afternoon"},{id:"PERIOD004",name:"4th Period",startTime:"15:00",endTime:"17:00",type:"afternoon"},{id:"PERIOD005",name:"Evening Class",startTime:"18:00",endTime:"20:00",type:"evening"}],g=[{id:"ATT001",studentId:"STU001",studentName:"Maria Cristina Santos",course:"Junior High School",checkIn:"7:45 AM",checkOut:"4:30 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",timestamp:new Date(new Date().setHours(7,45,0))},{id:"ATT002",studentId:"STU002",studentName:"Juan Carlos Dela Cruz",course:"Junior High School",checkIn:"7:50 AM",checkOut:"4:25 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",timestamp:new Date(new Date().setHours(7,50,0))},{id:"ATT003",studentId:"STU003",studentName:"Ana Marie Reyes",course:"Junior High School",checkIn:"8:15 AM",checkOut:"4:35 PM",date:new Date().toISOString().split("T")[0],status:"Late",type:"gate",timestamp:new Date(new Date().setHours(8,15,0))},{id:"ATT004",studentId:"STU004",studentName:"Jose Miguel Rodriguez",course:"Junior High School",date:new Date().toISOString().split("T")[0],status:"Absent",type:"subject",subject:"Mathematics",period:"1st Period",timestamp:new Date(new Date().setHours(8,0,0))},{id:"ATT005",studentId:"STU005",studentName:"Princess Mae Garcia",course:"Junior High School",checkIn:"7:55 AM",checkOut:"4:20 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",timestamp:new Date(new Date().setHours(7,55,0))},{id:"ATT006",studentId:"STU010",studentName:"John Michael Cruz",course:"Information and Communications Technology",checkIn:"7:40 AM",checkOut:"5:00 PM",date:new Date().toISOString().split("T")[0],status:"Present",type:"gate",timestamp:new Date(new Date().setHours(7,40,0))},{id:"ATT007",studentId:"STU012",studentName:"Ryan James Bautista",course:"Information and Communications Technology",checkIn:"8:10 AM",checkOut:"5:05 PM",date:new Date().toISOString().split("T")[0],status:"Late",type:"gate",timestamp:new Date(new Date().setHours(8,10,0))}];function h(a){return d.find(b=>b.id===a)}function i(a){return d.find(b=>b.qrCode===a)}function j(a){return e.find(b=>b.id===a)}function k(a){return f.find(b=>b.id===a)}function l(a){let b=new Date().toISOString().split("T")[0];return g.find(c=>c.studentId===a&&c.date===b)}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76587:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>E,patchFetch:()=>D,routeModule:()=>z,serverHooks:()=>C,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>B});var d={};c.r(d),c.d(d,{GET:()=>y,POST:()=>x});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(26162);let w=[];async function x(a){try{var b,c;let{studentId:d,action:e,subject:f,period:g,reason:h,offline:i=!1,originalTimestamp:j}=await a.json();if(!d||!e)return u.NextResponse.json({success:!1,error:"studentId and action are required"},{status:400});let k=(0,v.jP)(d);if(!k)return u.NextResponse.json({success:!1,error:"Student not found"},{status:404});if(f&&!(0,v.w0)(f))return u.NextResponse.json({success:!1,error:"Subject not found"},{status:404});if(g&&!(0,v.vQ)(g))return u.NextResponse.json({success:!1,error:"Period not found"},{status:404});let l=j?new Date(j):new Date,m={id:`ATT_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,studentId:k.id,studentName:k.name,course:k.course,date:l.toISOString().split("T")[0],status:function(a){switch(a){case"present":case"check-in":case"check-out":default:return"Present";case"late":return"Late";case"absent":return"Absent"}}(e),type:(b=e,c=f,"check-in"===b||"check-out"===b?"gate":c?"subject":"gate"),timestamp:l,subject:f,period:g};return"check-in"===e?m.checkIn=l.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}):"check-out"===e&&(m.checkOut=l.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0})),w.push(m),console.log(`Attendance recorded: ${k.name} - ${e}${i?" (offline)":""}`),u.NextResponse.json({success:!0,data:m,message:`Attendance recorded successfully${i?" (synced from offline)":""}`})}catch(a){return console.error("Attendance recording error:",a),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function y(a){try{let{searchParams:b}=new URL(a.url),c=b.get("studentId"),d=b.get("date"),e=w;return c&&(e=e.filter(a=>a.studentId===c)),d&&(e=e.filter(a=>a.date===d)),u.NextResponse.json({success:!0,data:e,message:"Attendance records retrieved successfully"})}catch(a){return console.error("Attendance retrieval error:",a),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let z=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/scanner/attendance/route",pathname:"/api/scanner/attendance",filename:"route",bundlePath:"app/api/scanner/attendance/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\api\\scanner\\attendance\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:A,workUnitAsyncStorage:B,serverHooks:C}=z;function D(){return(0,g.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:B})}async function E(a,b,c){var d;let e="/api/scanner/attendance/route";"/index"===e&&(e="/");let g=await z.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||z.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===z.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>z.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>z.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await z.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await z.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await z.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=76587));module.exports=c})();