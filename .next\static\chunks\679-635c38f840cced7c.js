"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[679],{5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},11275:(e,t,n)=>{n.d(t,{X:()=>l});var r=n(12115),o=n(52712);function l(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},14186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},22436:(e,t,n)=>{var r=n(12115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=r.useState,i=r.useEffect,a=r.useLayoutEffect,s=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=l({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return a(function(){o.value=n,o.getSnapshot=t,u(o)&&c({inst:o})},[e,n,t]),i(function(){return u(o)&&c({inst:o}),e(function(){u(o)&&c({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},22918:(e,t,n)=>{n.d(t,{UC:()=>eN,In:()=>ej,q7:()=>eM,VF:()=>eO,p4:()=>eI,ZL:()=>eP,bL:()=>eT,wn:()=>eB,PP:()=>eH,l9:()=>eE,WT:()=>eL,LM:()=>eD});var r=n(12115),o=n(47650),l=n(89367),i=n(85185),a=n(37328),s=n(6101),u=n(46081),c=n(94315),d=n(19178),f=n(92293),p=n(25519),h=n(61285),m=n(35152),v=n(34378),g=n(63655),y=n(99708),w=n(39033),x=n(5845),b=n(52712),S=n(45503),C=n(95155),R=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,C.jsx)(g.sG.span,{...e,ref:t,style:{...R,...e.style}})).displayName="VisuallyHidden";var A=n(38168),k=n(93795),T=[" ","Enter","ArrowUp","ArrowDown"],E=[" ","Enter"],L="Select",[j,P,N]=(0,a.N)(L),[D,M]=(0,u.A)(L,[N,m.Bk]),I=(0,m.Bk)(),[O,H]=D(L),[B,F]=D(L),V=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:g,form:y}=e,w=I(t),[b,S]=r.useState(null),[R,A]=r.useState(null),[k,T]=r.useState(!1),E=(0,c.jH)(d),[P,N]=(0,x.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:L}),[D,M]=(0,x.i)({prop:a,defaultProp:s,onChange:u,caller:L}),H=r.useRef(null),F=!b||y||!!b.closest("form"),[V,W]=r.useState(new Set),_=Array.from(V).map(e=>e.props.value).join(";");return(0,C.jsx)(m.bL,{...w,children:(0,C.jsxs)(O,{required:g,scope:t,trigger:b,onTriggerChange:S,valueNode:R,onValueNodeChange:A,valueNodeHasChildren:k,onValueNodeHasChildrenChange:T,contentId:(0,h.B)(),value:D,onValueChange:M,open:P,onOpenChange:N,dir:E,triggerPointerDownPosRef:H,disabled:v,children:[(0,C.jsx)(j.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{W(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),F?(0,C.jsxs)(eC,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:D,onChange:e=>M(e.target.value),disabled:v,form:y,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(V)]},_):null]})})};V.displayName=L;var W="SelectTrigger",_=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...l}=e,a=I(n),u=H(W,n),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=P(n),p=r.useRef("touch"),[h,v,y]=eA(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=ek(t,e,n);void 0!==r&&u.onValueChange(r.value)}),w=e=>{c||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(m.Mz,{asChild:!0,...a,children:(0,C.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eR(u.value)?"":void 0,...l,ref:d,onClick:(0,i.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,i.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,i.m)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(w(),e.preventDefault())})})})});_.displayName=W;var z="SelectValue",G=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,u=H(z,n),{onValueNodeHasChildrenChange:c}=u,d=void 0!==l,f=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{c(d)},[c,d]),(0,C.jsx)(g.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eR(u.value)?(0,C.jsx)(C.Fragment,{children:i}):l})});G.displayName=z;var q=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});q.displayName="SelectIcon";var K=e=>(0,C.jsx)(v.Z,{asChild:!0,...e});K.displayName="SelectPortal";var U="SelectContent",X=r.forwardRef((e,t)=>{let n=H(U,e.__scopeSelect),[l,i]=r.useState();return((0,b.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,C.jsx)(J,{...e,ref:t}):l?o.createPortal((0,C.jsx)(Y,{scope:e.__scopeSelect,children:(0,C.jsx)(j.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),l):null});X.displayName=U;var[Y,Z]=D(U),$=(0,y.TL)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S,...R}=e,T=H(U,n),[E,L]=r.useState(null),[j,N]=r.useState(null),D=(0,s.s)(t,e=>L(e)),[M,I]=r.useState(null),[O,B]=r.useState(null),F=P(n),[V,W]=r.useState(!1),_=r.useRef(!1);r.useEffect(()=>{if(E)return(0,A.Eq)(E)},[E]),(0,f.Oh)();let z=r.useCallback(e=>{let[t,...n]=F().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&j&&(j.scrollTop=0),n===r&&j&&(j.scrollTop=j.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[F,j]),G=r.useCallback(()=>z([M,E]),[z,M,E]);r.useEffect(()=>{V&&G()},[V,G]);let{onOpenChange:q,triggerPointerDownPosRef:K}=T;r.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=K.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=K.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():E.contains(n.target)||q(!1),document.removeEventListener("pointermove",t),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[E,q,K]),r.useEffect(()=>{let e=()=>q(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[q]);let[X,Z]=eA(e=>{let t=F().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=ek(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==T.value&&T.value===t||r)&&(I(e),r&&(_.current=!0))},[T.value]),et=r.useCallback(()=>null==E?void 0:E.focus(),[E]),en=r.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==T.value&&T.value===t||r)&&B(e)},[T.value]),er="popper"===o?ee:Q,eo=er===ee?{side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(Y,{scope:n,content:E,viewport:j,onViewportChange:N,itemRefCallback:J,selectedItem:M,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:G,selectedItemText:O,position:o,isPositioned:V,searchRef:X,children:(0,C.jsx)(k.A,{as:$,allowPinchZoom:!0,children:(0,C.jsx)(p.n,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(l,e=>{var t;null==(t=T.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,C.jsx)(er,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...R,...eo,onPlaced:()=>W(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,i.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...i}=e,a=H(U,n),u=Z(U,n),[c,d]=r.useState(null),[f,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=P(n),v=r.useRef(!1),y=r.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:S,focusSelectedItem:R}=u,A=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&f&&w&&x&&S){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=S.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,i=n.left-o,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,l.q)(i,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let o=t.right-r.right,i=window.innerWidth-n.right-o,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,l.q)(i,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}let i=m(),s=window.innerHeight-20,u=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+h+u+parseInt(d.paddingBottom,10)+g,b=Math.min(5*x.offsetHeight,y),C=window.getComputedStyle(w),R=parseInt(C.paddingTop,10),A=parseInt(C.paddingBottom,10),k=e.top+e.height/2-10,T=x.offsetHeight/2,E=p+h+(x.offsetTop+T);if(E<=k){let e=i.length>0&&x===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-k,T+(e?A:0)+(f.clientHeight-w.offsetTop-w.offsetHeight)+g);c.style.height=E+t+"px"}else{let e=i.length>0&&x===i[0].ref.current;c.style.top="0px";let t=Math.max(k,p+w.offsetTop+(e?R:0)+T);c.style.height=t+(y-E)+"px",w.scrollTop=E-k+w.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=b+"px",c.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,c,f,w,x,S,a.dir,o]);(0,b.N)(()=>A(),[A]);let[k,T]=r.useState();(0,b.N)(()=>{f&&T(window.getComputedStyle(f).zIndex)},[f]);let E=r.useCallback(e=>{e&&!0===y.current&&(A(),null==R||R(),y.current=!1)},[A,R]);return(0,C.jsx)(et,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:E,children:(0,C.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,C.jsx)(g.sG.div,{...i,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=I(n);return(0,C.jsx)(m.UC,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=D(U,{}),er="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...l}=e,a=Z(er,n),u=en(er,n),c=(0,s.s)(t,a.onViewportChange),d=r.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,C.jsx)(j.Slot,{scope:n,children:(0,C.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,i.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=er;var el="SelectGroup",[ei,ea]=D(el);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,C.jsx)(ei,{scope:n,id:o,children:(0,C.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=el;var es="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ea(es,n);return(0,C.jsx)(g.sG.div,{id:o.id,...r,ref:t})}).displayName=es;var eu="SelectItem",[ec,ed]=D(eu),ef=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:l=!1,textValue:a,...u}=e,c=H(eu,n),d=Z(eu,n),f=c.value===o,[p,m]=r.useState(null!=a?a:""),[v,y]=r.useState(!1),w=(0,s.s)(t,e=>{var t;return null==(t=d.itemRefCallback)?void 0:t.call(d,e,o,l)}),x=(0,h.B)(),b=r.useRef("touch"),S=()=>{l||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ec,{scope:n,value:o,disabled:l,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,C.jsx)(j.ItemSlot,{scope:n,value:o,disabled:l,textValue:p,children:(0,C.jsx)(g.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:w,onFocus:(0,i.m)(u.onFocus,()=>y(!0)),onBlur:(0,i.m)(u.onBlur,()=>y(!1)),onClick:(0,i.m)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,i.m)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,i.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,i.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,l){var t;null==(t=d.onItemLeave)||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=d.onItemLeave)||t.call(d)}}),onKeyDown:(0,i.m)(u.onKeyDown,e=>{var t;((null==(t=d.searchRef)?void 0:t.current)===""||" "!==e.key)&&(E.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ep="SelectItemText",eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:l,style:i,...a}=e,u=H(ep,n),c=Z(ep,n),d=ed(ep,n),f=F(ep,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),d.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,d.value,d.disabled)}),v=null==p?void 0:p.textContent,y=r.useMemo(()=>(0,C.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=f;return(0,b.N)(()=>(w(y),()=>x(y)),[w,x,y]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(g.sG.span,{id:d.textId,...a,ref:m}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});eh.displayName=ep;var em="SelectItemIndicator",ev=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ed(em,n).isSelected?(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ev.displayName=em;var eg="SelectScrollUpButton",ey=r.forwardRef((e,t)=>{let n=Z(eg,e.__scopeSelect),o=en(eg,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,C.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=eg;var ew="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{let n=Z(ew,e.__scopeSelect),o=en(ew,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,C.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=ew;var eb=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...l}=e,a=Z("SelectScrollButton",n),s=r.useRef(null),u=P(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,i.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.m)(l.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.m)(l.onPointerLeave,()=>{c()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=I(n),l=H(eS,n),i=Z(eS,n);return l.open&&"popper"===i.position?(0,C.jsx)(m.i3,{...o,...r,ref:t}):null}).displayName=eS;var eC=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...l}=e,i=r.useRef(null),a=(0,s.s)(t,i),u=(0,S.Z)(o);return r.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[u,o]),(0,C.jsx)(g.sG.select,{...l,style:{...R,...l.style},ref:a,defaultValue:o})});function eR(e){return""===e||void 0===e}function eA(e){let t=(0,w.c)(e),n=r.useRef(""),o=r.useRef(0),l=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),i=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,l,i]}function ek(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==n?s:void 0}eC.displayName="SelectBubbleInput";var eT=V,eE=_,eL=G,ej=q,eP=K,eN=X,eD=eo,eM=ef,eI=eh,eO=ev,eH=ey,eB=ex},35152:(e,t,n)=>{n.d(t,{Mz:()=>e1,i3:()=>e2,UC:()=>e5,bL:()=>e0,Bk:()=>eB});var r=n(12115);let o=["top","right","bottom","left"],l=Math.min,i=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let x=["left","right"],b=["right","left"],S=["top","bottom"],C=["bottom","top"];function R(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function A(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function k(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function T(e,t,n){let r,{reference:o,floating:l}=e,i=y(t),a=m(y(t)),s=v(a),u=p(t),c="y"===i,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,g=o[s]/2-l[s]/2;switch(u){case"top":r={x:d,y:o.y-l.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-l.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=g*(n&&c?-1:1);break;case"end":r[a]+=g*(n&&c?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=T(u,r,s),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[l]:{...p[l],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=T(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function L(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=A(h),v=a[p?"floating"===d?"reference":"floating":d],g=k(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(v)))||n?v:v.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,w=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),x=await (null==l.isElement?void 0:l.isElement(w))&&await (null==l.getScale?void 0:l.getScale(w))||{x:1,y:1},b=k(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:s}):y);return{top:(g.top-b.top+m.top)/x.y,bottom:(b.bottom-g.bottom+m.bottom)/x.y,left:(g.left-b.left+m.left)/x.x,right:(b.right-g.right+m.right)/x.x}}function j(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return o.some(t=>e[t]>=0)}let N=new Set(["left","top"]);async function D(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=p(n),a=h(n),s="y"===y(n),u=N.has(i)?-1:1,c=l&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof g&&(v="end"===a?-1*g:g),s?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function M(){return"undefined"!=typeof window}function I(e){return B(e)?(e.nodeName||"").toLowerCase():"#document"}function O(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function H(e){var t;return null==(t=(B(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function B(e){return!!M()&&(e instanceof Node||e instanceof O(e).Node)}function F(e){return!!M()&&(e instanceof Element||e instanceof O(e).Element)}function V(e){return!!M()&&(e instanceof HTMLElement||e instanceof O(e).HTMLElement)}function W(e){return!!M()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof O(e).ShadowRoot)}let _=new Set(["inline","contents"]);function z(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!_.has(o)}let G=new Set(["table","td","th"]),q=[":popover-open",":modal"];function K(e){return q.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let U=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function Z(e){let t=$(),n=F(e)?ee(e):e;return U.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||X.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function $(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Q(e){return J.has(I(e))}function ee(e){return O(e).getComputedStyle(e)}function et(e){return F(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===I(e))return e;let t=e.assignedSlot||e.parentNode||W(e)&&e.host||H(e);return W(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:V(n)&&z(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=O(o);if(l){let e=eo(i);return t.concat(i,i.visualViewport||[],z(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function el(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=V(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,s=a(n)!==l||a(r)!==i;return s&&(n=l,r=i),{width:n,height:r,$:s}}function ei(e){return F(e)?e:e.contextElement}function ea(e){let t=ei(e);if(!V(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=el(t),i=(l?a(n.width):n.width)/r,s=(l?a(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}let es=u(0);function eu(e){let t=O(e);return $()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:es}function ec(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=ei(e),a=u(1);t&&(r?F(r)&&(a=ea(r)):a=ea(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===O(i))&&o)?eu(i):u(0),c=(l.left+s.x)/a.x,d=(l.top+s.y)/a.y,f=l.width/a.x,p=l.height/a.y;if(i){let e=O(i),t=r&&F(r)?O(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=ea(o),t=o.getBoundingClientRect(),r=ee(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=l,d+=i,o=eo(n=O(o))}}return k({width:f,height:p,x:c,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:ec(H(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=O(e),r=H(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=$();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=H(e),n=et(e),r=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=i(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ed(e),s=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=i(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:a,y:s}}(H(e));else if(F(t))r=function(e,t){let n=ec(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=V(e)?ea(e):u(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=eu(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return k(r)}function em(e){return"static"===ee(e).position}function ev(e,t){if(!V(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return H(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=O(e);if(K(e))return r;if(!V(e)){let t=en(e);for(;t&&!Q(t);){if(F(t)&&!em(t))return t;t=en(t)}return r}let o=ev(e,t);for(;o&&(n=o,G.has(I(n)))&&em(o);)o=ev(o,t);return o&&Q(o)&&em(o)&&!Z(o)?r:o||function(e){let t=en(e);for(;V(t)&&!Q(t);){if(Z(t))return t;if(K(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=V(t),o=H(t),l="fixed"===n,i=ec(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!l)if(("body"!==I(t)||z(o))&&(a=et(t)),r){let e=ec(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=ed(o));l&&!r&&o&&(s.x=ed(o));let c=!o||r||l?u(0):ef(o,a);return{x:i.left+a.scrollLeft-s.x-c.x,y:i.top+a.scrollTop-s.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=H(r),a=!!t&&K(t.floating);if(r===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=V(r);if((f||!f&&!l)&&(("body"!==I(r)||z(i))&&(s=et(r)),V(r))){let e=ec(r);c=ea(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!i||f||l?u(0):ef(i,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:H,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?K(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>F(e)&&"body"!==I(e)),o=null,l="fixed"===ee(e).position,i=l?en(e):e;for(;F(i)&&!Q(i);){let t=ee(i),n=Z(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||z(i)&&!n&&function e(t,n){let r=en(t);return!(r===n||!F(r)||Q(r))&&("fixed"===ee(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=en(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=eh(t,n,o);return e.top=i(r.top,e.top),e.right=l(r.right,e.right),e.bottom=l(r.bottom,e.bottom),e.left=i(r.left,e.left),e},eh(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=el(e);return{width:t,height:n}},getScale:ea,isElement:F,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=A(p),w={x:n,y:r},x=m(y(o)),b=v(x),S=await s.getDimensions(d),C="y"===x,R=C?"clientHeight":"clientWidth",k=a.reference[b]+a.reference[x]-w[x]-a.floating[b],T=w[x]-a.reference[x],E=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),L=E?E[R]:0;L&&await (null==s.isElement?void 0:s.isElement(E))||(L=u.floating[R]||a.floating[b]);let j=L/2-S[b]/2-1,P=l(g[C?"top":"left"],j),N=l(g[C?"bottom":"right"],j),D=L-S[b]-N,M=L/2-S[b]/2+(k/2-T/2),I=i(P,l(M,D)),O=!c.arrow&&null!=h(o)&&M!==I&&a.reference[b]/2-(M<P?P:N)-S[b]/2<0,H=O?M<P?M-P:M-D:0;return{[x]:w[x]+H,data:{[x]:I,centerOffset:M-I-H,...O&&{alignmentOffset:H}},reset:O}}});var eS=n(47650),eC="undefined"!=typeof document?r.useLayoutEffect:function(){};function eR(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eR(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eR(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eA(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ek(e,t){let n=eA(e);return Math.round(t*n)/n}function eT(e){let t=r.useRef(e);return eC(()=>{t.current=e}),t}var eE=n(63655),eL=n(95155),ej=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,eL.jsx)(eE.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eL.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ej.displayName="Arrow";var eP=n(6101),eN=n(46081),eD=n(39033),eM=n(52712),eI=n(11275),eO="Popper",[eH,eB]=(0,eN.A)(eO),[eF,eV]=eH(eO),eW=e=>{let{__scopePopper:t,children:n}=e,[o,l]=r.useState(null);return(0,eL.jsx)(eF,{scope:t,anchor:o,onAnchorChange:l,children:n})};eW.displayName=eO;var e_="PopperAnchor",ez=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...l}=e,i=eV(e_,n),a=r.useRef(null),s=(0,eP.s)(t,a);return r.useEffect(()=>{i.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eL.jsx)(eE.sG.div,{...l,ref:s})});ez.displayName=e_;var eG="PopperContent",[eq,eK]=eH(eG),eU=r.forwardRef((e,t)=>{var n,o,a,u,c,d,g,A;let{__scopePopper:k,side:T="bottom",sideOffset:M=0,align:I="center",alignOffset:O=0,arrowPadding:B=0,avoidCollisions:F=!0,collisionBoundary:V=[],collisionPadding:W=0,sticky:_="partial",hideWhenDetached:z=!1,updatePositionStrategy:G="optimized",onPlaced:q,...K}=e,U=eV(eG,k),[X,Y]=r.useState(null),Z=(0,eP.s)(t,e=>Y(e)),[$,J]=r.useState(null),Q=(0,eI.X)($),ee=null!=(g=null==Q?void 0:Q.width)?g:0,et=null!=(A=null==Q?void 0:Q.height)?A:0,en="number"==typeof W?W:{top:0,right:0,bottom:0,left:0,...W},eo=Array.isArray(V)?V:[V],el=eo.length>0,ea={padding:en,boundary:eo.filter(e$),altBoundary:el},{refs:es,floatingStyles:eu,placement:ed,isPositioned:ef,middlewareData:ep}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:l,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eR(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),b=i||m,S=a||g,C=r.useRef(null),R=r.useRef(null),A=r.useRef(d),k=null!=u,T=eT(u),L=eT(l),j=eT(c),P=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),((e,t,n)=>{let r=new Map,o={platform:ew,...n},l={...o.platform,_c:r};return E(e,t,{...o,platform:l})})(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};N.current&&!eR(A.current,t)&&(A.current=t,eS.flushSync(()=>{f(t)}))})},[p,t,n,L,j]);eC(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let N=r.useRef(!1);eC(()=>(N.current=!0,()=>{N.current=!1}),[]),eC(()=>{if(b&&(C.current=b),S&&(R.current=S),b&&S){if(T.current)return T.current(b,S,P);P()}},[b,S,P,T,k]);let D=r.useMemo(()=>({reference:C,floating:R,setReference:w,setFloating:x}),[w,x]),M=r.useMemo(()=>({reference:b,floating:S}),[b,S]),I=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=ek(M.floating,d.x),r=ek(M.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...eA(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,M.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:D,elements:M,floatingStyles:I}),[d,P,D,M,I])}({strategy:"fixed",placement:T+("center"!==I?"-"+I:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=ei(e),h=a||u?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=H(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(c||t(),!m||!v)return;let g=s(h),y=s(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-s(o.clientHeight-(h+v))+"px "+-s(p)+"px",threshold:i(0,l(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ex(f,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?ec(e):null;return f&&function t(){let r=ec(e);y&&!ex(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===G})},elements:{reference:U.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await D(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}))({mainAxis:M+et,alignmentAxis:O}),F&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await L(t,c),v=y(p(o)),g=m(v),w=d[g],x=d[v];if(a){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=i(n,l(w,r))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=i(n,l(x,r))}let b=u.fn({...t,[g]:w,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:a,[v]:s}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===_?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:n,y:r},d=y(o),h=m(d),v=c[h],g=c[d],w=f(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){let e="y"===h?"height":"width",t=l.reference[h]-l.floating[e]+x.mainAxis,n=l.reference[h]+l.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var b,S;let e="y"===h?"width":"height",t=N.has(p(o)),n=l.reference[d]-l.floating[e]+(t&&(null==(b=i.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=l.reference[d]+l.reference[e]+(t?0:(null==(S=i.offset)?void 0:S[d])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:v,[d]:g}}}}(e),options:[e,t]}))():void 0,...ea}),F&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:g}=t,{mainAxis:A=!0,crossAxis:k=!0,fallbackPlacements:T,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:j="none",flipAlignment:P=!0,...N}=f(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let D=p(a),M=y(c),I=p(c)===c,O=await (null==d.isRTL?void 0:d.isRTL(g.floating)),H=T||(I||!P?[R(c)]:function(e){let t=R(e);return[w(e),t,w(t)]}(c)),B="none"!==j;!T&&B&&H.push(...function(e,t,n,r){let o=h(e),l=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?S:C;default:return[]}}(p(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(w)))),l}(c,P,j,O));let F=[c,...H],V=await L(t,N),W=[],_=(null==(r=s.flip)?void 0:r.overflows)||[];if(A&&W.push(V[D]),k){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(y(e)),l=v(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=R(i)),[i,R(i)]}(a,u,O);W.push(V[e[0]],V[e[1]])}if(_=[..._,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=F[e];if(t&&("alignment"!==k||M===y(t)||_.every(e=>y(e.placement)!==M||e.overflows[0]>0)))return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(l=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(E){case"bestFit":{let e=null==(i=_.filter(e=>{if(B){let t=y(e.placement);return t===M||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...ea}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:s,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),g=await L(t,v),w=p(s),x=h(s),b="y"===y(s),{width:S,height:C}=u.floating;"top"===w||"bottom"===w?(o=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=w,o="end"===x?"top":"bottom");let R=C-g.top-g.bottom,A=S-g.left-g.right,k=l(C-g[o],R),T=l(S-g[a],A),E=!t.middlewareData.shift,j=k,P=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(j=R),E&&!x){let e=i(g.left,0),t=i(g.right,0),n=i(g.top,0),r=i(g.bottom,0);b?P=S-2*(0!==e||0!==t?e+t:i(g.left,g.right)):j=C-2*(0!==n||0!==r?n+r:i(g.top,g.bottom))}await m({...t,availableWidth:P,availableHeight:j});let N=await c.getDimensions(d.floating);return S!==N.width||C!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...ea,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),$&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:$,padding:B}),eJ({arrowWidth:ee,arrowHeight:et}),z&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=j(await L(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{let e=j(await L(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...ea})]}),[eh,em]=eQ(ed),ev=(0,eD.c)(q);(0,eM.N)(()=>{ef&&(null==ev||ev())},[ef,ev]);let eg=null==(n=ep.arrow)?void 0:n.x,ey=null==(o=ep.arrow)?void 0:o.y,ej=(null==(a=ep.arrow)?void 0:a.centerOffset)!==0,[eN,eO]=r.useState();return(0,eM.N)(()=>{X&&eO(window.getComputedStyle(X).zIndex)},[X]),(0,eL.jsx)("div",{ref:es.setFloating,"data-radix-popper-content-wrapper":"",style:{...eu,transform:ef?eu.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eN,"--radix-popper-transform-origin":[null==(u=ep.transformOrigin)?void 0:u.x,null==(c=ep.transformOrigin)?void 0:c.y].join(" "),...(null==(d=ep.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eL.jsx)(eq,{scope:k,placedSide:eh,onArrowChange:J,arrowX:eg,arrowY:ey,shouldHideArrow:ej,children:(0,eL.jsx)(eE.sG.div,{"data-side":eh,"data-align":em,...K,ref:Z,style:{...K.style,animation:ef?void 0:"none"}})})})});eU.displayName=eG;var eX="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eZ=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eK(eX,n),l=eY[o.placedSide];return(0,eL.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eL.jsx)(ej,{...r,ref:t,style:{...r.style,display:"block"}})})});function e$(e){return null!==e}eZ.displayName=eX;var eJ=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=eQ(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(l=null==(r=u.arrow)?void 0:r.x)?l:0)+d/2,g=(null!=(i=null==(o=u.arrow)?void 0:o.y)?i:0)+f/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=c?m:"".concat(v,"px"),w="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function eQ(e){let[t,n="center"]=e.split("-");return[t,n]}var e0=eW,e1=ez,e5=eU,e2=eZ},40968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(12115),o=n(63655),l=n(95155),i=r.forwardRef((e,t)=>(0,l.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var a=i},45503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(12115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},47863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},49033:(e,t,n)=>{e.exports=n(22436)},54011:(e,t,n)=>{n.d(t,{H4:()=>R,_V:()=>C,bL:()=>S});var r=n(12115),o=n(46081),l=n(39033),i=n(52712),a=n(63655),s=n(49033);function u(){return()=>{}}var c=n(95155),d="Avatar",[f,p]=(0,o.A)(d),[h,m]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[l,i]=r.useState("idle");return(0,c.jsx)(h,{scope:n,imageLoadingStatus:l,onImageLoadingStatusChange:i,children:(0,c.jsx)(a.sG.span,{...o,ref:t})})});v.displayName=d;var g="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(g,n),h=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,l=(0,s.useSyncExternalStore)(u,()=>!0,()=>!1),a=r.useRef(null),c=l?(a.current||(a.current=new window.Image),a.current):null,[d,f]=r.useState(()=>b(c,e));return(0,i.N)(()=>{f(b(c,e))},[c,e]),(0,i.N)(()=>{let e=e=>()=>{f(e)};if(!c)return;let t=e("loaded"),r=e("error");return c.addEventListener("load",t),c.addEventListener("error",r),n&&(c.referrerPolicy=n),"string"==typeof o&&(c.crossOrigin=o),()=>{c.removeEventListener("load",t),c.removeEventListener("error",r)}},[c,o,n]),d}(o,f),v=(0,l.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,c.jsx)(a.sG.img,{...f,ref:t,src:o}):null});y.displayName=g;var w="AvatarFallback",x=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...l}=e,i=m(w,n),[s,u]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==i.imageLoadingStatus?(0,c.jsx)(a.sG.span,{...l,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=w;var S=v,C=y,R=x},54861:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55863:(e,t,n)=>{n.d(t,{C1:()=>b,bL:()=>x});var r=n(12115),o=n(46081),l=n(63655),i=n(95155),a="Progress",[s,u]=(0,o.A)(a),[c,d]=s(a),f=r.forwardRef((e,t)=>{var n,r,o,a;let{__scopeProgress:s,value:u=null,max:d,getValueLabel:f=m,...p}=e;(d||0===d)&&!y(d)&&console.error((n="".concat(d),r="Progress","Invalid prop `max` of value `".concat(n,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=y(d)?d:100;null===u||w(u,h)||console.error((o="".concat(u),a="Progress","Invalid prop `value` of value `".concat(o,"` supplied to `").concat(a,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let x=w(u,h)?u:null,b=g(x)?f(x,h):void 0;return(0,i.jsx)(c,{scope:s,value:x,max:h,children:(0,i.jsx)(l.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":g(x)?x:void 0,"aria-valuetext":b,role:"progressbar","data-state":v(x,h),"data-value":null!=x?x:void 0,"data-max":h,...p,ref:t})})});f.displayName=a;var p="ProgressIndicator",h=r.forwardRef((e,t)=>{var n;let{__scopeProgress:r,...o}=e,a=d(p,r);return(0,i.jsx)(l.sG.div,{"data-state":v(a.value,a.max),"data-value":null!=(n=a.value)?n:void 0,"data-max":a.max,...o,ref:t})});function m(e,t){return"".concat(Math.round(e/t*100),"%")}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function y(e){return g(e)&&!isNaN(e)&&e>0}function w(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=p;var x=f,b=h},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},71007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},85339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}}]);