{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8DACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0CACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/layout/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/sidebar.tsx <module evaluation>\",\n    \"AppSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/layout/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/sidebar.tsx\",\n    \"AppSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\"\nimport { AppSidebar } from \"@/components/layout/sidebar\"\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <div className=\"flex h-screen bg-background\">\n      {/* Desktop Sidebar */}\n      <aside className=\"hidden md:block\">\n        <AppSidebar />\n      </aside>\n      \n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Header />\n        <main className=\"flex-1 overflow-y-auto p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAM,WAAU;0BACf,cAAA,8OAAC,gIAAA,CAAA,aAAU;;;;;;;;;;0BAIb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,+HAAA,CAAA,SAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}