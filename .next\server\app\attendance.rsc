1:"$Sreact.fragment"
2:I[61321,["493","static/chunks/493-b2ac3368cd8d8b23.js","671","static/chunks/671-a5f23158e8e0d2d7.js","177","static/chunks/app/layout-0d3580470ff5d038.js"],"ThemeProvider"]
3:I[17236,["493","static/chunks/493-b2ac3368cd8d8b23.js","671","static/chunks/671-a5f23158e8e0d2d7.js","177","static/chunks/app/layout-0d3580470ff5d038.js"],"AuthProvider"]
4:I[87555,[],""]
5:I[31295,[],""]
6:I[56671,["493","static/chunks/493-b2ac3368cd8d8b23.js","671","static/chunks/671-a5f23158e8e0d2d7.js","177","static/chunks/app/layout-0d3580470ff5d038.js"],"Toaster"]
7:I[94364,["803","static/chunks/803-ef5b787ac76ce444.js","986","static/chunks/986-4c4782d01f8d38ae.js","319","static/chunks/319-91d74733620d61ae.js","493","static/chunks/493-b2ac3368cd8d8b23.js","685","static/chunks/685-b10ddf1f23860536.js","502","static/chunks/502-0e8a0bf68be887d1.js","305","static/chunks/app/(dashboard)/layout-5d522ef93106029d.js"],"AppSidebar"]
8:I[16441,["803","static/chunks/803-ef5b787ac76ce444.js","986","static/chunks/986-4c4782d01f8d38ae.js","319","static/chunks/319-91d74733620d61ae.js","493","static/chunks/493-b2ac3368cd8d8b23.js","685","static/chunks/685-b10ddf1f23860536.js","502","static/chunks/502-0e8a0bf68be887d1.js","305","static/chunks/app/(dashboard)/layout-5d522ef93106029d.js"],"Header"]
9:I[90894,[],"ClientPageRoot"]
a:I[33091,["803","static/chunks/803-ef5b787ac76ce444.js","550","static/chunks/550-42eebaaa1104251b.js","986","static/chunks/986-4c4782d01f8d38ae.js","679","static/chunks/679-635c38f840cced7c.js","319","static/chunks/319-91d74733620d61ae.js","110","static/chunks/110-19d3a006b2143e7c.js","902","static/chunks/902-6f82cc6120d3b9ed.js","498","static/chunks/498-04ba6b1c6052913f.js","16","static/chunks/app/(dashboard)/attendance/page-7bb8727015ba8925.js"],"default"]
d:I[59665,[],"OutletBoundary"]
f:I[74911,[],"AsyncMetadataOutlet"]
11:I[59665,[],"ViewportBoundary"]
13:I[59665,[],"MetadataBoundary"]
14:"$Sreact.suspense"
16:I[28393,[],""]
:HL["/_next/static/css/d7e9274c71f93616.css","style"]
0:{"P":null,"b":"ZNYS9Gx8MySfyZaXqd9LQ","p":"","c":["","attendance"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["attendance",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/d7e9274c71f93616.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":[["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}]]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","div",null,{"className":"flex h-screen bg-background","children":[["$","aside",null,{"className":"hidden md:block","children":["$","$L7",null,{}]}],["$","div",null,{"className":"flex-1 flex flex-col overflow-hidden","children":[["$","$L8",null,{}],["$","main",null,{"className":"flex-1 overflow-y-auto p-6","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:0:props:children:props:notFound:0:1:props:style","children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:1:props:style","children":404}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:2:props:style","children":["$","h2",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:0:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style","children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}]]}],{"children":["attendance",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L9",null,{"Component":"$a","searchParams":{},"params":{},"promises":["$@b","$@c"]}],null,["$","$Ld",null,{"children":["$Le",["$","$Lf",null,{"promise":"$@10"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$L11",null,{"children":"$L12"}],null],["$","$L13",null,{"children":["$","div",null,{"hidden":true,"children":["$","$14",null,{"fallback":null,"children":"$L15"}]}]}]]}],false]],"m":"$undefined","G":["$16",[]],"s":false,"S":true}
b:{}
c:"$0:f:0:1:2:children:2:children:2:children:1:props:children:0:props:params"
12:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
e:null
17:I[38175,[],"IconMark"]
10:{"metadata":[["$","title","0",{"children":"QRSAMS - QR-Code Based Student Attendance and Monitoring System"}],["$","meta","1",{"name":"description","content":"Tanauan School of Arts and Trade - Student Attendance Management System"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L17","3",{}]],"error":null,"digest":"$undefined"}
15:"$10:metadata"
