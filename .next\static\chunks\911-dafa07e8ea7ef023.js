"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[911],{381:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1243:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},7574:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("wind",[["path",{d:"M12.8 19.6A2 2 0 1 0 14 16H2",key:"148xed"}],["path",{d:"M17.5 8a2.5 2.5 0 1 1 2 4H2",key:"1u4tom"}],["path",{d:"M9.8 4.4A2 2 0 1 1 11 8H2",key:"75valh"}]])},14186:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},17580:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(12115);let n=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:n=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:u="",children:c,iconNode:s,...h}=e;return(0,r.createElement)("svg",{ref:t,...d,width:n,height:n,stroke:a,strokeWidth:i?24*Number(o)/Number(n):o,className:l("lucide",u),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...s.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(c)?c:[c]])}),i=(e,t)=>{let a=(0,r.forwardRef)((a,d)=>{let{className:i,...u}=a;return(0,r.createElement)(o,{ref:d,iconNode:t,className:l("lucide-".concat(n(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),i),...u})});return a.displayName=n(e),a}},22436:(e,t,a)=>{var r=a(12115),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=r.useState,d=r.useEffect,o=r.useLayoutEffect,i=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!n(e,a)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var a=t(),r=l({inst:{value:a,getSnapshot:t}}),n=r[0].inst,c=r[1];return o(function(){n.value=a,n.getSnapshot=t,u(n)&&c({inst:n})},[e,a,t]),d(function(){return u(n)&&c({inst:n}),e(function(){u(n)&&c({inst:n})})},[e]),i(a),a};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},23861:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},26386:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("cloud-rain",[["path",{d:"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242",key:"1pljnt"}],["path",{d:"M16 14v6",key:"1j4efv"}],["path",{d:"M8 14v6",key:"17c4r9"}],["path",{d:"M12 16v6",key:"c8a4gj"}]])},42851:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("cloud-snow",[["path",{d:"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242",key:"1pljnt"}],["path",{d:"M8 15h.01",key:"a7atzg"}],["path",{d:"M8 19h.01",key:"puxtts"}],["path",{d:"M12 17h.01",key:"p32p05"}],["path",{d:"M12 21h.01",key:"h35vbk"}],["path",{d:"M16 15h.01",key:"rnfrdf"}],["path",{d:"M16 19h.01",key:"1vcnzz"}]])},48691:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]])},49033:(e,t,a)=>{e.exports=a(22436)},50589:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},53904:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},55863:(e,t,a)=>{a.d(t,{C1:()=>g,bL:()=>M});var r=a(12115),n=a(46081),l=a(63655),d=a(95155),o="Progress",[i,u]=(0,n.A)(o),[c,s]=i(o),h=r.forwardRef((e,t)=>{var a,r,n,o;let{__scopeProgress:i,value:u=null,max:s,getValueLabel:h=v,...p}=e;(s||0===s)&&!m(s)&&console.error((a="".concat(s),r="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let y=m(s)?s:100;null===u||A(u,y)||console.error((n="".concat(u),o="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(o,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let M=A(u,y)?u:null,g=f(M)?h(M,y):void 0;return(0,d.jsx)(c,{scope:i,value:M,max:y,children:(0,d.jsx)(l.sG.div,{"aria-valuemax":y,"aria-valuemin":0,"aria-valuenow":f(M)?M:void 0,"aria-valuetext":g,role:"progressbar","data-state":k(M,y),"data-value":null!=M?M:void 0,"data-max":y,...p,ref:t})})});h.displayName=o;var p="ProgressIndicator",y=r.forwardRef((e,t)=>{var a;let{__scopeProgress:r,...n}=e,o=s(p,r);return(0,d.jsx)(l.sG.div,{"data-state":k(o.value,o.max),"data-value":null!=(a=o.value)?a:void 0,"data-max":o.max,...n,ref:t})});function v(e,t){return"".concat(Math.round(e/t*100),"%")}function k(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function f(e){return"number"==typeof e}function m(e){return f(e)&&!isNaN(e)&&e>0}function A(e,t){return f(e)&&!isNaN(e)&&e<=t&&e>=0}y.displayName=p;var M=h,g=y},62098:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},74466:(e,t,a)=>{a.d(t,{F:()=>d});var r=a(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.$,d=(e,t)=>a=>{var r;if((null==t?void 0:t.variants)==null)return l(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:d,defaultVariants:o}=t,i=Object.keys(d).map(e=>{let t=null==a?void 0:a[e],r=null==o?void 0:o[e];if(null===t)return null;let l=n(t)||n(r);return d[e][l]}),u=a&&Object.entries(a).reduce((e,t)=>{let[a,r]=t;return void 0===r||(e[a]=r),e},{});return l(e,i,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:a,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...o,...u}[t]):({...o,...u})[t]===a})?[...e,a,r]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}},79397:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},91788:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},97939:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("qr-code",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},98611:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]])}}]);