"use strict";exports.id=113,exports.ids=[113],exports.modules={3589:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},11096:(a,b,c)=>{c.d(b,{H4:()=>y,_V:()=>x,bL:()=>w});var d=c(43210),e=c(11273),f=c(13495),g=c(66156),h=c(14163),i=c(57379);function j(){return()=>{}}var k=c(60687),l="Avatar",[m,n]=(0,e.A)(l),[o,p]=m(l),q=d.forwardRef((a,b)=>{let{__scopeAvatar:c,...e}=a,[f,g]=d.useState("idle");return(0,k.jsx)(o,{scope:c,imageLoadingStatus:f,onImageLoadingStatusChange:g,children:(0,k.jsx)(h.sG.span,{...e,ref:b})})});q.displayName=l;var r="AvatarImage",s=d.forwardRef((a,b)=>{let{__scopeAvatar:c,src:e,onLoadingStatusChange:l=()=>{},...m}=a,n=p(r,c),o=function(a,{referrerPolicy:b,crossOrigin:c}){let e=(0,i.useSyncExternalStore)(j,()=>!0,()=>!1),f=d.useRef(null),h=e?(f.current||(f.current=new window.Image),f.current):null,[k,l]=d.useState(()=>v(h,a));return(0,g.N)(()=>{l(v(h,a))},[h,a]),(0,g.N)(()=>{let a=a=>()=>{l(a)};if(!h)return;let d=a("loaded"),e=a("error");return h.addEventListener("load",d),h.addEventListener("error",e),b&&(h.referrerPolicy=b),"string"==typeof c&&(h.crossOrigin=c),()=>{h.removeEventListener("load",d),h.removeEventListener("error",e)}},[h,c,b]),k}(e,m),q=(0,f.c)(a=>{l(a),n.onImageLoadingStatusChange(a)});return(0,g.N)(()=>{"idle"!==o&&q(o)},[o,q]),"loaded"===o?(0,k.jsx)(h.sG.img,{...m,ref:b,src:e}):null});s.displayName=r;var t="AvatarFallback",u=d.forwardRef((a,b)=>{let{__scopeAvatar:c,delayMs:e,...f}=a,g=p(t,c),[i,j]=d.useState(void 0===e);return d.useEffect(()=>{if(void 0!==e){let a=window.setTimeout(()=>j(!0),e);return()=>window.clearTimeout(a)}},[e]),i&&"loaded"!==g.imageLoadingStatus?(0,k.jsx)(h.sG.span,{...f,ref:b}):null});function v(a,b){return a?b?(a.src!==b&&(a.src=b),a.complete&&a.naturalWidth>0?"loaded":"loading"):"error":"idle"}u.displayName=t;var w=q,x=s,y=u},13964:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},18853:(a,b,c)=>{c.d(b,{X:()=>f});var d=c(43210),e=c(66156);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},25177:(a,b,c)=>{c.d(b,{C1:()=>v,bL:()=>u});var d=c(43210),e=c(11273),f=c(14163),g=c(60687),h="Progress",[i,j]=(0,e.A)(h),[k,l]=i(h),m=d.forwardRef((a,b)=>{var c,d;let{__scopeProgress:e,value:h=null,max:i,getValueLabel:j=p,...l}=a;(i||0===i)&&!s(i)&&console.error((c=`${i}`,`Invalid prop \`max\` of value \`${c}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=s(i)?i:100;null===h||t(h,m)||console.error((d=`${h}`,`Invalid prop \`value\` of value \`${d}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let n=t(h,m)?h:null,o=r(n)?j(n,m):void 0;return(0,g.jsx)(k,{scope:e,value:n,max:m,children:(0,g.jsx)(f.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":r(n)?n:void 0,"aria-valuetext":o,role:"progressbar","data-state":q(n,m),"data-value":n??void 0,"data-max":m,...l,ref:b})})});m.displayName=h;var n="ProgressIndicator",o=d.forwardRef((a,b)=>{let{__scopeProgress:c,...d}=a,e=l(n,c);return(0,g.jsx)(f.sG.div,{"data-state":q(e.value,e.max),"data-value":e.value??void 0,"data-max":e.max,...d,ref:b})});function p(a,b){return`${Math.round(a/b*100)}%`}function q(a,b){return null==a?"indeterminate":a===b?"complete":"loading"}function r(a){return"number"==typeof a}function s(a){return r(a)&&!isNaN(a)&&a>0}function t(a,b){return r(a)&&!isNaN(a)&&a<=b&&a>=0}o.displayName=n;var u=m,v=o},25911:(a,b,c)=>{c.d(b,{UC:()=>aG,In:()=>aE,q7:()=>aI,VF:()=>aK,p4:()=>aJ,ZL:()=>aF,bL:()=>aB,wn:()=>aM,PP:()=>aL,l9:()=>aC,WT:()=>aD,LM:()=>aH});var d=c(43210),e=c(51215),f=c(67969),g=c(70569),h=c(9510),i=c(98599),j=c(11273),k=c(43),l=c(31355),m=c(1359),n=c(32547),o=c(96963),p=c(55509),q=c(25028),r=c(14163),s=c(8730),t=c(13495),u=c(65551),v=c(66156),w=c(83721),x=c(60687),y=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});d.forwardRef((a,b)=>(0,x.jsx)(r.sG.span,{...a,ref:b,style:{...y,...a.style}})).displayName="VisuallyHidden";var z=c(63376),A=c(42247),B=[" ","Enter","ArrowUp","ArrowDown"],C=[" ","Enter"],D="Select",[E,F,G]=(0,h.N)(D),[H,I]=(0,j.A)(D,[G,p.Bk]),J=(0,p.Bk)(),[K,L]=H(D),[M,N]=H(D),O=a=>{let{__scopeSelect:b,children:c,open:e,defaultOpen:f,onOpenChange:g,value:h,defaultValue:i,onValueChange:j,dir:l,name:m,autoComplete:n,disabled:q,required:r,form:s}=a,t=J(b),[v,w]=d.useState(null),[y,z]=d.useState(null),[A,B]=d.useState(!1),C=(0,k.jH)(l),[F,G]=(0,u.i)({prop:e,defaultProp:f??!1,onChange:g,caller:D}),[H,I]=(0,u.i)({prop:h,defaultProp:i,onChange:j,caller:D}),L=d.useRef(null),N=!v||s||!!v.closest("form"),[O,P]=d.useState(new Set),Q=Array.from(O).map(a=>a.props.value).join(";");return(0,x.jsx)(p.bL,{...t,children:(0,x.jsxs)(K,{required:r,scope:b,trigger:v,onTriggerChange:w,valueNode:y,onValueNodeChange:z,valueNodeHasChildren:A,onValueNodeHasChildrenChange:B,contentId:(0,o.B)(),value:H,onValueChange:I,open:F,onOpenChange:G,dir:C,triggerPointerDownPosRef:L,disabled:q,children:[(0,x.jsx)(E.Provider,{scope:b,children:(0,x.jsx)(M,{scope:a.__scopeSelect,onNativeOptionAdd:d.useCallback(a=>{P(b=>new Set(b).add(a))},[]),onNativeOptionRemove:d.useCallback(a=>{P(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),N?(0,x.jsxs)(ax,{"aria-hidden":!0,required:r,tabIndex:-1,name:m,autoComplete:n,value:H,onChange:a=>I(a.target.value),disabled:q,form:s,children:[void 0===H?(0,x.jsx)("option",{value:""}):null,Array.from(O)]},Q):null]})})};O.displayName=D;var P="SelectTrigger",Q=d.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:e=!1,...f}=a,h=J(c),j=L(P,c),k=j.disabled||e,l=(0,i.s)(b,j.onTriggerChange),m=F(c),n=d.useRef("touch"),[o,q,s]=az(a=>{let b=m().filter(a=>!a.disabled),c=b.find(a=>a.value===j.value),d=aA(b,a,c);void 0!==d&&j.onValueChange(d.value)}),t=a=>{k||(j.onOpenChange(!0),s()),a&&(j.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,x.jsx)(p.Mz,{asChild:!0,...h,children:(0,x.jsx)(r.sG.button,{type:"button",role:"combobox","aria-controls":j.contentId,"aria-expanded":j.open,"aria-required":j.required,"aria-autocomplete":"none",dir:j.dir,"data-state":j.open?"open":"closed",disabled:k,"data-disabled":k?"":void 0,"data-placeholder":ay(j.value)?"":void 0,...f,ref:l,onClick:(0,g.m)(f.onClick,a=>{a.currentTarget.focus(),"mouse"!==n.current&&t(a)}),onPointerDown:(0,g.m)(f.onPointerDown,a=>{n.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(t(a),a.preventDefault())}),onKeyDown:(0,g.m)(f.onKeyDown,a=>{let b=""!==o.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||q(a.key),(!b||" "!==a.key)&&B.includes(a.key)&&(t(),a.preventDefault())})})})});Q.displayName=P;var R="SelectValue",S=d.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:g="",...h}=a,j=L(R,c),{onValueNodeHasChildrenChange:k}=j,l=void 0!==f,m=(0,i.s)(b,j.onValueNodeChange);return(0,v.N)(()=>{k(l)},[k,l]),(0,x.jsx)(r.sG.span,{...h,ref:m,style:{pointerEvents:"none"},children:ay(j.value)?(0,x.jsx)(x.Fragment,{children:g}):f})});S.displayName=R;var T=d.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,x.jsx)(r.sG.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});T.displayName="SelectIcon";var U=a=>(0,x.jsx)(q.Z,{asChild:!0,...a});U.displayName="SelectPortal";var V="SelectContent",W=d.forwardRef((a,b)=>{let c=L(V,a.__scopeSelect),[f,g]=d.useState();return((0,v.N)(()=>{g(new DocumentFragment)},[]),c.open)?(0,x.jsx)($,{...a,ref:b}):f?e.createPortal((0,x.jsx)(X,{scope:a.__scopeSelect,children:(0,x.jsx)(E.Slot,{scope:a.__scopeSelect,children:(0,x.jsx)("div",{children:a.children})})}),f):null});W.displayName=V;var[X,Y]=H(V),Z=(0,s.TL)("SelectContent.RemoveScroll"),$=d.forwardRef((a,b)=>{let{__scopeSelect:c,position:e="item-aligned",onCloseAutoFocus:f,onEscapeKeyDown:h,onPointerDownOutside:j,side:k,sideOffset:o,align:p,alignOffset:q,arrowPadding:r,collisionBoundary:s,collisionPadding:t,sticky:u,hideWhenDetached:v,avoidCollisions:w,...y}=a,B=L(V,c),[C,D]=d.useState(null),[E,G]=d.useState(null),H=(0,i.s)(b,a=>D(a)),[I,J]=d.useState(null),[K,M]=d.useState(null),N=F(c),[O,P]=d.useState(!1),Q=d.useRef(!1);d.useEffect(()=>{if(C)return(0,z.Eq)(C)},[C]),(0,m.Oh)();let R=d.useCallback(a=>{let[b,...c]=N().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&E&&(E.scrollTop=0),c===d&&E&&(E.scrollTop=E.scrollHeight),c?.focus(),document.activeElement!==e))return},[N,E]),S=d.useCallback(()=>R([I,C]),[R,I,C]);d.useEffect(()=>{O&&S()},[O,S]);let{onOpenChange:T,triggerPointerDownPosRef:U}=B;d.useEffect(()=>{if(C){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(U.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():C.contains(c.target)||T(!1),document.removeEventListener("pointermove",b),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[C,T,U]),d.useEffect(()=>{let a=()=>T(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[T]);let[W,Y]=az(a=>{let b=N().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=aA(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),$=d.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==B.value&&B.value===b||d)&&(J(a),d&&(Q.current=!0))},[B.value]),ab=d.useCallback(()=>C?.focus(),[C]),ac=d.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==B.value&&B.value===b||d)&&M(a)},[B.value]),ad="popper"===e?aa:_,ae=ad===aa?{side:k,sideOffset:o,align:p,alignOffset:q,arrowPadding:r,collisionBoundary:s,collisionPadding:t,sticky:u,hideWhenDetached:v,avoidCollisions:w}:{};return(0,x.jsx)(X,{scope:c,content:C,viewport:E,onViewportChange:G,itemRefCallback:$,selectedItem:I,onItemLeave:ab,itemTextRefCallback:ac,focusSelectedItem:S,selectedItemText:K,position:e,isPositioned:O,searchRef:W,children:(0,x.jsx)(A.A,{as:Z,allowPinchZoom:!0,children:(0,x.jsx)(n.n,{asChild:!0,trapped:B.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:(0,g.m)(f,a=>{B.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,x.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:h,onPointerDownOutside:j,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>B.onOpenChange(!1),children:(0,x.jsx)(ad,{role:"listbox",id:B.contentId,"data-state":B.open?"open":"closed",dir:B.dir,onContextMenu:a=>a.preventDefault(),...y,...ae,onPlaced:()=>P(!0),ref:H,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:(0,g.m)(y.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||Y(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=N().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>R(b)),a.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var _=d.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:e,...g}=a,h=L(V,c),j=Y(V,c),[k,l]=d.useState(null),[m,n]=d.useState(null),o=(0,i.s)(b,a=>n(a)),p=F(c),q=d.useRef(!1),s=d.useRef(!0),{viewport:t,selectedItem:u,selectedItemText:w,focusSelectedItem:y}=j,z=d.useCallback(()=>{if(h.trigger&&h.valueNode&&k&&m&&t&&u&&w){let a=h.trigger.getBoundingClientRect(),b=m.getBoundingClientRect(),c=h.valueNode.getBoundingClientRect(),d=w.getBoundingClientRect();if("rtl"!==h.dir){let e=d.left-b.left,g=c.left-e,h=a.left-g,i=a.width+h,j=Math.max(i,b.width),l=window.innerWidth-10,m=(0,f.q)(g,[10,Math.max(10,l-j)]);k.style.minWidth=i+"px",k.style.left=m+"px"}else{let e=b.right-d.right,g=window.innerWidth-c.right-e,h=window.innerWidth-a.right-g,i=a.width+h,j=Math.max(i,b.width),l=window.innerWidth-10,m=(0,f.q)(g,[10,Math.max(10,l-j)]);k.style.minWidth=i+"px",k.style.right=m+"px"}let g=p(),i=window.innerHeight-20,j=t.scrollHeight,l=window.getComputedStyle(m),n=parseInt(l.borderTopWidth,10),o=parseInt(l.paddingTop,10),r=parseInt(l.borderBottomWidth,10),s=n+o+j+parseInt(l.paddingBottom,10)+r,v=Math.min(5*u.offsetHeight,s),x=window.getComputedStyle(t),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=u.offsetHeight/2,C=n+o+(u.offsetTop+B);if(C<=A){let a=g.length>0&&u===g[g.length-1].ref.current;k.style.bottom="0px";let b=Math.max(i-A,B+(a?z:0)+(m.clientHeight-t.offsetTop-t.offsetHeight)+r);k.style.height=C+b+"px"}else{let a=g.length>0&&u===g[0].ref.current;k.style.top="0px";let b=Math.max(A,n+t.offsetTop+(a?y:0)+B);k.style.height=b+(s-C)+"px",t.scrollTop=C-A+t.offsetTop}k.style.margin="10px 0",k.style.minHeight=v+"px",k.style.maxHeight=i+"px",e?.(),requestAnimationFrame(()=>q.current=!0)}},[p,h.trigger,h.valueNode,k,m,t,u,w,h.dir,e]);(0,v.N)(()=>z(),[z]);let[A,B]=d.useState();(0,v.N)(()=>{m&&B(window.getComputedStyle(m).zIndex)},[m]);let C=d.useCallback(a=>{a&&!0===s.current&&(z(),y?.(),s.current=!1)},[z,y]);return(0,x.jsx)(ab,{scope:c,contentWrapper:k,shouldExpandOnScrollRef:q,onScrollButtonChange:C,children:(0,x.jsx)("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:A},children:(0,x.jsx)(r.sG.div,{...g,ref:o,style:{boxSizing:"border-box",maxHeight:"100%",...g.style}})})})});_.displayName="SelectItemAlignedPosition";var aa=d.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,g=J(c);return(0,x.jsx)(p.UC,{...g,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});aa.displayName="SelectPopperPosition";var[ab,ac]=H(V,{}),ad="SelectViewport",ae=d.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:e,...f}=a,h=Y(ad,c),j=ac(ad,c),k=(0,i.s)(b,h.onViewportChange),l=d.useRef(0);return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:e}),(0,x.jsx)(E.Slot,{scope:c,children:(0,x.jsx)(r.sG.div,{"data-radix-select-viewport":"",role:"presentation",...f,ref:k,style:{position:"relative",flex:1,overflow:"hidden auto",...f.style},onScroll:(0,g.m)(f.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=j;if(d?.current&&c){let a=Math.abs(l.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}l.current=b.scrollTop})})})]})});ae.displayName=ad;var af="SelectGroup",[ag,ah]=H(af);d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=(0,o.B)();return(0,x.jsx)(ag,{scope:c,id:e,children:(0,x.jsx)(r.sG.div,{role:"group","aria-labelledby":e,...d,ref:b})})}).displayName=af;var ai="SelectLabel";d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=ah(ai,c);return(0,x.jsx)(r.sG.div,{id:e.id,...d,ref:b})}).displayName=ai;var aj="SelectItem",[ak,al]=H(aj),am=d.forwardRef((a,b)=>{let{__scopeSelect:c,value:e,disabled:f=!1,textValue:h,...j}=a,k=L(aj,c),l=Y(aj,c),m=k.value===e,[n,p]=d.useState(h??""),[q,s]=d.useState(!1),t=(0,i.s)(b,a=>l.itemRefCallback?.(a,e,f)),u=(0,o.B)(),v=d.useRef("touch"),w=()=>{f||(k.onValueChange(e),k.onOpenChange(!1))};if(""===e)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,x.jsx)(ak,{scope:c,value:e,disabled:f,textId:u,isSelected:m,onItemTextChange:d.useCallback(a=>{p(b=>b||(a?.textContent??"").trim())},[]),children:(0,x.jsx)(E.ItemSlot,{scope:c,value:e,disabled:f,textValue:n,children:(0,x.jsx)(r.sG.div,{role:"option","aria-labelledby":u,"data-highlighted":q?"":void 0,"aria-selected":m&&q,"data-state":m?"checked":"unchecked","aria-disabled":f||void 0,"data-disabled":f?"":void 0,tabIndex:f?void 0:-1,...j,ref:t,onFocus:(0,g.m)(j.onFocus,()=>s(!0)),onBlur:(0,g.m)(j.onBlur,()=>s(!1)),onClick:(0,g.m)(j.onClick,()=>{"mouse"!==v.current&&w()}),onPointerUp:(0,g.m)(j.onPointerUp,()=>{"mouse"===v.current&&w()}),onPointerDown:(0,g.m)(j.onPointerDown,a=>{v.current=a.pointerType}),onPointerMove:(0,g.m)(j.onPointerMove,a=>{v.current=a.pointerType,f?l.onItemLeave?.():"mouse"===v.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,g.m)(j.onPointerLeave,a=>{a.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:(0,g.m)(j.onKeyDown,a=>{(l.searchRef?.current===""||" "!==a.key)&&(C.includes(a.key)&&w()," "===a.key&&a.preventDefault())})})})})});am.displayName=aj;var an="SelectItemText",ao=d.forwardRef((a,b)=>{let{__scopeSelect:c,className:f,style:g,...h}=a,j=L(an,c),k=Y(an,c),l=al(an,c),m=N(an,c),[n,o]=d.useState(null),p=(0,i.s)(b,a=>o(a),l.onItemTextChange,a=>k.itemTextRefCallback?.(a,l.value,l.disabled)),q=n?.textContent,s=d.useMemo(()=>(0,x.jsx)("option",{value:l.value,disabled:l.disabled,children:q},l.value),[l.disabled,l.value,q]),{onNativeOptionAdd:t,onNativeOptionRemove:u}=m;return(0,v.N)(()=>(t(s),()=>u(s)),[t,u,s]),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(r.sG.span,{id:l.textId,...h,ref:p}),l.isSelected&&j.valueNode&&!j.valueNodeHasChildren?e.createPortal(h.children,j.valueNode):null]})});ao.displayName=an;var ap="SelectItemIndicator",aq=d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return al(ap,c).isSelected?(0,x.jsx)(r.sG.span,{"aria-hidden":!0,...d,ref:b}):null});aq.displayName=ap;var ar="SelectScrollUpButton",as=d.forwardRef((a,b)=>{let c=Y(ar,a.__scopeSelect),e=ac(ar,a.__scopeSelect),[f,g]=d.useState(!1),h=(0,i.s)(b,e.onScrollButtonChange);return(0,v.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){g(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),f?(0,x.jsx)(av,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});as.displayName=ar;var at="SelectScrollDownButton",au=d.forwardRef((a,b)=>{let c=Y(at,a.__scopeSelect),e=ac(at,a.__scopeSelect),[f,g]=d.useState(!1),h=(0,i.s)(b,e.onScrollButtonChange);return(0,v.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;g(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),f?(0,x.jsx)(av,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});au.displayName=at;var av=d.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:e,...f}=a,h=Y("SelectScrollButton",c),i=d.useRef(null),j=F(c),k=d.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return d.useEffect(()=>()=>k(),[k]),(0,v.N)(()=>{let a=j().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[j]),(0,x.jsx)(r.sG.div,{"aria-hidden":!0,...f,ref:b,style:{flexShrink:0,...f.style},onPointerDown:(0,g.m)(f.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(e,50))}),onPointerMove:(0,g.m)(f.onPointerMove,()=>{h.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(e,50))}),onPointerLeave:(0,g.m)(f.onPointerLeave,()=>{k()})})});d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,x.jsx)(r.sG.div,{"aria-hidden":!0,...d,ref:b})}).displayName="SelectSeparator";var aw="SelectArrow";d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=J(c),f=L(aw,c),g=Y(aw,c);return f.open&&"popper"===g.position?(0,x.jsx)(p.i3,{...e,...d,ref:b}):null}).displayName=aw;var ax=d.forwardRef(({__scopeSelect:a,value:b,...c},e)=>{let f=d.useRef(null),g=(0,i.s)(e,f),h=(0,w.Z)(b);return d.useEffect(()=>{let a=f.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(h!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[h,b]),(0,x.jsx)(r.sG.select,{...c,style:{...y,...c.style},ref:g,defaultValue:b})});function ay(a){return""===a||void 0===a}function az(a){let b=(0,t.c)(a),c=d.useRef(""),e=d.useRef(0),f=d.useCallback(a=>{let d=c.current+a;b(d),function a(b){c.current=b,window.clearTimeout(e.current),""!==b&&(e.current=window.setTimeout(()=>a(""),1e3))}(d)},[b]),g=d.useCallback(()=>{c.current="",window.clearTimeout(e.current)},[]);return d.useEffect(()=>()=>window.clearTimeout(e.current),[]),[c,f,g]}function aA(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}ax.displayName="SelectBubbleInput";var aB=O,aC=Q,aD=S,aE=T,aF=U,aG=W,aH=ae,aI=am,aJ=ao,aK=aq,aL=as,aM=au},31158:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},35071:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},48730:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},53332:(a,b,c)=>{var d=c(43210),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},55509:(a,b,c)=>{c.d(b,{Mz:()=>a1,i3:()=>a3,UC:()=>a2,bL:()=>a0,Bk:()=>aM});var d=c(43210);let e=["top","right","bottom","left"],f=Math.min,g=Math.max,h=Math.round,i=Math.floor,j=a=>({x:a,y:a}),k={left:"right",right:"left",bottom:"top",top:"bottom"},l={start:"end",end:"start"};function m(a,b){return"function"==typeof a?a(b):a}function n(a){return a.split("-")[0]}function o(a){return a.split("-")[1]}function p(a){return"x"===a?"y":"x"}function q(a){return"y"===a?"height":"width"}let r=new Set(["top","bottom"]);function s(a){return r.has(n(a))?"y":"x"}function t(a){return a.replace(/start|end/g,a=>l[a])}let u=["left","right"],v=["right","left"],w=["top","bottom"],x=["bottom","top"];function y(a){return a.replace(/left|right|bottom|top/g,a=>k[a])}function z(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function A(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function B(a,b,c){let d,{reference:e,floating:f}=a,g=s(b),h=p(s(b)),i=q(h),j=n(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,r=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(o(b)){case"start":d[h]-=r*(c&&k?-1:1);break;case"end":d[h]+=r*(c&&k?-1:1)}return d}let C=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=B(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=B(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function D(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:n=!1,padding:o=0}=m(b,a),p=z(o),q=h[n?"floating"===l?"reference":"floating":l],r=A(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(q)))||c?q:q.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),s="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,t=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),u=await (null==f.isElement?void 0:f.isElement(t))&&await (null==f.getScale?void 0:f.getScale(t))||{x:1,y:1},v=A(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:s,offsetParent:t,strategy:i}):s);return{top:(r.top-v.top+p.top)/u.y,bottom:(v.bottom-r.bottom+p.bottom)/u.y,left:(r.left-v.left+p.left)/u.x,right:(v.right-r.right+p.right)/u.x}}function E(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function F(a){return e.some(b=>a[b]>=0)}let G=new Set(["left","top"]);async function H(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=n(c),h=o(c),i="y"===s(c),j=G.has(g)?-1:1,k=f&&i?-1:1,l=m(b,a),{mainAxis:p,crossAxis:q,alignmentAxis:r}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof r&&(q="end"===h?-1*r:r),i?{x:q*k,y:p*j}:{x:p*j,y:q*k}}function I(){return"undefined"!=typeof window}function J(a){return M(a)?(a.nodeName||"").toLowerCase():"#document"}function K(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function L(a){var b;return null==(b=(M(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function M(a){return!!I()&&(a instanceof Node||a instanceof K(a).Node)}function N(a){return!!I()&&(a instanceof Element||a instanceof K(a).Element)}function O(a){return!!I()&&(a instanceof HTMLElement||a instanceof K(a).HTMLElement)}function P(a){return!!I()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof K(a).ShadowRoot)}let Q=new Set(["inline","contents"]);function R(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aa(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!Q.has(e)}let S=new Set(["table","td","th"]),T=[":popover-open",":modal"];function U(a){return T.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let V=["transform","translate","scale","rotate","perspective"],W=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function Y(a){let b=Z(),c=N(a)?aa(a):a;return V.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||W.some(a=>(c.willChange||"").includes(a))||X.some(a=>(c.contain||"").includes(a))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let $=new Set(["html","body","#document"]);function _(a){return $.has(J(a))}function aa(a){return K(a).getComputedStyle(a)}function ab(a){return N(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function ac(a){if("html"===J(a))return a;let b=a.assignedSlot||a.parentNode||P(a)&&a.host||L(a);return P(b)?b.host:b}function ad(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=ac(b);return _(c)?b.ownerDocument?b.ownerDocument.body:b.body:O(c)&&R(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=K(e);if(f){let a=ae(g);return b.concat(g,g.visualViewport||[],R(e)?e:[],a&&c?ad(a):[])}return b.concat(e,ad(e,[],c))}function ae(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function af(a){let b=aa(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=O(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,i=h(c)!==f||h(d)!==g;return i&&(c=f,d=g),{width:c,height:d,$:i}}function ag(a){return N(a)?a:a.contextElement}function ah(a){let b=ag(a);if(!O(b))return j(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=af(b),g=(f?h(c.width):c.width)/d,i=(f?h(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),i&&Number.isFinite(i)||(i=1),{x:g,y:i}}let ai=j(0);function aj(a){let b=K(a);return Z()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:ai}function ak(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=ag(a),h=j(1);b&&(d?N(d)&&(h=ah(d)):h=ah(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===K(g))&&e)?aj(g):j(0),k=(f.left+i.x)/h.x,l=(f.top+i.y)/h.y,m=f.width/h.x,n=f.height/h.y;if(g){let a=K(g),b=d&&N(d)?K(d):d,c=a,e=ae(c);for(;e&&d&&b!==c;){let a=ah(e),b=e.getBoundingClientRect(),d=aa(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;k*=a.x,l*=a.y,m*=a.x,n*=a.y,k+=f,l+=g,e=ae(c=K(e))}}return A({width:m,height:n,x:k,y:l})}function al(a,b){let c=ab(a).scrollLeft;return b?b.left+c:ak(L(a)).left+c}function am(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:al(a,d)),y:d.top+b.scrollTop}}let an=new Set(["absolute","fixed"]);function ao(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=K(a),d=L(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=Z();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=L(a),c=ab(a),d=a.ownerDocument.body,e=g(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=g(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),h=-c.scrollLeft+al(a),i=-c.scrollTop;return"rtl"===aa(d).direction&&(h+=g(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:h,y:i}}(L(a));else if(N(b))d=function(a,b){let c=ak(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=O(a)?ah(a):j(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aj(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return A(d)}function ap(a){return"static"===aa(a).position}function aq(a,b){if(!O(a)||"fixed"===aa(a).position)return null;if(b)return b(a);let c=a.offsetParent;return L(a)===c&&(c=c.ownerDocument.body),c}function ar(a,b){var c;let d=K(a);if(U(a))return d;if(!O(a)){let b=ac(a);for(;b&&!_(b);){if(N(b)&&!ap(b))return b;b=ac(b)}return d}let e=aq(a,b);for(;e&&(c=e,S.has(J(c)))&&ap(e);)e=aq(e,b);return e&&_(e)&&ap(e)&&!Y(e)?d:e||function(a){let b=ac(a);for(;O(b)&&!_(b);){if(Y(b))return b;if(U(b))break;b=ac(b)}return null}(a)||d}let as=async function(a){let b=this.getOffsetParent||ar,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=O(b),e=L(b),f="fixed"===c,g=ak(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=j(0);if(d||!d&&!f)if(("body"!==J(b)||R(e))&&(h=ab(b)),d){let a=ak(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=al(e));f&&!d&&e&&(i.x=al(e));let k=!e||d||f?j(0):am(e,h);return{x:g.left+h.scrollLeft-i.x-k.x,y:g.top+h.scrollTop-i.y-k.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},at={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=L(d),h=!!b&&U(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},k=j(1),l=j(0),m=O(d);if((m||!m&&!f)&&(("body"!==J(d)||R(g))&&(i=ab(d)),O(d))){let a=ak(d);k=ah(d),l.x=a.x+d.clientLeft,l.y=a.y+d.clientTop}let n=!g||m||f?j(0):am(g,i,!0);return{width:c.width*k.x,height:c.height*k.y,x:c.x*k.x-i.scrollLeft*k.x+l.x+n.x,y:c.y*k.y-i.scrollTop*k.y+l.y+n.y}},getDocumentElement:L,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,h=[..."clippingAncestors"===c?U(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=ad(a,[],!1).filter(a=>N(a)&&"body"!==J(a)),e=null,f="fixed"===aa(a).position,g=f?ac(a):a;for(;N(g)&&!_(g);){let b=aa(g),c=Y(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&an.has(e.position)||R(g)&&!c&&function a(b,c){let d=ac(b);return!(d===c||!N(d)||_(d))&&("fixed"===aa(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=ac(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],i=h[0],j=h.reduce((a,c)=>{let d=ao(b,c,e);return a.top=g(d.top,a.top),a.right=f(d.right,a.right),a.bottom=f(d.bottom,a.bottom),a.left=g(d.left,a.left),a},ao(b,i,e));return{width:j.right-j.left,height:j.bottom-j.top,x:j.left,y:j.top}},getOffsetParent:ar,getElementRects:as,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=af(a);return{width:b,height:c}},getScale:ah,isElement:N,isRTL:function(a){return"rtl"===aa(a).direction}};function au(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let av=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:h,platform:i,elements:j,middlewareData:k}=b,{element:l,padding:n=0}=m(a,b)||{};if(null==l)return{};let r=z(n),t={x:c,y:d},u=p(s(e)),v=q(u),w=await i.getDimensions(l),x="y"===u,y=x?"clientHeight":"clientWidth",A=h.reference[v]+h.reference[u]-t[u]-h.floating[v],B=t[u]-h.reference[u],C=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l)),D=C?C[y]:0;D&&await (null==i.isElement?void 0:i.isElement(C))||(D=j.floating[y]||h.floating[v]);let E=D/2-w[v]/2-1,F=f(r[x?"top":"left"],E),G=f(r[x?"bottom":"right"],E),H=D-w[v]-G,I=D/2-w[v]/2+(A/2-B/2),J=g(F,f(I,H)),K=!k.arrow&&null!=o(e)&&I!==J&&h.reference[v]/2-(I<F?F:G)-w[v]/2<0,L=K?I<F?I-F:I-H:0;return{[u]:t[u]+L,data:{[u]:J,centerOffset:I-J-L,...K&&{alignmentOffset:L}},reset:K}}});var aw=c(51215),ax="undefined"!=typeof document?d.useLayoutEffect:function(){};function ay(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!ay(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!ay(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function az(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function aA(a,b){let c=az(a);return Math.round(b*c)/c}function aB(a){let b=d.useRef(a);return ax(()=>{b.current=a}),b}var aC=c(14163),aD=c(60687),aE=d.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,aD.jsx)(aC.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,aD.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aE.displayName="Arrow";var aF=c(98599),aG=c(11273),aH=c(13495),aI=c(66156),aJ=c(18853),aK="Popper",[aL,aM]=(0,aG.A)(aK),[aN,aO]=aL(aK),aP=a=>{let{__scopePopper:b,children:c}=a,[e,f]=d.useState(null);return(0,aD.jsx)(aN,{scope:b,anchor:e,onAnchorChange:f,children:c})};aP.displayName=aK;var aQ="PopperAnchor",aR=d.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:e,...f}=a,g=aO(aQ,c),h=d.useRef(null),i=(0,aF.s)(b,h);return d.useEffect(()=>{g.onAnchorChange(e?.current||h.current)}),e?null:(0,aD.jsx)(aC.sG.div,{...f,ref:i})});aR.displayName=aQ;var aS="PopperContent",[aT,aU]=aL(aS),aV=d.forwardRef((a,b)=>{let{__scopePopper:c,side:e="bottom",sideOffset:h=0,align:j="center",alignOffset:k=0,arrowPadding:l=0,avoidCollisions:r=!0,collisionBoundary:z=[],collisionPadding:A=0,sticky:B="partial",hideWhenDetached:I=!1,updatePositionStrategy:J="optimized",onPlaced:K,...M}=a,N=aO(aS,c),[O,P]=d.useState(null),Q=(0,aF.s)(b,a=>P(a)),[R,S]=d.useState(null),T=(0,aJ.X)(R),U=T?.width??0,V=T?.height??0,W="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},X=Array.isArray(z)?z:[z],Y=X.length>0,Z={padding:W,boundary:X.filter(aZ),altBoundary:Y},{refs:$,floatingStyles:_,placement:aa,isPositioned:ab,middlewareData:ac}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:e=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=d.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=d.useState(e);ay(n,e)||o(e);let[p,q]=d.useState(null),[r,s]=d.useState(null),t=d.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=d.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=d.useRef(null),y=d.useRef(null),z=d.useRef(l),A=null!=j,B=aB(j),D=aB(f),E=aB(k),F=d.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:at,...c},f={...e.platform,_c:d};return C(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!ay(z.current,b)&&(z.current=b,aw.flushSync(()=>{m(b)}))})},[n,b,c,D,E]);ax(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let G=d.useRef(!1);ax(()=>(G.current=!0,()=>{G.current=!1}),[]),ax(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,F);F()}},[v,w,F,B,A]);let H=d.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),I=d.useMemo(()=>({reference:v,floating:w}),[v,w]),J=d.useMemo(()=>{let a={position:c,left:0,top:0};if(!I.floating)return a;let b=aA(I.floating,l.x),d=aA(I.floating,l.y);return i?{...a,transform:"translate("+b+"px, "+d+"px)",...az(I.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,i,I.floating,l.x,l.y]);return d.useMemo(()=>({...l,update:F,refs:H,elements:I,floatingStyles:J}),[l,F,H,I,J])}({strategy:"fixed",placement:e+("center"!==j?"-"+j:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:h=!0,ancestorResize:j=!0,elementResize:k="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:m=!1}=d,n=ag(a),o=h||j?[...n?ad(n):[],...ad(b)]:[];o.forEach(a=>{h&&a.addEventListener("scroll",c,{passive:!0}),j&&a.addEventListener("resize",c)});let p=n&&l?function(a,b){let c,d=null,e=L(a);function h(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function j(k,l){void 0===k&&(k=!1),void 0===l&&(l=1),h();let m=a.getBoundingClientRect(),{left:n,top:o,width:p,height:q}=m;if(k||b(),!p||!q)return;let r=i(o),s=i(e.clientWidth-(n+p)),t={rootMargin:-r+"px "+-s+"px "+-i(e.clientHeight-(o+q))+"px "+-i(n)+"px",threshold:g(0,f(1,l))||1},u=!0;function v(b){let d=b[0].intersectionRatio;if(d!==l){if(!u)return j();d?j(!1,d):c=setTimeout(()=>{j(!1,1e-7)},1e3)}1!==d||au(m,a.getBoundingClientRect())||j(),u=!1}try{d=new IntersectionObserver(v,{...t,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(v,t)}d.observe(a)}(!0),h}(n,c):null,q=-1,r=null;k&&(r=new ResizeObserver(a=>{let[d]=a;d&&d.target===n&&r&&(r.unobserve(b),cancelAnimationFrame(q),q=requestAnimationFrame(()=>{var a;null==(a=r)||a.observe(b)})),c()}),n&&!m&&r.observe(n),r.observe(b));let s=m?ak(a):null;return m&&function b(){let d=ak(a);s&&!au(s,d)&&c(),s=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;o.forEach(a=>{h&&a.removeEventListener("scroll",c),j&&a.removeEventListener("resize",c)}),null==p||p(),null==(a=r)||a.disconnect(),r=null,m&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===J}),elements:{reference:N.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await H(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,void 0]}))({mainAxis:h+V,alignmentAxis:k}),r&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:h=!0,crossAxis:i=!1,limiter:j={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...k}=m(a,b),l={x:c,y:d},o=await D(b,k),q=s(n(e)),r=p(q),t=l[r],u=l[q];if(h){let a="y"===r?"top":"left",b="y"===r?"bottom":"right",c=t+o[a],d=t-o[b];t=g(c,f(t,d))}if(i){let a="y"===q?"top":"left",b="y"===q?"bottom":"right",c=u+o[a],d=u-o[b];u=g(c,f(u,d))}let v=j.fn({...b,[r]:t,[q]:u});return{...v,data:{x:v.x-c,y:v.y-d,enabled:{[r]:h,[q]:i}}}}}}(a),options:[a,void 0]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===B?{...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=m(a,b),k={x:c,y:d},l=s(e),o=p(l),q=k[o],r=k[l],t=m(h,b),u="number"==typeof t?{mainAxis:t,crossAxis:0}:{mainAxis:0,crossAxis:0,...t};if(i){let a="y"===o?"height":"width",b=f.reference[o]-f.floating[a]+u.mainAxis,c=f.reference[o]+f.reference[a]-u.mainAxis;q<b?q=b:q>c&&(q=c)}if(j){var v,w;let a="y"===o?"width":"height",b=G.has(n(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(v=g.offset)?void 0:v[l])||0)+(b?0:u.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(w=g.offset)?void 0:w[l])||0)-(b?u.crossAxis:0);r<c?r=c:r>d&&(r=d)}return{[o]:q,[l]:r}}}}(void 0),options:[void 0,void 0]}:void 0,...Z}),r&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:r}=b,{mainAxis:z=!0,crossAxis:A=!0,fallbackPlacements:B,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:F=!0,...G}=m(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let H=n(h),I=s(k),J=n(k)===k,K=await (null==l.isRTL?void 0:l.isRTL(r.floating)),L=B||(J||!F?[y(k)]:function(a){let b=y(a);return[t(a),b,t(b)]}(k)),M="none"!==E;!B&&M&&L.push(...function(a,b,c,d){let e=o(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?v:u;return b?u:v;case"left":case"right":return b?w:x;default:return[]}}(n(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(t)))),f}(k,F,E,K));let N=[k,...L],O=await D(b,G),P=[],Q=(null==(d=i.flip)?void 0:d.overflows)||[];if(z&&P.push(O[H]),A){let a=function(a,b,c){void 0===c&&(c=!1);let d=o(a),e=p(s(a)),f=q(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=y(g)),[g,y(g)]}(h,j,K);P.push(O[a[0]],O[a[1]])}if(Q=[...Q,{placement:h,overflows:P}],!P.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=N[a];if(b&&("alignment"!==A||I===s(b)||Q.every(a=>s(a.placement)!==I||a.overflows[0]>0)))return{data:{index:a,overflows:Q},reset:{placement:b}};let c=null==(f=Q.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(C){case"bestFit":{let a=null==(g=Q.filter(a=>{if(M){let b=s(a.placement);return b===I||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,void 0]}))({...Z}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,h,{placement:i,rects:j,platform:k,elements:l}=b,{apply:p=()=>{},...q}=m(a,b),r=await D(b,q),t=n(i),u=o(i),v="y"===s(i),{width:w,height:x}=j.floating;"top"===t||"bottom"===t?(e=t,h=u===(await (null==k.isRTL?void 0:k.isRTL(l.floating))?"start":"end")?"left":"right"):(h=t,e="end"===u?"top":"bottom");let y=x-r.top-r.bottom,z=w-r.left-r.right,A=f(x-r[e],y),B=f(w-r[h],z),C=!b.middlewareData.shift,E=A,F=B;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(F=z),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(E=y),C&&!u){let a=g(r.left,0),b=g(r.right,0),c=g(r.top,0),d=g(r.bottom,0);v?F=w-2*(0!==a||0!==b?a+b:g(r.left,r.right)):E=x-2*(0!==c||0!==d?c+d:g(r.top,r.bottom))}await p({...b,availableWidth:F,availableHeight:E});let G=await k.getDimensions(l.floating);return w!==G.width||x!==G.height?{reset:{rects:!0}}:{}}}}(a),options:[a,void 0]}))({...Z,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),R&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?av({element:c.current,padding:d}).fn(b):{}:c?av({element:c,padding:d}).fn(b):{}}}))(a),options:[a,void 0]}))({element:R,padding:l}),a$({arrowWidth:U,arrowHeight:V}),I&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=m(a,b);switch(d){case"referenceHidden":{let a=E(await D(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:F(a)}}}case"escaped":{let a=E(await D(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:F(a)}}}default:return{}}}}}(a),options:[a,void 0]}))({strategy:"referenceHidden",...Z})]}),[ae,af]=a_(aa),ah=(0,aH.c)(K);(0,aI.N)(()=>{ab&&ah?.()},[ab,ah]);let ai=ac.arrow?.x,aj=ac.arrow?.y,al=ac.arrow?.centerOffset!==0,[am,an]=d.useState();return(0,aI.N)(()=>{O&&an(window.getComputedStyle(O).zIndex)},[O]),(0,aD.jsx)("div",{ref:$.setFloating,"data-radix-popper-content-wrapper":"",style:{..._,transform:ab?_.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:am,"--radix-popper-transform-origin":[ac.transformOrigin?.x,ac.transformOrigin?.y].join(" "),...ac.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,aD.jsx)(aT,{scope:c,placedSide:ae,onArrowChange:S,arrowX:ai,arrowY:aj,shouldHideArrow:al,children:(0,aD.jsx)(aC.sG.div,{"data-side":ae,"data-align":af,...M,ref:Q,style:{...M.style,animation:ab?void 0:"none"}})})})});aV.displayName=aS;var aW="PopperArrow",aX={top:"bottom",right:"left",bottom:"top",left:"right"},aY=d.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=aU(aW,c),f=aX[e.placedSide];return(0,aD.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,aD.jsx)(aE,{...d,ref:b,style:{...d.style,display:"block"}})})});function aZ(a){return null!==a}aY.displayName=aW;var a$=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=a_(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function a_(a){let[b,c="center"]=a.split("-");return[b,c]}var a0=aP,a1=aR,a2=aV,a3=aY},57379:(a,b,c)=>{a.exports=c(53332)},58869:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},78148:(a,b,c)=>{c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},78272:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},83721:(a,b,c)=>{c.d(b,{Z:()=>e});var d=c(43210);function e(a){let b=d.useRef({value:a,previous:a});return d.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}},93613:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};