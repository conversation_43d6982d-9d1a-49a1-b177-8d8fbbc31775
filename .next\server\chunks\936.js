exports.id=936,exports.ids=[936],exports.modules={175:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if(!a||"object"!=typeof a)return!1;let b=Object.getPrototypeOf(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&"[object Object]"===Object.prototype.toString.call(a)}},1640:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObject=function(a){return null!==a&&("object"==typeof a||"function"==typeof a)}},1706:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.eq=function(a,b){return a===b||Number.isNaN(a)&&Number.isNaN(b)}},2041:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(43210),e=c(49605),f=c(54024),g=["axis","item"],h=(0,d.forwardRef)((a,b)=>d.createElement(f.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:g,tooltipPayloadSearcher:e.uN,categoricalChartProps:a,ref:b}))},2264:(a,b,c)=>{"use strict";c.d(b,{L:()=>J});var d=c(43210),e=c(54186),f=c(51426),g=c(24028),h=c(83409),i=c(21080),j=c(43209),k=c(94728),l=c(12128),m=["children"];function n(){return(n=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var o={width:"100%",height:"100%"},p=(0,d.forwardRef)((a,b)=>{var c,e,h=(0,f.yi)(),j=(0,f.rY)(),k=(0,g.$)();if(!(0,l.F)(h)||!(0,l.F)(j))return null;var{children:m,otherAttributes:p,title:q,desc:r}=a;return c="number"==typeof p.tabIndex?p.tabIndex:k?0:void 0,e="string"==typeof p.role?p.role:k?"application":void 0,d.createElement(i.u,n({},p,{title:q,desc:r,role:e,tabIndex:c,width:h,height:j,style:o,ref:b}),m)}),q=a=>{var{children:b}=a,c=(0,j.G)(k.U);if(!c)return null;var{width:e,height:f,y:g,x:h}=c;return d.createElement(i.u,{width:e,height:f,x:h,y:g},b)},r=(0,d.forwardRef)((a,b)=>{var{children:c}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,m);return(0,h.r)()?d.createElement(q,null,c):d.createElement(p,n({ref:b},e),c)}),s=c(49384),t=c(17118),u=c(85407),v=c(98009),w=c(11281),x=c(86445);c(52693);var y=c(44919),z=c(34258),A=c(97711),B=c(14221);function C(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var D=(0,d.forwardRef)((a,b)=>{var{children:c,className:e,height:f,onClick:g,onContextMenu:h,onDoubleClick:i,onMouseDown:k,onMouseEnter:l,onMouseLeave:m,onMouseMove:n,onMouseUp:o,onTouchEnd:p,onTouchMove:q,onTouchStart:r,style:D,width:E}=a,F=(0,j.j)(),[G,H]=(0,d.useState)(null),[I,J]=(0,d.useState)(null);(0,v.l3)();var K=function(){(0,j.j)();var[a,b]=(0,d.useState)(null);return(0,j.G)(x.et),b}(),L=(0,d.useCallback)(a=>{K(a),"function"==typeof b&&b(a),H(a),J(a)},[K,b,H,J]),M=(0,d.useCallback)(a=>{F((0,u.ky)(a)),F((0,y.y)({handler:g,reactEvent:a}))},[F,g]),N=(0,d.useCallback)(a=>{F((0,u.dj)(a)),F((0,y.y)({handler:l,reactEvent:a}))},[F,l]),O=(0,d.useCallback)(a=>{F((0,t.xS)()),F((0,y.y)({handler:m,reactEvent:a}))},[F,m]),P=(0,d.useCallback)(a=>{F((0,u.dj)(a)),F((0,y.y)({handler:n,reactEvent:a}))},[F,n]),Q=(0,d.useCallback)(()=>{F((0,w.Ru)())},[F]),R=(0,d.useCallback)(a=>{F((0,w.uZ)(a.key))},[F]),S=(0,d.useCallback)(a=>{F((0,y.y)({handler:h,reactEvent:a}))},[F,h]),T=(0,d.useCallback)(a=>{F((0,y.y)({handler:i,reactEvent:a}))},[F,i]),U=(0,d.useCallback)(a=>{F((0,y.y)({handler:k,reactEvent:a}))},[F,k]),V=(0,d.useCallback)(a=>{F((0,y.y)({handler:o,reactEvent:a}))},[F,o]),W=(0,d.useCallback)(a=>{F((0,y.y)({handler:r,reactEvent:a}))},[F,r]),X=(0,d.useCallback)(a=>{F((0,z.e)(a)),F((0,y.y)({handler:q,reactEvent:a}))},[F,q]),Y=(0,d.useCallback)(a=>{F((0,y.y)({handler:p,reactEvent:a}))},[F,p]);return d.createElement(A.$.Provider,{value:G},d.createElement(B.t.Provider,{value:I},d.createElement("div",{className:(0,s.$)("recharts-wrapper",e),style:function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?C(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):C(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({position:"relative",cursor:"default",width:E,height:f},D),onClick:M,onContextMenu:S,onDoubleClick:T,onFocus:Q,onKeyDown:R,onMouseDown:U,onMouseEnter:N,onMouseLeave:O,onMouseMove:P,onMouseUp:V,onTouchEnd:Y,onTouchMove:X,onTouchStart:W,ref:L},c)))}),E=c(22989),F=c(27934),G=(0,d.createContext)(void 0),H=a=>{var{children:b}=a,[c]=(0,d.useState)("".concat((0,E.NF)("recharts"),"-clip")),e=(0,F.oM)();if(null==e)return null;var{x:f,y:g,width:h,height:i}=e;return d.createElement(G.Provider,{value:c},d.createElement("defs",null,d.createElement("clipPath",{id:c},d.createElement("rect",{x:f,y:g,height:i,width:h}))),b)},I=["children","className","width","height","style","compact","title","desc"],J=(0,d.forwardRef)((a,b)=>{var{children:c,className:f,width:g,height:h,style:i,compact:j,title:k,desc:l}=a,m=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,I),n=(0,e.J9)(m,!1);return j?d.createElement(r,{otherAttributes:n,title:k,desc:l},c):d.createElement(D,{className:f,style:i,width:g,height:h,onClick:a.onClick,onMouseLeave:a.onMouseLeave,onMouseEnter:a.onMouseEnter,onMouseMove:a.onMouseMove,onMouseDown:a.onMouseDown,onMouseUp:a.onMouseUp,onContextMenu:a.onContextMenu,onDoubleClick:a.onDoubleClick,onTouchStart:a.onTouchStart,onTouchMove:a.onTouchMove,onTouchEnd:a.onTouchEnd},d.createElement(r,{otherAttributes:n,title:k,desc:l,ref:b},d.createElement(H,null,c)))})},3567:(a,b,c)=>{"use strict";var d=c(43210);"function"==typeof Object.is&&Object.is,d.useSyncExternalStore,d.useRef,d.useEffect,d.useMemo,d.useDebugValue},4057:(a,b,c)=>{"use strict";c.d(b,{QQ:()=>e,VU:()=>g,XC:()=>j,_U:()=>i,j2:()=>h});var d=c(43210),e=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],f=["points","pathLength"],g={svg:["viewBox","children"],polygon:f,polyline:f},h=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],i=(a,b)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var c=a;if((0,d.isValidElement)(a)&&(c=a.props),"object"!=typeof c&&"function"!=typeof c)return null;var e={};return Object.keys(c).forEach(a=>{h.includes(a)&&(e[a]=b||(b=>c[a](c,b)))}),e},j=(a,b,c)=>{if(null===a||"object"!=typeof a&&"function"!=typeof a)return null;var d=null;return Object.keys(a).forEach(e=>{var f=a[e];h.includes(e)&&"function"==typeof f&&(d||(d={}),d[e]=a=>(f(b,c,a),null))}),d}},4236:(a,b,c)=>{"use strict";c.d(b,{G9:()=>l,_S:()=>m,pU:()=>n,zk:()=>k});var d=c(43210),e=c(75787),f=c(83409),g=["children"],h=()=>{},i=(0,d.createContext)({addErrorBar:h,removeErrorBar:h}),j=(0,d.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function k(a){var{children:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,g);return d.createElement(j.Provider,{value:c},b)}var l=()=>(0,d.useContext)(j),m=a=>{var{children:b,xAxisId:c,yAxisId:g,zAxisId:h,dataKey:j,data:k,stackId:l,hide:m,type:n,barSize:o}=a,[p,q]=d.useState([]),r=(0,d.useCallback)(a=>{q(b=>[...b,a])},[q]),s=(0,d.useCallback)(a=>{q(b=>b.filter(b=>b!==a))},[q]),t=(0,f.r)();return d.createElement(i.Provider,{value:{addErrorBar:r,removeErrorBar:s}},d.createElement(e.p,{type:n,data:k,xAxisId:c,yAxisId:g,zAxisId:h,dataKey:j,errorBars:p,stackId:l,hide:m,barSize:o,isPanorama:t}),b)};function n(a){var{addErrorBar:b,removeErrorBar:c}=(0,d.useContext)(i);return null}},5338:(a,b,c)=>{"use strict";c.d(b,{CA:()=>p,MC:()=>j,QG:()=>o,Vi:()=>i,cU:()=>k,fR:()=>l});var d=c(76067),e=c(71392);function f(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function g(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?f(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):f(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var h=(0,d.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(a,b){a.xAxis[b.payload.id]=(0,e.h4)(b.payload)},removeXAxis(a,b){delete a.xAxis[b.payload.id]},addYAxis(a,b){a.yAxis[b.payload.id]=(0,e.h4)(b.payload)},removeYAxis(a,b){delete a.yAxis[b.payload.id]},addZAxis(a,b){a.zAxis[b.payload.id]=(0,e.h4)(b.payload)},removeZAxis(a,b){delete a.zAxis[b.payload.id]},updateYAxisWidth(a,b){var{id:c,width:d}=b.payload;a.yAxis[c]&&(a.yAxis[c]=g(g({},a.yAxis[c]),{},{width:d}))}}}),{addXAxis:i,removeXAxis:j,addYAxis:k,removeYAxis:l,addZAxis:m,removeZAxis:n,updateYAxisWidth:o}=h.actions,p=h.reducer},5664:(a,b,c)=>{a.exports=c(87509).get},6895:(a,b,c)=>{"use strict";c(3567)},8920:(a,b,c)=>{"use strict";c.d(b,{Be:()=>q,Cv:()=>w,D0:()=>y,Gl:()=>r,Dc:()=>x});var d=c(84648),e=c(86445),f=c(76966),g=c(19335),h=c(22989),i={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},j={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},k=c(53416),l=c(51426),m={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:i.angleAxisId,includeHidden:!1,name:void 0,reversed:i.reversed,scale:i.scale,tick:i.tick,tickCount:void 0,ticks:void 0,type:i.type,unit:void 0},n={allowDataOverflow:j.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:j.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:j.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:j.scale,tick:j.tick,tickCount:j.tickCount,ticks:void 0,type:j.type,unit:void 0},o={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:i.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:i.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:i.scale,tick:i.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},p={allowDataOverflow:j.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:j.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:j.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:j.scale,tick:j.tick,tickCount:j.tickCount,ticks:void 0,type:"category",unit:void 0},q=(a,b)=>null!=a.polarAxis.angleAxis[b]?a.polarAxis.angleAxis[b]:"radial"===a.layout.layoutType?o:m,r=(a,b)=>null!=a.polarAxis.radiusAxis[b]?a.polarAxis.radiusAxis[b]:"radial"===a.layout.layoutType?p:n,s=a=>a.polarOptions,t=(0,d.Mz)([e.Lp,e.A$,f.HZ],g.lY),u=(0,d.Mz)([s,t],(a,b)=>{if(null!=a)return(0,h.F4)(a.innerRadius,b,0)}),v=(0,d.Mz)([s,t],(a,b)=>{if(null!=a)return(0,h.F4)(a.outerRadius,b,.8*b)}),w=(0,d.Mz)([s],a=>{if(null==a)return[0,0];var{startAngle:b,endAngle:c}=a;return[b,c]});(0,d.Mz)([q,w],k.I);var x=(0,d.Mz)([t,u,v],(a,b,c)=>{if(null!=a&&null!=b&&null!=c)return[b,c]});(0,d.Mz)([r,x],k.I);var y=(0,d.Mz)([l.fz,s,u,v,e.Lp,e.A$],(a,b,c,d,e,f)=>{if(("centric"===a||"radial"===a)&&null!=b&&null!=c&&null!=d){var{cx:g,cy:i,startAngle:j,endAngle:k}=b;return{cx:(0,h.F4)(g,e,e/2),cy:(0,h.F4)(i,f,f/2),innerRadius:c,outerRadius:d,startAngle:j,endAngle:k,clockWise:!1}}})},9474:(a,b,c)=>{a.exports=c(33731).last},10521:(a,b,c)=>{"use strict";c.d(b,{R:()=>d});var d=function(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e]}},10687:(a,b,c)=>{a.exports=c(75446).sortBy},10907:(a,b,c)=>{"use strict";var d=c(43210),e=c(57379),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},10919:(a,b,c)=>{"use strict";c.d(b,{i:()=>E});var d=c(43210);let e=Math.cos,f=Math.sin,g=Math.sqrt,h=Math.PI,i=2*h,j={draw(a,b){let c=g(b/h);a.moveTo(c,0),a.arc(0,0,c,0,i)}},k=g(1/3),l=2*k,m=f(h/10)/f(7*h/10),n=f(i/10)*m,o=-e(i/10)*m,p=g(3),q=g(3)/2,r=1/g(12),s=(r/2+1)*3;var t=c(22786),u=c(15606);g(3),g(3);var v=c(49384),w=c(54186),x=c(22989),y=["type","size","sizeType"];function z(){return(z=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function A(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function B(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?A(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):A(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var C={symbolCircle:j,symbolCross:{draw(a,b){let c=g(b/5)/2;a.moveTo(-3*c,-c),a.lineTo(-c,-c),a.lineTo(-c,-3*c),a.lineTo(c,-3*c),a.lineTo(c,-c),a.lineTo(3*c,-c),a.lineTo(3*c,c),a.lineTo(c,c),a.lineTo(c,3*c),a.lineTo(-c,3*c),a.lineTo(-c,c),a.lineTo(-3*c,c),a.closePath()}},symbolDiamond:{draw(a,b){let c=g(b/l),d=c*k;a.moveTo(0,-c),a.lineTo(d,0),a.lineTo(0,c),a.lineTo(-d,0),a.closePath()}},symbolSquare:{draw(a,b){let c=g(b),d=-c/2;a.rect(d,d,c,c)}},symbolStar:{draw(a,b){let c=g(.8908130915292852*b),d=n*c,h=o*c;a.moveTo(0,-c),a.lineTo(d,h);for(let b=1;b<5;++b){let g=i*b/5,j=e(g),k=f(g);a.lineTo(k*c,-j*c),a.lineTo(j*d-k*h,k*d+j*h)}a.closePath()}},symbolTriangle:{draw(a,b){let c=-g(b/(3*p));a.moveTo(0,2*c),a.lineTo(-p*c,-c),a.lineTo(p*c,-c),a.closePath()}},symbolWye:{draw(a,b){let c=g(b/s),d=c/2,e=c*r,f=c*r+c,h=-d;a.moveTo(d,e),a.lineTo(d,f),a.lineTo(h,f),a.lineTo(-.5*d-q*e,q*d+-.5*e),a.lineTo(-.5*d-q*f,q*d+-.5*f),a.lineTo(-.5*h-q*f,q*h+-.5*f),a.lineTo(-.5*d+q*e,-.5*e-q*d),a.lineTo(-.5*d+q*f,-.5*f-q*d),a.lineTo(-.5*h+q*f,-.5*f-q*h),a.closePath()}}},D=Math.PI/180,E=a=>{var{type:b="circle",size:c=64,sizeType:e="area"}=a,f=B(B({},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,y)),{},{type:b,size:c,sizeType:e}),{className:g,cx:h,cy:i}=f,k=(0,w.J9)(f,!0);return h===+h&&i===+i&&c===+c?d.createElement("path",z({},k,{className:(0,v.$)("recharts-symbols",g),transform:"translate(".concat(h,", ").concat(i,")"),d:(()=>{var a=C["symbol".concat((0,x.Zb)(b))]||j;return(function(a,b){let c=null,d=(0,u.i)(e);function e(){let e;if(c||(c=e=d()),a.apply(this,arguments).draw(c,+b.apply(this,arguments)),e)return c=null,e+""||null}return a="function"==typeof a?a:(0,t.A)(a||j),b="function"==typeof b?b:(0,t.A)(void 0===b?64:+b),e.type=function(b){return arguments.length?(a="function"==typeof b?b:(0,t.A)(b),e):a},e.size=function(a){return arguments.length?(b="function"==typeof a?a:(0,t.A)(+a),e):b},e.context=function(a){return arguments.length?(c=null==a?null:a,e):c},e})().type(a).size(((a,b,c)=>{if("area"===b)return a;switch(c){case"cross":return 5*a*a/9;case"diamond":return .5*a*a/Math.sqrt(3);case"square":return a*a;case"star":var d=18*D;return 1.25*a*a*(Math.tan(d)-Math.tan(2*d)*Math.tan(d)**2);case"triangle":return Math.sqrt(3)*a*a/4;case"wye":return(21-10*Math.sqrt(3))*a*a/8;default:return Math.PI*a*a/4}})(c,e,b))()})()})):null};E.registerSymbol=(a,b)=>{C["symbol".concat((0,x.Zb)(a))]=b}},11117:a=>{"use strict";var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),new d().__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},11208:(a,b,c)=>{"use strict";function d(a){return`Minified Redux error #${a}; visit https://redux.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}c.d(b,{HY:()=>j,Qd:()=>h,Tw:()=>l,Zz:()=>k,ve:()=>m,y$:()=>i});var e="function"==typeof Symbol&&Symbol.observable||"@@observable",f=()=>Math.random().toString(36).substring(7).split("").join("."),g={INIT:`@@redux/INIT${f()}`,REPLACE:`@@redux/REPLACE${f()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${f()}`};function h(a){if("object"!=typeof a||null===a)return!1;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b||null===Object.getPrototypeOf(a)}function i(a,b,c){if("function"!=typeof a)throw Error(d(2));if("function"==typeof b&&"function"==typeof c||"function"==typeof c&&"function"==typeof arguments[3])throw Error(d(0));if("function"==typeof b&&void 0===c&&(c=b,b=void 0),void 0!==c){if("function"!=typeof c)throw Error(d(1));return c(i)(a,b)}let f=a,j=b,k=new Map,l=k,m=0,n=!1;function o(){l===k&&(l=new Map,k.forEach((a,b)=>{l.set(b,a)}))}function p(){if(n)throw Error(d(3));return j}function q(a){if("function"!=typeof a)throw Error(d(4));if(n)throw Error(d(5));let b=!0;o();let c=m++;return l.set(c,a),function(){if(b){if(n)throw Error(d(6));b=!1,o(),l.delete(c),k=null}}}function r(a){if(!h(a))throw Error(d(7));if(void 0===a.type)throw Error(d(8));if("string"!=typeof a.type)throw Error(d(17));if(n)throw Error(d(9));try{n=!0,j=f(j,a)}finally{n=!1}return(k=l).forEach(a=>{a()}),a}return r({type:g.INIT}),{dispatch:r,subscribe:q,getState:p,replaceReducer:function(a){if("function"!=typeof a)throw Error(d(10));f=a,r({type:g.REPLACE})},[e]:function(){return{subscribe(a){if("object"!=typeof a||null===a)throw Error(d(11));function b(){a.next&&a.next(p())}return b(),{unsubscribe:q(b)}},[e](){return this}}}}}function j(a){let b,c=Object.keys(a),e={};for(let b=0;b<c.length;b++){let d=c[b];"function"==typeof a[d]&&(e[d]=a[d])}let f=Object.keys(e);try{Object.keys(e).forEach(a=>{let b=e[a];if(void 0===b(void 0,{type:g.INIT}))throw Error(d(12));if(void 0===b(void 0,{type:g.PROBE_UNKNOWN_ACTION()}))throw Error(d(13))})}catch(a){b=a}return function(a={},c){if(b)throw b;let g=!1,h={};for(let b=0;b<f.length;b++){let i=f[b],j=e[i],k=a[i],l=j(k,c);if(void 0===l)throw c&&c.type,Error(d(14));h[i]=l,g=g||l!==k}return(g=g||f.length!==Object.keys(a).length)?h:a}}function k(...a){return 0===a.length?a=>a:1===a.length?a[0]:a.reduce((a,b)=>(...c)=>a(b(...c)))}function l(...a){return b=>(c,e)=>{let f=b(c,e),g=()=>{throw Error(d(15))},h={getState:f.getState,dispatch:(a,...b)=>g(a,...b)};return g=k(...a.map(a=>a(h)))(f.dispatch),{...f,dispatch:g}}}function m(a){return h(a)&&"type"in a&&"string"==typeof a.type}},11281:(a,b,c)=>{"use strict";c.d(b,{$7:()=>l,Ru:()=>k,uZ:()=>j});var d=c(76067),e=c(17118),f=c(69009),g=c(21426),h=c(85621),i=c(97371),j=(0,d.VP)("keyDown"),k=(0,d.VP)("focus"),l=(0,d.Nc)();l.startListening({actionCreator:j,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip,j=a.payload;if("ArrowRight"===j||"ArrowLeft"===j||"Enter"===j){var k=Number((0,i.P)(d,(0,f.n4)(c))),l=(0,f.R4)(c);if("Enter"===j){var m=(0,g.pg)(c,"axis","hover",String(d.index));b.dispatch((0,e.o4)({active:!d.active,activeIndex:d.index,activeDataKey:d.dataKey,activeCoordinate:m}));return}var n=k+("ArrowRight"===j?1:-1)*("left-to-right"===(0,h._y)(c)?1:-1);if(null!=l&&!(n>=l.length)&&!(n<0)){var o=(0,g.pg)(c,"axis","hover",String(n));b.dispatch((0,e.o4)({active:!0,activeIndex:n.toString(),activeDataKey:void 0,activeCoordinate:o}))}}}}}),l.startListening({actionCreator:k,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip;if(!d.active&&null==d.index){var f=(0,g.pg)(c,"axis","hover",String("0"));b.dispatch((0,e.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:f}))}}}})},12128:(a,b,c)=>{"use strict";function d(a){return Number.isFinite(a)}function e(a){return"number"==typeof a&&a>0&&Number.isFinite(a)}c.d(b,{F:()=>e,H:()=>d})},12640:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},12728:(a,b,c)=>{a.exports=c(92292).isEqual},13420:(a,b,c)=>{"use strict";c.d(b,{TK:()=>h});var d=c(43210),e=c(64267),f=c(43209),g=c(83409),h=a=>{var{chartData:b}=a,c=(0,f.j)(),h=(0,g.r)();return(0,d.useEffect)(()=>h?()=>{}:(c((0,e.hq)(b)),()=>{c((0,e.hq)(void 0))}),[b,c,h]),null}},14221:(a,b,c)=>{"use strict";c.d(b,{M:()=>f,t:()=>e});var d=c(43210),e=(0,d.createContext)(null),f=()=>(0,d.useContext)(e)},14454:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(1640),f=c(23457),g=c(1706);function h(a,b,c,d){if(b===a)return!0;switch(typeof b){case"object":return function(a,b,c,d){if(null==b)return!0;if(Array.isArray(b))return i(a,b,c,d);if(b instanceof Map){var e=a,g=b,h=c,k=d;if(0===g.size)return!0;if(!(e instanceof Map))return!1;for(let[a,b]of g.entries())if(!1===h(e.get(a),b,a,e,g,k))return!1;return!0}if(b instanceof Set)return j(a,b,c,d);let l=Object.keys(b);if(null==a)return 0===l.length;if(0===l.length)return!0;if(d&&d.has(b))return d.get(b)===a;d&&d.set(b,a);try{for(let e=0;e<l.length;e++){let g=l[e];if(!f.isPrimitive(a)&&!(g in a)||void 0===b[g]&&void 0!==a[g]||null===b[g]&&null!==a[g]||!c(a[g],b[g],g,a,b,d))return!1}return!0}finally{d&&d.delete(b)}}(a,b,c,d);case"function":if(Object.keys(b).length>0)return h(a,{...b},c,d);return g.eq(a,b);default:if(!e.isObject(a))return g.eq(a,b);if("string"==typeof b)return""===b;return!0}}function i(a,b,c,d){if(0===b.length)return!0;if(!Array.isArray(a))return!1;let e=new Set;for(let f=0;f<b.length;f++){let g=b[f],h=!1;for(let i=0;i<a.length;i++){if(e.has(i))continue;let j=a[i],k=!1;if(c(j,g,f,a,b,d)&&(k=!0),k){e.add(i),h=!0;break}}if(!h)return!1}return!0}function j(a,b,c,d){return 0===b.size||a instanceof Set&&i([...a],[...b],c,d)}b.isMatchWith=function(a,b,c){return"function"!=typeof c?d.isMatch(a,b):h(a,b,function a(b,d,e,f,g,i){let j=c(b,d,e,f,g,i);return void 0!==j?!!j:h(b,d,a,i)},new Map)},b.isSetMatch=j},14956:(a,b,c)=>{"use strict";c.d(b,{A:()=>g,_:()=>h}),c(43210);var d=c(83409),e=c(51426),f=c(43209);function g(a){var{legendPayload:b}=a;return(0,f.j)(),(0,d.r)(),null}function h(a){var{legendPayload:b}=a;return(0,f.j)(),(0,f.G)(e.fz),null}c(53044)},15202:(a,b,c)=>{"use strict";c.d(b,{s:()=>E});var d=c(43210),e=c(51215),f=c(14221),g=c(49384),h=c(21080),i=c(10919),j=c(4057);function k(){return(k=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function l(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function m(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class n extends d.PureComponent{renderIcon(a,b){var{inactiveColor:c}=this.props,e=32/6,f=32/3,g=a.inactive?c:a.color,h=null!=b?b:a.type;if("none"===h)return null;if("plainline"===h)return d.createElement("line",{strokeWidth:4,fill:"none",stroke:g,strokeDasharray:a.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===h)return d.createElement("path",{strokeWidth:4,fill:"none",stroke:g,d:"M0,".concat(16,"h").concat(f,"\n            A").concat(e,",").concat(e,",0,1,1,").concat(2*f,",").concat(16,"\n            H").concat(32,"M").concat(2*f,",").concat(16,"\n            A").concat(e,",").concat(e,",0,1,1,").concat(f,",").concat(16),className:"recharts-legend-icon"});if("rect"===h)return d.createElement("path",{stroke:"none",fill:g,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(d.isValidElement(a.legendIcon)){var j=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l(Object(c),!0).forEach(function(b){m(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return delete j.legendIcon,d.cloneElement(a.legendIcon,j)}return d.createElement(i.i,{fill:g,cx:16,cy:16,size:32,sizeType:"diameter",type:h})}renderItems(){var{payload:a,iconSize:b,layout:c,formatter:e,inactiveColor:f,iconType:i}=this.props,l={x:0,y:0,width:32,height:32},m={display:"horizontal"===c?"inline-block":"block",marginRight:10},n={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map((a,c)=>{var o=a.formatter||e,p=(0,g.$)({"recharts-legend-item":!0,["legend-item-".concat(c)]:!0,inactive:a.inactive});if("none"===a.type)return null;var q=a.inactive?f:a.color,r=o?o(a.value,a,c):a.value;return d.createElement("li",k({className:p,style:m,key:"legend-item-".concat(c)},(0,j.XC)(this.props,a,c)),d.createElement(h.u,{width:b,height:b,viewBox:l,style:n,"aria-label":"".concat(r," legend icon")},this.renderIcon(a,i)),d.createElement("span",{className:"recharts-legend-item-text",style:{color:q}},r))})}render(){var{payload:a,layout:b,align:c}=this.props;return a&&a.length?d.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===b?c:"left"}},this.renderItems()):null}}m(n,"displayName","Legend"),m(n,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var o=c(22989),p=c(45796),q=c(43209),r=c(23337),s=c(68392),t=c(51426);c(53044);var u=["contextPayload"];function v(){return(v=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function w(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function x(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?w(Object(c),!0).forEach(function(b){y(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):w(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function y(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function z(a){return a.value}function A(a){var{contextPayload:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,u),e=(0,p.s)(b,a.payloadUniqBy,z),f=x(x({},c),{},{payload:e});return d.isValidElement(a.content)?d.cloneElement(a.content,f):"function"==typeof a.content?d.createElement(a.content,f):d.createElement(n,f)}function B(a){return(0,q.j)(),null}function C(a){return(0,q.j)(),null}function D(a){var b=(0,q.G)(r.g0),c=(0,f.M)(),g=(0,t.Kp)(),{width:h,height:i,wrapperStyle:j,portal:k}=a,[l,m]=(0,s.V)([b]),n=(0,t.yi)(),o=(0,t.rY)(),p=n-(g.left||0)-(g.right||0),u=E.getWidthOrHeight(a.layout,i,h,p),w=k?j:x(x({position:"absolute",width:(null==u?void 0:u.width)||h||"auto",height:(null==u?void 0:u.height)||i||"auto"},function(a,b,c,d,e,f){var g,h,{layout:i,align:j,verticalAlign:k}=b;return a&&(void 0!==a.left&&null!==a.left||void 0!==a.right&&null!==a.right)||(g="center"===j&&"vertical"===i?{left:((d||0)-f.width)/2}:"right"===j?{right:c&&c.right||0}:{left:c&&c.left||0}),a&&(void 0!==a.top&&null!==a.top||void 0!==a.bottom&&null!==a.bottom)||(h="middle"===k?{top:((e||0)-f.height)/2}:"bottom"===k?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),x(x({},g),h)}(j,a,g,n,o,l)),j),y=null!=k?k:c;if(null==y)return null;var z=d.createElement("div",{className:"recharts-legend-wrapper",style:w,ref:m},d.createElement(B,{layout:a.layout,align:a.align,verticalAlign:a.verticalAlign,itemSorter:a.itemSorter}),d.createElement(C,{width:l.width,height:l.height}),d.createElement(A,v({},a,u,{margin:g,chartWidth:n,chartHeight:o,contextPayload:b})));return(0,e.createPortal)(z,y)}class E extends d.PureComponent{static getWidthOrHeight(a,b,c,d){return"vertical"===a&&(0,o.Et)(b)?{height:b}:"horizontal"===a?{width:c||d}:null}render(){return d.createElement(D,this.props)}}y(E,"displayName","Legend"),y(E,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"})},15606:(a,b,c)=>{"use strict";c.d(b,{i:()=>i});let d=Math.PI,e=2*d,f=e-1e-6;function g(a){this._+=a[0];for(let b=1,c=a.length;b<c;++b)this._+=arguments[b]+a[b]}class h{constructor(a){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==a?g:function(a){let b=Math.floor(a);if(!(b>=0))throw Error(`invalid digits: ${a}`);if(b>15)return g;let c=10**b;return function(a){this._+=a[0];for(let b=1,d=a.length;b<d;++b)this._+=Math.round(arguments[b]*c)/c+a[b]}}(a)}moveTo(a,b){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(a,b){this._append`L${this._x1=+a},${this._y1=+b}`}quadraticCurveTo(a,b,c,d){this._append`Q${+a},${+b},${this._x1=+c},${this._y1=+d}`}bezierCurveTo(a,b,c,d,e,f){this._append`C${+a},${+b},${+c},${+d},${this._x1=+e},${this._y1=+f}`}arcTo(a,b,c,e,f){if(a*=1,b*=1,c*=1,e*=1,(f*=1)<0)throw Error(`negative radius: ${f}`);let g=this._x1,h=this._y1,i=c-a,j=e-b,k=g-a,l=h-b,m=k*k+l*l;if(null===this._x1)this._append`M${this._x1=a},${this._y1=b}`;else if(m>1e-6)if(Math.abs(l*i-j*k)>1e-6&&f){let n=c-g,o=e-h,p=i*i+j*j,q=Math.sqrt(p),r=Math.sqrt(m),s=f*Math.tan((d-Math.acos((p+m-(n*n+o*o))/(2*q*r)))/2),t=s/r,u=s/q;Math.abs(t-1)>1e-6&&this._append`L${a+t*k},${b+t*l}`,this._append`A${f},${f},0,0,${+(l*n>k*o)},${this._x1=a+u*i},${this._y1=b+u*j}`}else this._append`L${this._x1=a},${this._y1=b}`}arc(a,b,c,g,h,i){if(a*=1,b*=1,c*=1,i=!!i,c<0)throw Error(`negative radius: ${c}`);let j=c*Math.cos(g),k=c*Math.sin(g),l=a+j,m=b+k,n=1^i,o=i?g-h:h-g;null===this._x1?this._append`M${l},${m}`:(Math.abs(this._x1-l)>1e-6||Math.abs(this._y1-m)>1e-6)&&this._append`L${l},${m}`,c&&(o<0&&(o=o%e+e),o>f?this._append`A${c},${c},0,1,${n},${a-j},${b-k}A${c},${c},0,1,${n},${this._x1=l},${this._y1=m}`:o>1e-6&&this._append`A${c},${c},0,${+(o>=d)},${n},${this._x1=a+c*Math.cos(h)},${this._y1=b+c*Math.sin(h)}`)}rect(a,b,c,d){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}h${c*=1}v${+d}h${-c}Z`}toString(){return this._}}function i(a){let b=3;return a.digits=function(c){if(!arguments.length)return b;if(null==c)b=null;else{let a=Math.floor(c);if(!(a>=0))throw RangeError(`invalid digits: ${c}`);b=a}return a},()=>new h(b)}h.prototype},15708:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(95819);b.toNumber=function(a){return d.isSymbol(a)?NaN:Number(a)}},17118:(a,b,c)=>{"use strict";c.d(b,{E1:()=>q,En:()=>s,Ix:()=>h,ML:()=>n,Nt:()=>o,RD:()=>k,UF:()=>j,XB:()=>i,jF:()=>p,k_:()=>f,o4:()=>r,oP:()=>l,xS:()=>m});var d=c(76067),e=c(71392),f={active:!1,index:null,dataKey:void 0,coordinate:void 0},g=(0,d.Z0)({name:"tooltip",initialState:{itemInteraction:{click:f,hover:f},axisInteraction:{click:f,hover:f},keyboardInteraction:f,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(a,b){a.tooltipItemPayloads.push((0,e.h4)(b.payload))},removeTooltipEntrySettings(a,b){var c=(0,e.ss)(a).tooltipItemPayloads.indexOf((0,e.h4)(b.payload));c>-1&&a.tooltipItemPayloads.splice(c,1)},setTooltipSettingsState(a,b){a.settings=b.payload},setActiveMouseOverItemIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.itemInteraction.hover.active=!0,a.itemInteraction.hover.index=b.payload.activeIndex,a.itemInteraction.hover.dataKey=b.payload.activeDataKey,a.itemInteraction.hover.coordinate=b.payload.activeCoordinate},mouseLeaveChart(a){a.itemInteraction.hover.active=!1,a.axisInteraction.hover.active=!1},mouseLeaveItem(a){a.itemInteraction.hover.active=!1},setActiveClickItemIndex(a,b){a.syncInteraction.active=!1,a.itemInteraction.click.active=!0,a.keyboardInteraction.active=!1,a.itemInteraction.click.index=b.payload.activeIndex,a.itemInteraction.click.dataKey=b.payload.activeDataKey,a.itemInteraction.click.coordinate=b.payload.activeCoordinate},setMouseOverAxisIndex(a,b){a.syncInteraction.active=!1,a.axisInteraction.hover.active=!0,a.keyboardInteraction.active=!1,a.axisInteraction.hover.index=b.payload.activeIndex,a.axisInteraction.hover.dataKey=b.payload.activeDataKey,a.axisInteraction.hover.coordinate=b.payload.activeCoordinate},setMouseClickAxisIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.axisInteraction.click.active=!0,a.axisInteraction.click.index=b.payload.activeIndex,a.axisInteraction.click.dataKey=b.payload.activeDataKey,a.axisInteraction.click.coordinate=b.payload.activeCoordinate},setSyncInteraction(a,b){a.syncInteraction=b.payload},setKeyboardInteraction(a,b){a.keyboardInteraction.active=b.payload.active,a.keyboardInteraction.index=b.payload.activeIndex,a.keyboardInteraction.coordinate=b.payload.activeCoordinate,a.keyboardInteraction.dataKey=b.payload.activeDataKey}}}),{addTooltipEntrySettings:h,removeTooltipEntrySettings:i,setTooltipSettingsState:j,setActiveMouseOverItemIndex:k,mouseLeaveItem:l,mouseLeaveChart:m,setActiveClickItemIndex:n,setMouseOverAxisIndex:o,setMouseClickAxisIndex:p,setSyncInteraction:q,setKeyboardInteraction:r}=g.actions,s=g.reducer},17617:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92681),e=c(40144),f=c(74838),g=c(30415);b.iteratee=function(a){if(null==a)return d.identity;switch(typeof a){case"function":return a;case"object":if(Array.isArray(a)&&2===a.length)return g.matchesProperty(a[0],a[1]);return f.matches(a);case"string":case"symbol":case"number":return e.property(a)}}},17874:(a,b,c)=>{"use strict";c.d(b,{f:()=>m});var d=c(22989),e=c(96075),f=c(20237);class g{static create(a){return new g(a)}constructor(a){this.scale=a}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(a){var{bandAware:b,position:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==a){if(c)switch(c){case"start":default:return this.scale(a);case"middle":var d=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+d;case"end":var e=this.bandwidth?this.bandwidth():0;return this.scale(a)+e}if(b){var f=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+f}return this.scale(a)}}isInRange(a){var b=this.range(),c=b[0],d=b[b.length-1];return c<=d?a>=c&&a<=d:a>=d&&a<=c}}!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):a[b]=1e-4}(g,"EPS",1e-4);var h=function(a){var{width:b,height:c}=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=(d%180+180)%180*Math.PI/180,f=Math.atan(c/b);return Math.abs(e>f&&e<Math.PI-f?c/Math.sin(e):b/Math.cos(e))};function i(a,b,c){if(b<1)return[];if(1===b&&void 0===c)return a;for(var d=[],e=0;e<a.length;e+=b)if(void 0!==c&&!0!==c(a[e]))return;else d.push(a[e]);return d}function j(a,b,c,d,e){if(a*b<a*d||a*b>a*e)return!1;var f=c();return a*(b-a*f/2-d)>=0&&a*(b+a*f/2-e)<=0}function k(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function l(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?k(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):k(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function m(a,b,c){var g,{tick:k,ticks:m,viewBox:n,minTickGap:o,orientation:p,interval:q,tickFormatter:r,unit:s,angle:t}=a;if(!m||!m.length||!k)return[];if((0,d.Et)(q)||f.m.isSsr)return null!=(g=i(m,((0,d.Et)(q)?q:0)+1))?g:[];var u="top"===p||"bottom"===p?"width":"height",v=s&&"width"===u?(0,e.P)(s,{fontSize:b,letterSpacing:c}):{width:0,height:0},w=(a,d)=>{var f,g="function"==typeof r?r(a.value,d):a.value;return"width"===u?(f=(0,e.P)(g,{fontSize:b,letterSpacing:c}),h({width:f.width+v.width,height:f.height+v.height},t)):(0,e.P)(g,{fontSize:b,letterSpacing:c})[u]},x=m.length>=2?(0,d.sA)(m[1].coordinate-m[0].coordinate):1,y=function(a,b,c){var d="width"===c,{x:e,y:f,width:g,height:h}=a;return 1===b?{start:d?e:f,end:d?e+g:f+h}:{start:d?e+g:f+h,end:d?e:f}}(n,x,u);return"equidistantPreserveStart"===q?function(a,b,c,d,e){for(var f,g=(d||[]).slice(),{start:h,end:k}=b,l=0,m=1,n=h;m<=g.length;)if(f=function(){var b,f=null==d?void 0:d[l];if(void 0===f)return{v:i(d,m)};var g=l,o=()=>(void 0===b&&(b=c(f,g)),b),p=f.coordinate,q=0===l||j(a,p,o,n,k);q||(l=0,n=h,m+=1),q&&(n=p+a*(o()/2+e),l+=m)}())return f.v;return[]}(x,y,w,m,o):("preserveStart"===q||"preserveStartEnd"===q?function(a,b,c,d,e,f){var g=(d||[]).slice(),h=g.length,{start:i,end:k}=b;if(f){var m=d[h-1],n=c(m,h-1),o=a*(m.coordinate+a*n/2-k);g[h-1]=m=l(l({},m),{},{tickCoord:o>0?m.coordinate-o*a:m.coordinate}),j(a,m.tickCoord,()=>n,i,k)&&(k=m.tickCoord-a*(n/2+e),g[h-1]=l(l({},m),{},{isShow:!0}))}for(var p=f?h-1:h,q=function(b){var d,f=g[b],h=()=>(void 0===d&&(d=c(f,b)),d);if(0===b){var m=a*(f.coordinate-a*h()/2-i);g[b]=f=l(l({},f),{},{tickCoord:m<0?f.coordinate-m*a:f.coordinate})}else g[b]=f=l(l({},f),{},{tickCoord:f.coordinate});j(a,f.tickCoord,h,i,k)&&(i=f.tickCoord+a*(h()/2+e),g[b]=l(l({},f),{},{isShow:!0}))},r=0;r<p;r++)q(r);return g}(x,y,w,m,o,"preserveStartEnd"===q):function(a,b,c,d,e){for(var f=(d||[]).slice(),g=f.length,{start:h}=b,{end:i}=b,k=function(b){var d,k=f[b],m=()=>(void 0===d&&(d=c(k,b)),d);if(b===g-1){var n=a*(k.coordinate+a*m()/2-i);f[b]=k=l(l({},k),{},{tickCoord:n>0?k.coordinate-n*a:k.coordinate})}else f[b]=k=l(l({},k),{},{tickCoord:k.coordinate});j(a,k.tickCoord,m,h,i)&&(i=k.tickCoord-a*(m()/2+e),f[b]=l(l({},k),{},{isShow:!0}))},m=g-1;m>=0;m--)k(m);return f}(x,y,w,m,o)).filter(a=>a.isShow)}},19335:(a,b,c)=>{"use strict";function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function e(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?d(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):d(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}c.d(b,{IZ:()=>g,Kg:()=>f,lY:()=>h,yy:()=>i}),c(43210);var f=Math.PI/180,g=(a,b,c,d)=>({x:a+Math.cos(-f*d)*c,y:b+Math.sin(-f*d)*c}),h=function(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(a-(c.left||0)-(c.right||0)),Math.abs(b-(c.top||0)-(c.bottom||0)))/2},i=(a,b)=>{var c,{x:d,y:f}=a,{radius:g,angle:h}=((a,b)=>{var{x:c,y:d}=a,{cx:e,cy:f}=b,g=((a,b)=>{var{x:c,y:d}=a,{x:e,y:f}=b;return Math.sqrt((c-e)**2+(d-f)**2)})({x:c,y:d},{x:e,y:f});if(g<=0)return{radius:g,angle:0};var h=Math.acos((c-e)/g);return d>f&&(h=2*Math.PI-h),{radius:g,angle:180*h/Math.PI,angleInRadian:h}})({x:d,y:f},b),{innerRadius:i,outerRadius:j}=b;if(g<i||g>j||0===g)return null;var{startAngle:k,endAngle:l}=(a=>{var{startAngle:b,endAngle:c}=a,d=Math.min(Math.floor(b/360),Math.floor(c/360));return{startAngle:b-360*d,endAngle:c-360*d}})(b),m=h;if(k<=l){for(;m>l;)m-=360;for(;m<k;)m+=360;c=m>=k&&m<=l}else{for(;m>k;)m-=360;for(;m<l;)m+=360;c=m>=l&&m<=k}return c?e(e({},b),{},{radius:g,angle:((a,b)=>{var{startAngle:c,endAngle:d}=b;return a+360*Math.min(Math.floor(c/360),Math.floor(d/360))})(m,b)}):null}},19420:(a,b,c)=>{"use strict";c.d(b,{i:()=>B});var d=c(43210),e=c(12728),f=c.n(e),g=(a,b)=>[0,3*a,3*b-6*a,3*a-3*b+1],h=(a,b)=>a.map((a,c)=>a*b**c).reduce((a,b)=>a+b),i=(a,b)=>c=>h(g(a,b),c),j=function(){let a,b;for(var c,d,e,f,j=arguments.length,k=Array(j),l=0;l<j;l++)k[l]=arguments[l];if(1===k.length)switch(k[0]){case"linear":[c,e,d,f]=[0,0,1,1];break;case"ease":[c,e,d,f]=[.25,.1,.25,1];break;case"ease-in":[c,e,d,f]=[.42,0,1,1];break;case"ease-out":[c,e,d,f]=[.42,0,.58,1];break;case"ease-in-out":[c,e,d,f]=[0,0,.58,1];break;default:var m=k[0].split("(");"cubic-bezier"===m[0]&&4===m[1].split(")")[0].split(",").length&&([c,e,d,f]=m[1].split(")")[0].split(",").map(a=>parseFloat(a)))}else 4===k.length&&([c,e,d,f]=k);var n=i(c,d),o=i(e,f),p=(a=c,b=d,c=>h([...g(a,b).map((a,b)=>a*b).slice(1),0],c)),q=a=>a>1?1:a<0?0:a,r=a=>{for(var b=a>1?1:a,c=b,d=0;d<8;++d){var e=n(c)-b,f=p(c);if(1e-4>Math.abs(e-b)||f<1e-4)break;c=q(c-e/f)}return o(c)};return r.isStepper=!1,r},k=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:b=100,damping:c=8,dt:d=17}=a,e=(a,e,f)=>{var g=f+(-(a-e)*b-f*c)*d/1e3,h=f*d/1e3+a;return 1e-4>Math.abs(h-e)&&1e-4>Math.abs(g)?[e,0]:[h,g]};return e.isStepper=!0,e.dt=d,e};function l(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function m(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var n=(a,b)=>Object.keys(b).reduce((c,d)=>m(m({},c),{},{[d]:a(d,b[d])}),{});function o(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var q=(a,b,c)=>a+(b-a)*c,r=a=>{var{from:b,to:c}=a;return b!==c},s=(a,b,c)=>{var d=n((b,c)=>{if(r(c)){var[d,e]=a(c.from,c.to,c.velocity);return p(p({},c),{},{from:d,velocity:e})}return c},b);return c<1?n((a,b)=>r(b)?p(p({},b),{},{velocity:q(b.velocity,d[a].velocity,c),from:q(b.from,d[a].from,c)}):b,b):s(a,d,c-1)};class t{setTimeout(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=performance.now(),d=null,e=f=>{f-c>=b?a(f):"function"==typeof requestAnimationFrame&&(d=requestAnimationFrame(e))};return d=requestAnimationFrame(e),()=>{cancelAnimationFrame(d)}}}var u=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function v(){return(v=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function w(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function x(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?w(Object(c),!0).forEach(function(b){y(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):w(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function y(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class z extends d.PureComponent{constructor(a,b){super(a,b),y(this,"mounted",!1),y(this,"manager",null),y(this,"stopJSAnimation",null),y(this,"unSubscribe",null);var{isActive:c,attributeName:d,from:e,to:f,children:g,duration:h,animationManager:i}=this.props;if(this.manager=i,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!c||h<=0){this.state={style:{}},"function"==typeof g&&(this.state={style:f});return}if(e){if("function"==typeof g){this.state={style:e};return}this.state={style:d?{[d]:e}:e}}else this.state={style:{}}}componentDidMount(){var{isActive:a,canBegin:b}=this.props;this.mounted=!0,a&&b&&this.runAnimation(this.props)}componentDidUpdate(a){var{isActive:b,canBegin:c,attributeName:d,shouldReAnimate:e,to:g,from:h}=this.props,{style:i}=this.state;if(c){if(!b){this.state&&i&&(d&&i[d]!==g||!d&&i!==g)&&this.setState({style:d?{[d]:g}:g});return}if(!f()(a.to,g)||!a.canBegin||!a.isActive){var j=!a.canBegin||!a.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var k=j||e?h:a.to;this.state&&i&&(d&&i[d]!==k||!d&&i!==k)&&this.setState({style:d?{[d]:k}:k}),this.runAnimation(x(x({},this.props),{},{from:k,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:a}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}handleStyleChange(a){this.changeStyle(a)}changeStyle(a){this.mounted&&this.setState({style:a})}runJSAnimation(a){var b,c,d,e,f,g,h,i,l,m,o,t,u,v,w,x,y,z,A,B,C,D,E,F,G,{from:H,to:I,duration:J,easing:K,begin:L,onAnimationEnd:M,onAnimationStart:N}=a,O=(D=(a=>{if("string"==typeof a)switch(a){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return j(a);case"spring":return k();default:if("cubic-bezier"===a.split("(")[0])return j(a)}return"function"==typeof a?a:null})(K),E=this.changeStyle,F=this.manager.getTimeoutController(),G=[Object.keys(H),Object.keys(I)].reduce((a,b)=>a.filter(a=>b.includes(a))),!0===D.isStepper?(b=H,c=I,d=D,e=G,f=E,g=F,i=e.reduce((a,d)=>p(p({},a),{},{[d]:{from:b[d],velocity:0,to:c[d]}}),{}),l=null,m=a=>{h||(h=a);var e=(a-h)/d.dt;i=s(d,i,e),f(p(p(p({},b),c),n((a,b)=>b.from,i))),h=a,Object.values(i).filter(r).length&&(l=g.setTimeout(m))},()=>(l=g.setTimeout(m),()=>{l()})):(o=H,t=I,u=D,v=J,w=G,x=E,y=F,A=null,B=w.reduce((a,b)=>p(p({},a),{},{[b]:[o[b],t[b]]}),{}),C=a=>{z||(z=a);var b=(a-z)/v,c=n((a,c)=>q(...c,u(b)),B);if(x(p(p(p({},o),t),c)),b<1)A=y.setTimeout(C);else{var d=n((a,b)=>q(...b,u(1)),B);x(p(p(p({},o),t),d))}},()=>(A=y.setTimeout(C),()=>{A()}))),P=()=>{this.stopJSAnimation=O()};this.manager.start([N,L,P,J,M])}runAnimation(a){let b;var{begin:c,duration:d,attributeName:e,to:f,easing:g,onAnimationStart:h,onAnimationEnd:i,children:j}=a;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof g||"function"==typeof j||"spring"===g)return void this.runJSAnimation(a);var k=e?{[e]:f}:f,l=(b=Object.keys(k),b.map(a=>"".concat(a.replace(/([A-Z])/g,a=>"-".concat(a.toLowerCase()))," ").concat(d,"ms ").concat(g)).join(","));this.manager.start([h,c,x(x({},k),{},{transition:l}),d,i])}render(){var a=this.props,{children:b,begin:c,duration:e,attributeName:f,easing:g,isActive:h,from:i,to:j,canBegin:k,onAnimationEnd:l,shouldReAnimate:m,onAnimationReStart:n,animationManager:o}=a,p=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,u),q=d.Children.count(b),r=this.state.style;if("function"==typeof b)return b(r);if(!h||0===q||e<=0)return b;var s=a=>{var{style:b={},className:c}=a.props;return(0,d.cloneElement)(a,x(x({},p),{},{style:x(x({},b),r),className:c}))};return 1===q?s(d.Children.only(b)):d.createElement("div",null,d.Children.map(b,a=>s(a)))}}y(z,"displayName","Animate"),y(z,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var A=(0,d.createContext)(null);function B(a){var b,c,e,f,g,h,i,j=(0,d.useContext)(A);return d.createElement(z,v({},a,{animationManager:null!=(h=null!=(i=a.animationManager)?i:j)?h:(b=new t,c=()=>null,e=!1,f=null,g=a=>{if(!e){if(Array.isArray(a)){if(!a.length)return;var[d,...h]=a;if("number"==typeof d){f=b.setTimeout(g.bind(null,h),d);return}g(d),f=b.setTimeout(g.bind(null,h));return}"object"==typeof a&&c(a),"function"==typeof a&&a()}},{stop:()=>{e=!0},start:a=>{e=!1,f&&(f(),f=null),g(a)},subscribe:a=>(c=a,()=>{c=()=>null}),getTimeoutController:()=>b})}))}},19598:(a,b,c)=>{"use strict";c.d(b,{h:()=>t});var d=c(43210),e=c(49384),f=c(71579),g=c(5338),h=c(43209),i=c(85621),j=c(76966),k=c(83409),l=c(97633),m=["dangerouslySetInnerHTML","ticks"];function n(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function o(){return(o=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function p(a){return(0,h.j)(),null}var q=a=>{var b,{yAxisId:c,className:n,width:p,label:q}=a,r=(0,d.useRef)(null),s=(0,d.useRef)(null),t=(0,h.G)(j.c2),u=(0,k.r)(),v=(0,h.j)(),w="yAxis",x=(0,h.G)(a=>(0,i.iV)(a,w,c,u)),y=(0,h.G)(a=>(0,i.wP)(a,c)),z=(0,h.G)(a=>(0,i.KR)(a,c)),A=(0,h.G)(a=>(0,i.Zi)(a,w,c,u));if((0,d.useLayoutEffect)(()=>{if(!("auto"!==p||!y||(0,l.Z)(q)||(0,d.isValidElement)(q))){var a,b=r.current,e=null==b||null==(a=b.tickRefs)?void 0:a.current,{tickSize:f,tickMargin:h}=b.props,i=(a=>{var{ticks:b,label:c,labelGapWithTick:d=5,tickSize:e=0,tickMargin:f=0}=a,g=0;if(b){b.forEach(a=>{if(a){var b=a.getBoundingClientRect();b.width>g&&(g=b.width)}});var h=c?c.getBoundingClientRect().width:0;return Math.round(g+(e+f)+h+(c?d:0))}return 0})({ticks:e,label:s.current,labelGapWithTick:5,tickSize:f,tickMargin:h});Math.round(y.width)!==Math.round(i)&&v((0,g.QG)({id:c,width:i}))}},[r,null==r||null==(b=r.current)||null==(b=b.tickRefs)?void 0:b.current,null==y?void 0:y.width,y,v,q,c,p]),null==y||null==z)return null;var{dangerouslySetInnerHTML:B,ticks:C}=a,D=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,m);return d.createElement(f.u,o({},D,{ref:r,labelRef:s,scale:x,x:z.x,y:z.y,width:y.width,height:y.height,className:(0,e.$)("recharts-".concat(w," ").concat(w),n),viewBox:t,ticks:A}))},r=a=>{var b,c,e,f,g;return d.createElement(d.Fragment,null,d.createElement(p,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.yAxisId,scale:a.scale,type:a.type,domain:a.domain,allowDataOverflow:a.allowDataOverflow,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,padding:a.padding,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,width:a.width,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(e=a.angle)?e:0,minTickGap:null!=(f=a.minTickGap)?f:5,tick:null==(g=a.tick)||g,tickFormatter:a.tickFormatter}),d.createElement(q,a))},s={allowDataOverflow:i.cd.allowDataOverflow,allowDecimals:i.cd.allowDecimals,allowDuplicatedCategory:i.cd.allowDuplicatedCategory,hide:!1,mirror:i.cd.mirror,orientation:i.cd.orientation,padding:i.cd.padding,reversed:i.cd.reversed,scale:i.cd.scale,tickCount:i.cd.tickCount,type:i.cd.type,width:i.cd.width,yAxisId:0};class t extends d.Component{render(){return d.createElement(r,this.props)}}n(t,"displayName","YAxis"),n(t,"defaultProps",s)},20237:(a,b,c)=>{"use strict";c.d(b,{m:()=>d});var d={isSsr:!0}},20911:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(55100);b.debounce=function(a,b=0,c={}){let e;"object"!=typeof c&&(c={});let{leading:f=!1,trailing:g=!0,maxWait:h}=c,i=[,,];f&&(i[0]="leading"),g&&(i[1]="trailing");let j=null,k=d.debounce(function(...b){e=a.apply(this,b),j=null},b,{edges:i}),l=function(...b){return null!=h&&(null===j&&(j=Date.now()),Date.now()-j>=h)?(e=a.apply(this,b),j=Date.now(),k.cancel(),k.schedule(),e):(k.apply(this,b),e)};return l.cancel=k.cancel,l.flush=()=>(k.flush(),e),l}},21080:(a,b,c)=>{"use strict";c.d(b,{u:()=>i});var d=c(43210),e=c(49384),f=c(54186),g=["children","width","height","viewBox","className","style","title","desc"];function h(){return(h=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var i=(0,d.forwardRef)((a,b)=>{var{children:c,width:i,height:j,viewBox:k,className:l,style:m,title:n,desc:o}=a,p=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,g),q=k||{width:i,height:j,x:0,y:0},r=(0,e.$)("recharts-surface",l);return d.createElement("svg",h({},(0,f.J9)(p,!0,"svg"),{className:r,width:i,height:j,style:m,viewBox:"".concat(q.x," ").concat(q.y," ").concat(q.width," ").concat(q.height),ref:b}),d.createElement("title",null,n),d.createElement("desc",null,o),c)})},21251:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isTypedArray=function(a){return ArrayBuffer.isView(a)&&!(a instanceof DataView)}},21424:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toArray=function(a){return Array.isArray(a)?a:Array.from(a)}},21426:(a,b,c)=>{"use strict";c.d(b,{BZ:()=>H,aX:()=>K,dS:()=>G,dp:()=>D,fW:()=>w,pg:()=>F,r1:()=>A,u9:()=>I,yn:()=>J});var d=c(84648),e=c(10687),f=c.n(e),g=c(43209),h=c(64279),i=c(57282),j=c(69009),k=c(97350),l=c(51426),m=c(76966),n=c(86445),o=c(28550),p=c(32520),q=c(97371),r=c(49396),s=c(77100),t=c(72198),u=c(78242),v=c(32751),w=()=>(0,g.G)(k.iO),x=(a,b)=>b,y=(a,b,c)=>c,z=(a,b,c,d)=>d,A=(0,d.Mz)(j.R4,a=>f()(a,a=>a.coordinate)),B=(0,d.Mz)([u.J,x,y,z],p.i),C=(0,d.Mz)([B,j.n4],q.P),D=(a,b,c)=>{if(null!=b){var d=(0,u.J)(a);return"axis"===b?"hover"===c?d.axisInteraction.hover.dataKey:d.axisInteraction.click.dataKey:"hover"===c?d.itemInteraction.hover.dataKey:d.itemInteraction.click.dataKey}},E=(0,d.Mz)([u.J,x,y,z],s.q),F=(0,d.Mz)([n.Lp,n.A$,l.fz,m.HZ,j.R4,z,E,t.x],r.o),G=(0,d.Mz)([B,F],(a,b)=>{var c;return null!=(c=a.coordinate)?c:b}),H=(0,d.Mz)(j.R4,C,o.E),I=(0,d.Mz)([E,C,i.LF,j.Dn,H,t.x,x],v.N),J=(0,d.Mz)([B],a=>({isActive:a.active,activeIndex:a.index})),K=(a,b,c,d,e,f,g,i)=>{if(a&&b&&d&&e&&f){var j=(0,h.r4)(a.chartX,a.chartY,b,c,i);if(j){var k=(0,h.SW)(j,b),l=(0,h.gH)(k,g,f,d,e),m=(0,h.bk)(b,f,l,j);return{activeIndex:String(l),activeCoordinate:m}}}}},22786:(a,b,c)=>{"use strict";function d(a){return function(){return a}}c.d(b,{A:()=>d})},22989:(a,b,c)=>{"use strict";c.d(b,{CG:()=>n,Dj:()=>o,Et:()=>i,F4:()=>m,GW:()=>p,M8:()=>g,NF:()=>l,Zb:()=>s,_3:()=>h,eP:()=>q,sA:()=>f,uy:()=>r,vh:()=>j});var d=c(5664),e=c.n(d),f=a=>0===a?0:a>0?1:-1,g=a=>"number"==typeof a&&a!=+a,h=a=>"string"==typeof a&&a.indexOf("%")===a.length-1,i=a=>("number"==typeof a||a instanceof Number)&&!g(a),j=a=>i(a)||"string"==typeof a,k=0,l=a=>{var b=++k;return"".concat(a||"").concat(b)},m=function(a,b){var c,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!i(a)&&"string"!=typeof a)return d;if(h(a)){if(null==b)return d;var f=a.indexOf("%");c=b*parseFloat(a.slice(0,f))/100}else c=+a;return g(c)&&(c=d),e&&null!=b&&c>b&&(c=b),c},n=a=>{if(!Array.isArray(a))return!1;for(var b=a.length,c={},d=0;d<b;d++)if(c[a[d]])return!0;else c[a[d]]=!0;return!1},o=(a,b)=>i(a)&&i(b)?c=>a+c*(b-a):()=>b;function p(a,b,c){return i(a)&&i(b)?a+c*(b-a):b}function q(a,b,c){if(a&&a.length)return a.find(a=>a&&("function"==typeof b?b(a):e()(a,b))===c)}var r=a=>null==a,s=a=>r(a)?a:"".concat(a.charAt(0).toUpperCase()).concat(a.slice(1))},23337:(a,b,c)=>{"use strict";c.d(b,{dc:()=>h,ff:()=>g,g0:()=>i});var d=c(84648),e=c(10687),f=c.n(e),g=a=>a.legend.settings,h=a=>a.legend.size,i=(0,d.Mz)([a=>a.legend.payload,g],(a,b)=>{var{itemSorter:c}=b,d=a.flat(1);return c?f()(d,c):d})},23457:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPrimitive=function(a){return null==a||"object"!=typeof a&&"function"!=typeof a}},23561:(a,b,c)=>{"use strict";c.d(b,{E:()=>B});var d=c(43210),e=c(49384),f=c(22989),g=c(20237),h=c(54186),i=c(96075),j=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,k=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,l=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,m=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,n={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},o=Object.keys(n);class p{static parse(a){var b,[,c,d]=null!=(b=m.exec(a))?b:[];return new p(parseFloat(c),null!=d?d:"")}constructor(a,b){this.num=a,this.unit=b,this.num=a,this.unit=b,(0,f.M8)(a)&&(this.unit=""),""===b||l.test(b)||(this.num=NaN,this.unit=""),o.includes(b)&&(this.num=a*n[b],this.unit="px")}add(a){return this.unit!==a.unit?new p(NaN,""):new p(this.num+a.num,this.unit)}subtract(a){return this.unit!==a.unit?new p(NaN,""):new p(this.num-a.num,this.unit)}multiply(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new p(NaN,""):new p(this.num*a.num,this.unit||a.unit)}divide(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new p(NaN,""):new p(this.num/a.num,this.unit||a.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,f.M8)(this.num)}}function q(a){if(a.includes("NaN"))return"NaN";for(var b=a;b.includes("*")||b.includes("/");){var c,[,d,e,f]=null!=(c=j.exec(b))?c:[],g=p.parse(null!=d?d:""),h=p.parse(null!=f?f:""),i="*"===e?g.multiply(h):g.divide(h);if(i.isNaN())return"NaN";b=b.replace(j,i.toString())}for(;b.includes("+")||/.-\d+(?:\.\d+)?/.test(b);){var l,[,m,n,o]=null!=(l=k.exec(b))?l:[],q=p.parse(null!=m?m:""),r=p.parse(null!=o?o:""),s="+"===n?q.add(r):q.subtract(r);if(s.isNaN())return"NaN";b=b.replace(k,s.toString())}return b}var r=/\(([^()]*)\)/;function s(a){var b=function(a){try{var b;return b=a.replace(/\s+/g,""),b=function(a){for(var b,c=a;null!=(b=r.exec(c));){var[,d]=b;c=c.replace(r,q(d))}return c}(b),b=q(b)}catch(a){return"NaN"}}(a.slice(5,-1));return"NaN"===b?"":b}var t=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],u=["dx","dy","angle","className","breakAll"];function v(){return(v=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function w(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var x=/[ \f\n\r\t\v\u2028\u2029]+/,y=a=>{var{children:b,breakAll:c,style:d}=a;try{var e=[];(0,f.uy)(b)||(e=c?b.toString().split(""):b.toString().split(x));var g=e.map(a=>({word:a,width:(0,i.P)(a,d).width})),h=c?0:(0,i.P)("\xa0",d).width;return{wordsWithComputedWidth:g,spaceWidth:h}}catch(a){return null}},z=a=>[{words:(0,f.uy)(a)?[]:a.toString().split(x)}],A="#808080",B=(0,d.forwardRef)((a,b)=>{var c,{x:i=0,y:j=0,lineHeight:k="1em",capHeight:l="0.71em",scaleToFit:m=!1,textAnchor:n="start",verticalAnchor:o="end",fill:p=A}=a,q=w(a,t),r=(0,d.useMemo)(()=>(a=>{var{width:b,scaleToFit:c,children:d,style:e,breakAll:h,maxLines:i}=a;if((b||c)&&!g.m.isSsr){var j=y({breakAll:h,children:d,style:e});if(!j)return z(d);var{wordsWithComputedWidth:k,spaceWidth:l}=j;return((a,b,c,d,e)=>{var g,{maxLines:h,children:i,style:j,breakAll:k}=a,l=(0,f.Et)(h),m=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return a.reduce((a,b)=>{var{word:f,width:g}=b,h=a[a.length-1];return h&&(null==d||e||h.width+g+c<Number(d))?(h.words.push(f),h.width+=g+c):a.push({words:[f],width:g}),a},[])},n=m(b),o=a=>a.reduce((a,b)=>a.width>b.width?a:b);if(!l||e||!(n.length>h||o(n).width>Number(d)))return n;for(var p=a=>{var b=m(y({breakAll:k,style:j,children:i.slice(0,a)+"…"}).wordsWithComputedWidth);return[b.length>h||o(b).width>Number(d),b]},q=0,r=i.length-1,s=0;q<=r&&s<=i.length-1;){var t=Math.floor((q+r)/2),[u,v]=p(t-1),[w]=p(t);if(u||w||(q=t+1),u&&w&&(r=t-1),!u&&w){g=v;break}s++}return g||n})({breakAll:h,children:d,maxLines:i,style:e},k,l,b,c)}return z(d)})({breakAll:q.breakAll,children:q.children,maxLines:q.maxLines,scaleToFit:m,style:q.style,width:q.width}),[q.breakAll,q.children,q.maxLines,m,q.style,q.width]),{dx:x,dy:B,angle:C,className:D,breakAll:E}=q,F=w(q,u);if(!(0,f.vh)(i)||!(0,f.vh)(j))return null;var G=i+((0,f.Et)(x)?x:0),H=j+((0,f.Et)(B)?B:0);switch(o){case"start":c=s("calc(".concat(l,")"));break;case"middle":c=s("calc(".concat((r.length-1)/2," * -").concat(k," + (").concat(l," / 2))"));break;default:c=s("calc(".concat(r.length-1," * -").concat(k,")"))}var I=[];if(m){var J=r[0].width,{width:K}=q;I.push("scale(".concat((0,f.Et)(K)?K/J:1,")"))}return C&&I.push("rotate(".concat(C,", ").concat(G,", ").concat(H,")")),I.length&&(F.transform=I.join(" ")),d.createElement("text",v({},(0,h.J9)(F,!0),{ref:b,x:G,y:H,className:(0,e.$)("recharts-text",D),textAnchor:n,fill:p.includes("url")?A:p}),r.map((a,b)=>{var e=a.words.join(E?"":" ");return d.createElement("tspan",{x:G,dy:0===b?c:k,key:"".concat(e,"-").concat(b)},e)}))});B.displayName="Text"},23812:(a,b,c)=>{"use strict";c.d(b,{m:()=>ac});var d=c(43210),e=c(51215),f=c(10687),g=c.n(f),h=c(49384),i=c(22989);function j(){return(j=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function k(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function l(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?k(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):k(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function m(a){return Array.isArray(a)&&(0,i.vh)(a[0])&&(0,i.vh)(a[1])?a.join(" ~ "):a}var n=a=>{var{separator:b=" : ",contentStyle:c={},itemStyle:e={},labelStyle:f={},payload:k,formatter:n,itemSorter:o,wrapperClassName:p,labelClassName:q,label:r,labelFormatter:s,accessibilityLayer:t=!1}=a,u=l({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},c),v=l({margin:0},f),w=!(0,i.uy)(r),x=w?r:"",y=(0,h.$)("recharts-default-tooltip",p),z=(0,h.$)("recharts-tooltip-label",q);return w&&s&&null!=k&&(x=s(r,k)),d.createElement("div",j({className:y,style:u},t?{role:"status","aria-live":"assertive"}:{}),d.createElement("p",{className:z,style:v},d.isValidElement(x)?x:"".concat(x)),(()=>{if(k&&k.length){var a=(o?g()(k,o):k).map((a,c)=>{if("none"===a.type)return null;var f=a.formatter||n||m,{value:g,name:h}=a,j=g,o=h;if(f){var p=f(g,h,a,c,k);if(Array.isArray(p))[j,o]=p;else{if(null==p)return null;j=p}}var q=l({display:"block",paddingTop:4,paddingBottom:4,color:a.color||"#000"},e);return d.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(c),style:q},(0,i.vh)(o)?d.createElement("span",{className:"recharts-tooltip-item-name"},o):null,(0,i.vh)(o)?d.createElement("span",{className:"recharts-tooltip-item-separator"},b):null,d.createElement("span",{className:"recharts-tooltip-item-value"},j),d.createElement("span",{className:"recharts-tooltip-item-unit"},a.unit||""))});return d.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},a)}return null})())},o="recharts-tooltip-wrapper",p={visibility:"hidden"};function q(a){var{allowEscapeViewBox:b,coordinate:c,key:d,offsetTopLeft:e,position:f,reverseDirection:g,tooltipDimension:h,viewBox:j,viewBoxDimension:k}=a;if(f&&(0,i.Et)(f[d]))return f[d];var l=c[d]-h-(e>0?e:0),m=c[d]+e;if(b[d])return g[d]?l:m;var n=j[d];return null==n?0:g[d]?l<n?Math.max(m,n):Math.max(l,n):null==k?0:m+h>n+k?Math.max(l,n):Math.max(m,n)}function r(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function s(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?r(Object(c),!0).forEach(function(b){t(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):r(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function t(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class u extends d.PureComponent{constructor(){super(...arguments),t(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),t(this,"handleKeyDown",a=>{if("Escape"===a.key){var b,c,d,e;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(b=null==(c=this.props.coordinate)?void 0:c.x)?b:0,y:null!=(d=null==(e=this.props.coordinate)?void 0:e.y)?d:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var a,b;this.state.dismissed&&((null==(a=this.props.coordinate)?void 0:a.x)!==this.state.dismissedAtCoordinate.x||(null==(b=this.props.coordinate)?void 0:b.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:a,allowEscapeViewBox:b,animationDuration:c,animationEasing:e,children:f,coordinate:g,hasPayload:j,isAnimationActive:k,offset:l,position:m,reverseDirection:n,useTranslate3d:r,viewBox:t,wrapperStyle:u,lastBoundingBox:v,innerRef:w,hasPortalFromProps:x}=this.props,{cssClasses:y,cssProperties:z}=function(a){var b,c,d,{allowEscapeViewBox:e,coordinate:f,offsetTopLeft:g,position:j,reverseDirection:k,tooltipBox:l,useTranslate3d:m,viewBox:n}=a;return{cssProperties:b=l.height>0&&l.width>0&&f?function(a){var{translateX:b,translateY:c,useTranslate3d:d}=a;return{transform:d?"translate3d(".concat(b,"px, ").concat(c,"px, 0)"):"translate(".concat(b,"px, ").concat(c,"px)")}}({translateX:c=q({allowEscapeViewBox:e,coordinate:f,key:"x",offsetTopLeft:g,position:j,reverseDirection:k,tooltipDimension:l.width,viewBox:n,viewBoxDimension:n.width}),translateY:d=q({allowEscapeViewBox:e,coordinate:f,key:"y",offsetTopLeft:g,position:j,reverseDirection:k,tooltipDimension:l.height,viewBox:n,viewBoxDimension:n.height}),useTranslate3d:m}):p,cssClasses:function(a){var{coordinate:b,translateX:c,translateY:d}=a;return(0,h.$)(o,{["".concat(o,"-right")]:(0,i.Et)(c)&&b&&(0,i.Et)(b.x)&&c>=b.x,["".concat(o,"-left")]:(0,i.Et)(c)&&b&&(0,i.Et)(b.x)&&c<b.x,["".concat(o,"-bottom")]:(0,i.Et)(d)&&b&&(0,i.Et)(b.y)&&d>=b.y,["".concat(o,"-top")]:(0,i.Et)(d)&&b&&(0,i.Et)(b.y)&&d<b.y})}({translateX:c,translateY:d,coordinate:f})}}({allowEscapeViewBox:b,coordinate:g,offsetTopLeft:l,position:m,reverseDirection:n,tooltipBox:{height:v.height,width:v.width},useTranslate3d:r,viewBox:t}),A=x?{}:s(s({transition:k&&a?"transform ".concat(c,"ms ").concat(e):void 0},z),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&j?"visible":"hidden",position:"absolute",top:0,left:0}),B=s(s({},A),{},{visibility:!this.state.dismissed&&a&&j?"visible":"hidden"},u);return d.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:y,style:B,ref:w},f)}}var v=c(20237),w=c(45796),x=c(51426),y=c(24028),z=c(68392),A=c(81888),B=c(54186),C=["x","y","top","left","width","height","className"];function D(){return(D=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function E(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var F=a=>{var{x:b=0,y:c=0,top:e=0,left:f=0,width:g=0,height:j=0,className:k}=a,l=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?E(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):E(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({x:b,y:c,top:e,left:f,width:g,height:j},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,C));return(0,i.Et)(b)&&(0,i.Et)(c)&&(0,i.Et)(g)&&(0,i.Et)(j)&&(0,i.Et)(e)&&(0,i.Et)(f)?d.createElement("path",D({},(0,B.J9)(l,!0),{className:(0,h.$)("recharts-cross",k),d:"M".concat(b,",").concat(e,"v").concat(j,"M").concat(f,",").concat(c,"h").concat(g)})):null},G=c(71524),H=c(19335);function I(a){var{cx:b,cy:c,radius:d,startAngle:e,endAngle:f}=a;return{points:[(0,H.IZ)(b,c,d,e),(0,H.IZ)(b,c,d,f)],cx:b,cy:c,radius:d,startAngle:e,endAngle:f}}var J=c(34955),K=c(43209),L=c(64279),M=c(69009);function N(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function O(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?N(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):N(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var P=c(21426);function Q(){return(Q=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function R(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function S(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?R(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):R(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function T(a){var b,c,e,{coordinate:f,payload:g,index:i,offset:j,tooltipAxisBandSize:k,layout:l,cursor:m,tooltipEventType:n,chartName:o}=a;if(!m||!f||"ScatterChart"!==o&&"axis"!==n)return null;if("ScatterChart"===o)c=f,e=F;else if("BarChart"===o)b=k/2,c={stroke:"none",fill:"#ccc",x:"horizontal"===l?f.x-b:j.left+.5,y:"horizontal"===l?j.top+.5:f.y-b,width:"horizontal"===l?k:j.width-1,height:"horizontal"===l?j.height-1:k},e=G.M;else if("radial"===l){var{cx:p,cy:q,radius:r,startAngle:s,endAngle:t}=I(f);c={cx:p,cy:q,startAngle:s,endAngle:t,innerRadius:r,outerRadius:r},e=J.h}else c={points:function(a,b,c){var d,e,f,g;if("horizontal"===a)f=d=b.x,e=c.top,g=c.top+c.height;else if("vertical"===a)g=e=b.y,d=c.left,f=c.left+c.width;else if(null!=b.cx&&null!=b.cy)if("centric"!==a)return I(b);else{var{cx:h,cy:i,innerRadius:j,outerRadius:k,angle:l}=b,m=(0,H.IZ)(h,i,j,l),n=(0,H.IZ)(h,i,k,l);d=m.x,e=m.y,f=n.x,g=n.y}return[{x:d,y:e},{x:f,y:g}]}(l,f,j)},e=A.I;var u="object"==typeof m&&"className"in m?m.className:void 0,v=S(S(S(S({stroke:"#ccc",pointerEvents:"none"},j),c),(0,B.J9)(m,!1)),{},{payload:g,payloadIndex:i,className:(0,h.$)("recharts-tooltip-cursor",u)});return(0,d.isValidElement)(m)?(0,d.cloneElement)(m,v):(0,d.createElement)(e,v)}function U(a){var b,c,e,f=(b=(0,K.G)(M.Dn),c=(0,K.G)(M.R4),e=(0,K.G)(M.fl),(0,L.Hj)(O(O({},b),{},{scale:e}),c)),g=(0,x.W7)(),h=(0,x.WX)(),i=(0,P.fW)();return d.createElement(T,Q({},a,{coordinate:a.coordinate,index:a.index,payload:a.payload,offset:g,layout:h,tooltipAxisBandSize:f,chartName:i}))}var V=c(97711);c(17118);var W=c(98009),X=c(43075),Y=c(73865);function Z(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function $(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?Z(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):Z(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function _(a){return a.dataKey}var aa=[],ab={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!v.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function ac(a){var b,c=(0,Y.e)(a,ab),{active:f,allowEscapeViewBox:g,animationDuration:h,animationEasing:i,content:j,filterNull:k,isAnimationActive:l,offset:m,payloadUniqBy:o,position:p,reverseDirection:q,useTranslate3d:r,wrapperStyle:s,cursor:t,shared:v,trigger:A,defaultIndex:B,portal:C,axisId:D}=c;(0,K.j)();var E="number"==typeof B?String(B):B,F=(0,x.sk)(),G=(0,y.$)(),H=(0,X.Td)(v),{activeIndex:I,isActive:J}=(0,K.G)(a=>(0,P.yn)(a,H,A,E)),L=(0,K.G)(a=>(0,P.u9)(a,H,A,E)),M=(0,K.G)(a=>(0,P.BZ)(a,H,A,E)),N=(0,K.G)(a=>(0,P.dS)(a,H,A,E)),O=(0,V.X)(),Q=null!=f?f:J,[R,S]=(0,z.V)([L,Q]),T="axis"===H?M:void 0;(0,W.m7)(H,A,N,T,I,Q);var Z=null!=C?C:O;if(null==Z)return null;var ac=null!=L?L:aa;Q||(ac=aa),k&&ac.length&&(ac=(0,w.s)(L.filter(a=>null!=a.value&&(!0!==a.hide||c.includeHidden)),o,_));var ad=ac.length>0,ae=d.createElement(u,{allowEscapeViewBox:g,animationDuration:h,animationEasing:i,isAnimationActive:l,active:Q,coordinate:N,hasPayload:ad,offset:m,position:p,reverseDirection:q,useTranslate3d:r,viewBox:F,wrapperStyle:s,lastBoundingBox:R,innerRef:S,hasPortalFromProps:!!C},(b=$($({},c),{},{payload:ac,label:T,active:Q,coordinate:N,accessibilityLayer:G}),d.isValidElement(j)?d.cloneElement(j,b):"function"==typeof j?d.createElement(j,b):d.createElement(n,b)));return d.createElement(d.Fragment,null,(0,e.createPortal)(ae,Z),Q&&d.createElement(U,{cursor:t,tooltipEventType:H,coordinate:N,payload:L,index:I}))}},23814:(a,b,c)=>{"use strict";c.d(b,{W:()=>f,h:()=>e});var d=c(84648),e=(0,d.Mz)(a=>a.cartesianAxis.xAxis,a=>Object.values(a)),f=(0,d.Mz)(a=>a.cartesianAxis.yAxis,a=>Object.values(a))},23854:(a,b,c)=>{a.exports=c(45263).uniqBy},24028:(a,b,c)=>{"use strict";c.d(b,{$:()=>e});var d=c(43209),e=()=>(0,d.G)(a=>a.rootProps.accessibilityLayer)},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},25679:(a,b,c)=>{"use strict";c.d(b,{f:()=>d});var d=a=>null;d.displayName="Cell"},25893:(a,b,c)=>{"use strict";c.d(b,{p:()=>e}),c(43210),c(32181);var d=c(43209);function e(a){return(0,d.j)(),null}},26349:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(48130);b.isArrayLike=function(a){return null!=a&&"function"!=typeof a&&d.isLength(a.length)}},26652:(a,b,c)=>{"use strict";c.d(b,{E:()=>d});var d=(0,c(43210).createContext)(null)},27469:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.argumentsTag="[object Arguments]",b.arrayBufferTag="[object ArrayBuffer]",b.arrayTag="[object Array]",b.bigInt64ArrayTag="[object BigInt64Array]",b.bigUint64ArrayTag="[object BigUint64Array]",b.booleanTag="[object Boolean]",b.dataViewTag="[object DataView]",b.dateTag="[object Date]",b.errorTag="[object Error]",b.float32ArrayTag="[object Float32Array]",b.float64ArrayTag="[object Float64Array]",b.functionTag="[object Function]",b.int16ArrayTag="[object Int16Array]",b.int32ArrayTag="[object Int32Array]",b.int8ArrayTag="[object Int8Array]",b.mapTag="[object Map]",b.numberTag="[object Number]",b.objectTag="[object Object]",b.regexpTag="[object RegExp]",b.setTag="[object Set]",b.stringTag="[object String]",b.symbolTag="[object Symbol]",b.uint16ArrayTag="[object Uint16Array]",b.uint32ArrayTag="[object Uint32Array]",b.uint8ArrayTag="[object Uint8Array]",b.uint8ClampedArrayTag="[object Uint8ClampedArray]"},27747:(a,b,c)=>{"use strict";c.d(b,{W:()=>s});var d=c(43210),e=c(49384),f=c(71579),g=c(43209);c(5338);var h=c(85621),i=c(76966),j=c(83409),k=["children"],l=["dangerouslySetInnerHTML","ticks"];function m(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function n(){return(n=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function o(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function p(a){(0,g.j)();var b=(0,d.useMemo)(()=>{var{children:b}=a;return o(a,k)},[a]),c=(0,g.G)(a=>(0,h.Rl)(a,b.id));return b===c?a.children:null}var q=a=>{var{xAxisId:b,className:c}=a,k=(0,g.G)(i.c2),m=(0,j.r)(),p="xAxis",q=(0,g.G)(a=>(0,h.iV)(a,p,b,m)),r=(0,g.G)(a=>(0,h.Zi)(a,p,b,m)),s=(0,g.G)(a=>(0,h.Lw)(a,b)),t=(0,g.G)(a=>(0,h.L$)(a,b));if(null==s||null==t)return null;var{dangerouslySetInnerHTML:u,ticks:v}=a,w=o(a,l);return d.createElement(f.u,n({},w,{scale:q,x:t.x,y:t.y,width:s.width,height:s.height,className:(0,e.$)("recharts-".concat(p," ").concat(p),c),viewBox:k,ticks:r}))},r=a=>{var b,c,e,f,g;return d.createElement(p,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.xAxisId,scale:a.scale,type:a.type,padding:a.padding,allowDataOverflow:a.allowDataOverflow,domain:a.domain,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,height:a.height,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(e=a.angle)?e:0,minTickGap:null!=(f=a.minTickGap)?f:5,tick:null==(g=a.tick)||g,tickFormatter:a.tickFormatter},d.createElement(q,a))};class s extends d.Component{render(){return d.createElement(r,this.props)}}m(s,"displayName","XAxis"),m(s,"defaultProps",{allowDataOverflow:h.PU.allowDataOverflow,allowDecimals:h.PU.allowDecimals,allowDuplicatedCategory:h.PU.allowDuplicatedCategory,height:h.PU.height,hide:!1,mirror:h.PU.mirror,orientation:h.PU.orientation,padding:h.PU.padding,reversed:h.PU.reversed,scale:h.PU.scale,tickCount:h.PU.tickCount,type:h.PU.type,xAxisId:0})},27934:(a,b,c)=>{"use strict";c.d(b,{EI:()=>p,oM:()=>o,ZI:()=>m,gi:()=>n});var d=c(85621),e=c(43209),f=c(83409),g=c(69009),h=c(84648),i=c(76966),j=(0,h.Mz)([i.HZ],a=>{if(a)return{top:a.top,bottom:a.bottom,left:a.left,right:a.right}}),k=c(86445),l=(0,h.Mz)([j,k.Lp,k.A$],(a,b,c)=>{if(a&&null!=b&&null!=c)return{x:a.left,y:a.top,width:Math.max(0,b-a.left-a.right),height:Math.max(0,c-a.top-a.bottom)}}),m=a=>{var b=(0,f.r)();return(0,e.G)(c=>(0,d.Gx)(c,"xAxis",a,b))},n=a=>{var b=(0,f.r)();return(0,e.G)(c=>(0,d.Gx)(c,"yAxis",a,b))},o=()=>(0,e.G)(l),p=()=>(0,e.G)(g.JG)},27977:(a,b,c)=>{"use strict";c.d(b,{g:()=>j});var d=c(84648),e=c(51426),f=c(69009),g=c(76966),h=c(21426),i=c(8920),j=(0,d.Mz)([(a,b)=>b,e.fz,i.D0,f.Re,f.gL,f.R4,h.r1,g.HZ],h.aX)},28382:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.flatten=function(a,b=1){let c=[],d=Math.floor(b),e=(a,b)=>{for(let f=0;f<a.length;f++){let g=a[f];Array.isArray(g)&&b<d?e(g,b+1):c.push(g)}};return e(a,0),c}},28550:(a,b,c)=>{"use strict";c.d(b,{E:()=>e});var d=c(22989),e=(a,b)=>{var c,e=Number(b);if(!(0,d.M8)(e)&&null!=b)return e>=0?null==a||null==(c=a[e])?void 0:c.value:void 0}},29243:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(91428);b.isArguments=function(a){return null!==a&&"object"==typeof a&&"[object Arguments]"===d.getTag(a)}},29632:(a,b,c)=>{"use strict";a.exports=c(97668)},29862:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92923),e=c(27469);b.cloneDeepWith=function(a,b){return d.cloneDeepWith(a,(c,f,g,h)=>{let i=b?.(c,f,g,h);if(null!=i)return i;if("object"==typeof a)switch(Object.prototype.toString.call(a)){case e.numberTag:case e.stringTag:case e.booleanTag:{let b=new a.constructor(a?.valueOf());return d.copyProperties(b,a),b}case e.argumentsTag:{let b={};return d.copyProperties(b,a),b.length=a.length,b[Symbol.iterator]=a[Symbol.iterator],b}default:return}})}},30415:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(30657),f=c(59138),g=c(87509),h=c(57841);b.matchesProperty=function(a,b){switch(typeof a){case"object":Object.is(a?.valueOf(),-0)&&(a="-0");break;case"number":a=e.toKey(a)}return b=f.cloneDeep(b),function(c){let e=g.get(c,a);return void 0===e?h.has(c,a):void 0===b?void 0===e:d.isMatch(e,b)}}},30657:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toKey=function(a){return"string"==typeof a||"symbol"==typeof a?a:Object.is(a?.valueOf?.(),-0)?"-0":String(a)}},30802:(a,b,c)=>{"use strict";c.d(b,{As:()=>l,Ch:()=>h,TK:()=>m,Vi:()=>k,ZF:()=>j,g5:()=>i,iZ:()=>n,lm:()=>g});var d=c(76067),e=c(71392),f=(0,d.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(a){a.countOfBars+=1},removeBar(a){a.countOfBars-=1},addCartesianGraphicalItem(a,b){a.cartesianItems.push((0,e.h4)(b.payload))},replaceCartesianGraphicalItem(a,b){var{prev:c,next:d}=b.payload,f=(0,e.ss)(a).cartesianItems.indexOf((0,e.h4)(c));f>-1&&(a.cartesianItems[f]=(0,e.h4)(d))},removeCartesianGraphicalItem(a,b){var c=(0,e.ss)(a).cartesianItems.indexOf((0,e.h4)(b.payload));c>-1&&a.cartesianItems.splice(c,1)},addPolarGraphicalItem(a,b){a.polarItems.push((0,e.h4)(b.payload))},removePolarGraphicalItem(a,b){var c=(0,e.ss)(a).polarItems.indexOf((0,e.h4)(b.payload));c>-1&&a.polarItems.splice(c,1)}}}),{addBar:g,removeBar:h,addCartesianGraphicalItem:i,replaceCartesianGraphicalItem:j,removeCartesianGraphicalItem:k,addPolarGraphicalItem:l,removePolarGraphicalItem:m}=f.actions,n=f.reducer},30921:(a,b,c)=>{a.exports=c(71337).range},32181:(a,b,c)=>{"use strict";c.d(b,{mZ:()=>h,vE:()=>g});var d=c(76067),e={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},f=(0,d.Z0)({name:"rootProps",initialState:e,reducers:{updateOptions:(a,b)=>{var c;a.accessibilityLayer=b.payload.accessibilityLayer,a.barCategoryGap=b.payload.barCategoryGap,a.barGap=null!=(c=b.payload.barGap)?c:e.barGap,a.barSize=b.payload.barSize,a.maxBarSize=b.payload.maxBarSize,a.stackOffset=b.payload.stackOffset,a.syncId=b.payload.syncId,a.syncMethod=b.payload.syncMethod,a.className=b.payload.className}}}),g=f.reducer,{updateOptions:h}=f.actions},32520:(a,b,c)=>{"use strict";c.d(b,{i:()=>g});var d=c(17118);function e(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function f(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?e(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):e(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var g=(a,b,c,e)=>{if(null==b)return d.k_;var g=function(a,b,c){return"axis"===b?"click"===c?a.axisInteraction.click:a.axisInteraction.hover:"click"===c?a.itemInteraction.click:a.itemInteraction.hover}(a,b,c);if(null==g)return d.k_;if(g.active)return g;if(a.keyboardInteraction.active)return a.keyboardInteraction;if(a.syncInteraction.active&&null!=a.syncInteraction.index)return a.syncInteraction;var h=!0===a.settings.active;if(null!=g.index){if(h)return f(f({},g),{},{active:!0})}else if(null!=e)return{active:!0,coordinate:void 0,dataKey:void 0,index:e};return f(f({},d.k_),{},{coordinate:g.coordinate})}},32751:(a,b,c)=>{"use strict";c.d(b,{N:()=>h});var d=c(22989),e=c(64279);function f(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function g(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?f(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):f(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var h=(a,b,c,f,h,i,j)=>{if(null!=b&&null!=i){var{chartData:k,computedData:l,dataStartIndex:m,dataEndIndex:n}=c;return a.reduce((a,c)=>{var o,p,q,r,s,{dataDefinedOnItem:t,settings:u}=c,v=function(a,b,c){return Array.isArray(a)&&a&&b+c!==0?a.slice(b,c+1):a}((o=t,p=k,null!=o?o:p),m,n),w=null!=(q=null==u?void 0:u.dataKey)?q:null==f?void 0:f.dataKey,x=null==u?void 0:u.nameKey;return Array.isArray(r=null!=f&&f.dataKey&&Array.isArray(v)&&!Array.isArray(v[0])&&"axis"===j?(0,d.eP)(v,f.dataKey,h):i(v,b,l,x))?r.forEach(b=>{var c=g(g({},u),{},{name:b.name,unit:b.unit,color:void 0,fill:void 0});a.push((0,e.GF)({tooltipEntrySettings:c,dataKey:b.dataKey,payload:b.payload,value:(0,e.kr)(b.payload,b.dataKey),name:b.name}))}):a.push((0,e.GF)({tooltipEntrySettings:u,dataKey:w,payload:r,value:(0,e.kr)(r,w),name:null!=(s=(0,e.kr)(r,x))?s:null==u?void 0:u.name})),a},[])}}},33731:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(97766),e=c(21424),f=c(26349);b.last=function(a){if(f.isArrayLike(a))return d.last(e.toArray(a))}},34258:(a,b,c)=>{"use strict";c.d(b,{e:()=>o,k:()=>p});var d=c(76067),e=c(17118),f=c(27977),g=c(77357),h=c(43075),i=c(75601),j=c(84648),k=c(72198),l=c(78242),m=(0,j.Mz)([l.J],a=>a.tooltipItemPayloads),n=(0,j.Mz)([m,k.x,(a,b,c)=>b,(a,b,c)=>c],(a,b,c,d)=>{var e=a.find(a=>a.settings.dataKey===d);if(null!=e){var{positions:f}=e;if(null!=f)return b(f,c)}}),o=(0,d.VP)("touchMove"),p=(0,d.Nc)();p.startListening({actionCreator:o,effect:(a,b)=>{var c=a.payload,d=b.getState(),j=(0,h.au)(d,d.tooltip.settings.shared);if("axis"===j){var k=(0,f.g)(d,(0,g.w)({clientX:c.touches[0].clientX,clientY:c.touches[0].clientY,currentTarget:c.currentTarget}));(null==k?void 0:k.activeIndex)!=null&&b.dispatch((0,e.Nt)({activeIndex:k.activeIndex,activeDataKey:void 0,activeCoordinate:k.activeCoordinate}))}else if("item"===j){var l,m=c.touches[0],o=document.elementFromPoint(m.clientX,m.clientY);if(!o||!o.getAttribute)return;var p=o.getAttribute(i.F0),q=null!=(l=o.getAttribute(i.um))?l:void 0,r=n(b.getState(),p,q);b.dispatch((0,e.RD)({activeDataKey:q,activeIndex:p,activeCoordinate:r}))}}})},34955:(a,b,c)=>{"use strict";c.d(b,{h:()=>n});var d=c(43210),e=c(49384),f=c(54186),g=c(19335),h=c(22989),i=c(73865);function j(){return(j=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var k=a=>{var{cx:b,cy:c,radius:d,angle:e,sign:f,isExternal:h,cornerRadius:i,cornerIsExternal:j}=a,k=i*(h?1:-1)+d,l=Math.asin(i/k)/g.Kg,m=j?e:e+f*l,n=(0,g.IZ)(b,c,k,m);return{center:n,circleTangency:(0,g.IZ)(b,c,d,m),lineTangency:(0,g.IZ)(b,c,k*Math.cos(l*g.Kg),j?e-f*l:e),theta:l}},l=a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:f,endAngle:i}=a,j=((a,b)=>(0,h.sA)(b-a)*Math.min(Math.abs(b-a),359.999))(f,i),k=f+j,l=(0,g.IZ)(b,c,e,f),m=(0,g.IZ)(b,c,e,k),n="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(e,",").concat(e,",0,\n    ").concat(+(Math.abs(j)>180),",").concat(+(f>k),",\n    ").concat(m.x,",").concat(m.y,"\n  ");if(d>0){var o=(0,g.IZ)(b,c,d,f),p=(0,g.IZ)(b,c,d,k);n+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(d,",").concat(d,",0,\n            ").concat(+(Math.abs(j)>180),",").concat(+(f<=k),",\n            ").concat(o.x,",").concat(o.y," Z")}else n+="L ".concat(b,",").concat(c," Z");return n},m={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},n=a=>{var b,c=(0,i.e)(a,m),{cx:g,cy:n,innerRadius:o,outerRadius:p,cornerRadius:q,forceCornerRadius:r,cornerIsExternal:s,startAngle:t,endAngle:u,className:v}=c;if(p<o||t===u)return null;var w=(0,e.$)("recharts-sector",v),x=p-o,y=(0,h.F4)(q,x,0,!0);return b=y>0&&360>Math.abs(t-u)?(a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,cornerRadius:f,forceCornerRadius:g,cornerIsExternal:i,startAngle:j,endAngle:m}=a,n=(0,h.sA)(m-j),{circleTangency:o,lineTangency:p,theta:q}=k({cx:b,cy:c,radius:e,angle:j,sign:n,cornerRadius:f,cornerIsExternal:i}),{circleTangency:r,lineTangency:s,theta:t}=k({cx:b,cy:c,radius:e,angle:m,sign:-n,cornerRadius:f,cornerIsExternal:i}),u=i?Math.abs(j-m):Math.abs(j-m)-q-t;if(u<0)return g?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(f,",").concat(f,",0,0,1,").concat(2*f,",0\n        a").concat(f,",").concat(f,",0,0,1,").concat(-(2*f),",0\n      "):l({cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:j,endAngle:m});var v="M ".concat(p.x,",").concat(p.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(n<0),",").concat(o.x,",").concat(o.y,"\n    A").concat(e,",").concat(e,",0,").concat(+(u>180),",").concat(+(n<0),",").concat(r.x,",").concat(r.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(n<0),",").concat(s.x,",").concat(s.y,"\n  ");if(d>0){var{circleTangency:w,lineTangency:x,theta:y}=k({cx:b,cy:c,radius:d,angle:j,sign:n,isExternal:!0,cornerRadius:f,cornerIsExternal:i}),{circleTangency:z,lineTangency:A,theta:B}=k({cx:b,cy:c,radius:d,angle:m,sign:-n,isExternal:!0,cornerRadius:f,cornerIsExternal:i}),C=i?Math.abs(j-m):Math.abs(j-m)-y-B;if(C<0&&0===f)return"".concat(v,"L").concat(b,",").concat(c,"Z");v+="L".concat(A.x,",").concat(A.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(n<0),",").concat(z.x,",").concat(z.y,"\n      A").concat(d,",").concat(d,",0,").concat(+(C>180),",").concat(+(n>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(n<0),",").concat(x.x,",").concat(x.y,"Z")}else v+="L".concat(b,",").concat(c,"Z");return v})({cx:g,cy:n,innerRadius:o,outerRadius:p,cornerRadius:Math.min(y,x/2),forceCornerRadius:r,cornerIsExternal:s,startAngle:t,endAngle:u}):l({cx:g,cy:n,innerRadius:o,outerRadius:p,startAngle:t,endAngle:u}),d.createElement("path",j({},(0,f.J9)(c,!0),{className:w,d:b}))}},35314:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isUnsafeProperty=function(a){return"__proto__"===a}},36023:(a,b)=>{"use strict";function c(a){return"symbol"==typeof a?1:null===a?2:void 0===a?3:4*(a!=a)}Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.compareValues=(a,b,d)=>{if(a!==b){let e=c(a),f=c(b);if(e===f&&0===e){if(a<b)return"desc"===d?1:-1;if(a>b)return"desc"===d?-1:1}return"desc"===d?f-e:e-f}return 0}},36166:(a,b,c)=>{"use strict";c.d(b,{N:()=>d});var d=(a,b)=>b},36304:(a,b,c)=>{"use strict";c.d(b,{n:()=>f});var d=c(43210),e=c(22989);function f(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",c=(0,d.useRef)((0,e.NF)(b)),f=(0,d.useRef)(a);return f.current!==a&&(c.current=(0,e.NF)(b),f.current=a),c.current}},37586:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(36023),e=c(76021),f=c(43574);b.orderBy=function(a,b,c,g){if(null==a)return[];c=g?void 0:c,Array.isArray(a)||(a=Object.values(a)),Array.isArray(b)||(b=null==b?[null]:[b]),0===b.length&&(b=[null]),Array.isArray(c)||(c=null==c?[]:[c]),c=c.map(a=>String(a));let h=(a,b)=>{let c=a;for(let a=0;a<b.length&&null!=c;++a)c=c[b[a]];return c},i=b.map(a=>(Array.isArray(a)&&1===a.length&&(a=a[0]),null==a||"function"==typeof a||Array.isArray(a)||e.isKey(a))?a:{key:a,path:f.toPath(a)});return a.map(a=>({original:a,criteria:i.map(b=>{var c,d;return c=b,null==(d=a)||null==c?d:"object"==typeof c&&"key"in c?Object.hasOwn(d,c.key)?d[c.key]:h(d,c.path):"function"==typeof c?c(d):Array.isArray(c)?h(d,c):"object"==typeof d?d[c]:d})})).slice().sort((a,b)=>{for(let e=0;e<i.length;e++){let f=d.compareValues(a.criteria[e],b.criteria[e],c[e]);if(0!==f)return f}return 0}).map(a=>a.original)}},37625:(a,b,c)=>{"use strict";c.d(b,{r:()=>f}),c(43210);var d=c(43209);c(17118);var e=c(83409);function f(a){var{fn:b,args:c}=a;return(0,d.j)(),(0,e.r)(),null}},39733:(a,b,c)=>{"use strict";a.exports=c(10907)},40144:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(87509);b.property=function(a){return function(b){return d.get(b,a)}}},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41887:(a,b,c)=>{"use strict";c.d(b,{y:()=>aC,L:()=>aB});var d=c(43210),e=c(49384),f=c(98986),g=c(54186),h=c(4236),i=c(27934),j=c(73865),k=c(19420),l=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function m(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function n(){return(n=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function o(a){var{direction:b,width:c,dataKey:e,isAnimationActive:j,animationBegin:m,animationDuration:o,animationEasing:p}=a,q=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,l),r=(0,g.J9)(q,!1),{data:s,dataPointFormatter:t,xAxisId:u,yAxisId:v,errorBarOffset:w}=(0,h.G9)(),x=(0,i.ZI)(u),y=(0,i.gi)(v);if((null==x?void 0:x.scale)==null||(null==y?void 0:y.scale)==null||null==s||"x"===b&&"number"!==x.type)return null;var z=s.map(a=>{var g,h,{x:i,y:l,value:q,errorVal:s}=t(a,e,b);if(!s)return null;var u=[];if(Array.isArray(s)?[g,h]=s:g=h=s,"x"===b){var{scale:v}=x,z=l+w,A=z+c,B=z-c,C=v(q-g),D=v(q+h);u.push({x1:D,y1:A,x2:D,y2:B}),u.push({x1:C,y1:z,x2:D,y2:z}),u.push({x1:C,y1:A,x2:C,y2:B})}else if("y"===b){var{scale:E}=y,F=i+w,G=F-c,H=F+c,I=E(q-g),J=E(q+h);u.push({x1:G,y1:J,x2:H,y2:J}),u.push({x1:F,y1:I,x2:F,y2:J}),u.push({x1:G,y1:I,x2:H,y2:I})}var K="".concat(i+w,"px ").concat(l+w,"px");return d.createElement(f.W,n({className:"recharts-errorBar",key:"bar-".concat(u.map(a=>"".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2)))},r),u.map(a=>{var b=j?{transformOrigin:"".concat(a.x1-5,"px")}:void 0;return d.createElement(k.i,{from:{transform:"scaleY(0)",transformOrigin:K},to:{transform:"scaleY(1)",transformOrigin:K},begin:m,easing:p,isActive:j,duration:o,key:"line-".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2),style:{transformOrigin:K}},d.createElement("line",n({},a,{style:b})))}))});return d.createElement(f.W,{className:"recharts-errorBars"},z)}var p=(0,d.createContext)(void 0);function q(a){var{direction:b,children:c}=a;return d.createElement(p.Provider,{value:b},c)}var r={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function s(a){var b,c,e=(b=a.direction,c=(0,d.useContext)(p),null!=b?b:null!=c?c:"x"),{width:f,isAnimationActive:g,animationBegin:i,animationDuration:k,animationEasing:l}=(0,j.e)(a,r);return d.createElement(d.Fragment,null,d.createElement(h.pU,{dataKey:a.dataKey,direction:e}),d.createElement(o,n({},a,{direction:e,width:f,isAnimationActive:g,animationBegin:i,animationDuration:k,animationEasing:l})))}class t extends d.Component{render(){return d.createElement(s,this.props)}}m(t,"defaultProps",r),m(t,"displayName","ErrorBar");var u=c(25679),v=c(98845),w=c(22989),x=c(20237),y=c(64279),z=c(4057),A=c(67629),B=["x","y"];function C(){return(C=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function D(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function E(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?D(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):D(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function F(a,b){var{x:c,y:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,B),f=parseInt("".concat(c),10),g=parseInt("".concat(d),10),h=parseInt("".concat(b.height||e.height),10),i=parseInt("".concat(b.width||e.width),10);return E(E(E(E(E({},b),e),f?{x:f}:{}),g?{y:g}:{}),{},{height:h,width:i,name:b.name,radius:b.radius})}function G(a){return d.createElement(A.y,C({shapeType:"rectangle",propTransformer:F,activeClassName:"recharts-active-bar"},a))}var H=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(c,d)=>{if((0,w.Et)(a))return a;var e=(0,w.Et)(c)||(0,w.uy)(c);return e?a(c,d):(e||function(a,b){if(!a)throw Error("Invariant failed")}(!1),b)}},I=c(61545),J=c(37625),K=c(43209),L=c(30802),M=()=>{var a=(0,K.j)();return(0,d.useEffect)(()=>(a((0,L.lm)()),()=>{a((0,L.Ch)())})),null},N=c(46993),O=c(51426),P=c(84648),Q=c(85621),R=c(57282),S=c(76966),T=c(97350),U=c(12128);function V(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function W(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?V(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):V(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var X=(a,b,c,d,e)=>e,Y=(a,b,c)=>{var d=null!=c?c:a;if(!(0,w.uy)(d))return(0,w.F4)(d,b,0)},Z=(0,P.Mz)([O.fz,Q.ld,(a,b)=>b,(a,b,c)=>c,(a,b,c,d)=>d],(a,b,c,d,e)=>b.filter(b=>"horizontal"===a?b.xAxisId===c:b.yAxisId===d).filter(a=>a.isPanorama===e).filter(a=>!1===a.hide).filter(a=>"bar"===a.type));function $(a){return null!=a.stackId&&null!=a.dataKey}var _=(0,P.Mz)([Z,T.x3,(a,b,c)=>"horizontal"===(0,O.fz)(a)?(0,Q.BQ)(a,"xAxis",b):(0,Q.BQ)(a,"yAxis",c)],(a,b,c)=>{var d=a.filter($),e=a.filter(a=>null==a.stackId);return[...Object.entries(d.reduce((a,b)=>(a[b.stackId]||(a[b.stackId]=[]),a[b.stackId].push(b),a),{})).map(a=>{var[d,e]=a;return{stackId:d,dataKeys:e.map(a=>a.dataKey),barSize:Y(b,c,e[0].barSize)}}),...e.map(a=>({stackId:void 0,dataKeys:[a.dataKey].filter(a=>null!=a),barSize:Y(b,c,a.barSize)}))]}),aa=(a,b,c,d)=>{var e,f;return"horizontal"===(0,O.fz)(a)?(e=(0,Q.Gx)(a,"xAxis",b,d),f=(0,Q.CR)(a,"xAxis",b,d)):(e=(0,Q.Gx)(a,"yAxis",c,d),f=(0,Q.CR)(a,"yAxis",c,d)),(0,y.Hj)(e,f)},ab=(0,P.Mz)([_,T.JN,T._5,T.gY,(a,b,c,d,e)=>{var f,g,h,i,j=(0,O.fz)(a),k=(0,T.JN)(a),{maxBarSize:l}=e,m=(0,w.uy)(l)?k:l;return"horizontal"===j?(h=(0,Q.Gx)(a,"xAxis",b,d),i=(0,Q.CR)(a,"xAxis",b,d)):(h=(0,Q.Gx)(a,"yAxis",c,d),i=(0,Q.CR)(a,"yAxis",c,d)),null!=(f=null!=(g=(0,y.Hj)(h,i,!0))?g:m)?f:0},aa,(a,b,c,d,e)=>e.maxBarSize],(a,b,c,d,e,f,g)=>{var h=function(a,b,c,d,e){var f,g=d.length;if(!(g<1)){var h=(0,w.F4)(a,c,0,!0),i=[];if((0,U.H)(d[0].barSize)){var j=!1,k=c/g,l=d.reduce((a,b)=>a+(b.barSize||0),0);(l+=(g-1)*h)>=c&&(l-=(g-1)*h,h=0),l>=c&&k>0&&(j=!0,k*=.9,l=g*k);var m={offset:((c-l)/2|0)-h,size:0};f=d.reduce((a,b)=>{var c,d=[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:m.offset+m.size+h,size:j?k:null!=(c=b.barSize)?c:0}}];return m=d[d.length-1].position,d},i)}else{var n=(0,w.F4)(b,c,0,!0);c-2*n-(g-1)*h<=0&&(h=0);var o=(c-2*n-(g-1)*h)/g;o>1&&(o>>=0);var p=(0,U.H)(e)?Math.min(o,e):o;f=d.reduce((a,b,c)=>[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:n+(o+h)*c+(o-p)/2,size:p}}],i)}return f}}(c,d,e!==f?e:f,a,(0,w.uy)(g)?b:g);return e!==f&&null!=h&&(h=h.map(a=>W(W({},a),{},{position:W(W({},a.position),{},{offset:a.position.offset-e/2})}))),h}),ac=(0,P.Mz)([ab,X],(a,b)=>{if(null!=a){var c=a.find(a=>a.stackId===b.stackId&&a.dataKeys.includes(b.dataKey));if(null!=c)return c.position}}),ad=(0,P.Mz)([Q.ld,X],(a,b)=>{if(a.some(a=>"bar"===a.type&&b.dataKey===a.dataKey&&b.stackId===a.stackId&&b.stackId===a.stackId))return b}),ae=(0,P.Mz)([(a,b,c,d)=>"horizontal"===(0,O.fz)(a)?(0,Q.TC)(a,"yAxis",c,d):(0,Q.TC)(a,"xAxis",b,d),X],(a,b)=>{if(!a||(null==b?void 0:b.dataKey)==null)return;var{stackId:c}=b;if(null!=c){var d=a[c];if(d){var{stackedData:e}=d;if(e)return e.find(a=>a.key===b.dataKey)}}}),af=(0,P.Mz)([S.HZ,(a,b,c,d)=>(0,Q.Gx)(a,"xAxis",b,d),(a,b,c,d)=>(0,Q.Gx)(a,"yAxis",c,d),(a,b,c,d)=>(0,Q.CR)(a,"xAxis",b,d),(a,b,c,d)=>(0,Q.CR)(a,"yAxis",c,d),ac,O.fz,R.HS,aa,ae,ad,(a,b,c,d,e,f)=>f],(a,b,c,d,e,f,g,h,i,j,k,l)=>{var m,{chartData:n,dataStartIndex:o,dataEndIndex:p}=h;if(null!=k&&null!=f&&("horizontal"===g||"vertical"===g)&&null!=b&&null!=c&&null!=d&&null!=e&&null!=i){var{data:q}=k;if(null!=(m=null!=q&&q.length>0?q:null==n?void 0:n.slice(o,p+1)))return aB({layout:g,barSettings:k,pos:f,bandSize:i,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,stackedData:j,displayedData:m,offset:a,cells:l})}}),ag=c(83409),ah=c(69009),ai=c(14956),aj=c(36304),ak=["onMouseEnter","onMouseLeave","onClick"],al=["value","background","tooltipPosition"],am=["onMouseEnter","onClick","onMouseLeave"];function an(){return(an=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function ao(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ap(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ao(Object(c),!0).forEach(function(b){aq(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ao(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function aq(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function ar(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function as(a){var{dataKey:b,stroke:c,strokeWidth:d,fill:e,name:f,hide:g,unit:h}=a;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:c,strokeWidth:d,fill:e,dataKey:b,nameKey:void 0,name:(0,y.uM)(f,b),hide:g,type:a.tooltipType,color:a.fill,unit:h}}}function at(a){var b=(0,K.G)(ah.A2),{data:c,dataKey:e,background:f,allOtherBarProps:h}=a,{onMouseEnter:i,onMouseLeave:j,onClick:k}=h,l=ar(h,ak),m=(0,I.Cj)(i,e),n=(0,I.Pg)(j),o=(0,I.Ub)(k,e);if(!f||null==c)return null;var p=(0,g.J9)(f,!1);return d.createElement(d.Fragment,null,c.map((a,c)=>{var{value:g,background:h,tooltipPosition:i}=a,j=ar(a,al);if(!h)return null;var k=m(a,c),q=n(a,c),r=o(a,c),s=ap(ap(ap(ap(ap({option:f,isActive:String(c)===b},j),{},{fill:"#eee"},h),p),(0,z.XC)(l,a,c)),{},{onMouseEnter:k,onMouseLeave:q,onClick:r,dataKey:e,index:c,className:"recharts-bar-background-rectangle"});return d.createElement(G,an({key:"background-bar-".concat(c)},s))}))}function au(a){var{data:b,props:c,showLabels:e}=a,h=(0,g.J9)(c,!1),{shape:i,dataKey:j,activeBar:k}=c,l=(0,K.G)(ah.A2),m=(0,K.G)(ah.Xb),{onMouseEnter:n,onClick:o,onMouseLeave:p}=c,q=ar(c,am),r=(0,I.Cj)(n,j),s=(0,I.Pg)(p),t=(0,I.Ub)(o,j);return b?d.createElement(d.Fragment,null,b.map((a,b)=>{var c=k&&String(b)===l&&(null==m||j===m),e=ap(ap(ap({},h),a),{},{isActive:c,option:c?k:i,index:b,dataKey:j});return d.createElement(f.W,an({className:"recharts-bar-rectangle"},(0,z.XC)(q,a,b),{onMouseEnter:r(a,b),onMouseLeave:s(a,b),onClick:t(a,b),key:"rectangle-".concat(null==a?void 0:a.x,"-").concat(null==a?void 0:a.y,"-").concat(null==a?void 0:a.value,"-").concat(b)}),d.createElement(G,e))}),e&&v.Z.renderCallByParent(c,b)):null}function av(a){var{props:b,previousRectanglesRef:c}=a,{data:e,layout:g,isAnimationActive:h,animationBegin:i,animationDuration:j,animationEasing:l,onAnimationEnd:m,onAnimationStart:n}=b,o=c.current,p=(0,aj.n)(b,"recharts-bar-"),[q,r]=(0,d.useState)(!1),s=(0,d.useCallback)(()=>{"function"==typeof m&&m(),r(!1)},[m]),t=(0,d.useCallback)(()=>{"function"==typeof n&&n(),r(!0)},[n]);return d.createElement(k.i,{begin:i,duration:j,isActive:h,easing:l,from:{t:0},to:{t:1},onAnimationEnd:s,onAnimationStart:t,key:p},a=>{var{t:h}=a,i=1===h?e:e.map((a,b)=>{var c=o&&o[b];if(c){var d=(0,w.Dj)(c.x,a.x),e=(0,w.Dj)(c.y,a.y),f=(0,w.Dj)(c.width,a.width),i=(0,w.Dj)(c.height,a.height);return ap(ap({},a),{},{x:d(h),y:e(h),width:f(h),height:i(h)})}if("horizontal"===g){var j=(0,w.Dj)(0,a.height)(h);return ap(ap({},a),{},{y:a.y+a.height-j,height:j})}var k=(0,w.Dj)(0,a.width)(h);return ap(ap({},a),{},{width:k})});return h>0&&(c.current=i),d.createElement(f.W,null,d.createElement(au,{props:b,data:i,showLabels:!q}))})}function aw(a){var{data:b,isAnimationActive:c}=a,e=(0,d.useRef)(null);return c&&b&&b.length&&(null==e.current||e.current!==b)?d.createElement(av,{previousRectanglesRef:e,props:a}):d.createElement(au,{props:a,data:b,showLabels:!0})}var ax=(a,b)=>{var c=Array.isArray(a.value)?a.value[1]:a.value;return{x:a.x,y:a.y,value:c,errorVal:(0,y.kr)(a,b)}};class ay extends d.PureComponent{constructor(){super(...arguments),aq(this,"id",(0,w.NF)("recharts-bar-"))}render(){var{hide:a,data:b,dataKey:c,className:g,xAxisId:h,yAxisId:i,needClip:j,background:k,id:l,layout:m}=this.props;if(a)return null;var n=(0,e.$)("recharts-bar",g),o=(0,w.uy)(l)?this.id:l;return d.createElement(f.W,{className:n},j&&d.createElement("defs",null,d.createElement(N.Q,{clipPathId:o,xAxisId:h,yAxisId:i})),d.createElement(f.W,{className:"recharts-bar-rectangles",clipPath:j?"url(#clipPath-".concat(o,")"):null},d.createElement(at,{data:b,dataKey:c,background:k,allOtherBarProps:this.props}),d.createElement(aw,this.props)),d.createElement(q,{direction:"horizontal"===m?"y":"x"},this.props.children))}}var az={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!x.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function aA(a){var b,{xAxisId:c,yAxisId:e,hide:f,legendType:i,minPointSize:k,activeBar:l,animationBegin:m,animationDuration:n,animationEasing:o,isAnimationActive:p}=(0,j.e)(a,az),{needClip:q}=(0,N.l)(c,e),r=(0,O.WX)(),s=(0,ag.r)(),t=(0,d.useMemo)(()=>({barSize:a.barSize,data:void 0,dataKey:a.dataKey,maxBarSize:a.maxBarSize,minPointSize:k,stackId:(0,y.$8)(a.stackId)}),[a.barSize,a.dataKey,a.maxBarSize,k,a.stackId]),v=(0,g.aS)(a.children,u.f),w=(0,K.G)(a=>af(a,c,e,s,t,v));if("vertical"!==r&&"horizontal"!==r)return null;var x=null==w?void 0:w[0];return b=null==x||null==x.height||null==x.width?0:"vertical"===r?x.height/2:x.width/2,d.createElement(h.zk,{xAxisId:c,yAxisId:e,data:w,dataPointFormatter:ax,errorBarOffset:b},d.createElement(ay,an({},a,{layout:r,needClip:q,data:w,xAxisId:c,yAxisId:e,hide:f,legendType:i,minPointSize:k,activeBar:l,animationBegin:m,animationDuration:n,animationEasing:o,isAnimationActive:p})))}function aB(a){var{layout:b,barSettings:{dataKey:c,minPointSize:d},pos:e,bandSize:f,xAxis:g,yAxis:h,xAxisTicks:i,yAxisTicks:j,stackedData:k,displayedData:l,offset:m,cells:n}=a,o="horizontal"===b?h:g,p=k?o.scale.domain():null,q=(0,y.DW)({numericAxis:o});return l.map((a,l)=>{k?r=(0,y._f)(k[l],p):Array.isArray(r=(0,y.kr)(a,c))||(r=[q,r]);var o=H(d,0)(r[1],l);if("horizontal"===b){var r,s,t,u,v,x,z,[A,B]=[h.scale(r[0]),h.scale(r[1])];s=(0,y.y2)({axis:g,ticks:i,bandSize:f,offset:e.offset,entry:a,index:l}),t=null!=(z=null!=B?B:A)?z:void 0,u=e.size;var C=A-B;if(v=(0,w.M8)(C)?0:C,x={x:s,y:m.top,width:u,height:m.height},Math.abs(o)>0&&Math.abs(v)<Math.abs(o)){var D=(0,w.sA)(v||o)*(Math.abs(o)-Math.abs(v));t-=D,v+=D}}else{var[E,F]=[g.scale(r[0]),g.scale(r[1])];if(s=E,t=(0,y.y2)({axis:h,ticks:j,bandSize:f,offset:e.offset,entry:a,index:l}),u=F-E,v=e.size,x={x:m.left,y:t,width:m.width,height:v},Math.abs(o)>0&&Math.abs(u)<Math.abs(o)){var G=(0,w.sA)(u||o)*(Math.abs(o)-Math.abs(u));u+=G}}return ap(ap({},a),{},{x:s,y:t,width:u,height:v,value:k?r:r[1],payload:a,background:x,tooltipPosition:{x:s+u/2,y:t+v/2}},n&&n[l]&&n[l].props)})}class aC extends d.PureComponent{render(){return d.createElement(h._S,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},d.createElement(M,null),d.createElement(ai.A,{legendPayload:(a=>{var{dataKey:b,name:c,fill:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:(0,y.uM)(c,b),payload:a}]})(this.props)}),d.createElement(J.r,{fn:as,args:this.props}),d.createElement(aA,this.props))}}aq(aC,"displayName","Bar"),aq(aC,"defaultProps",az)},42066:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(14454);b.isMatch=function(a,b){return d.isMatchWith(a,b,()=>void 0)}},42750:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(15708);b.toFinite=function(a){return a?(a=d.toNumber(a))===1/0||a===-1/0?(a<0?-1:1)*Number.MAX_VALUE:a==a?a:0:0===a?a:0}},43075:(a,b,c)=>{"use strict";c.d(b,{$g:()=>g,Hw:()=>f,Td:()=>i,au:()=>h,xH:()=>e});var d=c(43209),e=a=>a.options.defaultTooltipEventType,f=a=>a.options.validateTooltipEventTypes;function g(a,b,c){if(null==a)return b;var d=a?"axis":"item";return null==c?b:c.includes(d)?d:b}function h(a,b){return g(b,e(a),f(a))}function i(a){return(0,d.G)(b=>h(b,a))}},43084:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(20911);b.throttle=function(a,b=0,c={}){let{leading:e=!0,trailing:f=!0}=c;return d.debounce(a,b,{leading:e,maxWait:b,trailing:f})}},43209:(a,b,c)=>{"use strict";c.d(b,{G:()=>l,j:()=>h});var d=c(39733),e=c(43210),f=c(26652),g=a=>a,h=()=>{var a=(0,e.useContext)(f.E);return a?a.store.dispatch:g},i=()=>{},j=()=>i,k=(a,b)=>a===b;function l(a){var b=(0,e.useContext)(f.E);return(0,d.useSyncExternalStoreWithSelector)(b?b.subscription.addNestedSub:j,b?b.store.getState:i,b?b.store.getState:i,b?a:i,k)}},43574:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toPath=function(a){let b=[],c=a.length;if(0===c)return b;let d=0,e="",f="",g=!1;for(46===a.charCodeAt(0)&&(b.push(""),d++);d<c;){let h=a[d];f?"\\"===h&&d+1<c?e+=a[++d]:h===f?f="":e+=h:g?'"'===h||"'"===h?f=h:"]"===h?(g=!1,b.push(e),e=""):e+=h:"["===h?(g=!0,e&&(b.push(e),e="")):"."===h?e&&(b.push(e),e=""):e+=h,d++}return e&&b.push(e),b}},44919:(a,b,c)=>{"use strict";c.d(b,{x:()=>g,y:()=>f});var d=c(76067),e=c(69009),f=(0,d.VP)("externalEvent"),g=(0,d.Nc)();g.startListening({actionCreator:f,effect:(a,b)=>{if(null!=a.payload.handler){var c=b.getState(),d={activeCoordinate:(0,e.eE)(c),activeDataKey:(0,e.Xb)(c),activeIndex:(0,e.A2)(c),activeLabel:(0,e.BZ)(c),activeTooltipIndex:(0,e.A2)(c),isTooltipActive:(0,e.yn)(c)};a.payload.handler(d,a.payload.reactEvent)}}})},45263:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(59618),e=c(92681),f=c(90830),g=c(17617);b.uniqBy=function(a,b=e.identity){return f.isArrayLikeObject(a)?d.uniqBy(Array.from(a),g.iteratee(b)):[]}},45796:(a,b,c)=>{"use strict";c.d(b,{s:()=>f});var d=c(23854),e=c.n(d);function f(a,b,c){return!0===b?e()(a,c):"function"==typeof b?e()(a,b):a}},46993:(a,b,c)=>{"use strict";c.d(b,{Q:()=>i,l:()=>h});var d=c(43210),e=c(43209),f=c(85621),g=c(27934);function h(a,b){var c,d,g=(0,e.G)(b=>(0,f.Rl)(b,a)),h=(0,e.G)(a=>(0,f.sf)(a,b)),i=null!=(c=null==g?void 0:g.allowDataOverflow)?c:f.PU.allowDataOverflow,j=null!=(d=null==h?void 0:h.allowDataOverflow)?d:f.cd.allowDataOverflow;return{needClip:i||j,needClipX:i,needClipY:j}}function i(a){var{xAxisId:b,yAxisId:c,clipPathId:e}=a,f=(0,g.oM)(),{needClipX:i,needClipY:j,needClip:k}=h(b,c);if(!k)return null;var{x:l,y:m,width:n,height:o}=f;return d.createElement("clipPath",{id:"clipPath-".concat(e)},d.createElement("rect",{x:i?l:l-n/2,y:j?m:m-o/2,width:i?n:2*n,height:j?o:2*o}))}},48130:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isLength=function(a){return Number.isSafeInteger(a)&&a>=0}},48482:(a,b,c)=>{"use strict";c.d(b,{u:()=>l});var d=c(49384),e=c(43210),f=c(67766),g=c.n(f),h=c(22989),i=c(10521);function j(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function k(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?j(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):j(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var l=(0,e.forwardRef)((a,b)=>{var{aspect:c,initialDimension:f={width:-1,height:-1},width:j="100%",height:l="100%",minWidth:m=0,minHeight:n,maxHeight:o,children:p,debounce:q=0,id:r,className:s,onResize:t,style:u={}}=a,v=(0,e.useRef)(null),w=(0,e.useRef)();w.current=t,(0,e.useImperativeHandle)(b,()=>v.current);var[x,y]=(0,e.useState)({containerWidth:f.width,containerHeight:f.height}),z=(0,e.useCallback)((a,b)=>{y(c=>{var d=Math.round(a),e=Math.round(b);return c.containerWidth===d&&c.containerHeight===e?c:{containerWidth:d,containerHeight:e}})},[]);(0,e.useEffect)(()=>{var a=a=>{var b,{width:c,height:d}=a[0].contentRect;z(c,d),null==(b=w.current)||b.call(w,c,d)};q>0&&(a=g()(a,q,{trailing:!0,leading:!1}));var b=new ResizeObserver(a),{width:c,height:d}=v.current.getBoundingClientRect();return z(c,d),b.observe(v.current),()=>{b.disconnect()}},[z,q]);var A=(0,e.useMemo)(()=>{var{containerWidth:a,containerHeight:b}=x;if(a<0||b<0)return null;(0,i.R)((0,h._3)(j)||(0,h._3)(l),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",j,l),(0,i.R)(!c||c>0,"The aspect(%s) must be greater than zero.",c);var d=(0,h._3)(j)?a:j,f=(0,h._3)(l)?b:l;return c&&c>0&&(d?f=d/c:f&&(d=f*c),o&&f>o&&(f=o)),(0,i.R)(d>0||f>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",d,f,j,l,m,n,c),e.Children.map(p,a=>(0,e.cloneElement)(a,{width:d,height:f,style:k({width:d,height:f},a.props.style)}))},[c,p,l,o,n,m,x,j]);return e.createElement("div",{id:r?"".concat(r):void 0,className:(0,d.$)("recharts-responsive-container",s),style:k(k({},u),{},{width:j,height:l,minWidth:m,minHeight:n,maxHeight:o}),ref:v},e.createElement("div",{style:{width:0,height:0,overflow:"visible"}},A))})},48657:(a,b,c)=>{"use strict";function d(a){return"object"==typeof a&&"length"in a?a:Array.from(a)}c.d(b,{A:()=>d}),Array.prototype.slice},49396:(a,b,c)=>{"use strict";c.d(b,{o:()=>d});var d=(a,b,c,d,e,f,g,h)=>{if(null!=f&&null!=h){var i=g[0],j=null==i?void 0:h(i.positions,f);if(null!=j)return j;var k=null==e?void 0:e[Number(f)];if(k)if("horizontal"===c)return{x:k.coordinate,y:(d.top+b)/2};else return{x:(d.left+a)/2,y:k.coordinate}}}},49605:(a,b,c)=>{"use strict";c.d(b,{dl:()=>i,lJ:()=>h,uN:()=>f});var d=c(76067),e=c(22989);function f(a,b){if(b){var c=Number.parseInt(b,10);if(!(0,e.M8)(c))return null==a?void 0:a[c]}}var g=(0,d.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:a=>{null==a.eventEmitter&&(a.eventEmitter=Symbol("rechartsEventEmitter"))}}}),h=g.reducer,{createEventEmitter:i}=g.actions},49899:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObjectLike=function(a){return"object"==typeof a&&null!==a}},51426:(a,b,c)=>{"use strict";c.d(b,{Kp:()=>o,W7:()=>k,WX:()=>q,fz:()=>p,rY:()=>m,sk:()=>i,yi:()=>l}),c(43210);var d=c(43209),e=c(76966),f=c(86445),g=c(83409),h=c(94728),i=()=>{var a,b=(0,g.r)(),c=(0,d.G)(e.Ds),f=(0,d.G)(h.U),i=null==(a=(0,d.G)(h.C))?void 0:a.padding;return b&&f&&i?{width:f.width-i.left-i.right,height:f.height-i.top-i.bottom,x:i.left,y:i.top}:c},j={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},k=()=>{var a;return null!=(a=(0,d.G)(e.HZ))?a:j},l=()=>(0,d.G)(f.Lp),m=()=>(0,d.G)(f.A$),n={top:0,right:0,bottom:0,left:0},o=()=>{var a;return null!=(a=(0,d.G)(a=>a.layout.margin))?a:n},p=a=>a.layout.layoutType,q=()=>(0,d.G)(p)},52371:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92923);b.cloneDeep=function(a){return d.cloneDeepWithImpl(a,void 0,a,new Map,void 0)}},52693:(a,b,c)=>{"use strict";c.d(b,{B_:()=>e,JK:()=>f,Vp:()=>i,gX:()=>g,hF:()=>h});var d=(0,c(76067).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(a,b){a.layoutType=b.payload},setChartSize(a,b){a.width=b.payload.width,a.height=b.payload.height},setMargin(a,b){a.margin.top=b.payload.top,a.margin.right=b.payload.right,a.margin.bottom=b.payload.bottom,a.margin.left=b.payload.left},setScale(a,b){a.scale=b.payload}}}),{setMargin:e,setLayout:f,setChartSize:g,setScale:h}=d.actions,i=d.reducer},53044:(a,b,c)=>{"use strict";c.d(b,{CU:()=>k,Lx:()=>i,h1:()=>h,hx:()=>g,u3:()=>j});var d=c(76067),e=c(71392),f=(0,d.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(a,b){a.size.width=b.payload.width,a.size.height=b.payload.height},setLegendSettings(a,b){a.settings.align=b.payload.align,a.settings.layout=b.payload.layout,a.settings.verticalAlign=b.payload.verticalAlign,a.settings.itemSorter=b.payload.itemSorter},addLegendPayload(a,b){a.payload.push((0,e.h4)(b.payload))},removeLegendPayload(a,b){var c=(0,e.ss)(a).payload.indexOf((0,e.h4)(b.payload));c>-1&&a.payload.splice(c,1)}}}),{setLegendSize:g,setLegendSettings:h,addLegendPayload:i,removeLegendPayload:j}=f.actions,k=f.reducer},53416:(a,b,c)=>{"use strict";c.d(b,{I:()=>d});var d=(a,b)=>{if(a&&b)return null!=a&&a.reversed?[b[1],b[0]]:b}},54024:(a,b,c)=>{"use strict";c.d(b,{P:()=>o});var d=c(43210),e=c(64231),f=c(13420),g=c(71680),h=c(25893),i=c(2264),j=c(73865),k=c(12128),l=["width","height"];function m(){return(m=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var n={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},o=(0,d.forwardRef)(function(a,b){var c,o=(0,j.e)(a.categoricalChartProps,n),{width:p,height:q}=o,r=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(o,l);if(!(0,k.F)(p)||!(0,k.F)(q))return null;var{chartName:s,defaultTooltipEventType:t,validateTooltipEventTypes:u,tooltipPayloadSearcher:v,categoricalChartProps:w}=a;return d.createElement(e.J,{preloadedState:{options:{chartName:s,defaultTooltipEventType:t,validateTooltipEventTypes:u,tooltipPayloadSearcher:v,eventEmitter:void 0}},reduxStoreName:null!=(c=w.id)?c:s},d.createElement(f.TK,{chartData:w.data}),d.createElement(g.s,{width:p,height:q,layout:o.layout,margin:o.margin}),d.createElement(h.p,{accessibilityLayer:o.accessibilityLayer,barCategoryGap:o.barCategoryGap,maxBarSize:o.maxBarSize,stackOffset:o.stackOffset,barGap:o.barGap,barSize:o.barSize,syncId:o.syncId,syncMethod:o.syncMethod,className:o.className}),d.createElement(i.L,m({},r,{width:p,height:q,ref:b})))})},54186:(a,b,c)=>{"use strict";c.d(b,{J9:()=>p,aS:()=>n,y$:()=>o});var d=c(5664),e=c.n(d),f=c(43210),g=c(29632),h=c(22989),i=c(4057),j=a=>"string"==typeof a?a:a?a.displayName||a.name||"Component":"",k=null,l=null,m=a=>{if(a===k&&Array.isArray(l))return l;var b=[];return f.Children.forEach(a,a=>{(0,h.uy)(a)||((0,g.isFragment)(a)?b=b.concat(m(a.props.children)):b.push(a))}),l=b,k=a,b};function n(a,b){var c=[],d=[];return d=Array.isArray(b)?b.map(a=>j(a)):[j(b)],m(a).forEach(a=>{var b=e()(a,"type.displayName")||e()(a,"type.name");-1!==d.indexOf(b)&&c.push(a)}),c}var o=a=>!a||"object"!=typeof a||!("clipDot"in a)||!!a.clipDot,p=(a,b,c)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var d=a;if((0,f.isValidElement)(a)&&(d=a.props),"object"!=typeof d&&"function"!=typeof d)return null;var e={};return Object.keys(d).forEach(a=>{var f;((a,b,c,d)=>{var e,f=null!=(e=d&&(null===i.VU||void 0===i.VU?void 0:i.VU[d]))?e:[];return b.startsWith("data-")||"function"!=typeof a&&(d&&f.includes(b)||i.QQ.includes(b))||c&&i.j2.includes(b)})(null==(f=d)?void 0:f[a],a,b,c)&&(e[a]=d[a])}),e}},55100:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.debounce=function(a,b,{signal:c,edges:d}={}){let e,f=null,g=null!=d&&d.includes("leading"),h=null==d||d.includes("trailing"),i=()=>{null!==f&&(a.apply(e,f),e=void 0,f=null)},j=null,k=()=>{null!=j&&clearTimeout(j),j=setTimeout(()=>{j=null,h&&i(),l()},b)},l=()=>{null!==j&&(clearTimeout(j),j=null),e=void 0,f=null},m=function(...a){if(c?.aborted)return;e=this,f=a;let b=null==j;k(),g&&b&&i()};return m.schedule=k,m.cancel=l,m.flush=()=>{i()},c?.addEventListener("abort",l,{once:!0}),m}},55146:(a,b,c)=>{"use strict";c.d(b,{B8:()=>D,UC:()=>F,bL:()=>C,l9:()=>E});var d=c(43210),e=c(70569),f=c(11273),g=c(72942),h=c(46059),i=c(14163),j=c(43),k=c(65551),l=c(96963),m=c(60687),n="Tabs",[o,p]=(0,f.A)(n,[g.RG]),q=(0,g.RG)(),[r,s]=o(n),t=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,onValueChange:e,defaultValue:f,orientation:g="horizontal",dir:h,activationMode:o="automatic",...p}=a,q=(0,j.jH)(h),[s,t]=(0,k.i)({prop:d,onChange:e,defaultProp:f??"",caller:n});return(0,m.jsx)(r,{scope:c,baseId:(0,l.B)(),value:s,onValueChange:t,orientation:g,dir:q,activationMode:o,children:(0,m.jsx)(i.sG.div,{dir:q,"data-orientation":g,...p,ref:b})})});t.displayName=n;var u="TabsList",v=d.forwardRef((a,b)=>{let{__scopeTabs:c,loop:d=!0,...e}=a,f=s(u,c),h=q(c);return(0,m.jsx)(g.bL,{asChild:!0,...h,orientation:f.orientation,dir:f.dir,loop:d,children:(0,m.jsx)(i.sG.div,{role:"tablist","aria-orientation":f.orientation,...e,ref:b})})});v.displayName=u;var w="TabsTrigger",x=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,disabled:f=!1,...h}=a,j=s(w,c),k=q(c),l=A(j.baseId,d),n=B(j.baseId,d),o=d===j.value;return(0,m.jsx)(g.q7,{asChild:!0,...k,focusable:!f,active:o,children:(0,m.jsx)(i.sG.button,{type:"button",role:"tab","aria-selected":o,"aria-controls":n,"data-state":o?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:l,...h,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():j.onValueChange(d)}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&j.onValueChange(d)}),onFocus:(0,e.m)(a.onFocus,()=>{let a="manual"!==j.activationMode;o||f||!a||j.onValueChange(d)})})})});x.displayName=w;var y="TabsContent",z=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,forceMount:f,children:g,...j}=a,k=s(y,c),l=A(k.baseId,e),n=B(k.baseId,e),o=e===k.value,p=d.useRef(o);return d.useEffect(()=>{let a=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,m.jsx)(h.C,{present:f||o,children:({present:c})=>(0,m.jsx)(i.sG.div,{"data-state":o?"active":"inactive","data-orientation":k.orientation,role:"tabpanel","aria-labelledby":l,hidden:!c,id:n,tabIndex:0,...j,ref:b,style:{...a.style,animationDuration:p.current?"0s":void 0},children:c&&g})})});function A(a,b){return`${a}-trigger-${b}`}function B(a,b){return`${a}-content-${b}`}z.displayName=y;var C=t,D=v,E=x,F=z},56651:(a,b,c)=>{"use strict";c.d(b,{r:()=>u});var d=c(43210),e=c(49605),f=c(64231),g=c(13420),h=c(71680),i=c(25893),j=c(43209);function k(a){return(0,j.j)(),null}c(61645);var l=c(2264),m=c(73865),n=c(12128),o=["width","height","layout"];function p(){return(p=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var q={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},r=(0,d.forwardRef)(function(a,b){var c,e=(0,m.e)(a.categoricalChartProps,q),{width:j,height:r,layout:s}=e,t=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(e,o);if(!(0,n.F)(j)||!(0,n.F)(r))return null;var{chartName:u,defaultTooltipEventType:v,validateTooltipEventTypes:w,tooltipPayloadSearcher:x}=a;return d.createElement(f.J,{preloadedState:{options:{chartName:u,defaultTooltipEventType:v,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,eventEmitter:void 0}},reduxStoreName:null!=(c=e.id)?c:u},d.createElement(g.TK,{chartData:e.data}),d.createElement(h.s,{width:j,height:r,layout:s,margin:e.margin}),d.createElement(i.p,{accessibilityLayer:e.accessibilityLayer,barCategoryGap:e.barCategoryGap,maxBarSize:e.maxBarSize,stackOffset:e.stackOffset,barGap:e.barGap,barSize:e.barSize,syncId:e.syncId,syncMethod:e.syncMethod,className:e.className}),d.createElement(k,{cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius}),d.createElement(l.L,p({width:j,height:r},t,{ref:b})))}),s=["item"],t={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},u=(0,d.forwardRef)((a,b)=>{var c=(0,m.e)(a,t);return d.createElement(r,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:s,tooltipPayloadSearcher:e.uN,categoricalChartProps:c,ref:b})})},56988:(a,b,c)=>{"use strict";c.d(b,{F:()=>ar,L:()=>al});var d=c(43210),e=c(5664),f=c.n(e),g=c(49384),h=c(84648),i=c(57282),j=c(76966),k=c(64279),l=c(85621),m=c(51426),n=c(36166),o=c(60559),p=c(97350),q=a=>a.graphicalItems.polarItems,r=(0,h.Mz)([n.N,o.E],l.eo),s=(0,h.Mz)([q,l.DP,r],l.ec),t=(0,h.Mz)([s],l.rj),u=(0,h.Mz)([t,i.z3],l.Nk),v=(0,h.Mz)([u,l.DP,s],l.fb),w=(0,h.Mz)([u,l.DP,s],(a,b,c)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var d;return{value:(0,k.kr)(a,null!=(d=b.dataKey)?d:c.dataKey),errorDomain:[]}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:(0,k.kr)(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]}))),x=()=>void 0,y=(0,h.Mz)([l.DP,l.AV,x,w,x],l.wL),z=(0,h.Mz)([l.DP,m.fz,u,v,p.eC,n.N,y],l.tP),A=(0,h.Mz)([z,l.DP,l.xM],l.xp);function B(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function C(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?B(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):B(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}(0,h.Mz)([l.DP,z,A,n.N],l.g1);var D=(a,b)=>b,E=[],F=(a,b,c)=>(null==c?void 0:c.length)===0?E:c,G=(0,h.Mz)([i.z3,D,F],(a,b,c)=>{var d,{chartData:e}=a;if((d=(null==b?void 0:b.data)!=null&&b.data.length>0?b.data:e)&&d.length||null==c||(d=c.map(a=>C(C({},b.presentationProps),a.props))),null!=d)return d}),H=(0,h.Mz)([G,D,F],(a,b,c)=>{if(null!=a)return a.map((a,d)=>{var e,f,g=(0,k.kr)(a,b.nameKey,b.name);return f=null!=c&&null!=(e=c[d])&&null!=(e=e.props)&&e.fill?c[d].props.fill:"object"==typeof a&&null!=a&&"fill"in a?a.fill:b.fill,{value:(0,k.uM)(g,b.dataKey),color:f,payload:a,type:b.legendType}})}),I=(0,h.Mz)([q,D],(a,b)=>{if(a.some(a=>"pie"===a.type&&b.dataKey===a.dataKey&&b.data===a.data))return b}),J=(0,h.Mz)([G,I,F,j.HZ],(a,b,c,d)=>{if(null!=b&&null!=a)return al({offset:d,pieSettings:b,displayedData:a,cells:c})}),K=c(43209),L=c(75787),M=c(98986),N=c(81888),O=c(23561),P=c(25679),Q=c(54186),R=c(20237),S=c(19335),T=c(22989),U=c(4057),V=c(67629),W=c(61545),X=c(37625),Y=c(69009),Z=c(14956),$=c(75601),_=c(36304),aa=c(73865),ab=c(19420),ac=["onMouseEnter","onClick","onMouseLeave"];function ad(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ae(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ad(Object(c),!0).forEach(function(b){af(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ad(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function af(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function ag(){return(ag=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function ah(a){var b=(0,d.useMemo)(()=>(0,Q.J9)(a,!1),[a]),c=(0,d.useMemo)(()=>(0,Q.aS)(a.children,P.f),[a.children]),e=(0,d.useMemo)(()=>({name:a.name,nameKey:a.nameKey,tooltipType:a.tooltipType,data:a.data,dataKey:a.dataKey,cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,minAngle:a.minAngle,paddingAngle:a.paddingAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius,cornerRadius:a.cornerRadius,legendType:a.legendType,fill:a.fill,presentationProps:b}),[a.cornerRadius,a.cx,a.cy,a.data,a.dataKey,a.endAngle,a.innerRadius,a.minAngle,a.name,a.nameKey,a.outerRadius,a.paddingAngle,a.startAngle,a.tooltipType,a.legendType,a.fill,b]),f=(0,K.G)(a=>H(a,e,c));return d.createElement(Z._,{legendPayload:f})}function ai(a){var{dataKey:b,nameKey:c,sectors:d,stroke:e,strokeWidth:f,fill:g,name:h,hide:i,tooltipType:j}=a;return{dataDefinedOnItem:null==d?void 0:d.map(a=>a.tooltipPayload),positions:null==d?void 0:d.map(a=>a.tooltipPosition),settings:{stroke:e,strokeWidth:f,fill:g,dataKey:b,nameKey:c,name:(0,k.uM)(h,b),hide:i,type:j,color:g,unit:""}}}function aj(a){var{sectors:b,props:c,showLabels:e}=a,{label:f,labelLine:h,dataKey:i}=c;if(!e||!f||!b)return null;var j=(0,Q.J9)(c,!1),l=(0,Q.J9)(f,!1),m=(0,Q.J9)(h,!1),n="object"==typeof f&&"offsetRadius"in f&&f.offsetRadius||20,o=b.map((a,b)=>{var c,e,o=(a.startAngle+a.endAngle)/2,p=(0,S.IZ)(a.cx,a.cy,a.outerRadius+n,o),q=ae(ae(ae(ae({},j),a),{},{stroke:"none"},l),{},{index:b,textAnchor:(c=p.x)>(e=a.cx)?"start":c<e?"end":"middle"},p),r=ae(ae(ae(ae({},j),a),{},{fill:"none",stroke:a.fill},m),{},{index:b,points:[(0,S.IZ)(a.cx,a.cy,a.outerRadius,o),p],key:"line"});return d.createElement(M.W,{key:"label-".concat(a.startAngle,"-").concat(a.endAngle,"-").concat(a.midAngle,"-").concat(b)},h&&((a,b)=>{if(d.isValidElement(a))return d.cloneElement(a,b);if("function"==typeof a)return a(b);var c=(0,g.$)("recharts-pie-label-line","boolean"!=typeof a?a.className:"");return d.createElement(N.I,ag({},b,{type:"linear",className:c}))})(h,r),((a,b,c)=>{if(d.isValidElement(a))return d.cloneElement(a,b);var e=c;if("function"==typeof a&&(e=a(b),d.isValidElement(e)))return e;var f=(0,g.$)("recharts-pie-label-text","boolean"!=typeof a&&"function"!=typeof a?a.className:"");return d.createElement(O.E,ag({},b,{alignmentBaseline:"middle",className:f}),e)})(f,q,(0,k.kr)(a,i)))});return d.createElement(M.W,{className:"recharts-pie-labels"},o)}function ak(a){var{sectors:b,activeShape:c,inactiveShape:e,allOtherPieProps:f,showLabels:g}=a,h=(0,K.G)(Y.A2),{onMouseEnter:i,onClick:j,onMouseLeave:k}=f,l=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(f,ac),m=(0,W.Cj)(i,f.dataKey),n=(0,W.Pg)(k),o=(0,W.Ub)(j,f.dataKey);return null==b?null:d.createElement(d.Fragment,null,b.map((a,g)=>{if((null==a?void 0:a.startAngle)===0&&(null==a?void 0:a.endAngle)===0&&1!==b.length)return null;var i=c&&String(g)===h,j=i?c:h?e:null,k=ae(ae({},a),{},{stroke:a.stroke,tabIndex:-1,[$.F0]:g,[$.um]:f.dataKey});return d.createElement(M.W,ag({tabIndex:-1,className:"recharts-pie-sector"},(0,U.XC)(l,a,g),{onMouseEnter:m(a,g),onMouseLeave:n(a,g),onClick:o(a,g),key:"sector-".concat(null==a?void 0:a.startAngle,"-").concat(null==a?void 0:a.endAngle,"-").concat(a.midAngle,"-").concat(g)}),d.createElement(V.y,ag({option:j,isActive:i,shapeType:"sector"},k)))}),d.createElement(aj,{sectors:b,props:f,showLabels:g}))}function al(a){var b,c,d,{pieSettings:e,displayedData:f,cells:g,offset:h}=a,{cornerRadius:i,startAngle:j,endAngle:l,dataKey:m,nameKey:n,tooltipType:o}=e,p=Math.abs(e.minAngle),q=(0,T.sA)(l-j)*Math.min(Math.abs(l-j),360),r=Math.abs(q),s=f.length<=1?0:null!=(b=e.paddingAngle)?b:0,t=f.filter(a=>0!==(0,k.kr)(a,m,0)).length,u=r-t*p-(r>=360?t:t-1)*s,v=f.reduce((a,b)=>{var c=(0,k.kr)(b,m,0);return a+((0,T.Et)(c)?c:0)},0);return v>0&&(c=f.map((a,b)=>{var c,f=(0,k.kr)(a,m,0),l=(0,k.kr)(a,n,b),r=((a,b,c)=>{let d,e,f;var{top:g,left:h,width:i,height:j}=b,k=(0,S.lY)(i,j),l=h+(0,T.F4)(a.cx,i,i/2),m=g+(0,T.F4)(a.cy,j,j/2),n=(0,T.F4)(a.innerRadius,k,0);return{cx:l,cy:m,innerRadius:n,outerRadius:(d=c,e=a.outerRadius,f=k,"function"==typeof e?e(d):(0,T.F4)(e,f,.8*f)),maxRadius:a.maxRadius||Math.sqrt(i*i+j*j)/2}})(e,h,a),t=((0,T.Et)(f)?f:0)/v,w=ae(ae({},a),g&&g[b]&&g[b].props),x=(c=b?d.endAngle+(0,T.sA)(q)*s*(0!==f):j)+(0,T.sA)(q)*((0!==f?p:0)+t*u),y=(c+x)/2,z=(r.innerRadius+r.outerRadius)/2,A=[{name:l,value:f,payload:w,dataKey:m,type:o}],B=(0,S.IZ)(r.cx,r.cy,z,y);return d=ae(ae(ae(ae({},e.presentationProps),{},{percent:t,cornerRadius:i,name:l,tooltipPayload:A,midAngle:y,middleRadius:z,tooltipPosition:B},w),r),{},{value:(0,k.kr)(a,m),startAngle:c,endAngle:x,payload:w,paddingAngle:(0,T.sA)(q)*s})})),c}function am(a){var{props:b,previousSectorsRef:c}=a,{sectors:e,isAnimationActive:g,animationBegin:h,animationDuration:i,animationEasing:j,activeShape:k,inactiveShape:l,onAnimationStart:m,onAnimationEnd:n}=b,o=(0,_.n)(b,"recharts-pie-"),p=c.current,[q,r]=(0,d.useState)(!0),s=(0,d.useCallback)(()=>{"function"==typeof n&&n(),r(!1)},[n]),t=(0,d.useCallback)(()=>{"function"==typeof m&&m(),r(!0)},[m]);return d.createElement(ab.i,{begin:h,duration:i,isActive:g,easing:j,from:{t:0},to:{t:1},onAnimationStart:t,onAnimationEnd:s,key:o},a=>{var{t:g}=a,h=[],i=(e&&e[0]).startAngle;return e.forEach((a,b)=>{var c=p&&p[b],d=b>0?f()(a,"paddingAngle",0):0;if(c){var e=(0,T.Dj)(c.endAngle-c.startAngle,a.endAngle-a.startAngle),j=ae(ae({},a),{},{startAngle:i+d,endAngle:i+e(g)+d});h.push(j),i=j.endAngle}else{var{endAngle:k,startAngle:l}=a,m=(0,T.Dj)(0,k-l)(g),n=ae(ae({},a),{},{startAngle:i+d,endAngle:i+m+d});h.push(n),i=n.endAngle}}),c.current=h,d.createElement(M.W,null,d.createElement(ak,{sectors:h,activeShape:k,inactiveShape:l,allOtherPieProps:b,showLabels:!q}))})}function an(a){var{sectors:b,isAnimationActive:c,activeShape:e,inactiveShape:f}=a,g=(0,d.useRef)(null),h=g.current;return c&&b&&b.length&&(!h||h!==b)?d.createElement(am,{props:a,previousSectorsRef:g}):d.createElement(ak,{sectors:b,activeShape:e,inactiveShape:f,allOtherPieProps:a,showLabels:!0})}function ao(a){var{hide:b,className:c,rootTabIndex:e}=a,f=(0,g.$)("recharts-pie",c);return b?null:d.createElement(M.W,{tabIndex:e,className:f},d.createElement(an,a))}var ap={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!R.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function aq(a){var b=(0,aa.e)(a,ap),c=(0,d.useMemo)(()=>(0,Q.aS)(a.children,P.f),[a.children]),e=(0,Q.J9)(b,!1),f=(0,d.useMemo)(()=>({name:b.name,nameKey:b.nameKey,tooltipType:b.tooltipType,data:b.data,dataKey:b.dataKey,cx:b.cx,cy:b.cy,startAngle:b.startAngle,endAngle:b.endAngle,minAngle:b.minAngle,paddingAngle:b.paddingAngle,innerRadius:b.innerRadius,outerRadius:b.outerRadius,cornerRadius:b.cornerRadius,legendType:b.legendType,fill:b.fill,presentationProps:e}),[b.cornerRadius,b.cx,b.cy,b.data,b.dataKey,b.endAngle,b.innerRadius,b.minAngle,b.name,b.nameKey,b.outerRadius,b.paddingAngle,b.startAngle,b.tooltipType,b.legendType,b.fill,e]),g=(0,K.G)(a=>J(a,f,c));return d.createElement(d.Fragment,null,d.createElement(X.r,{fn:ai,args:ae(ae({},b),{},{sectors:g})}),d.createElement(ao,ag({},b,{sectors:g})))}class ar extends d.PureComponent{constructor(){super(...arguments),af(this,"id",(0,T.NF)("recharts-pie-"))}render(){return d.createElement(d.Fragment,null,d.createElement(L.v,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),d.createElement(ah,this.props),d.createElement(aq,this.props),this.props.children)}}af(ar,"displayName","Pie"),af(ar,"defaultProps",ap)},57282:(a,b,c)=>{"use strict";c.d(b,{HS:()=>g,LF:()=>e,z3:()=>f});var d=c(84648),e=a=>a.chartData,f=(0,d.Mz)([e],a=>{var b=null!=a.chartData?a.chartData.length-1:0;return{chartData:a.chartData,computedData:a.computedData,dataEndIndex:b,dataStartIndex:0}}),g=(a,b,c,d)=>d?f(a):e(a)},57841:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(76431),e=c(98150),f=c(29243),g=c(43574);b.has=function(a,b){let c;if(0===(c=Array.isArray(b)?b:"string"==typeof b&&d.isDeepKey(b)&&a?.[b]==null?g.toPath(b):[b]).length)return!1;let h=a;for(let a=0;a<c.length;a++){let b=c[a];if((null==h||!Object.hasOwn(h,b))&&!((Array.isArray(h)||f.isArguments(h))&&e.isIndex(b)&&b<h.length))return!1;h=h[b]}return!0}},59138:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(29862);b.cloneDeep=function(a){return d.cloneDeepWith(a)}},59618:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.uniqBy=function(a,b){let c=new Map;for(let d=0;d<a.length;d++){let e=a[d],f=b(e);c.has(f)||c.set(f,e)}return Array.from(c.values())}},60324:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if("object"!=typeof a||null==a)return!1;if(null===Object.getPrototypeOf(a))return!0;if("[object Object]"!==Object.prototype.toString.call(a)){let b=a[Symbol.toStringTag];return null!=b&&!!Object.getOwnPropertyDescriptor(a,Symbol.toStringTag)?.writable&&a.toString()===`[object ${b}]`}let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}},60559:(a,b,c)=>{"use strict";c.d(b,{E:()=>d});var d=(a,b,c)=>c},61545:(a,b,c)=>{"use strict";c.d(b,{Cj:()=>f,Pg:()=>g,Ub:()=>h});var d=c(43209),e=c(17118),f=(a,b)=>{var c=(0,d.j)();return(d,f)=>g=>{null==a||a(d,f,g),c((0,e.RD)({activeIndex:String(f),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}},g=a=>{var b=(0,d.j)();return(c,d)=>f=>{null==a||a(c,d,f),b((0,e.oP)())}},h=(a,b)=>{var c=(0,d.j)();return(d,f)=>g=>{null==a||a(d,f,g),c((0,e.ML)({activeIndex:String(f),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}}},61645:(a,b,c)=>{"use strict";c.d(b,{J:()=>f,U:()=>e});var d=(0,c(76067).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(a,b)=>b.payload}}),{updatePolarOptions:e}=d.actions,f=d.reducer},61855:(a,b,c)=>{"use strict";c.d(b,{Q:()=>h});var d=c(43210),e=c(49605),f=c(54024),g=["axis"],h=(0,d.forwardRef)((a,b)=>d.createElement(f.P,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:g,tooltipPayloadSearcher:e.uN,categoricalChartProps:a,ref:b}))},64231:(a,b,c)=>{"use strict";c.d(b,{J:()=>Z});var d=c(43210);c(6895);var e={notify(){},get:()=>[]},f="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,g="undefined"!=typeof navigator&&"ReactNative"===navigator.product,h=f||g?d.useLayoutEffect:d.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var i=Symbol.for("react-redux-context"),j="undefined"!=typeof globalThis?globalThis:{},k=function(){if(!d.createContext)return{};let a=j[i]??=new Map,b=a.get(d.createContext);return b||(b=d.createContext(null),a.set(d.createContext,b)),b}(),l=function(a){let{children:b,context:c,serverState:f,store:g}=a,i=d.useMemo(()=>{let a=function(a,b){let c,d=e,f=0,g=!1;function h(){k.onStateChange&&k.onStateChange()}function i(){if(f++,!c){let b,e;c=a.subscribe(h),b=null,e=null,d={clear(){b=null,e=null},notify(){let a=b;for(;a;)a.callback(),a=a.next},get(){let a=[],c=b;for(;c;)a.push(c),c=c.next;return a},subscribe(a){let c=!0,d=e={callback:a,next:null,prev:e};return d.prev?d.prev.next=d:b=d,function(){c&&null!==b&&(c=!1,d.next?d.next.prev=d.prev:e=d.prev,d.prev?d.prev.next=d.next:b=d.next)}}}}}function j(){f--,c&&0===f&&(c(),c=void 0,d.clear(),d=e)}let k={addNestedSub:function(a){i();let b=d.subscribe(a),c=!1;return()=>{c||(c=!0,b(),j())}},notifyNestedSubs:function(){d.notify()},handleChangeWrapper:h,isSubscribed:function(){return g},trySubscribe:function(){g||(g=!0,i())},tryUnsubscribe:function(){g&&(g=!1,j())},getListeners:()=>d};return k}(g);return{store:g,subscription:a,getServerState:f?()=>f:void 0}},[g,f]),j=d.useMemo(()=>g.getState(),[g]);return h(()=>{let{subscription:a}=i;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),j!==g.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[i,j]),d.createElement((c||k).Provider,{value:i},b)},m=c(11208),n=c(76067),o=c(49605),p=c(17118),q=c(64267),r=c(52693),s=c(85407);function t(a,b){return b instanceof HTMLElement?"HTMLElement <".concat(b.tagName,' class="').concat(b.className,'">'):b===window?"global.window":b}var u=c(5338),v=c(30802),w=c(71392),x=(0,n.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(a,b)=>{a.dots.push(b.payload)},removeDot:(a,b)=>{var c=(0,w.ss)(a).dots.findIndex(a=>a===b.payload);-1!==c&&a.dots.splice(c,1)},addArea:(a,b)=>{a.areas.push(b.payload)},removeArea:(a,b)=>{var c=(0,w.ss)(a).areas.findIndex(a=>a===b.payload);-1!==c&&a.areas.splice(c,1)},addLine:(a,b)=>{a.lines.push(b.payload)},removeLine:(a,b)=>{var c=(0,w.ss)(a).lines.findIndex(a=>a===b.payload);-1!==c&&a.lines.splice(c,1)}}}),{addDot:y,removeDot:z,addArea:A,removeArea:B,addLine:C,removeLine:D}=x.actions,E=x.reducer,F={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},G=(0,n.Z0)({name:"brush",initialState:F,reducers:{setBrushSettings:(a,b)=>null==b.payload?F:b.payload}}),{setBrushSettings:H}=G.actions,I=G.reducer,J=c(53044),K=c(32181),L=(0,n.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(a,b){a.radiusAxis[b.payload.id]=(0,w.h4)(b.payload)},removeRadiusAxis(a,b){delete a.radiusAxis[b.payload.id]},addAngleAxis(a,b){a.angleAxis[b.payload.id]=(0,w.h4)(b.payload)},removeAngleAxis(a,b){delete a.angleAxis[b.payload.id]}}}),{addRadiusAxis:M,removeRadiusAxis:N,addAngleAxis:O,removeAngleAxis:P}=L.actions,Q=L.reducer,R=c(61645),S=c(11281),T=c(44919),U=c(34258),V=(0,m.HY)({brush:I,cartesianAxis:u.CA,chartData:q.LV,graphicalItems:v.iZ,layout:r.Vp,legend:J.CU,options:o.lJ,polarAxis:Q,polarOptions:R.J,referenceElements:E,rootProps:K.vE,tooltip:p.En}),W=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,n.U1)({reducer:V,preloadedState:a,middleware:a=>a({serializableCheck:!1}).concat([s.YF.middleware,s.fP.middleware,S.$7.middleware,T.x.middleware,U.k.middleware]),devTools:{serialize:{replacer:t},name:"recharts-".concat(b)}})},X=c(83409),Y=c(26652);function Z(a){var{preloadedState:b,children:c,reduxStoreName:e}=a,f=(0,X.r)(),g=(0,d.useRef)(null);if(f)return c;null==g.current&&(g.current=W(b,e));var h=Y.E;return d.createElement(l,{context:h,store:g.current},c)}},64267:(a,b,c)=>{"use strict";c.d(b,{LV:()=>h,M:()=>f,hq:()=>e});var d=(0,c(76067).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(a,b){if(a.chartData=b.payload,null==b.payload){a.dataStartIndex=0,a.dataEndIndex=0;return}b.payload.length>0&&a.dataEndIndex!==b.payload.length-1&&(a.dataEndIndex=b.payload.length-1)},setComputedData(a,b){a.computedData=b.payload},setDataStartEndIndexes(a,b){var{startIndex:c,endIndex:d}=b.payload;null!=c&&(a.dataStartIndex=c),null!=d&&(a.dataEndIndex=d)}}}),{setChartData:e,setDataStartEndIndexes:f,setComputedData:g}=d.actions,h=d.reducer},64279:(a,b,c)=>{"use strict";c.d(b,{qx:()=>H,IH:()=>G,s0:()=>t,gH:()=>s,SW:()=>N,YB:()=>x,bk:()=>M,Hj:()=>I,DW:()=>E,y2:()=>D,nb:()=>C,PW:()=>v,Mk:()=>F,$8:()=>B,yy:()=>A,Rh:()=>w,GF:()=>J,uM:()=>K,kr:()=>r,r4:()=>L,_L:()=>u,_f:()=>y});var d=c(10687),e=c.n(d),f=c(5664),g=c.n(f);function h(a,b){if((e=a.length)>1)for(var c,d,e,f=1,g=a[b[0]],h=g.length;f<e;++f)for(d=g,g=a[b[f]],c=0;c<h;++c)g[c][1]+=g[c][0]=isNaN(d[c][1])?d[c][0]:d[c][1]}var i=c(48657),j=c(22786);function k(a){for(var b=a.length,c=Array(b);--b>=0;)c[b]=b;return c}function l(a,b){return a[b]}function m(a){let b=[];return b.key=a,b}var n=c(22989),o=c(19335);function p(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function q(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?p(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):p(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function r(a,b,c){return(0,n.uy)(a)||(0,n.uy)(b)?c:(0,n.vh)(b)?g()(a,b,c):"function"==typeof b?b(a):c}var s=(a,b,c,d,e)=>{var f,g=-1,h=null!=(f=null==b?void 0:b.length)?f:0;if(h<=1||null==a)return 0;if("angleAxis"===d&&null!=e&&1e-6>=Math.abs(Math.abs(e[1]-e[0])-360))for(var i=0;i<h;i++){var j=i>0?c[i-1].coordinate:c[h-1].coordinate,k=c[i].coordinate,l=i>=h-1?c[0].coordinate:c[i+1].coordinate,m=void 0;if((0,n.sA)(k-j)!==(0,n.sA)(l-k)){var o=[];if((0,n.sA)(l-k)===(0,n.sA)(e[1]-e[0])){m=l;var p=k+e[1]-e[0];o[0]=Math.min(p,(p+j)/2),o[1]=Math.max(p,(p+j)/2)}else{m=j;var q=l+e[1]-e[0];o[0]=Math.min(k,(q+k)/2),o[1]=Math.max(k,(q+k)/2)}var r=[Math.min(k,(m+k)/2),Math.max(k,(m+k)/2)];if(a>r[0]&&a<=r[1]||a>=o[0]&&a<=o[1]){({index:g}=c[i]);break}}else{var s=Math.min(j,l),t=Math.max(j,l);if(a>(s+k)/2&&a<=(t+k)/2){({index:g}=c[i]);break}}}else if(b){for(var u=0;u<h;u++)if(0===u&&a<=(b[u].coordinate+b[u+1].coordinate)/2||u>0&&u<h-1&&a>(b[u].coordinate+b[u-1].coordinate)/2&&a<=(b[u].coordinate+b[u+1].coordinate)/2||u===h-1&&a>(b[u].coordinate+b[u-1].coordinate)/2){({index:g}=b[u]);break}}return g},t=(a,b,c)=>{if(b&&c){var{width:d,height:e}=c,{align:f,verticalAlign:g,layout:h}=b;if(("vertical"===h||"horizontal"===h&&"middle"===g)&&"center"!==f&&(0,n.Et)(a[f]))return q(q({},a),{},{[f]:a[f]+(d||0)});if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==g&&(0,n.Et)(a[g]))return q(q({},a),{},{[g]:a[g]+(e||0)})}return a},u=(a,b)=>"horizontal"===a&&"xAxis"===b||"vertical"===a&&"yAxis"===b||"centric"===a&&"angleAxis"===b||"radial"===a&&"radiusAxis"===b,v=(a,b,c,d)=>{if(d)return a.map(a=>a.coordinate);var e,f,g=a.map(a=>(a.coordinate===b&&(e=!0),a.coordinate===c&&(f=!0),a.coordinate));return e||g.push(b),f||g.push(c),g},w=(a,b,c)=>{if(!a)return null;var{duplicateDomain:d,type:e,range:f,scale:g,realScaleType:h,isCategorical:i,categoricalDomain:j,tickCount:k,ticks:l,niceTicks:m,axisType:o}=a;if(!g)return null;var p="scaleBand"===h&&g.bandwidth?g.bandwidth()/2:2,q=(b||c)&&"category"===e&&g.bandwidth?g.bandwidth()/p:0;return(q="angleAxis"===o&&f&&f.length>=2?2*(0,n.sA)(f[0]-f[1])*q:q,b&&(l||m))?(l||m||[]).map((a,b)=>({coordinate:g(d?d.indexOf(a):a)+q,value:a,offset:q,index:b})).filter(a=>!(0,n.M8)(a.coordinate)):i&&j?j.map((a,b)=>({coordinate:g(a)+q,value:a,index:b,offset:q})):g.ticks&&!c&&null!=k?g.ticks(k).map((a,b)=>({coordinate:g(a)+q,value:a,offset:q,index:b})):g.domain().map((a,b)=>({coordinate:g(a)+q,value:d?d[a]:a,index:b,offset:q}))},x=a=>{var b=a.domain();if(b&&!(b.length<=2)){var c=b.length,d=a.range(),e=Math.min(d[0],d[1])-1e-4,f=Math.max(d[0],d[1])+1e-4,g=a(b[0]),h=a(b[c-1]);(g<e||g>f||h<e||h>f)&&a.domain([b[0],b[c-1]])}},y=(a,b)=>{if(!b||2!==b.length||!(0,n.Et)(b[0])||!(0,n.Et)(b[1]))return a;var c=Math.min(b[0],b[1]),d=Math.max(b[0],b[1]),e=[a[0],a[1]];return(!(0,n.Et)(a[0])||a[0]<c)&&(e[0]=c),(!(0,n.Et)(a[1])||a[1]>d)&&(e[1]=d),e[0]>d&&(e[0]=d),e[1]<c&&(e[1]=c),e},z={sign:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0,g=0;g<b;++g){var h=(0,n.M8)(a[g][c][1])?a[g][c][0]:a[g][c][1];h>=0?(a[g][c][0]=e,a[g][c][1]=e+h,e=a[g][c][1]):(a[g][c][0]=f,a[g][c][1]=f+h,f=a[g][c][1])}},expand:function(a,b){if((d=a.length)>0){for(var c,d,e,f=0,g=a[0].length;f<g;++f){for(e=c=0;c<d;++c)e+=a[c][f][1]||0;if(e)for(c=0;c<d;++c)a[c][f][1]/=e}h(a,b)}},none:h,silhouette:function(a,b){if((c=a.length)>0){for(var c,d=0,e=a[b[0]],f=e.length;d<f;++d){for(var g=0,i=0;g<c;++g)i+=a[g][d][1]||0;e[d][1]+=e[d][0]=-i/2}h(a,b)}},wiggle:function(a,b){if((e=a.length)>0&&(d=(c=a[b[0]]).length)>0){for(var c,d,e,f=0,g=1;g<d;++g){for(var i=0,j=0,k=0;i<e;++i){for(var l=a[b[i]],m=l[g][1]||0,n=(m-(l[g-1][1]||0))/2,o=0;o<i;++o){var p=a[b[o]];n+=(p[g][1]||0)-(p[g-1][1]||0)}j+=m,k+=n*m}c[g-1][1]+=c[g-1][0]=f,j&&(f-=k/j)}c[g-1][1]+=c[g-1][0]=f,h(a,b)}},positive:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0;f<b;++f){var g=(0,n.M8)(a[f][c][1])?a[f][c][0]:a[f][c][1];g>=0?(a[f][c][0]=e,a[f][c][1]=e+g,e=a[f][c][1]):(a[f][c][0]=0,a[f][c][1]=0)}}},A=(a,b,c)=>{var d=z[c];return(function(){var a=(0,j.A)([]),b=k,c=h,d=l;function e(e){var f,g,h=Array.from(a.apply(this,arguments),m),j=h.length,k=-1;for(let a of e)for(f=0,++k;f<j;++f)(h[f][k]=[0,+d(a,h[f].key,k,e)]).data=a;for(f=0,g=(0,i.A)(b(h));f<j;++f)h[g[f]].index=f;return c(h,g),h}return e.keys=function(b){return arguments.length?(a="function"==typeof b?b:(0,j.A)(Array.from(b)),e):a},e.value=function(a){return arguments.length?(d="function"==typeof a?a:(0,j.A)(+a),e):d},e.order=function(a){return arguments.length?(b=null==a?k:"function"==typeof a?a:(0,j.A)(Array.from(a)),e):b},e.offset=function(a){return arguments.length?(c=null==a?h:a,e):c},e})().keys(b).value((a,b)=>+r(a,b,0)).order(k).offset(d)(a)};function B(a){return null==a?void 0:String(a)}function C(a){var{axis:b,ticks:c,bandSize:d,entry:e,index:f,dataKey:g}=a;if("category"===b.type){if(!b.allowDuplicatedCategory&&b.dataKey&&!(0,n.uy)(e[b.dataKey])){var h=(0,n.eP)(c,"value",e[b.dataKey]);if(h)return h.coordinate+d/2}return c[f]?c[f].coordinate+d/2:null}var i=r(e,(0,n.uy)(g)?b.dataKey:g);return(0,n.uy)(i)?null:b.scale(i)}var D=a=>{var{axis:b,ticks:c,offset:d,bandSize:e,entry:f,index:g}=a;if("category"===b.type)return c[g]?c[g].coordinate+d:null;var h=r(f,b.dataKey,b.scale.domain()[g]);return(0,n.uy)(h)?null:b.scale(h)-e/2+d},E=a=>{var{numericAxis:b}=a,c=b.scale.domain();if("number"===b.type){var d=Math.min(c[0],c[1]),e=Math.max(c[0],c[1]);return d<=0&&e>=0?0:e<0?e:d}return c[0]},F=(a,b,c)=>{if(null!=a)return(a=>[a[0]===1/0?0:a[0],a[1]===-1/0?0:a[1]])(Object.keys(a).reduce((d,e)=>{var{stackedData:f}=a[e],g=f.reduce((a,d)=>{var e=(a=>{var b=a.flat(2).filter(n.Et);return[Math.min(...b),Math.max(...b)]})(d.slice(b,c+1));return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0]);return[Math.min(g[0],d[0]),Math.max(g[1],d[1])]},[1/0,-1/0]))},G=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,H=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=(a,b,c)=>{if(a&&a.scale&&a.scale.bandwidth){var d=a.scale.bandwidth();if(!c||d>0)return d}if(a&&b&&b.length>=2){for(var f=e()(b,a=>a.coordinate),g=1/0,h=1,i=f.length;h<i;h++){var j=f[h],k=f[h-1];g=Math.min((j.coordinate||0)-(k.coordinate||0),g)}return g===1/0?0:g}return c?void 0:0};function J(a){var{tooltipEntrySettings:b,dataKey:c,payload:d,value:e,name:f}=a;return q(q({},b),{},{dataKey:c,payload:d,value:e,name:f})}function K(a,b){return a?String(a):"string"==typeof b?b:void 0}function L(a,b,c,d,e){return"horizontal"===c||"vertical"===c?a>=e.left&&a<=e.left+e.width&&b>=e.top&&b<=e.top+e.height?{x:a,y:b}:null:d?(0,o.yy)({x:a,y:b},d):null}var M=(a,b,c,d)=>{var e=b.find(a=>a&&a.index===c);if(e){if("horizontal"===a)return{x:e.coordinate,y:d.y};if("vertical"===a)return{x:d.x,y:e.coordinate};if("centric"===a){var f=e.coordinate,{radius:g}=d;return q(q(q({},d),(0,o.IZ)(d.cx,d.cy,g,f)),{},{angle:f,radius:g})}var h=e.coordinate,{angle:i}=d;return q(q(q({},d),(0,o.IZ)(d.cx,d.cy,h,i)),{},{angle:i,radius:h})}return{x:0,y:0}},N=(a,b)=>"horizontal"===b?a.x:"vertical"===b?a.y:"centric"===b?a.angle:a.radius},66777:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(98150),e=c(26349),f=c(1640),g=c(1706);b.isIterateeCall=function(a,b,c){return!!f.isObject(c)&&(!!("number"==typeof b&&e.isArrayLike(c)&&d.isIndex(b))&&b<c.length||"string"==typeof b&&b in c)&&g.eq(c[b],a)}},67629:(a,b,c)=>{"use strict";c.d(b,{y:()=>x});var d=c(43210),e=c(92867),f=c.n(e),g=c(71524),h=c(49384),i=c(54186),j=c(73865),k=c(19420);function l(){return(l=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var m=(a,b,c,d,e)=>{var f=c-d;return"M ".concat(a,",").concat(b)+"L ".concat(a+c,",").concat(b)+"L ".concat(a+c-f/2,",").concat(b+e)+"L ".concat(a+c-f/2-d,",").concat(b+e)+"L ".concat(a,",").concat(b," Z")},n={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},o=a=>{var b=(0,j.e)(a,n),c=(0,d.useRef)(),[e,f]=(0,d.useState)(-1);(0,d.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&f(a)}catch(a){}},[]);var{x:g,y:o,upperWidth:p,lowerWidth:q,height:r,className:s}=b,{animationEasing:t,animationDuration:u,animationBegin:v,isUpdateAnimationActive:w}=b;if(g!==+g||o!==+o||p!==+p||q!==+q||r!==+r||0===p&&0===q||0===r)return null;var x=(0,h.$)("recharts-trapezoid",s);return w?d.createElement(k.i,{canBegin:e>0,from:{upperWidth:0,lowerWidth:0,height:r,x:g,y:o},to:{upperWidth:p,lowerWidth:q,height:r,x:g,y:o},duration:u,animationEasing:t,isActive:w},a=>{var{upperWidth:f,lowerWidth:g,height:h,x:j,y:n}=a;return d.createElement(k.i,{canBegin:e>0,from:"0px ".concat(-1===e?1:e,"px"),to:"".concat(e,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:u,easing:t},d.createElement("path",l({},(0,i.J9)(b,!0),{className:x,d:m(j,n,f,g,h),ref:c})))}):d.createElement("g",null,d.createElement("path",l({},(0,i.J9)(b,!0),{className:x,d:m(g,o,p,q,r)})))},p=c(34955),q=c(98986),r=c(10919),s=["option","shapeType","propTransformer","activeClassName","isActive"];function t(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function u(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?t(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):t(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function v(a,b){return u(u({},b),a)}function w(a){var{shapeType:b,elementProps:c}=a;switch(b){case"rectangle":return d.createElement(g.M,c);case"trapezoid":return d.createElement(o,c);case"sector":return d.createElement(p.h,c);case"symbols":if("symbols"===b)return d.createElement(r.i,c);break;default:return null}}function x(a){var b,{option:c,shapeType:e,propTransformer:g=v,activeClassName:h="recharts-active-shape",isActive:i}=a,j=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,s);if((0,d.isValidElement)(c))b=(0,d.cloneElement)(c,u(u({},j),(0,d.isValidElement)(c)?c.props:c));else if("function"==typeof c)b=c(j);else if(f()(c)&&"boolean"!=typeof c){var k=g(c,j);b=d.createElement(w,{shapeType:e,elementProps:k})}else b=d.createElement(w,{shapeType:e,elementProps:j});return i?d.createElement(q.W,{className:h},b):b}},67766:(a,b,c)=>{a.exports=c(43084).throttle},68392:(a,b,c)=>{"use strict";c.d(b,{V:()=>e});var d=c(43210);function e(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[b,c]=(0,d.useState)({height:0,left:0,top:0,width:0}),e=(0,d.useCallback)(a=>{if(null!=a){var d=a.getBoundingClientRect(),e={height:d.height,left:d.left,top:d.top,width:d.width};(Math.abs(e.height-b.height)>1||Math.abs(e.left-b.left)>1||Math.abs(e.top-b.top)>1||Math.abs(e.width-b.width)>1)&&c({height:e.height,left:e.left,top:e.top,width:e.width})}},[b.width,b.height,b.top,b.left,...a]);return[b,e]}},69009:(a,b,c)=>{"use strict";c.d(b,{BZ:()=>af,eE:()=>aj,Xb:()=>ag,JG:()=>am,A2:()=>ae,yn:()=>ak,Dn:()=>y,gL:()=>X,fl:()=>Y,R4:()=>_,Re:()=>w,n4:()=>E});var d=c(84648),e=c(85621),f=c(51426),g=c(64279),h=c(57282),i=c(97350),j=c(22989),k=c(53416),l=c(43075),m=c(28550),n=c(32520),o=c(97371),p=c(49396),q=c(86445),r=c(76966),s=c(77100),t=c(72198),u=c(78242),v=c(32751),w=a=>{var b=(0,f.fz)(a);return"horizontal"===b?"xAxis":"vertical"===b?"yAxis":"centric"===b?"angleAxis":"radiusAxis"},x=a=>a.tooltip.settings.axisId,y=a=>{var b=w(a),c=x(a);return(0,e.Hd)(a,b,c)},z=(0,d.Mz)([y,f.fz,e.um,i.iO,w],e.sr),A=(0,d.Mz)([a=>a.graphicalItems.cartesianItems,a=>a.graphicalItems.polarItems],(a,b)=>[...a,...b]),B=(0,d.Mz)([w,x],e.eo),C=(0,d.Mz)([A,y,B],e.ec),D=(0,d.Mz)([C],e.rj),E=(0,d.Mz)([D,h.LF],e.Nk),F=(0,d.Mz)([E,y,C],e.fb),G=(0,d.Mz)([y],e.S5),H=(0,d.Mz)([E,C,i.eC],e.MK),I=(0,d.Mz)([H,h.LF,w],e.pM),J=(0,d.Mz)([C],e.IO),K=(0,d.Mz)([E,y,J,w],e.kz),L=(0,d.Mz)([e.Kr,w,x],e.P9),M=(0,d.Mz)([L,w],e.Oz),N=(0,d.Mz)([e.gT,w,x],e.P9),O=(0,d.Mz)([N,w],e.q),P=(0,d.Mz)([e.$X,w,x],e.P9),Q=(0,d.Mz)([P,w],e.bb),R=(0,d.Mz)([M,Q,O],e.yi),S=(0,d.Mz)([y,G,I,K,R],e.wL),T=(0,d.Mz)([y,f.fz,E,F,i.eC,w,S],e.tP),U=(0,d.Mz)([T,y,z],e.xp),V=(0,d.Mz)([y,T,U,w],e.g1),W=a=>{var b=w(a),c=x(a);return(0,e.D5)(a,b,c,!1)},X=(0,d.Mz)([y,W],k.I),Y=(0,d.Mz)([y,z,V,X],e.Qn),Z=(0,d.Mz)([f.fz,F,y,w],e.tF),$=(0,d.Mz)([f.fz,F,y,w],e.iv),_=(0,d.Mz)([f.fz,y,z,Y,W,Z,$,w],(a,b,c,d,e,f,h,i)=>{if(b){var{type:k}=b,l=(0,g._L)(a,i);if(d){var m="scaleBand"===c&&d.bandwidth?d.bandwidth()/2:2,n="category"===k&&d.bandwidth?d.bandwidth()/m:0;return(n="angleAxis"===i&&null!=e&&(null==e?void 0:e.length)>=2?2*(0,j.sA)(e[0]-e[1])*n:n,l&&h)?h.map((a,b)=>({coordinate:d(a)+n,value:a,index:b,offset:n})):d.domain().map((a,b)=>({coordinate:d(a)+n,value:f?f[a]:a,index:b,offset:n}))}}}),aa=(0,d.Mz)([l.xH,l.Hw,a=>a.tooltip.settings],(a,b,c)=>(0,l.$g)(c.shared,a,b)),ab=a=>a.tooltip.settings.trigger,ac=a=>a.tooltip.settings.defaultIndex,ad=(0,d.Mz)([u.J,aa,ab,ac],n.i),ae=(0,d.Mz)([ad,E],o.P),af=(0,d.Mz)([_,ae],m.E),ag=(0,d.Mz)([ad],a=>{if(a)return a.dataKey}),ah=(0,d.Mz)([u.J,aa,ab,ac],s.q),ai=(0,d.Mz)([q.Lp,q.A$,f.fz,r.HZ,_,ac,ah,t.x],p.o),aj=(0,d.Mz)([ad,ai],(a,b)=>null!=a&&a.coordinate?a.coordinate:b),ak=(0,d.Mz)([ad],a=>a.active),al=(0,d.Mz)([ah,ae,h.LF,y,af,t.x,aa],v.N),am=(0,d.Mz)([al],a=>{if(null!=a)return Array.from(new Set(a.map(a=>a.payload).filter(a=>null!=a)))})},69404:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(175),e=c(91653),f=c(91428),g=c(27469),h=c(1706);b.isEqualWith=function(a,b,c){return function a(b,c,i,j,k,l,m){let n=m(b,c,i,j,k,l);if(void 0!==n)return n;if(typeof b==typeof c)switch(typeof b){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return b===c;case"number":return b===c||Object.is(b,c)}return function b(c,i,j,k){if(Object.is(c,i))return!0;let l=f.getTag(c),m=f.getTag(i);if(l===g.argumentsTag&&(l=g.objectTag),m===g.argumentsTag&&(m=g.objectTag),l!==m)return!1;switch(l){case g.stringTag:return c.toString()===i.toString();case g.numberTag:{let a=c.valueOf(),b=i.valueOf();return h.eq(a,b)}case g.booleanTag:case g.dateTag:case g.symbolTag:return Object.is(c.valueOf(),i.valueOf());case g.regexpTag:return c.source===i.source&&c.flags===i.flags;case g.functionTag:return c===i}let n=(j=j??new Map).get(c),o=j.get(i);if(null!=n&&null!=o)return n===i;j.set(c,i),j.set(i,c);try{switch(l){case g.mapTag:if(c.size!==i.size)return!1;for(let[b,d]of c.entries())if(!i.has(b)||!a(d,i.get(b),b,c,i,j,k))return!1;return!0;case g.setTag:{if(c.size!==i.size)return!1;let b=Array.from(c.values()),d=Array.from(i.values());for(let e=0;e<b.length;e++){let f=b[e],g=d.findIndex(b=>a(f,b,void 0,c,i,j,k));if(-1===g)return!1;d.splice(g,1)}return!0}case g.arrayTag:case g.uint8ArrayTag:case g.uint8ClampedArrayTag:case g.uint16ArrayTag:case g.uint32ArrayTag:case g.bigUint64ArrayTag:case g.int8ArrayTag:case g.int16ArrayTag:case g.int32ArrayTag:case g.bigInt64ArrayTag:case g.float32ArrayTag:case g.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(c)!==Buffer.isBuffer(i)||c.length!==i.length)return!1;for(let b=0;b<c.length;b++)if(!a(c[b],i[b],b,c,i,j,k))return!1;return!0;case g.arrayBufferTag:if(c.byteLength!==i.byteLength)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.dataViewTag:if(c.byteLength!==i.byteLength||c.byteOffset!==i.byteOffset)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.errorTag:return c.name===i.name&&c.message===i.message;case g.objectTag:{if(!(b(c.constructor,i.constructor,j,k)||d.isPlainObject(c)&&d.isPlainObject(i)))return!1;let f=[...Object.keys(c),...e.getSymbols(c)],g=[...Object.keys(i),...e.getSymbols(i)];if(f.length!==g.length)return!1;for(let b=0;b<f.length;b++){let d=f[b],e=c[d];if(!Object.hasOwn(i,d))return!1;let g=i[d];if(!a(e,g,d,c,i,j,k))return!1}return!0}default:return!1}}finally{j.delete(c),j.delete(i)}}(b,c,l,m)}(a,b,void 0,void 0,void 0,void 0,c)}},71337:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(66777),e=c(42750);b.range=function(a,b,c){c&&"number"!=typeof c&&d.isIterateeCall(a,b,c)&&(b=c=void 0),a=e.toFinite(a),void 0===b?(b=a,a=0):b=e.toFinite(b),c=void 0===c?a<b?1:-1:e.toFinite(c);let f=Math.max(Math.ceil((b-a)/(c||1)),0),g=Array(f);for(let b=0;b<f;b++)g[b]=a,a+=c;return g}},71392:(a,b,c)=>{"use strict";c.d(b,{Qx:()=>j,a6:()=>k,h4:()=>T,jM:()=>S,ss:()=>Q});var d,e=Symbol.for("immer-nothing"),f=Symbol.for("immer-draftable"),g=Symbol.for("immer-state");function h(a){throw Error(`[Immer] minified error nr: ${a}. Full error at: https://bit.ly/3cXEKWf`)}var i=Object.getPrototypeOf;function j(a){return!!a&&!!a[g]}function k(a){return!!a&&(m(a)||Array.isArray(a)||!!a[f]||!!a.constructor?.[f]||r(a)||s(a))}var l=Object.prototype.constructor.toString();function m(a){if(!a||"object"!=typeof a)return!1;let b=i(a);if(null===b)return!0;let c=Object.hasOwnProperty.call(b,"constructor")&&b.constructor;return c===Object||"function"==typeof c&&Function.toString.call(c)===l}function n(a,b){0===o(a)?Reflect.ownKeys(a).forEach(c=>{b(c,a[c],a)}):a.forEach((c,d)=>b(d,c,a))}function o(a){let b=a[g];return b?b.type_:Array.isArray(a)?1:r(a)?2:3*!!s(a)}function p(a,b){return 2===o(a)?a.has(b):Object.prototype.hasOwnProperty.call(a,b)}function q(a,b,c){let d=o(a);2===d?a.set(b,c):3===d?a.add(c):a[b]=c}function r(a){return a instanceof Map}function s(a){return a instanceof Set}function t(a){return a.copy_||a.base_}function u(a,b){if(r(a))return new Map(a);if(s(a))return new Set(a);if(Array.isArray(a))return Array.prototype.slice.call(a);let c=m(a);if(!0!==b&&("class_only"!==b||c)){let b=i(a);return null!==b&&c?{...a}:Object.assign(Object.create(b),a)}{let b=Object.getOwnPropertyDescriptors(a);delete b[g];let c=Reflect.ownKeys(b);for(let d=0;d<c.length;d++){let e=c[d],f=b[e];!1===f.writable&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(b[e]={configurable:!0,writable:!0,enumerable:f.enumerable,value:a[e]})}return Object.create(i(a),b)}}function v(a,b=!1){return x(a)||j(a)||!k(a)||(o(a)>1&&(a.set=a.add=a.clear=a.delete=w),Object.freeze(a),b&&Object.entries(a).forEach(([a,b])=>v(b,!0))),a}function w(){h(2)}function x(a){return Object.isFrozen(a)}var y={};function z(a){let b=y[a];return b||h(0,a),b}function A(a,b){b&&(z("Patches"),a.patches_=[],a.inversePatches_=[],a.patchListener_=b)}function B(a){C(a),a.drafts_.forEach(E),a.drafts_=null}function C(a){a===d&&(d=a.parent_)}function D(a){return d={drafts_:[],parent_:d,immer_:a,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function E(a){let b=a[g];0===b.type_||1===b.type_?b.revoke_():b.revoked_=!0}function F(a,b){b.unfinalizedDrafts_=b.drafts_.length;let c=b.drafts_[0];return void 0!==a&&a!==c?(c[g].modified_&&(B(b),h(4)),k(a)&&(a=G(b,a),b.parent_||I(b,a)),b.patches_&&z("Patches").generateReplacementPatches_(c[g].base_,a,b.patches_,b.inversePatches_)):a=G(b,c,[]),B(b),b.patches_&&b.patchListener_(b.patches_,b.inversePatches_),a!==e?a:void 0}function G(a,b,c){if(x(b))return b;let d=b[g];if(!d)return n(b,(e,f)=>H(a,d,b,e,f,c)),b;if(d.scope_!==a)return b;if(!d.modified_)return I(a,d.base_,!0),d.base_;if(!d.finalized_){d.finalized_=!0,d.scope_.unfinalizedDrafts_--;let b=d.copy_,e=b,f=!1;3===d.type_&&(e=new Set(b),b.clear(),f=!0),n(e,(e,g)=>H(a,d,b,e,g,c,f)),I(a,b,!1),c&&a.patches_&&z("Patches").generatePatches_(d,c,a.patches_,a.inversePatches_)}return d.copy_}function H(a,b,c,d,e,f,g){if(j(e)){let g=G(a,e,f&&b&&3!==b.type_&&!p(b.assigned_,d)?f.concat(d):void 0);if(q(c,d,g),!j(g))return;a.canAutoFreeze_=!1}else g&&c.add(e);if(k(e)&&!x(e)){if(!a.immer_.autoFreeze_&&a.unfinalizedDrafts_<1)return;G(a,e),(!b||!b.scope_.parent_)&&"symbol"!=typeof d&&Object.prototype.propertyIsEnumerable.call(c,d)&&I(a,e)}}function I(a,b,c=!1){!a.parent_&&a.immer_.autoFreeze_&&a.canAutoFreeze_&&v(b,c)}var J={get(a,b){if(b===g)return a;let c=t(a);if(!p(c,b)){var d=a,e=c,f=b;let g=M(e,f);return g?"value"in g?g.value:g.get?.call(d.draft_):void 0}let h=c[b];return a.finalized_||!k(h)?h:h===L(a.base_,b)?(O(a),a.copy_[b]=P(h,a)):h},has:(a,b)=>b in t(a),ownKeys:a=>Reflect.ownKeys(t(a)),set(a,b,c){let d=M(t(a),b);if(d?.set)return d.set.call(a.draft_,c),!0;if(!a.modified_){let d=L(t(a),b),e=d?.[g];if(e&&e.base_===c)return a.copy_[b]=c,a.assigned_[b]=!1,!0;if((c===d?0!==c||1/c==1/d:c!=c&&d!=d)&&(void 0!==c||p(a.base_,b)))return!0;O(a),N(a)}return!!(a.copy_[b]===c&&(void 0!==c||b in a.copy_)||Number.isNaN(c)&&Number.isNaN(a.copy_[b]))||(a.copy_[b]=c,a.assigned_[b]=!0,!0)},deleteProperty:(a,b)=>(void 0!==L(a.base_,b)||b in a.base_?(a.assigned_[b]=!1,O(a),N(a)):delete a.assigned_[b],a.copy_&&delete a.copy_[b],!0),getOwnPropertyDescriptor(a,b){let c=t(a),d=Reflect.getOwnPropertyDescriptor(c,b);return d?{writable:!0,configurable:1!==a.type_||"length"!==b,enumerable:d.enumerable,value:c[b]}:d},defineProperty(){h(11)},getPrototypeOf:a=>i(a.base_),setPrototypeOf(){h(12)}},K={};function L(a,b){let c=a[g];return(c?t(c):a)[b]}function M(a,b){if(!(b in a))return;let c=i(a);for(;c;){let a=Object.getOwnPropertyDescriptor(c,b);if(a)return a;c=i(c)}}function N(a){!a.modified_&&(a.modified_=!0,a.parent_&&N(a.parent_))}function O(a){a.copy_||(a.copy_=u(a.base_,a.scope_.immer_.useStrictShallowCopy_))}function P(a,b){let c=r(a)?z("MapSet").proxyMap_(a,b):s(a)?z("MapSet").proxySet_(a,b):function(a,b){let c=Array.isArray(a),e={type_:+!!c,scope_:b?b.scope_:d,modified_:!1,finalized_:!1,assigned_:{},parent_:b,base_:a,draft_:null,copy_:null,revoke_:null,isManual_:!1},f=e,g=J;c&&(f=[e],g=K);let{revoke:h,proxy:i}=Proxy.revocable(f,g);return e.draft_=i,e.revoke_=h,i}(a,b);return(b?b.scope_:d).drafts_.push(c),c}function Q(a){return j(a)||h(10,a),function a(b){let c;if(!k(b)||x(b))return b;let d=b[g];if(d){if(!d.modified_)return d.base_;d.finalized_=!0,c=u(b,d.scope_.immer_.useStrictShallowCopy_)}else c=u(b,!0);return n(c,(b,d)=>{q(c,b,a(d))}),d&&(d.finalized_=!1),c}(a)}n(J,(a,b)=>{K[a]=function(){return arguments[0]=arguments[0][0],b.apply(this,arguments)}}),K.deleteProperty=function(a,b){return K.set.call(this,a,b,void 0)},K.set=function(a,b,c){return J.set.call(this,a[0],b,c,a[0])};var R=new class{constructor(a){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(a,b,c)=>{let d;if("function"==typeof a&&"function"!=typeof b){let c=b;b=a;let d=this;return function(a=c,...e){return d.produce(a,a=>b.call(this,a,...e))}}if("function"!=typeof b&&h(6),void 0!==c&&"function"!=typeof c&&h(7),k(a)){let e=D(this),f=P(a,void 0),g=!0;try{d=b(f),g=!1}finally{g?B(e):C(e)}return A(e,c),F(d,e)}if(a&&"object"==typeof a)h(1,a);else{if(void 0===(d=b(a))&&(d=a),d===e&&(d=void 0),this.autoFreeze_&&v(d,!0),c){let b=[],e=[];z("Patches").generateReplacementPatches_(a,d,b,e),c(b,e)}return d}},this.produceWithPatches=(a,b)=>{let c,d;return"function"==typeof a?(b,...c)=>this.produceWithPatches(b,b=>a(b,...c)):[this.produce(a,b,(a,b)=>{c=a,d=b}),c,d]},"boolean"==typeof a?.autoFreeze&&this.setAutoFreeze(a.autoFreeze),"boolean"==typeof a?.useStrictShallowCopy&&this.setUseStrictShallowCopy(a.useStrictShallowCopy)}createDraft(a){k(a)||h(8),j(a)&&(a=Q(a));let b=D(this),c=P(a,void 0);return c[g].isManual_=!0,C(b),c}finishDraft(a,b){let c=a&&a[g];c&&c.isManual_||h(9);let{scope_:d}=c;return A(d,b),F(void 0,d)}setAutoFreeze(a){this.autoFreeze_=a}setUseStrictShallowCopy(a){this.useStrictShallowCopy_=a}applyPatches(a,b){let c;for(c=b.length-1;c>=0;c--){let d=b[c];if(0===d.path.length&&"replace"===d.op){a=d.value;break}}c>-1&&(b=b.slice(c+1));let d=z("Patches").applyPatches_;return j(a)?d(a,b):this.produce(a,a=>d(a,b))}},S=R.produce;function T(a){return a}R.produceWithPatches.bind(R),R.setAutoFreeze.bind(R),R.setUseStrictShallowCopy.bind(R),R.applyPatches.bind(R),R.createDraft.bind(R),R.finishDraft.bind(R)},71444:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},71524:(a,b,c)=>{"use strict";c.d(b,{M:()=>l});var d=c(43210),e=c(49384),f=c(54186),g=c(73865),h=c(19420);function i(){return(i=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var j=(a,b,c,d,e)=>{var f,g=Math.min(Math.abs(c)/2,Math.abs(d)/2),h=d>=0?1:-1,i=c>=0?1:-1,j=+(d>=0&&c>=0||d<0&&c<0);if(g>0&&e instanceof Array){for(var k=[0,0,0,0],l=0;l<4;l++)k[l]=e[l]>g?g:e[l];f="M".concat(a,",").concat(b+h*k[0]),k[0]>0&&(f+="A ".concat(k[0],",").concat(k[0],",0,0,").concat(j,",").concat(a+i*k[0],",").concat(b)),f+="L ".concat(a+c-i*k[1],",").concat(b),k[1]>0&&(f+="A ".concat(k[1],",").concat(k[1],",0,0,").concat(j,",\n        ").concat(a+c,",").concat(b+h*k[1])),f+="L ".concat(a+c,",").concat(b+d-h*k[2]),k[2]>0&&(f+="A ".concat(k[2],",").concat(k[2],",0,0,").concat(j,",\n        ").concat(a+c-i*k[2],",").concat(b+d)),f+="L ".concat(a+i*k[3],",").concat(b+d),k[3]>0&&(f+="A ".concat(k[3],",").concat(k[3],",0,0,").concat(j,",\n        ").concat(a,",").concat(b+d-h*k[3])),f+="Z"}else if(g>0&&e===+e&&e>0){var m=Math.min(g,e);f="M ".concat(a,",").concat(b+h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+i*m,",").concat(b,"\n            L ").concat(a+c-i*m,",").concat(b,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c,",").concat(b+h*m,"\n            L ").concat(a+c,",").concat(b+d-h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c-i*m,",").concat(b+d,"\n            L ").concat(a+i*m,",").concat(b+d,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a,",").concat(b+d-h*m," Z")}else f="M ".concat(a,",").concat(b," h ").concat(c," v ").concat(d," h ").concat(-c," Z");return f},k={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},l=a=>{var b=(0,g.e)(a,k),c=(0,d.useRef)(null),[l,m]=(0,d.useState)(-1);(0,d.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&m(a)}catch(a){}},[]);var{x:n,y:o,width:p,height:q,radius:r,className:s}=b,{animationEasing:t,animationDuration:u,animationBegin:v,isAnimationActive:w,isUpdateAnimationActive:x}=b;if(n!==+n||o!==+o||p!==+p||q!==+q||0===p||0===q)return null;var y=(0,e.$)("recharts-rectangle",s);return x?d.createElement(h.i,{canBegin:l>0,from:{width:p,height:q,x:n,y:o},to:{width:p,height:q,x:n,y:o},duration:u,animationEasing:t,isActive:x},a=>{var{width:e,height:g,x:k,y:m}=a;return d.createElement(h.i,{canBegin:l>0,from:"0px ".concat(-1===l?1:l,"px"),to:"".concat(l,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:u,isActive:w,easing:t},d.createElement("path",i({},(0,f.J9)(b,!0),{className:y,d:j(k,m,e,g,r),ref:c})))}):d.createElement("path",i({},(0,f.J9)(b,!0),{className:y,d:j(n,o,p,q,r)}))}},71579:(a,b,c)=>{"use strict";c.d(b,{u:()=>w});var d=c(43210),e=c(5664),f=c.n(e),g=c(49384);function h(a,b){for(var c in a)if(({}).hasOwnProperty.call(a,c)&&(!({}).hasOwnProperty.call(b,c)||a[c]!==b[c]))return!1;for(var d in b)if(({}).hasOwnProperty.call(b,d)&&!({}).hasOwnProperty.call(a,d))return!1;return!0}var i=c(98986),j=c(23561),k=c(97633),l=c(22989),m=c(4057),n=c(54186),o=c(17874),p=["viewBox"],q=["viewBox"];function r(){return(r=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function s(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function t(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?s(Object(c),!0).forEach(function(b){v(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):s(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function u(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function v(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class w extends d.Component{constructor(a){super(a),this.tickRefs=d.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(a,b){var{viewBox:c}=a,d=u(a,p),e=this.props,{viewBox:f}=e,g=u(e,q);return!h(c,f)||!h(d,g)||!h(b,this.state)}getTickLineCoord(a){var b,c,d,e,f,g,{x:h,y:i,width:j,height:k,orientation:m,tickSize:n,mirror:o,tickMargin:p}=this.props,q=o?-1:1,r=a.tickSize||n,s=(0,l.Et)(a.tickCoord)?a.tickCoord:a.coordinate;switch(m){case"top":b=c=a.coordinate,g=(d=(e=i+!o*k)-q*r)-q*p,f=s;break;case"left":d=e=a.coordinate,f=(b=(c=h+!o*j)-q*r)-q*p,g=s;break;case"right":d=e=a.coordinate,f=(b=(c=h+o*j)+q*r)+q*p,g=s;break;default:b=c=a.coordinate,g=(d=(e=i+o*k)+q*r)+q*p,f=s}return{line:{x1:b,y1:d,x2:c,y2:e},tick:{x:f,y:g}}}getTickTextAnchor(){var a,{orientation:b,mirror:c}=this.props;switch(b){case"left":a=c?"start":"end";break;case"right":a=c?"end":"start";break;default:a="middle"}return a}getTickVerticalAnchor(){var{orientation:a,mirror:b}=this.props;switch(a){case"left":case"right":return"middle";case"top":return b?"start":"end";default:return b?"end":"start"}}renderAxisLine(){var{x:a,y:b,width:c,height:e,orientation:h,mirror:i,axisLine:j}=this.props,k=t(t(t({},(0,n.J9)(this.props,!1)),(0,n.J9)(j,!1)),{},{fill:"none"});if("top"===h||"bottom"===h){var l=+("top"===h&&!i||"bottom"===h&&i);k=t(t({},k),{},{x1:a,y1:b+l*e,x2:a+c,y2:b+l*e})}else{var m=+("left"===h&&!i||"right"===h&&i);k=t(t({},k),{},{x1:a+m*c,y1:b,x2:a+m*c,y2:b+e})}return d.createElement("line",r({},k,{className:(0,g.$)("recharts-cartesian-axis-line",f()(j,"className"))}))}static renderTickItem(a,b,c){var e,f=(0,g.$)(b.className,"recharts-cartesian-axis-tick-value");if(d.isValidElement(a))e=d.cloneElement(a,t(t({},b),{},{className:f}));else if("function"==typeof a)e=a(t(t({},b),{},{className:f}));else{var h="recharts-cartesian-axis-tick-value";"boolean"!=typeof a&&(h=(0,g.$)(h,a.className)),e=d.createElement(j.E,r({},b,{className:h}),c)}return e}renderTicks(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:e,stroke:h,tick:j,tickFormatter:k,unit:l}=this.props,p=(0,o.f)(t(t({},this.props),{},{ticks:c}),a,b),q=this.getTickTextAnchor(),s=this.getTickVerticalAnchor(),u=(0,n.J9)(this.props,!1),v=(0,n.J9)(j,!1),x=t(t({},u),{},{fill:"none"},(0,n.J9)(e,!1)),y=p.map((a,b)=>{var{line:c,tick:n}=this.getTickLineCoord(a),o=t(t(t(t({textAnchor:q,verticalAnchor:s},u),{},{stroke:"none",fill:h},v),n),{},{index:b,payload:a,visibleTicksCount:p.length,tickFormatter:k});return d.createElement(i.W,r({className:"recharts-cartesian-axis-tick",key:"tick-".concat(a.value,"-").concat(a.coordinate,"-").concat(a.tickCoord)},(0,m.XC)(this.props,a,b)),e&&d.createElement("line",r({},x,c,{className:(0,g.$)("recharts-cartesian-axis-tick-line",f()(e,"className"))})),j&&w.renderTickItem(j,o,"".concat("function"==typeof k?k(a.value,b):a.value).concat(l||"")))});return y.length>0?d.createElement("g",{className:"recharts-cartesian-axis-ticks"},y):null}render(){var{axisLine:a,width:b,height:c,className:e,hide:f}=this.props;if(f)return null;var{ticks:h}=this.props;return null!=b&&b<=0||null!=c&&c<=0?null:d.createElement(i.W,{className:(0,g.$)("recharts-cartesian-axis",e),ref:a=>{if(a){var b=a.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(b);var c=b[0];if(c){var d=window.getComputedStyle(c).fontSize,e=window.getComputedStyle(c).letterSpacing;(d!==this.state.fontSize||e!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(c).fontSize,letterSpacing:window.getComputedStyle(c).letterSpacing})}}}},a&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,h),k.J.renderCallByParent(this.props))}}v(w,"displayName","CartesianAxis"),v(w,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},71680:(a,b,c)=>{"use strict";c.d(b,{s:()=>f}),c(43210);var d=c(83409);c(52693);var e=c(43209);function f(a){var{layout:b,width:c,height:f,margin:g}=a;return(0,e.j)(),(0,d.r)(),null}},72198:(a,b,c)=>{"use strict";c.d(b,{x:()=>d});var d=a=>a.options.tooltipPayloadSearcher},72942:(a,b,c)=>{"use strict";c.d(b,{RG:()=>v,bL:()=>E,q7:()=>F});var d=c(43210),e=c(70569),f=c(9510),g=c(98599),h=c(11273),i=c(96963),j=c(14163),k=c(13495),l=c(65551),m=c(43),n=c(60687),o="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},q="RovingFocusGroup",[r,s,t]=(0,f.N)(q),[u,v]=(0,h.A)(q,[t]),[w,x]=u(q),y=d.forwardRef((a,b)=>(0,n.jsx)(r.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(r.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(z,{...a,ref:b})})}));y.displayName=q;var z=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:f,loop:h=!1,dir:i,currentTabStopId:r,defaultCurrentTabStopId:t,onCurrentTabStopIdChange:u,onEntryFocus:v,preventScrollOnEntryFocus:x=!1,...y}=a,z=d.useRef(null),A=(0,g.s)(b,z),B=(0,m.jH)(i),[C,E]=(0,l.i)({prop:r,defaultProp:t??null,onChange:u,caller:q}),[F,G]=d.useState(!1),H=(0,k.c)(v),I=s(c),J=d.useRef(!1),[K,L]=d.useState(0);return d.useEffect(()=>{let a=z.current;if(a)return a.addEventListener(o,H),()=>a.removeEventListener(o,H)},[H]),(0,n.jsx)(w,{scope:c,orientation:f,dir:B,loop:h,currentTabStopId:C,onItemFocus:d.useCallback(a=>E(a),[E]),onItemShiftTab:d.useCallback(()=>G(!0),[]),onFocusableItemAdd:d.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:d.useCallback(()=>L(a=>a-1),[]),children:(0,n.jsx)(j.sG.div,{tabIndex:F||0===K?-1:0,"data-orientation":f,...y,ref:A,style:{outline:"none",...a.style},onMouseDown:(0,e.m)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,e.m)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!F){let b=new CustomEvent(o,p);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=I().filter(a=>a.focusable);D([a.find(a=>a.active),a.find(a=>a.id===C),...a].filter(Boolean).map(a=>a.ref.current),x)}}J.current=!1}),onBlur:(0,e.m)(a.onBlur,()=>G(!1))})})}),A="RovingFocusGroupItem",B=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:g=!1,tabStopId:h,children:k,...l}=a,m=(0,i.B)(),o=h||m,p=x(A,c),q=p.currentTabStopId===o,t=s(c),{onFocusableItemAdd:u,onFocusableItemRemove:v,currentTabStopId:w}=p;return d.useEffect(()=>{if(f)return u(),()=>v()},[f,u,v]),(0,n.jsx)(r.ItemSlot,{scope:c,id:o,focusable:f,active:g,children:(0,n.jsx)(j.sG.span,{tabIndex:q?0:-1,"data-orientation":p.orientation,...l,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f?p.onItemFocus(o):a.preventDefault()}),onFocus:(0,e.m)(a.onFocus,()=>p.onItemFocus(o)),onKeyDown:(0,e.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void p.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return C[e]}(a,p.orientation,p.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=t().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=p.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>D(c))}}),children:"function"==typeof k?k({isCurrentTabStop:q,hasTabStop:null!=w}):k})})});B.displayName=A;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var E=y,F=B},73865:(a,b,c)=>{"use strict";function d(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function e(a,b){var c=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?d(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):d(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return Object.keys(b).reduce((a,c)=>(void 0===a[c]&&void 0!==b[c]&&(a[c]=b[c]),a),c)}c.d(b,{e:()=>e})},74838:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(52371);b.matches=function(a){return a=e.cloneDeep(a),b=>d.isMatch(b,a)}},75446:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(37586),e=c(28382),f=c(66777);b.sortBy=function(a,...b){let c=b.length;return c>1&&f.isIterateeCall(a,b[0],b[1])?b=[]:c>2&&f.isIterateeCall(b[0],b[1],b[2])&&(b=[b[0]]),d.orderBy(a,e.flatten(b),["asc"])}},75601:(a,b,c)=>{"use strict";c.d(b,{F0:()=>d,tQ:()=>f,um:()=>e});var d="data-recharts-item-index",e="data-recharts-item-data-key",f=60},75787:(a,b,c)=>{"use strict";c.d(b,{p:()=>f,v:()=>g});var d=c(43210),e=c(43209);function f(a){return(0,e.j)(),(0,d.useRef)(null),null}function g(a){return(0,e.j)(),null}c(30802),c(64279)},76021:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(95819),e=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,f=/^\w*$/;b.isKey=function(a,b){return!Array.isArray(a)&&(!!("number"==typeof a||"boolean"==typeof a||null==a||d.isSymbol(a))||"string"==typeof a&&(f.test(a)||!e.test(a))||null!=b&&Object.hasOwn(b,a))}},76067:(a,b,c)=>{"use strict";c.d(b,{U1:()=>n,VP:()=>i,Nc:()=>X,Z0:()=>r});var d=c(11208);function e(a){return({dispatch:b,getState:c})=>d=>e=>"function"==typeof e?e(b,c,a):d(e)}var f=e(),g=c(71392),h="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?d.Zz:d.Zz.apply(null,arguments)};function i(a,b){function c(...d){if(b){let c=b(...d);if(!c)throw Error(Y(0));return{type:a,payload:c.payload,..."meta"in c&&{meta:c.meta},..."error"in c&&{error:c.error}}}return{type:a,payload:d[0]}}return c.toString=()=>`${a}`,c.type=a,c.match=b=>(0,d.ve)(b)&&b.type===a,c}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var j=class a extends Array{constructor(...b){super(...b),Object.setPrototypeOf(this,a.prototype)}static get[Symbol.species](){return a}concat(...a){return super.concat.apply(this,a)}prepend(...b){return 1===b.length&&Array.isArray(b[0])?new a(...b[0].concat(this)):new a(...b.concat(this))}};function k(a){return(0,g.a6)(a)?(0,g.jM)(a,()=>{}):a}function l(a,b,c){return a.has(b)?a.get(b):a.set(b,c(b)).get(b)}var m=a=>b=>{setTimeout(b,a)};function n(a){let b,c,g,i=function(a){let{thunk:b=!0,immutableCheck:c=!0,serializableCheck:d=!0,actionCreatorCheck:g=!0}=a??{},h=new j;return b&&("boolean"==typeof b?h.push(f):h.push(e(b.extraArgument))),h},{reducer:k,middleware:l,devTools:n=!0,duplicateMiddlewareCheck:o=!0,preloadedState:p,enhancers:q}=a||{};if("function"==typeof k)b=k;else if((0,d.Qd)(k))b=(0,d.HY)(k);else throw Error(Y(1));c="function"==typeof l?l(i):i();let r=d.Zz;n&&(r=h({trace:!1,..."object"==typeof n&&n}));let s=(g=(0,d.Tw)(...c),function(a){let{autoBatch:b=!0}=a??{},c=new j(g);return b&&c.push(((a={type:"raf"})=>b=>(...c)=>{let d=b(...c),e=!0,f=!1,g=!1,h=new Set,i="tick"===a.type?queueMicrotask:"raf"===a.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:m(10):"callback"===a.type?a.queueNotification:m(a.timeout),j=()=>{g=!1,f&&(f=!1,h.forEach(a=>a()))};return Object.assign({},d,{subscribe(a){let b=d.subscribe(()=>e&&a());return h.add(a),()=>{b(),h.delete(a)}},dispatch(a){try{return(f=!(e=!a?.meta?.RTK_autoBatch))&&!g&&(g=!0,i(j)),d.dispatch(a)}finally{e=!0}}})})("object"==typeof b?b:void 0)),c}),t=r(..."function"==typeof q?q(s):s());return(0,d.y$)(b,p,t)}function o(a){let b,c={},d=[],e={addCase(a,b){let d="string"==typeof a?a:a.type;if(!d)throw Error(Y(28));if(d in c)throw Error(Y(29));return c[d]=b,e},addMatcher:(a,b)=>(d.push({matcher:a,reducer:b}),e),addDefaultCase:a=>(b=a,e)};return a(e),[c,d,b]}var p=Symbol.for("rtk-slice-createasyncthunk"),q=(a=>(a.reducer="reducer",a.reducerWithPrepare="reducerWithPrepare",a.asyncThunk="asyncThunk",a))(q||{}),r=function({creators:a}={}){let b=a?.asyncThunk?.[p];return function(a){let c,{name:d,reducerPath:e=d}=a;if(!d)throw Error(Y(11));let f=("function"==typeof a.reducers?a.reducers(function(){function a(a,b){return{_reducerDefinitionType:"asyncThunk",payloadCreator:a,...b}}return a.withTypes=()=>a,{reducer:a=>Object.assign({[a.name]:(...b)=>a(...b)}[a.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(a,b)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:a,reducer:b}),asyncThunk:a}}()):a.reducers)||{},h=Object.keys(f),j={},m={},n={},p=[],q={addCase(a,b){let c="string"==typeof a?a:a.type;if(!c)throw Error(Y(12));if(c in m)throw Error(Y(13));return m[c]=b,q},addMatcher:(a,b)=>(p.push({matcher:a,reducer:b}),q),exposeAction:(a,b)=>(n[a]=b,q),exposeCaseReducer:(a,b)=>(j[a]=b,q)};function r(){let[b={},c=[],d]="function"==typeof a.extraReducers?o(a.extraReducers):[a.extraReducers],e={...b,...m};return function(a,b){let c,[d,e,f]=o(b);if("function"==typeof a)c=()=>k(a());else{let b=k(a);c=()=>b}function h(a=c(),b){let i=[d[b.type],...e.filter(({matcher:a})=>a(b)).map(({reducer:a})=>a)];return 0===i.filter(a=>!!a).length&&(i=[f]),i.reduce((a,c)=>{if(c)if((0,g.Qx)(a)){let d=c(a,b);return void 0===d?a:d}else{if((0,g.a6)(a))return(0,g.jM)(a,a=>c(a,b));let d=c(a,b);if(void 0===d){if(null===a)return a;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}return a},a)}return h.getInitialState=c,h}(a.initialState,a=>{for(let b in e)a.addCase(b,e[b]);for(let b of p)a.addMatcher(b.matcher,b.reducer);for(let b of c)a.addMatcher(b.matcher,b.reducer);d&&a.addDefaultCase(d)})}h.forEach(c=>{let e=f[c],g={reducerName:c,type:`${d}/${c}`,createNotation:"function"==typeof a.reducers};"asyncThunk"===e._reducerDefinitionType?function({type:a,reducerName:b},c,d,e){if(!e)throw Error(Y(18));let{payloadCreator:f,fulfilled:g,pending:h,rejected:i,settled:j,options:k}=c,l=e(a,f,k);d.exposeAction(b,l),g&&d.addCase(l.fulfilled,g),h&&d.addCase(l.pending,h),i&&d.addCase(l.rejected,i),j&&d.addMatcher(l.settled,j),d.exposeCaseReducer(b,{fulfilled:g||s,pending:h||s,rejected:i||s,settled:j||s})}(g,e,q,b):function({type:a,reducerName:b,createNotation:c},d,e){let f,g;if("reducer"in d){if(c&&"reducerWithPrepare"!==d._reducerDefinitionType)throw Error(Y(17));f=d.reducer,g=d.prepare}else f=d;e.addCase(a,f).exposeCaseReducer(b,f).exposeAction(b,g?i(a,g):i(a))}(g,e,q)});let t=a=>a,u=new Map,v=new WeakMap;function w(a,b){return c||(c=r()),c(a,b)}function x(){return c||(c=r()),c.getInitialState()}function y(b,c=!1){function d(a){let e=a[b];return void 0===e&&c&&(e=l(v,d,x)),e}function e(b=t){let d=l(u,c,()=>new WeakMap);return l(d,b,()=>{let d={};for(let[e,f]of Object.entries(a.selectors??{}))d[e]=function(a,b,c,d){function e(f,...g){let h=b(f);return void 0===h&&d&&(h=c()),a(h,...g)}return e.unwrapped=a,e}(f,b,()=>l(v,b,x),c);return d})}return{reducerPath:b,getSelectors:e,get selectors(){return e(d)},selectSlice:d}}let z={name:d,reducer:w,actions:n,caseReducers:j,getInitialState:x,...y(e),injectInto(a,{reducerPath:b,...c}={}){let d=b??e;return a.inject({reducerPath:d,reducer:w},c),{...z,...y(d,!0)}}};return z}}();function s(){}var t="listener",u="completed",v="cancelled",w=`task-${v}`,x=`task-${u}`,y=`${t}-${v}`,z=`${t}-${u}`,A=class{constructor(a){this.code=a,this.message=`task ${v} (reason: ${a})`}name="TaskAbortError";message},B=(a,b)=>{if("function"!=typeof a)throw TypeError(Y(32))},C=()=>{},D=(a,b=C)=>(a.catch(b),a),E=(a,b)=>(a.addEventListener("abort",b,{once:!0}),()=>a.removeEventListener("abort",b)),F=(a,b)=>{let c=a.signal;c.aborted||("reason"in c||Object.defineProperty(c,"reason",{enumerable:!0,value:b,configurable:!0,writable:!0}),a.abort(b))},G=a=>{if(a.aborted){let{reason:b}=a;throw new A(b)}};function H(a,b){let c=C;return new Promise((d,e)=>{let f=()=>e(new A(a.reason));if(a.aborted)return void f();c=E(a,f),b.finally(()=>c()).then(d,e)}).finally(()=>{c=C})}var I=async(a,b)=>{try{await Promise.resolve();let b=await a();return{status:"ok",value:b}}catch(a){return{status:a instanceof A?"cancelled":"rejected",error:a}}finally{b?.()}},J=a=>b=>D(H(a,b).then(b=>(G(a),b))),K=a=>{let b=J(a);return a=>b(new Promise(b=>setTimeout(b,a)))},{assign:L}=Object,M={},N="listenerMiddleware",O=a=>{let{type:b,actionCreator:c,matcher:d,predicate:e,effect:f}=a;if(b)e=i(b).match;else if(c)b=c.type,e=c.match;else if(d)e=d;else if(e);else throw Error(Y(21));return B(f,"options.listener"),{predicate:e,type:b,effect:f}},P=L(a=>{let{type:b,predicate:c,effect:d}=O(a);return{id:((a=21)=>{let b="",c=a;for(;c--;)b+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return b})(),effect:d,type:b,predicate:c,pending:new Set,unsubscribe:()=>{throw Error(Y(22))}}},{withTypes:()=>P}),Q=(a,b)=>{let{type:c,effect:d,predicate:e}=O(b);return Array.from(a.values()).find(a=>("string"==typeof c?a.type===c:a.predicate===e)&&a.effect===d)},R=a=>{a.pending.forEach(a=>{F(a,y)})},S=(a,b,c)=>{try{a(b,c)}catch(a){setTimeout(()=>{throw a},0)}},T=L(i(`${N}/add`),{withTypes:()=>T}),U=i(`${N}/removeAll`),V=L(i(`${N}/remove`),{withTypes:()=>V}),W=(...a)=>{console.error(`${N}/error`,...a)},X=(a={})=>{let b=new Map,{extra:c,onError:e=W}=a;B(e,"onError");let f=a=>(a=>(a.unsubscribe=()=>b.delete(a.id),b.set(a.id,a),b=>{a.unsubscribe(),b?.cancelActive&&R(a)}))(Q(b,a)??P(a));L(f,{withTypes:()=>f});let g=a=>{let c=Q(b,a);return c&&(c.unsubscribe(),a.cancelActive&&R(c)),!!c};L(g,{withTypes:()=>g});let h=async(a,d,g,h)=>{let i=new AbortController,j=((a,b)=>{let c=async(c,d)=>{G(b);let e=()=>{},f=[new Promise((b,d)=>{let f=a({predicate:c,effect:(a,c)=>{c.unsubscribe(),b([a,c.getState(),c.getOriginalState()])}});e=()=>{f(),d()}})];null!=d&&f.push(new Promise(a=>setTimeout(a,d,null)));try{let a=await H(b,Promise.race(f));return G(b),a}finally{e()}};return(a,b)=>D(c(a,b))})(f,i.signal),k=[];try{a.pending.add(i),await Promise.resolve(a.effect(d,L({},g,{getOriginalState:h,condition:(a,b)=>j(a,b).then(Boolean),take:j,delay:K(i.signal),pause:J(i.signal),extra:c,signal:i.signal,fork:((a,b)=>(c,d)=>{B(c,"taskExecutor");let e=new AbortController;E(a,()=>F(e,a.reason));let f=I(async()=>{G(a),G(e.signal);let b=await c({pause:J(e.signal),delay:K(e.signal),signal:e.signal});return G(e.signal),b},()=>F(e,x));return d?.autoJoin&&b.push(f.catch(C)),{result:J(a)(f),cancel(){F(e,w)}}})(i.signal,k),unsubscribe:a.unsubscribe,subscribe:()=>{b.set(a.id,a)},cancelActiveListeners:()=>{a.pending.forEach((a,b,c)=>{a!==i&&(F(a,y),c.delete(a))})},cancel:()=>{F(i,y),a.pending.delete(i)},throwIfCancelled:()=>{G(i.signal)}})))}catch(a){a instanceof A||S(e,a,{raisedBy:"effect"})}finally{await Promise.all(k),F(i,z),a.pending.delete(i)}},i=(a=>()=>{a.forEach(R),a.clear()})(b);return{middleware:a=>c=>j=>{let k;if(!(0,d.ve)(j))return c(j);if(T.match(j))return f(j.payload);if(U.match(j))return void i();if(V.match(j))return g(j.payload);let l=a.getState(),m=()=>{if(l===M)throw Error(Y(23));return l};try{if(k=c(j),b.size>0){let c=a.getState();for(let d of Array.from(b.values())){let b=!1;try{b=d.predicate(j,c,l)}catch(a){b=!1,S(e,a,{raisedBy:"predicate"})}b&&h(d,j,a,m)}}}finally{l=M}return k},startListening:f,stopListening:g,clearListeners:i}};function Y(a){return`Minified Redux Toolkit error #${a}; visit https://redux-toolkit.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original")},76431:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isDeepKey=function(a){switch(typeof a){case"number":case"symbol":return!1;case"string":return a.includes(".")||a.includes("[")||a.includes("]")}}},76966:(a,b,c)=>{"use strict";c.d(b,{Ds:()=>o,HZ:()=>n,c2:()=>p});var d=c(84648),e=c(5664),f=c.n(e),g=c(23337),h=c(64279),i=c(86445),j=c(23814),k=c(75601);function l(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function m(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var n=(0,d.Mz)([i.Lp,i.A$,i.HK,a=>a.brush.height,j.h,j.W,g.ff,g.dc],(a,b,c,d,e,g,i,j)=>{var l=g.reduce((a,b)=>{var{orientation:c}=b;if(!b.mirror&&!b.hide){var d="number"==typeof b.width?b.width:k.tQ;return m(m({},a),{},{[c]:a[c]+d})}return a},{left:c.left||0,right:c.right||0}),n=e.reduce((a,b)=>{var{orientation:c}=b;return b.mirror||b.hide?a:m(m({},a),{},{[c]:f()(a,"".concat(c))+b.height})},{top:c.top||0,bottom:c.bottom||0}),o=m(m({},n),l),p=o.bottom;o.bottom+=d;var q=a-(o=(0,h.s0)(o,i,j)).left-o.right,r=b-o.top-o.bottom;return m(m({brushBottom:p},o),{},{width:Math.max(q,0),height:Math.max(r,0)})}),o=(0,d.Mz)(n,a=>({x:a.left,y:a.top,width:a.width,height:a.height})),p=(0,d.Mz)(i.Lp,i.A$,(a,b)=>({x:0,y:0,width:a,height:b}))},77100:(a,b,c)=>{"use strict";c.d(b,{q:()=>d});var d=(a,b,c,d)=>{var e;return"axis"===b?a.tooltipItemPayloads:0===a.tooltipItemPayloads.length?[]:null==(e="hover"===c?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey)&&null!=d?[a.tooltipItemPayloads[0]]:a.tooltipItemPayloads.filter(a=>{var b;return(null==(b=a.settings)?void 0:b.dataKey)===e})}},77357:(a,b,c)=>{"use strict";c.d(b,{w:()=>d});var d=a=>{var b=a.currentTarget.getBoundingClientRect(),c=b.width/a.currentTarget.offsetWidth,d=b.height/a.currentTarget.offsetHeight;return{chartX:Math.round((a.clientX-b.left)/c),chartY:Math.round((a.clientY-b.top)/d)}}},78242:(a,b,c)=>{"use strict";c.d(b,{J:()=>d});var d=a=>a.tooltip},81888:(a,b,c)=>{"use strict";c.d(b,{I:()=>P});var d=c(43210);function e(){}function f(a,b,c){a._context.bezierCurveTo((2*a._x0+a._x1)/3,(2*a._y0+a._y1)/3,(a._x0+2*a._x1)/3,(a._y0+2*a._y1)/3,(a._x0+4*a._x1+b)/6,(a._y0+4*a._y1+c)/6)}function g(a){this._context=a}function h(a){this._context=a}function i(a){this._context=a}g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:f(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:f(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},h.prototype={areaStart:e,areaEnd:e,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._x2=a,this._y2=b;break;case 1:this._point=2,this._x3=a,this._y3=b;break;case 2:this._point=3,this._x4=a,this._y4=b,this._context.moveTo((this._x0+4*this._x1+a)/6,(this._y0+4*this._y1+b)/6);break;default:f(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var c=(this._x0+4*this._x1+a)/6,d=(this._y0+4*this._y1+b)/6;this._line?this._context.lineTo(c,d):this._context.moveTo(c,d);break;case 3:this._point=4;default:f(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}};class j{constructor(a,b){this._context=a,this._x=b}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+a)/2,this._y0,this._x0,b,a,b):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+b)/2,a,this._y0,a,b)}this._x0=a,this._y0=b}}function k(a){this._context=a}function l(a){this._context=a}function m(a){return new l(a)}k.prototype={areaStart:e,areaEnd:e,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(a,b){a*=1,b*=1,this._point?this._context.lineTo(a,b):(this._point=1,this._context.moveTo(a,b))}};function n(a,b,c){var d=a._x1-a._x0,e=b-a._x1,f=(a._y1-a._y0)/(d||e<0&&-0),g=(c-a._y1)/(e||d<0&&-0);return((f<0?-1:1)+(g<0?-1:1))*Math.min(Math.abs(f),Math.abs(g),.5*Math.abs((f*e+g*d)/(d+e)))||0}function o(a,b){var c=a._x1-a._x0;return c?(3*(a._y1-a._y0)/c-b)/2:b}function p(a,b,c){var d=a._x0,e=a._y0,f=a._x1,g=a._y1,h=(f-d)/3;a._context.bezierCurveTo(d+h,e+h*b,f-h,g-h*c,f,g)}function q(a){this._context=a}function r(a){this._context=new s(a)}function s(a){this._context=a}function t(a){this._context=a}function u(a){var b,c,d=a.length-1,e=Array(d),f=Array(d),g=Array(d);for(e[0]=0,f[0]=2,g[0]=a[0]+2*a[1],b=1;b<d-1;++b)e[b]=1,f[b]=4,g[b]=4*a[b]+2*a[b+1];for(e[d-1]=2,f[d-1]=7,g[d-1]=8*a[d-1]+a[d],b=1;b<d;++b)c=e[b]/f[b-1],f[b]-=c,g[b]-=c*g[b-1];for(e[d-1]=g[d-1]/f[d-1],b=d-2;b>=0;--b)e[b]=(g[b]-e[b+1])/f[b];for(b=0,f[d-1]=(a[d]+e[d-1])/2;b<d-1;++b)f[b]=2*a[b+1]-e[b+1];return[e,f]}function v(a,b){this._context=a,this._t=b}l.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._context.lineTo(a,b)}}},q.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:p(this,this._t0,o(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){var c=NaN;if(b*=1,(a*=1)!==this._x1||b!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,p(this,o(this,c=n(this,a,b)),c);break;default:p(this,this._t0,c=n(this,a,b))}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b,this._t0=c}}},(r.prototype=Object.create(q.prototype)).point=function(a,b){q.prototype.point.call(this,b,a)},s.prototype={moveTo:function(a,b){this._context.moveTo(b,a)},closePath:function(){this._context.closePath()},lineTo:function(a,b){this._context.lineTo(b,a)},bezierCurveTo:function(a,b,c,d,e,f){this._context.bezierCurveTo(b,a,d,c,f,e)}},t.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var a=this._x,b=this._y,c=a.length;if(c)if(this._line?this._context.lineTo(a[0],b[0]):this._context.moveTo(a[0],b[0]),2===c)this._context.lineTo(a[1],b[1]);else for(var d=u(a),e=u(b),f=0,g=1;g<c;++f,++g)this._context.bezierCurveTo(d[0][f],e[0][f],d[1][f],e[1][f],a[g],b[g]);(this._line||0!==this._line&&1===c)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(a,b){this._x.push(+a),this._y.push(+b)}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,b),this._context.lineTo(a,b);else{var c=this._x*(1-this._t)+a*this._t;this._context.lineTo(c,this._y),this._context.lineTo(c,b)}}this._x=a,this._y=b}};var w=c(48657),x=c(22786),y=c(15606);function z(a){return a[0]}function A(a){return a[1]}function B(a,b){var c=(0,x.A)(!0),d=null,e=m,f=null,g=(0,y.i)(h);function h(h){var i,j,k,l=(h=(0,w.A)(h)).length,m=!1;for(null==d&&(f=e(k=g())),i=0;i<=l;++i)!(i<l&&c(j=h[i],i,h))===m&&((m=!m)?f.lineStart():f.lineEnd()),m&&f.point(+a(j,i,h),+b(j,i,h));if(k)return f=null,k+""||null}return a="function"==typeof a?a:void 0===a?z:(0,x.A)(a),b="function"==typeof b?b:void 0===b?A:(0,x.A)(b),h.x=function(b){return arguments.length?(a="function"==typeof b?b:(0,x.A)(+b),h):a},h.y=function(a){return arguments.length?(b="function"==typeof a?a:(0,x.A)(+a),h):b},h.defined=function(a){return arguments.length?(c="function"==typeof a?a:(0,x.A)(!!a),h):c},h.curve=function(a){return arguments.length?(e=a,null!=d&&(f=e(d)),h):e},h.context=function(a){return arguments.length?(null==a?d=f=null:f=e(d=a),h):d},h}function C(a,b,c){var d=null,e=(0,x.A)(!0),f=null,g=m,h=null,i=(0,y.i)(j);function j(j){var k,l,m,n,o,p=(j=(0,w.A)(j)).length,q=!1,r=Array(p),s=Array(p);for(null==f&&(h=g(o=i())),k=0;k<=p;++k){if(!(k<p&&e(n=j[k],k,j))===q)if(q=!q)l=k,h.areaStart(),h.lineStart();else{for(h.lineEnd(),h.lineStart(),m=k-1;m>=l;--m)h.point(r[m],s[m]);h.lineEnd(),h.areaEnd()}q&&(r[k]=+a(n,k,j),s[k]=+b(n,k,j),h.point(d?+d(n,k,j):r[k],c?+c(n,k,j):s[k]))}if(o)return h=null,o+""||null}function k(){return B().defined(e).curve(g).context(f)}return a="function"==typeof a?a:void 0===a?z:(0,x.A)(+a),b="function"==typeof b?b:void 0===b?(0,x.A)(0):(0,x.A)(+b),c="function"==typeof c?c:void 0===c?A:(0,x.A)(+c),j.x=function(b){return arguments.length?(a="function"==typeof b?b:(0,x.A)(+b),d=null,j):a},j.x0=function(b){return arguments.length?(a="function"==typeof b?b:(0,x.A)(+b),j):a},j.x1=function(a){return arguments.length?(d=null==a?null:"function"==typeof a?a:(0,x.A)(+a),j):d},j.y=function(a){return arguments.length?(b="function"==typeof a?a:(0,x.A)(+a),c=null,j):b},j.y0=function(a){return arguments.length?(b="function"==typeof a?a:(0,x.A)(+a),j):b},j.y1=function(a){return arguments.length?(c=null==a?null:"function"==typeof a?a:(0,x.A)(+a),j):c},j.lineX0=j.lineY0=function(){return k().x(a).y(b)},j.lineY1=function(){return k().x(a).y(c)},j.lineX1=function(){return k().x(d).y(b)},j.defined=function(a){return arguments.length?(e="function"==typeof a?a:(0,x.A)(!!a),j):e},j.curve=function(a){return arguments.length?(g=a,null!=f&&(h=g(f)),j):g},j.context=function(a){return arguments.length?(null==a?f=h=null:h=g(f=a),j):f},j}var D=c(49384),E=c(4057),F=c(54186),G=c(22989),H=c(12128);function I(){return(I=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function J(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function K(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?J(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):J(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var L={curveBasisClosed:function(a){return new h(a)},curveBasisOpen:function(a){return new i(a)},curveBasis:function(a){return new g(a)},curveBumpX:function(a){return new j(a,!0)},curveBumpY:function(a){return new j(a,!1)},curveLinearClosed:function(a){return new k(a)},curveLinear:m,curveMonotoneX:function(a){return new q(a)},curveMonotoneY:function(a){return new r(a)},curveNatural:function(a){return new t(a)},curveStep:function(a){return new v(a,.5)},curveStepAfter:function(a){return new v(a,1)},curveStepBefore:function(a){return new v(a,0)}},M=a=>(0,H.H)(a.x)&&(0,H.H)(a.y),N=a=>a.x,O=a=>a.y,P=a=>{var{className:b,points:c,path:e,pathRef:f}=a;if((!c||!c.length)&&!e)return null;var g=c&&c.length?(a=>{var b,{type:c="linear",points:d=[],baseLine:e,layout:f,connectNulls:g=!1}=a,h=((a,b)=>{if("function"==typeof a)return a;var c="curve".concat((0,G.Zb)(a));return("curveMonotone"===c||"curveBump"===c)&&b?L["".concat(c).concat("vertical"===b?"Y":"X")]:L[c]||m})(c,f),i=g?d.filter(M):d;if(Array.isArray(e)){var j=g?e.filter(a=>M(a)):e,k=i.map((a,b)=>K(K({},a),{},{base:j[b]}));return(b="vertical"===f?C().y(O).x1(N).x0(a=>a.base.x):C().x(N).y1(O).y0(a=>a.base.y)).defined(M).curve(h),b(k)}return(b="vertical"===f&&(0,G.Et)(e)?C().y(O).x1(N).x0(e):(0,G.Et)(e)?C().x(N).y1(O).y0(e):B().x(N).y(O)).defined(M).curve(h),b(i)})(a):e;return d.createElement("path",I({},(0,F.J9)(a,!1),(0,E._U)(a),{className:(0,D.$)("recharts-curve",b),d:null===g?void 0:g,ref:f}))}},83409:(a,b,c)=>{"use strict";c.d(b,{r:()=>f});var d=c(43210),e=(0,d.createContext)(null),f=()=>null!=(0,d.useContext)(e)},84648:(a,b,c)=>{"use strict";c.d(b,{Mz:()=>u});var d=a=>Array.isArray(a)?a:[a],e=0,f=class{revision=e;_value;_lastValue;_isEqual=g;constructor(a,b=g){this._value=this._lastValue=a,this._isEqual=b}get value(){return this._value}set value(a){this.value!==a&&(this._value=a,this.revision=++e)}};function g(a,b){return a===b}function h(a){return a instanceof f||console.warn("Not a valid cell! ",a),a.value}var i=(a,b)=>!1;function j(){return function(a,b=g){return new f(null,b)}(0,i)}var k=a=>{let b=a.collectionTag;null===b&&(b=a.collectionTag=j()),h(b)};Symbol();var l=0,m=Object.getPrototypeOf({}),n=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy(this,o);tag=j();tags={};children={};collectionTag=null;id=l++},o={get:(a,b)=>(function(){let{value:c}=a,d=Reflect.get(c,b);if("symbol"==typeof b||b in m)return d;if("object"==typeof d&&null!==d){var e;let c=a.children[b];return void 0===c&&(c=a.children[b]=Array.isArray(e=d)?new p(e):new n(e)),c.tag&&h(c.tag),c.proxy}{let c=a.tags[b];return void 0===c&&((c=a.tags[b]=j()).value=d),h(c),d}})(),ownKeys:a=>(k(a),Reflect.ownKeys(a.value)),getOwnPropertyDescriptor:(a,b)=>Reflect.getOwnPropertyDescriptor(a.value,b),has:(a,b)=>Reflect.has(a.value,b)},p=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy([this],q);tag=j();tags={};children={};collectionTag=null;id=l++},q={get:([a],b)=>("length"===b&&k(a),o.get(a,b)),ownKeys:([a])=>o.ownKeys(a),getOwnPropertyDescriptor:([a],b)=>o.getOwnPropertyDescriptor(a,b),has:([a],b)=>o.has(a,b)},r="undefined"!=typeof WeakRef?WeakRef:class{constructor(a){this.value=a}deref(){return this.value}};function s(){return{s:0,v:void 0,o:null,p:null}}function t(a,b={}){let c,d=s(),{resultEqualityCheck:e}=b,f=0;function g(){let b,g=d,{length:h}=arguments;for(let a=0;a<h;a++){let b=arguments[a];if("function"==typeof b||"object"==typeof b&&null!==b){let a=g.o;null===a&&(g.o=a=new WeakMap);let c=a.get(b);void 0===c?(g=s(),a.set(b,g)):g=c}else{let a=g.p;null===a&&(g.p=a=new Map);let c=a.get(b);void 0===c?(g=s(),a.set(b,g)):g=c}}let i=g;if(1===g.s)b=g.v;else if(b=a.apply(null,arguments),f++,e){let a=c?.deref?.()??c;null!=a&&e(a,b)&&(b=a,0!==f&&f--),c="object"==typeof b&&null!==b||"function"==typeof b?new r(b):b}return i.s=1,i.v=b,b}return g.clearCache=()=>{d=s(),g.resetResultsCount()},g.resultsCount=()=>f,g.resetResultsCount=()=>{f=0},g}var u=function(a,...b){let c="function"==typeof a?{memoize:a,memoizeOptions:b}:a,e=(...a)=>{let b,e=0,f=0,g={},h=a.pop();"object"==typeof h&&(g=h,h=a.pop()),function(a,b=`expected a function, instead received ${typeof a}`){if("function"!=typeof a)throw TypeError(b)}(h,`createSelector expects an output function after the inputs, but received: [${typeof h}]`);let{memoize:i,memoizeOptions:j=[],argsMemoize:k=t,argsMemoizeOptions:l=[],devModeChecks:m={}}={...c,...g},n=d(j),o=d(l),p=function(a){let b=Array.isArray(a[0])?a[0]:a;return!function(a,b="expected all items to be functions, instead received the following types: "){if(!a.every(a=>"function"==typeof a)){let c=a.map(a=>"function"==typeof a?`function ${a.name||"unnamed"}()`:typeof a).join(", ");throw TypeError(`${b}[${c}]`)}}(b,"createSelector expects all input-selectors to be functions, but received the following types: "),b}(a),q=i(function(){return e++,h.apply(null,arguments)},...n);return Object.assign(k(function(){f++;let a=function(a,b){let c=[],{length:d}=a;for(let e=0;e<d;e++)c.push(a[e].apply(null,b));return c}(p,arguments);return b=q.apply(null,a)},...o),{resultFunc:h,memoizedResultFunc:q,dependencies:p,dependencyRecomputations:()=>f,resetDependencyRecomputations:()=>{f=0},lastResult:()=>b,recomputations:()=>e,resetRecomputations:()=>{e=0},memoize:i,argsMemoize:k})};return Object.assign(e,{withTypes:()=>e}),e}(t),v=Object.assign((a,b=u)=>{!function(a,b=`expected an object, instead received ${typeof a}`){if("object"!=typeof a)throw TypeError(b)}(a,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof a}`);let c=Object.keys(a);return b(c.map(b=>a[b]),(...a)=>a.reduce((a,b,d)=>(a[c[d]]=b,a),{}))},{withTypes:()=>v})},85168:(a,b,c)=>{"use strict";c.d(b,{d:()=>G});var d=c(43210),e=c(10521),f=c(22989),g=c(54186),h=c(64279),i=c(17874),j=c(71579),k=c(51426),l=c(85621),m=c(43209),n=c(83409),o=c(73865),p=["x1","y1","x2","y2","key"],q=["offset"],r=["xAxisId","yAxisId"],s=["xAxisId","yAxisId"];function t(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function u(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?t(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):t(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function v(){return(v=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function w(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var x=a=>{var{fill:b}=a;if(!b||"none"===b)return null;var{fillOpacity:c,x:e,y:f,width:g,height:h,ry:i}=a;return d.createElement("rect",{x:e,y:f,ry:i,width:g,height:h,stroke:"none",fill:b,fillOpacity:c,className:"recharts-cartesian-grid-bg"})};function y(a,b){var c;if(d.isValidElement(a))c=d.cloneElement(a,b);else if("function"==typeof a)c=a(b);else{var{x1:e,y1:f,x2:h,y2:i,key:j}=b,k=w(b,p),l=(0,g.J9)(k,!1),{offset:m}=l,n=w(l,q);c=d.createElement("line",v({},n,{x1:e,y1:f,x2:h,y2:i,fill:"none",key:j}))}return c}function z(a){var{x:b,width:c,horizontal:e=!0,horizontalPoints:f}=a;if(!e||!f||!f.length)return null;var{xAxisId:g,yAxisId:h}=a,i=w(a,r),j=f.map((a,d)=>y(e,u(u({},i),{},{x1:b,y1:a,x2:b+c,y2:a,key:"line-".concat(d),index:d})));return d.createElement("g",{className:"recharts-cartesian-grid-horizontal"},j)}function A(a){var{y:b,height:c,vertical:e=!0,verticalPoints:f}=a;if(!e||!f||!f.length)return null;var{xAxisId:g,yAxisId:h}=a,i=w(a,s),j=f.map((a,d)=>y(e,u(u({},i),{},{x1:a,y1:b,x2:a,y2:b+c,key:"line-".concat(d),index:d})));return d.createElement("g",{className:"recharts-cartesian-grid-vertical"},j)}function B(a){var{horizontalFill:b,fillOpacity:c,x:e,y:f,width:g,height:h,horizontalPoints:i,horizontal:j=!0}=a;if(!j||!b||!b.length)return null;var k=i.map(a=>Math.round(a+f-f)).sort((a,b)=>a-b);f!==k[0]&&k.unshift(0);var l=k.map((a,i)=>{var j=k[i+1]?k[i+1]-a:f+h-a;if(j<=0)return null;var l=i%b.length;return d.createElement("rect",{key:"react-".concat(i),y:a,x:e,height:j,width:g,stroke:"none",fill:b[l],fillOpacity:c,className:"recharts-cartesian-grid-bg"})});return d.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function C(a){var{vertical:b=!0,verticalFill:c,fillOpacity:e,x:f,y:g,width:h,height:i,verticalPoints:j}=a;if(!b||!c||!c.length)return null;var k=j.map(a=>Math.round(a+f-f)).sort((a,b)=>a-b);f!==k[0]&&k.unshift(0);var l=k.map((a,b)=>{var j=k[b+1]?k[b+1]-a:f+h-a;if(j<=0)return null;var l=b%c.length;return d.createElement("rect",{key:"react-".concat(b),x:a,y:g,width:j,height:i,stroke:"none",fill:c[l],fillOpacity:e,className:"recharts-cartesian-grid-bg"})});return d.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var D=(a,b)=>{var{xAxis:c,width:d,height:e,offset:f}=a;return(0,h.PW)((0,i.f)(u(u(u({},j.u.defaultProps),c),{},{ticks:(0,h.Rh)(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.left,f.left+f.width,b)},E=(a,b)=>{var{yAxis:c,width:d,height:e,offset:f}=a;return(0,h.PW)((0,i.f)(u(u(u({},j.u.defaultProps),c),{},{ticks:(0,h.Rh)(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.top,f.top+f.height,b)},F={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function G(a){var b=(0,k.yi)(),c=(0,k.rY)(),g=(0,k.W7)(),h=u(u({},(0,o.e)(a,F)),{},{x:(0,f.Et)(a.x)?a.x:g.left,y:(0,f.Et)(a.y)?a.y:g.top,width:(0,f.Et)(a.width)?a.width:g.width,height:(0,f.Et)(a.height)?a.height:g.height}),{xAxisId:i,yAxisId:j,x:p,y:q,width:r,height:s,syncWithTicks:t,horizontalValues:w,verticalValues:y}=h,G=(0,n.r)(),H=(0,m.G)(a=>(0,l.ZB)(a,"xAxis",i,G)),I=(0,m.G)(a=>(0,l.ZB)(a,"yAxis",j,G));if(!(0,f.Et)(r)||r<=0||!(0,f.Et)(s)||s<=0||!(0,f.Et)(p)||p!==+p||!(0,f.Et)(q)||q!==+q)return null;var J=h.verticalCoordinatesGenerator||D,K=h.horizontalCoordinatesGenerator||E,{horizontalPoints:L,verticalPoints:M}=h;if((!L||!L.length)&&"function"==typeof K){var N=w&&w.length,O=K({yAxis:I?u(u({},I),{},{ticks:N?w:I.ticks}):void 0,width:b,height:c,offset:g},!!N||t);(0,e.R)(Array.isArray(O),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof O,"]")),Array.isArray(O)&&(L=O)}if((!M||!M.length)&&"function"==typeof J){var P=y&&y.length,Q=J({xAxis:H?u(u({},H),{},{ticks:P?y:H.ticks}):void 0,width:b,height:c,offset:g},!!P||t);(0,e.R)(Array.isArray(Q),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof Q,"]")),Array.isArray(Q)&&(M=Q)}return d.createElement("g",{className:"recharts-cartesian-grid"},d.createElement(x,{fill:h.fill,fillOpacity:h.fillOpacity,x:h.x,y:h.y,width:h.width,height:h.height,ry:h.ry}),d.createElement(B,v({},h,{horizontalPoints:L})),d.createElement(C,v({},h,{verticalPoints:M})),d.createElement(z,v({},h,{offset:g,horizontalPoints:L,xAxis:H,yAxis:I})),d.createElement(A,v({},h,{offset:g,verticalPoints:M,xAxis:H,yAxis:I})))}G.displayName="CartesianGrid"},85407:(a,b,c)=>{"use strict";c.d(b,{YF:()=>j,dj:()=>k,fP:()=>l,ky:()=>i});var d=c(76067),e=c(17118),f=c(27977),g=c(43075),h=c(77357),i=(0,d.VP)("mouseClick"),j=(0,d.Nc)();j.startListening({actionCreator:i,effect:(a,b)=>{var c=a.payload,d=(0,f.g)(b.getState(),(0,h.w)(c));(null==d?void 0:d.activeIndex)!=null&&b.dispatch((0,e.jF)({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}});var k=(0,d.VP)("mouseMove"),l=(0,d.Nc)();l.startListening({actionCreator:k,effect:(a,b)=>{var c=a.payload,d=b.getState(),i=(0,g.au)(d,d.tooltip.settings.shared),j=(0,f.g)(d,(0,h.w)(c));"axis"===i&&((null==j?void 0:j.activeIndex)!=null?b.dispatch((0,e.Nt)({activeIndex:j.activeIndex,activeDataKey:void 0,activeCoordinate:j.activeCoordinate})):b.dispatch((0,e.xS)()))}})},85621:(a,b,c)=>{"use strict";c.d(b,{kz:()=>eC,fb:()=>eu,q:()=>eQ,tP:()=>eZ,g1:()=>e4,iv:()=>fu,Nk:()=>es,pM:()=>eA,Oz:()=>eO,tF:()=>fs,rj:()=>eq,ec:()=>em,bb:()=>eS,xp:()=>e2,wL:()=>eW,sr:()=>e_,Qn:()=>e1,MK:()=>ey,IO:()=>eo,P9:()=>eI,S5:()=>eF,PU:()=>ea,cd:()=>ec,eo:()=>ej,yi:()=>eG,ZB:()=>fw,D5:()=>fc,iV:()=>fe,Hd:()=>eh,Gx:()=>fz,DP:()=>eg,BQ:()=>fr,_y:()=>fB,AV:()=>eV,um:()=>ei,xM:()=>e0,gT:()=>eK,Kr:()=>eH,$X:()=>eM,TC:()=>ez,Zi:()=>fx,CR:()=>fy,ld:()=>ek,L$:()=>fo,Rl:()=>eb,Lw:()=>fl,KR:()=>fp,sf:()=>ed,wP:()=>fq});var d={};c.r(d),c.d(d,{scaleBand:()=>o,scaleDiverging:()=>function a(){var b=aM(c_()(au));return b.copy=function(){return cY(b,a())},i.apply(b,arguments)},scaleDivergingLog:()=>function a(){var b=aU(c_()).domain([.1,1,10]);return b.copy=function(){return cY(b,a()).base(b.base())},i.apply(b,arguments)},scaleDivergingPow:()=>c0,scaleDivergingSqrt:()=>c1,scaleDivergingSymlog:()=>function a(){var b=aX(c_());return b.copy=function(){return cY(b,a()).constant(b.constant())},i.apply(b,arguments)},scaleIdentity:()=>function a(b){var c;function d(a){return null==a||isNaN(a*=1)?c:a}return d.invert=d,d.domain=d.range=function(a){return arguments.length?(b=Array.from(a,as),d):b.slice()},d.unknown=function(a){return arguments.length?(c=a,d):c},d.copy=function(){return a(b).unknown(c)},b=arguments.length?Array.from(b,as):[0,1],aM(d)},scaleImplicit:()=>m,scaleLinear:()=>function a(){var b=aA();return b.copy=function(){return ay(b,a())},h.apply(b,arguments),aM(b)},scaleLog:()=>function a(){let b=aU(az()).domain([1,10]);return b.copy=()=>ay(b,a()).base(b.base()),h.apply(b,arguments),b},scaleOrdinal:()=>n,scalePoint:()=>p,scalePow:()=>a0,scaleQuantile:()=>function a(){var b,c=[],d=[],e=[];function f(){var a=0,b=Math.max(1,d.length);for(e=Array(b-1);++a<b;)e[a-1]=function(a,b,c=B){if(!(!(d=a.length)||isNaN(b*=1))){if(b<=0||d<2)return+c(a[0],0,a);if(b>=1)return+c(a[d-1],d-1,a);var d,e=(d-1)*b,f=Math.floor(e),g=+c(a[f],f,a);return g+(c(a[f+1],f+1,a)-g)*(e-f)}}(c,a/b);return g}function g(a){return null==a||isNaN(a*=1)?b:d[D(e,a)]}return g.invertExtent=function(a){var b=d.indexOf(a);return b<0?[NaN,NaN]:[b>0?e[b-1]:c[0],b<e.length?e[b]:c[c.length-1]]},g.domain=function(a){if(!arguments.length)return c.slice();for(let b of(c=[],a))null==b||isNaN(b*=1)||c.push(b);return c.sort(x),f()},g.range=function(a){return arguments.length?(d=Array.from(a),f()):d.slice()},g.unknown=function(a){return arguments.length?(b=a,g):b},g.quantiles=function(){return e.slice()},g.copy=function(){return a().domain(c).range(d).unknown(b)},h.apply(g,arguments)},scaleQuantize:()=>function a(){var b,c=0,d=1,e=1,f=[.5],g=[0,1];function i(a){return null!=a&&a<=a?g[D(f,a,0,e)]:b}function j(){var a=-1;for(f=Array(e);++a<e;)f[a]=((a+1)*d-(a-e)*c)/(e+1);return i}return i.domain=function(a){return arguments.length?([c,d]=a,c*=1,d*=1,j()):[c,d]},i.range=function(a){return arguments.length?(e=(g=Array.from(a)).length-1,j()):g.slice()},i.invertExtent=function(a){var b=g.indexOf(a);return b<0?[NaN,NaN]:b<1?[c,f[0]]:b>=e?[f[e-1],d]:[f[b-1],f[b]]},i.unknown=function(a){return arguments.length&&(b=a),i},i.thresholds=function(){return f.slice()},i.copy=function(){return a().domain([c,d]).range(g).unknown(b)},h.apply(aM(i),arguments)},scaleRadial:()=>function a(){var b,c=aA(),d=[0,1],e=!1;function f(a){var d,f=Math.sign(d=c(a))*Math.sqrt(Math.abs(d));return isNaN(f)?b:e?Math.round(f):f}return f.invert=function(a){return c.invert(a2(a))},f.domain=function(a){return arguments.length?(c.domain(a),f):c.domain()},f.range=function(a){return arguments.length?(c.range((d=Array.from(a,as)).map(a2)),f):d.slice()},f.rangeRound=function(a){return f.range(a).round(!0)},f.round=function(a){return arguments.length?(e=!!a,f):e},f.clamp=function(a){return arguments.length?(c.clamp(a),f):c.clamp()},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a(c.domain(),d).round(e).clamp(c.clamp()).unknown(b)},h.apply(f,arguments),aM(f)},scaleSequential:()=>function a(){var b=aM(cX()(au));return b.copy=function(){return cY(b,a())},i.apply(b,arguments)},scaleSequentialLog:()=>function a(){var b=aU(cX()).domain([1,10]);return b.copy=function(){return cY(b,a()).base(b.base())},i.apply(b,arguments)},scaleSequentialPow:()=>cZ,scaleSequentialQuantile:()=>function a(){var b=[],c=au;function d(a){if(null!=a&&!isNaN(a*=1))return c((D(b,a,1)-1)/(b.length-1))}return d.domain=function(a){if(!arguments.length)return b.slice();for(let c of(b=[],a))null==c||isNaN(c*=1)||b.push(c);return b.sort(x),d},d.interpolator=function(a){return arguments.length?(c=a,d):c},d.range=function(){return b.map((a,d)=>c(d/(b.length-1)))},d.quantiles=function(a){return Array.from({length:a+1},(c,d)=>(function(a,b,c){if(!(!(d=(a=Float64Array.from(function*(a,b){if(void 0===b)for(let b of a)null!=b&&(b*=1)>=b&&(yield b);else{let c=-1;for(let d of a)null!=(d=b(d,++c,a))&&(d*=1)>=d&&(yield d)}}(a,void 0))).length)||isNaN(b*=1))){if(b<=0||d<2)return a4(a);if(b>=1)return a3(a);var d,e=(d-1)*b,f=Math.floor(e),g=a3((function a(b,c,d=0,e=1/0,f){if(c=Math.floor(c),d=Math.floor(Math.max(0,d)),e=Math.floor(Math.min(b.length-1,e)),!(d<=c&&c<=e))return b;for(f=void 0===f?a5:function(a=x){if(a===x)return a5;if("function"!=typeof a)throw TypeError("compare is not a function");return(b,c)=>{let d=a(b,c);return d||0===d?d:(0===a(c,c))-(0===a(b,b))}}(f);e>d;){if(e-d>600){let g=e-d+1,h=c-d+1,i=Math.log(g),j=.5*Math.exp(2*i/3),k=.5*Math.sqrt(i*j*(g-j)/g)*(h-g/2<0?-1:1),l=Math.max(d,Math.floor(c-h*j/g+k)),m=Math.min(e,Math.floor(c+(g-h)*j/g+k));a(b,c,l,m,f)}let g=b[c],h=d,i=e;for(a6(b,d,c),f(b[e],g)>0&&a6(b,d,e);h<i;){for(a6(b,h,i),++h,--i;0>f(b[h],g);)++h;for(;f(b[i],g)>0;)--i}0===f(b[d],g)?a6(b,d,i):a6(b,++i,e),i<=c&&(d=i+1),c<=i&&(e=i-1)}return b})(a,f).subarray(0,f+1));return g+(a4(a.subarray(f+1))-g)*(e-f)}})(b,d/a))},d.copy=function(){return a(c).domain(b)},i.apply(d,arguments)},scaleSequentialSqrt:()=>c$,scaleSequentialSymlog:()=>function a(){var b=aX(cX());return b.copy=function(){return cY(b,a()).constant(b.constant())},i.apply(b,arguments)},scaleSqrt:()=>a1,scaleSymlog:()=>function a(){var b=aX(az());return b.copy=function(){return ay(b,a()).constant(b.constant())},h.apply(b,arguments)},scaleThreshold:()=>function a(){var b,c=[.5],d=[0,1],e=1;function f(a){return null!=a&&a<=a?d[D(c,a,0,e)]:b}return f.domain=function(a){return arguments.length?(e=Math.min((c=Array.from(a)).length,d.length-1),f):c.slice()},f.range=function(a){return arguments.length?(d=Array.from(a),e=Math.min(c.length,d.length-1),f):d.slice()},f.invertExtent=function(a){var b=d.indexOf(a);return[c[b-1],c[b]]},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a().domain(c).range(d).unknown(b)},h.apply(f,arguments)},scaleTime:()=>cV,scaleUtc:()=>cW,tickFormat:()=>aL});var e=c(84648),f=c(30921),g=c.n(f);function h(a,b){switch(arguments.length){case 0:break;case 1:this.range(a);break;default:this.range(b).domain(a)}return this}function i(a,b){switch(arguments.length){case 0:break;case 1:"function"==typeof a?this.interpolator(a):this.range(a);break;default:this.domain(a),"function"==typeof b?this.interpolator(b):this.range(b)}return this}class j extends Map{constructor(a,b=l){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:b}}),null!=a)for(let[b,c]of a)this.set(b,c)}get(a){return super.get(k(this,a))}has(a){return super.has(k(this,a))}set(a,b){return super.set(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):(a.set(d,c),c)}(this,a),b)}delete(a){return super.delete(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)&&(c=a.get(d),a.delete(d)),c}(this,a))}}function k({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):c}function l(a){return null!==a&&"object"==typeof a?a.valueOf():a}let m=Symbol("implicit");function n(){var a=new j,b=[],c=[],d=m;function e(e){let f=a.get(e);if(void 0===f){if(d!==m)return d;a.set(e,f=b.push(e)-1)}return c[f%c.length]}return e.domain=function(c){if(!arguments.length)return b.slice();for(let d of(b=[],a=new j,c))a.has(d)||a.set(d,b.push(d)-1);return e},e.range=function(a){return arguments.length?(c=Array.from(a),e):c.slice()},e.unknown=function(a){return arguments.length?(d=a,e):d},e.copy=function(){return n(b,c).unknown(d)},h.apply(e,arguments),e}function o(){var a,b,c=n().unknown(void 0),d=c.domain,e=c.range,f=0,g=1,i=!1,j=0,k=0,l=.5;function m(){var c=d().length,h=g<f,m=h?g:f,n=h?f:g;a=(n-m)/Math.max(1,c-j+2*k),i&&(a=Math.floor(a)),m+=(n-m-a*(c-j))*l,b=a*(1-j),i&&(m=Math.round(m),b=Math.round(b));var o=(function(a,b,c){a*=1,b*=1,c=(e=arguments.length)<2?(b=a,a=0,1):e<3?1:+c;for(var d=-1,e=0|Math.max(0,Math.ceil((b-a)/c)),f=Array(e);++d<e;)f[d]=a+d*c;return f})(c).map(function(b){return m+a*b});return e(h?o.reverse():o)}return delete c.unknown,c.domain=function(a){return arguments.length?(d(a),m()):d()},c.range=function(a){return arguments.length?([f,g]=a,f*=1,g*=1,m()):[f,g]},c.rangeRound=function(a){return[f,g]=a,f*=1,g*=1,i=!0,m()},c.bandwidth=function(){return b},c.step=function(){return a},c.round=function(a){return arguments.length?(i=!!a,m()):i},c.padding=function(a){return arguments.length?(j=Math.min(1,k=+a),m()):j},c.paddingInner=function(a){return arguments.length?(j=Math.min(1,a),m()):j},c.paddingOuter=function(a){return arguments.length?(k=+a,m()):k},c.align=function(a){return arguments.length?(l=Math.max(0,Math.min(1,a)),m()):l},c.copy=function(){return o(d(),[f,g]).round(i).paddingInner(j).paddingOuter(k).align(l)},h.apply(m(),arguments)}function p(){return function a(b){var c=b.copy;return b.padding=b.paddingOuter,delete b.paddingInner,delete b.paddingOuter,b.copy=function(){return a(c())},b}(o.apply(null,arguments).paddingInner(1))}let q=Math.sqrt(50),r=Math.sqrt(10),s=Math.sqrt(2);function t(a,b,c){let d,e,f,g=(b-a)/Math.max(0,c),h=Math.floor(Math.log10(g)),i=g/Math.pow(10,h),j=i>=q?10:i>=r?5:i>=s?2:1;return(h<0?(d=Math.round(a*(f=Math.pow(10,-h)/j)),e=Math.round(b*f),d/f<a&&++d,e/f>b&&--e,f=-f):(d=Math.round(a/(f=Math.pow(10,h)*j)),e=Math.round(b/f),d*f<a&&++d,e*f>b&&--e),e<d&&.5<=c&&c<2)?t(a,b,2*c):[d,e,f]}function u(a,b,c){if(b*=1,a*=1,!((c*=1)>0))return[];if(a===b)return[a];let d=b<a,[e,f,g]=d?t(b,a,c):t(a,b,c);if(!(f>=e))return[];let h=f-e+1,i=Array(h);if(d)if(g<0)for(let a=0;a<h;++a)i[a]=-((f-a)/g);else for(let a=0;a<h;++a)i[a]=(f-a)*g;else if(g<0)for(let a=0;a<h;++a)i[a]=-((e+a)/g);else for(let a=0;a<h;++a)i[a]=(e+a)*g;return i}function v(a,b,c){return t(a*=1,b*=1,c*=1)[2]}function w(a,b,c){b*=1,a*=1,c*=1;let d=b<a,e=d?v(b,a,c):v(a,b,c);return(d?-1:1)*(e<0?-(1/e):e)}function x(a,b){return null==a||null==b?NaN:a<b?-1:a>b?1:a>=b?0:NaN}function y(a,b){return null==a||null==b?NaN:b<a?-1:b>a?1:b>=a?0:NaN}function z(a){let b,c,d;function e(a,d,f=0,g=a.length){if(f<g){if(0!==b(d,d))return g;do{let b=f+g>>>1;0>c(a[b],d)?f=b+1:g=b}while(f<g)}return f}return 2!==a.length?(b=x,c=(b,c)=>x(a(b),c),d=(b,c)=>a(b)-c):(b=a===x||a===y?a:A,c=a,d=a),{left:e,center:function(a,b,c=0,f=a.length){let g=e(a,b,c,f-1);return g>c&&d(a[g-1],b)>-d(a[g],b)?g-1:g},right:function(a,d,e=0,f=a.length){if(e<f){if(0!==b(d,d))return f;do{let b=e+f>>>1;0>=c(a[b],d)?e=b+1:f=b}while(e<f)}return e}}}function A(){return 0}function B(a){return null===a?NaN:+a}let C=z(x),D=C.right;function E(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function F(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function G(){}C.left,z(B).center;var H="\\s*([+-]?\\d+)\\s*",I="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",J="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",K=/^#([0-9a-f]{3,8})$/,L=RegExp(`^rgb\\(${H},${H},${H}\\)$`),M=RegExp(`^rgb\\(${J},${J},${J}\\)$`),N=RegExp(`^rgba\\(${H},${H},${H},${I}\\)$`),O=RegExp(`^rgba\\(${J},${J},${J},${I}\\)$`),P=RegExp(`^hsl\\(${I},${J},${J}\\)$`),Q=RegExp(`^hsla\\(${I},${J},${J},${I}\\)$`),R={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function S(){return this.rgb().formatHex()}function T(){return this.rgb().formatRgb()}function U(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=K.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?V(b):3===c?new Y(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?W(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?W(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=L.exec(a))?new Y(b[1],b[2],b[3],1):(b=M.exec(a))?new Y(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=N.exec(a))?W(b[1],b[2],b[3],b[4]):(b=O.exec(a))?W(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=P.exec(a))?ac(b[1],b[2]/100,b[3]/100,1):(b=Q.exec(a))?ac(b[1],b[2]/100,b[3]/100,b[4]):R.hasOwnProperty(a)?V(R[a]):"transparent"===a?new Y(NaN,NaN,NaN,0):null}function V(a){return new Y(a>>16&255,a>>8&255,255&a,1)}function W(a,b,c,d){return d<=0&&(a=b=c=NaN),new Y(a,b,c,d)}function X(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof G||(e=U(e)),e)?new Y((e=e.rgb()).r,e.g,e.b,e.opacity):new Y:new Y(a,b,c,null==d?1:d)}function Y(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function Z(){return`#${ab(this.r)}${ab(this.g)}${ab(this.b)}`}function $(){let a=_(this.opacity);return`${1===a?"rgb(":"rgba("}${aa(this.r)}, ${aa(this.g)}, ${aa(this.b)}${1===a?")":`, ${a})`}`}function _(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function aa(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function ab(a){return((a=aa(a))<16?"0":"")+a.toString(16)}function ac(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new ae(a,b,c,d)}function ad(a){if(a instanceof ae)return new ae(a.h,a.s,a.l,a.opacity);if(a instanceof G||(a=U(a)),!a)return new ae;if(a instanceof ae)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new ae(g,h,i,a.opacity)}function ae(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function af(a){return(a=(a||0)%360)<0?a+360:a}function ag(a){return Math.max(0,Math.min(1,a||0))}function ah(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function ai(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}E(G,U,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:S,formatHex:S,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ad(this).formatHsl()},formatRgb:T,toString:T}),E(Y,X,F(G,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new Y(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new Y(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new Y(aa(this.r),aa(this.g),aa(this.b),_(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Z,formatHex:Z,formatHex8:function(){return`#${ab(this.r)}${ab(this.g)}${ab(this.b)}${ab((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:$,toString:$})),E(ae,function(a,b,c,d){return 1==arguments.length?ad(a):new ae(a,b,c,null==d?1:d)},F(G,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new ae(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new ae(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new Y(ah(a>=240?a-240:a+120,e,d),ah(a,e,d),ah(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new ae(af(this.h),ag(this.s),ag(this.l),_(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=_(this.opacity);return`${1===a?"hsl(":"hsla("}${af(this.h)}, ${100*ag(this.s)}%, ${100*ag(this.l)}%${1===a?")":`, ${a})`}`}}));let aj=a=>()=>a;function ak(a,b){var c=b-a;return c?function(b){return a+b*c}:aj(isNaN(a)?b:a)}let al=function a(b){var c,d=1==(c=+b)?ak:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):aj(isNaN(a)?b:a)};function e(a,b){var c=d((a=X(a)).r,(b=X(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=ak(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function am(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=X(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}function an(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}am(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return ai((c-d/b)*b,g,e,f,h)}}),am(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return ai((c-d/b)*b,e,f,g,h)}});var ao=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ap=RegExp(ao.source,"g");function aq(a,b){var c,d,e=typeof b;return null==b||"boolean"===e?aj(b):("number"===e?an:"string"===e?(d=U(b))?(b=d,al):function(a,b){var c,d,e,f,g,h=ao.lastIndex=ap.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=ao.exec(a))&&(f=ap.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:an(e,f)})),h=ap.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}:b instanceof U?al:b instanceof Date?function(a,b){var c=new Date;return a*=1,b*=1,function(d){return c.setTime(a*(1-d)+b*d),c}}:!ArrayBuffer.isView(c=b)||c instanceof DataView?Array.isArray(b)?function(a,b){var c,d=b?b.length:0,e=a?Math.min(d,a.length):0,f=Array(e),g=Array(d);for(c=0;c<e;++c)f[c]=aq(a[c],b[c]);for(;c<d;++c)g[c]=b[c];return function(a){for(c=0;c<e;++c)g[c]=f[c](a);return g}}:"function"!=typeof b.valueOf&&"function"!=typeof b.toString||isNaN(b)?function(a,b){var c,d={},e={};for(c in(null===a||"object"!=typeof a)&&(a={}),(null===b||"object"!=typeof b)&&(b={}),b)c in a?d[c]=aq(a[c],b[c]):e[c]=b[c];return function(a){for(c in d)e[c]=d[c](a);return e}}:an:function(a,b){b||(b=[]);var c,d=a?Math.min(b.length,a.length):0,e=b.slice();return function(f){for(c=0;c<d;++c)e[c]=a[c]*(1-f)+b[c]*f;return e}})(a,b)}function ar(a,b){return a*=1,b*=1,function(c){return Math.round(a*(1-c)+b*c)}}function as(a){return+a}var at=[0,1];function au(a){return a}function av(a,b){var c;return(b-=a*=1)?function(c){return(c-a)/b}:(c=isNaN(b)?NaN:.5,function(){return c})}function aw(a,b,c){var d=a[0],e=a[1],f=b[0],g=b[1];return e<d?(d=av(e,d),f=c(g,f)):(d=av(d,e),f=c(f,g)),function(a){return f(d(a))}}function ax(a,b,c){var d=Math.min(a.length,b.length)-1,e=Array(d),f=Array(d),g=-1;for(a[d]<a[0]&&(a=a.slice().reverse(),b=b.slice().reverse());++g<d;)e[g]=av(a[g],a[g+1]),f[g]=c(b[g],b[g+1]);return function(b){var c=D(a,b,1,d)-1;return f[c](e[c](b))}}function ay(a,b){return b.domain(a.domain()).range(a.range()).interpolate(a.interpolate()).clamp(a.clamp()).unknown(a.unknown())}function az(){var a,b,c,d,e,f,g=at,h=at,i=aq,j=au;function k(){var a,b,c,i=Math.min(g.length,h.length);return j!==au&&(a=g[0],b=g[i-1],a>b&&(c=a,a=b,b=c),j=function(c){return Math.max(a,Math.min(b,c))}),d=i>2?ax:aw,e=f=null,l}function l(b){return null==b||isNaN(b*=1)?c:(e||(e=d(g.map(a),h,i)))(a(j(b)))}return l.invert=function(c){return j(b((f||(f=d(h,g.map(a),an)))(c)))},l.domain=function(a){return arguments.length?(g=Array.from(a,as),k()):g.slice()},l.range=function(a){return arguments.length?(h=Array.from(a),k()):h.slice()},l.rangeRound=function(a){return h=Array.from(a),i=ar,k()},l.clamp=function(a){return arguments.length?(j=!!a||au,k()):j!==au},l.interpolate=function(a){return arguments.length?(i=a,k()):i},l.unknown=function(a){return arguments.length?(c=a,l):c},function(c,d){return a=c,b=d,k()}}function aA(){return az()(au,au)}var aB=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function aC(a){var b;if(!(b=aB.exec(a)))throw Error("invalid format: "+a);return new aD({fill:b[1],align:b[2],sign:b[3],symbol:b[4],zero:b[5],width:b[6],comma:b[7],precision:b[8]&&b[8].slice(1),trim:b[9],type:b[10]})}function aD(a){this.fill=void 0===a.fill?" ":a.fill+"",this.align=void 0===a.align?">":a.align+"",this.sign=void 0===a.sign?"-":a.sign+"",this.symbol=void 0===a.symbol?"":a.symbol+"",this.zero=!!a.zero,this.width=void 0===a.width?void 0:+a.width,this.comma=!!a.comma,this.precision=void 0===a.precision?void 0:+a.precision,this.trim=!!a.trim,this.type=void 0===a.type?"":a.type+""}function aE(a,b){if((c=(a=b?a.toExponential(b-1):a.toExponential()).indexOf("e"))<0)return null;var c,d=a.slice(0,c);return[d.length>1?d[0]+d.slice(2):d,+a.slice(c+1)]}function aF(a){return(a=aE(Math.abs(a)))?a[1]:NaN}function aG(a,b){var c=aE(a,b);if(!c)return a+"";var d=c[0],e=c[1];return e<0?"0."+Array(-e).join("0")+d:d.length>e+1?d.slice(0,e+1)+"."+d.slice(e+1):d+Array(e-d.length+2).join("0")}aC.prototype=aD.prototype,aD.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let aH={"%":(a,b)=>(100*a).toFixed(b),b:a=>Math.round(a).toString(2),c:a=>a+"",d:function(a){return Math.abs(a=Math.round(a))>=1e21?a.toLocaleString("en").replace(/,/g,""):a.toString(10)},e:(a,b)=>a.toExponential(b),f:(a,b)=>a.toFixed(b),g:(a,b)=>a.toPrecision(b),o:a=>Math.round(a).toString(8),p:(a,b)=>aG(100*a,b),r:aG,s:function(a,b){var c=aE(a,b);if(!c)return a+"";var d=c[0],e=c[1],f=e-(c9=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,g=d.length;return f===g?d:f>g?d+Array(f-g+1).join("0"):f>0?d.slice(0,f)+"."+d.slice(f):"0."+Array(1-f).join("0")+aE(a,Math.max(0,b+f-1))[0]},X:a=>Math.round(a).toString(16).toUpperCase(),x:a=>Math.round(a).toString(16)};function aI(a){return a}var aJ=Array.prototype.map,aK=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function aL(a,b,c,d){var e,f,g=w(a,b,c);switch((d=aC(null==d?",f":d)).type){case"s":var h=Math.max(Math.abs(a),Math.abs(b));return null!=d.precision||isNaN(f=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(aF(h)/3)))-aF(Math.abs(g))))||(d.precision=f),dc(d,h);case"":case"e":case"g":case"p":case"r":null!=d.precision||isNaN(f=Math.max(0,aF(Math.abs(Math.max(Math.abs(a),Math.abs(b)))-(e=Math.abs(e=g)))-aF(e))+1)||(d.precision=f-("e"===d.type));break;case"f":case"%":null!=d.precision||isNaN(f=Math.max(0,-aF(Math.abs(g))))||(d.precision=f-("%"===d.type)*2)}return db(d)}function aM(a){var b=a.domain;return a.ticks=function(a){var c=b();return u(c[0],c[c.length-1],null==a?10:a)},a.tickFormat=function(a,c){var d=b();return aL(d[0],d[d.length-1],null==a?10:a,c)},a.nice=function(c){null==c&&(c=10);var d,e,f=b(),g=0,h=f.length-1,i=f[g],j=f[h],k=10;for(j<i&&(e=i,i=j,j=e,e=g,g=h,h=e);k-- >0;){if((e=v(i,j,c))===d)return f[g]=i,f[h]=j,b(f);if(e>0)i=Math.floor(i/e)*e,j=Math.ceil(j/e)*e;else if(e<0)i=Math.ceil(i*e)/e,j=Math.floor(j*e)/e;else break;d=e}return a},a}function aN(a,b){a=a.slice();var c,d=0,e=a.length-1,f=a[d],g=a[e];return g<f&&(c=d,d=e,e=c,c=f,f=g,g=c),a[d]=b.floor(f),a[e]=b.ceil(g),a}function aO(a){return Math.log(a)}function aP(a){return Math.exp(a)}function aQ(a){return-Math.log(-a)}function aR(a){return-Math.exp(-a)}function aS(a){return isFinite(a)?+("1e"+a):a<0?0:a}function aT(a){return(b,c)=>-a(-b,c)}function aU(a){let b,c,d=a(aO,aP),e=d.domain,f=10;function g(){var g,h;return b=(g=f)===Math.E?Math.log:10===g&&Math.log10||2===g&&Math.log2||(g=Math.log(g),a=>Math.log(a)/g),c=10===(h=f)?aS:h===Math.E?Math.exp:a=>Math.pow(h,a),e()[0]<0?(b=aT(b),c=aT(c),a(aQ,aR)):a(aO,aP),d}return d.base=function(a){return arguments.length?(f=+a,g()):f},d.domain=function(a){return arguments.length?(e(a),g()):e()},d.ticks=a=>{let d,g,h=e(),i=h[0],j=h[h.length-1],k=j<i;k&&([i,j]=[j,i]);let l=b(i),m=b(j),n=null==a?10:+a,o=[];if(!(f%1)&&m-l<n){if(l=Math.floor(l),m=Math.ceil(m),i>0){for(;l<=m;++l)for(d=1;d<f;++d)if(!((g=l<0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}}else for(;l<=m;++l)for(d=f-1;d>=1;--d)if(!((g=l>0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}2*o.length<n&&(o=u(i,j,n))}else o=u(l,m,Math.min(m-l,n)).map(c);return k?o.reverse():o},d.tickFormat=(a,e)=>{if(null==a&&(a=10),null==e&&(e=10===f?"s":","),"function"!=typeof e&&(f%1||null!=(e=aC(e)).precision||(e.trim=!0),e=db(e)),a===1/0)return e;let g=Math.max(1,f*a/d.ticks().length);return a=>{let d=a/c(Math.round(b(a)));return d*f<f-.5&&(d*=f),d<=g?e(a):""}},d.nice=()=>e(aN(e(),{floor:a=>c(Math.floor(b(a))),ceil:a=>c(Math.ceil(b(a)))})),d}function aV(a){return function(b){return Math.sign(b)*Math.log1p(Math.abs(b/a))}}function aW(a){return function(b){return Math.sign(b)*Math.expm1(Math.abs(b))*a}}function aX(a){var b=1,c=a(aV(1),aW(b));return c.constant=function(c){return arguments.length?a(aV(b=+c),aW(b)):b},aM(c)}function aY(a){return function(b){return b<0?-Math.pow(-b,a):Math.pow(b,a)}}function aZ(a){return a<0?-Math.sqrt(-a):Math.sqrt(a)}function a$(a){return a<0?-a*a:a*a}function a_(a){var b=a(au,au),c=1;return b.exponent=function(b){return arguments.length?1==(c=+b)?a(au,au):.5===c?a(aZ,a$):a(aY(c),aY(1/c)):c},aM(b)}function a0(){var a=a_(az());return a.copy=function(){return ay(a,a0()).exponent(a.exponent())},h.apply(a,arguments),a}function a1(){return a0.apply(null,arguments).exponent(.5)}function a2(a){return Math.sign(a)*a*a}function a3(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c<b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c<e||void 0===c&&e>=e)&&(c=e)}return c}function a4(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c>b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c>e||void 0===c&&e>=e)&&(c=e)}return c}function a5(a,b){return(null==a||!(a>=a))-(null==b||!(b>=b))||(a<b?-1:+(a>b))}function a6(a,b,c){let d=a[b];a[b]=a[c],a[c]=d}db=(da=function(a){var b,c,d,e=void 0===a.grouping||void 0===a.thousands?aI:(b=aJ.call(a.grouping,Number),c=a.thousands+"",function(a,d){for(var e=a.length,f=[],g=0,h=b[0],i=0;e>0&&h>0&&(i+h+1>d&&(h=Math.max(1,d-i)),f.push(a.substring(e-=h,e+h)),!((i+=h+1)>d));)h=b[g=(g+1)%b.length];return f.reverse().join(c)}),f=void 0===a.currency?"":a.currency[0]+"",g=void 0===a.currency?"":a.currency[1]+"",h=void 0===a.decimal?".":a.decimal+"",i=void 0===a.numerals?aI:(d=aJ.call(a.numerals,String),function(a){return a.replace(/[0-9]/g,function(a){return d[+a]})}),j=void 0===a.percent?"%":a.percent+"",k=void 0===a.minus?"−":a.minus+"",l=void 0===a.nan?"NaN":a.nan+"";function m(a){var b=(a=aC(a)).fill,c=a.align,d=a.sign,m=a.symbol,n=a.zero,o=a.width,p=a.comma,q=a.precision,r=a.trim,s=a.type;"n"===s?(p=!0,s="g"):aH[s]||(void 0===q&&(q=12),r=!0,s="g"),(n||"0"===b&&"="===c)&&(n=!0,b="0",c="=");var t="$"===m?f:"#"===m&&/[boxX]/.test(s)?"0"+s.toLowerCase():"",u="$"===m?g:/[%p]/.test(s)?j:"",v=aH[s],w=/[defgprs%]/.test(s);function x(a){var f,g,j,m=t,x=u;if("c"===s)x=v(a)+x,a="";else{var y=(a*=1)<0||1/a<0;if(a=isNaN(a)?l:v(Math.abs(a),q),r&&(a=function(a){a:for(var b,c=a.length,d=1,e=-1;d<c;++d)switch(a[d]){case".":e=b=d;break;case"0":0===e&&(e=d),b=d;break;default:if(!+a[d])break a;e>0&&(e=0)}return e>0?a.slice(0,e)+a.slice(b+1):a}(a)),y&&0==+a&&"+"!==d&&(y=!1),m=(y?"("===d?d:k:"-"===d||"("===d?"":d)+m,x=("s"===s?aK[8+c9/3]:"")+x+(y&&"("===d?")":""),w){for(f=-1,g=a.length;++f<g;)if(48>(j=a.charCodeAt(f))||j>57){x=(46===j?h+a.slice(f+1):a.slice(f))+x,a=a.slice(0,f);break}}}p&&!n&&(a=e(a,1/0));var z=m.length+a.length+x.length,A=z<o?Array(o-z+1).join(b):"";switch(p&&n&&(a=e(A+a,A.length?o-x.length:1/0),A=""),c){case"<":a=m+a+x+A;break;case"=":a=m+A+a+x;break;case"^":a=A.slice(0,z=A.length>>1)+m+a+x+A.slice(z);break;default:a=A+m+a+x}return i(a)}return q=void 0===q?6:/[gprs]/.test(s)?Math.max(1,Math.min(21,q)):Math.max(0,Math.min(20,q)),x.toString=function(){return a+""},x}return{format:m,formatPrefix:function(a,b){var c=m(((a=aC(a)).type="f",a)),d=3*Math.max(-8,Math.min(8,Math.floor(aF(b)/3))),e=Math.pow(10,-d),f=aK[8+d/3];return function(a){return c(e*a)+f}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,dc=da.formatPrefix;let a7=new Date,a8=new Date;function a9(a,b,c,d){function e(b){return a(b=0==arguments.length?new Date:new Date(+b)),b}return e.floor=b=>(a(b=new Date(+b)),b),e.ceil=c=>(a(c=new Date(c-1)),b(c,1),a(c),c),e.round=a=>{let b=e(a),c=e.ceil(a);return a-b<c-a?b:c},e.offset=(a,c)=>(b(a=new Date(+a),null==c?1:Math.floor(c)),a),e.range=(c,d,f)=>{let g,h=[];if(c=e.ceil(c),f=null==f?1:Math.floor(f),!(c<d)||!(f>0))return h;do h.push(g=new Date(+c)),b(c,f),a(c);while(g<c&&c<d);return h},e.filter=c=>a9(b=>{if(b>=b)for(;a(b),!c(b);)b.setTime(b-1)},(a,d)=>{if(a>=a)if(d<0)for(;++d<=0;)for(;b(a,-1),!c(a););else for(;--d>=0;)for(;b(a,1),!c(a););}),c&&(e.count=(b,d)=>(a7.setTime(+b),a8.setTime(+d),a(a7),a(a8),Math.floor(c(a7,a8))),e.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e.filter(d?b=>d(b)%a==0:b=>e.count(0,b)%a==0):e:null),e}let ba=a9(()=>{},(a,b)=>{a.setTime(+a+b)},(a,b)=>b-a);ba.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?a9(b=>{b.setTime(Math.floor(b/a)*a)},(b,c)=>{b.setTime(+b+c*a)},(b,c)=>(c-b)/a):ba:null,ba.range;let bb=a9(a=>{a.setTime(a-a.getMilliseconds())},(a,b)=>{a.setTime(+a+1e3*b)},(a,b)=>(b-a)/1e3,a=>a.getUTCSeconds());bb.range;let bc=a9(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds())},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getMinutes());bc.range;let bd=a9(a=>{a.setUTCSeconds(0,0)},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getUTCMinutes());bd.range;let be=a9(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds()-6e4*a.getMinutes())},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getHours());be.range;let bf=a9(a=>{a.setUTCMinutes(0,0,0)},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getUTCHours());bf.range;let bg=a9(a=>a.setHours(0,0,0,0),(a,b)=>a.setDate(a.getDate()+b),(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/864e5,a=>a.getDate()-1);bg.range;let bh=a9(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>a.getUTCDate()-1);bh.range;let bi=a9(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>Math.floor(a/864e5));function bj(a){return a9(b=>{b.setDate(b.getDate()-(b.getDay()+7-a)%7),b.setHours(0,0,0,0)},(a,b)=>{a.setDate(a.getDate()+7*b)},(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/6048e5)}bi.range;let bk=bj(0),bl=bj(1),bm=bj(2),bn=bj(3),bo=bj(4),bp=bj(5),bq=bj(6);function br(a){return a9(b=>{b.setUTCDate(b.getUTCDate()-(b.getUTCDay()+7-a)%7),b.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+7*b)},(a,b)=>(b-a)/6048e5)}bk.range,bl.range,bm.range,bn.range,bo.range,bp.range,bq.range;let bs=br(0),bt=br(1),bu=br(2),bv=br(3),bw=br(4),bx=br(5),by=br(6);bs.range,bt.range,bu.range,bv.range,bw.range,bx.range,by.range;let bz=a9(a=>{a.setDate(1),a.setHours(0,0,0,0)},(a,b)=>{a.setMonth(a.getMonth()+b)},(a,b)=>b.getMonth()-a.getMonth()+(b.getFullYear()-a.getFullYear())*12,a=>a.getMonth());bz.range;let bA=a9(a=>{a.setUTCDate(1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCMonth(a.getUTCMonth()+b)},(a,b)=>b.getUTCMonth()-a.getUTCMonth()+(b.getUTCFullYear()-a.getUTCFullYear())*12,a=>a.getUTCMonth());bA.range;let bB=a9(a=>{a.setMonth(0,1),a.setHours(0,0,0,0)},(a,b)=>{a.setFullYear(a.getFullYear()+b)},(a,b)=>b.getFullYear()-a.getFullYear(),a=>a.getFullYear());bB.every=a=>isFinite(a=Math.floor(a))&&a>0?a9(b=>{b.setFullYear(Math.floor(b.getFullYear()/a)*a),b.setMonth(0,1),b.setHours(0,0,0,0)},(b,c)=>{b.setFullYear(b.getFullYear()+c*a)}):null,bB.range;let bC=a9(a=>{a.setUTCMonth(0,1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCFullYear(a.getUTCFullYear()+b)},(a,b)=>b.getUTCFullYear()-a.getUTCFullYear(),a=>a.getUTCFullYear());function bD(a,b,c,d,e,f){let g=[[bb,1,1e3],[bb,5,5e3],[bb,15,15e3],[bb,30,3e4],[f,1,6e4],[f,5,3e5],[f,15,9e5],[f,30,18e5],[e,1,36e5],[e,3,108e5],[e,6,216e5],[e,12,432e5],[d,1,864e5],[d,2,1728e5],[c,1,6048e5],[b,1,2592e6],[b,3,7776e6],[a,1,31536e6]];function h(b,c,d){let e=Math.abs(c-b)/d,f=z(([,,a])=>a).right(g,e);if(f===g.length)return a.every(w(b/31536e6,c/31536e6,d));if(0===f)return ba.every(Math.max(w(b,c,d),1));let[h,i]=g[e/g[f-1][2]<g[f][2]/e?f-1:f];return h.every(i)}return[function(a,b,c){let d=b<a;d&&([a,b]=[b,a]);let e=c&&"function"==typeof c.range?c:h(a,b,c),f=e?e.range(a,+b+1):[];return d?f.reverse():f},h]}bC.every=a=>isFinite(a=Math.floor(a))&&a>0?a9(b=>{b.setUTCFullYear(Math.floor(b.getUTCFullYear()/a)*a),b.setUTCMonth(0,1),b.setUTCHours(0,0,0,0)},(b,c)=>{b.setUTCFullYear(b.getUTCFullYear()+c*a)}):null,bC.range;let[bE,bF]=bD(bC,bA,bs,bi,bf,bd),[bG,bH]=bD(bB,bz,bk,bg,be,bc);function bI(a){if(0<=a.y&&a.y<100){var b=new Date(-1,a.m,a.d,a.H,a.M,a.S,a.L);return b.setFullYear(a.y),b}return new Date(a.y,a.m,a.d,a.H,a.M,a.S,a.L)}function bJ(a){if(0<=a.y&&a.y<100){var b=new Date(Date.UTC(-1,a.m,a.d,a.H,a.M,a.S,a.L));return b.setUTCFullYear(a.y),b}return new Date(Date.UTC(a.y,a.m,a.d,a.H,a.M,a.S,a.L))}function bK(a,b,c){return{y:a,m:b,d:c,H:0,M:0,S:0,L:0}}var bL={"-":"",_:" ",0:"0"},bM=/^\s*\d+/,bN=/^%/,bO=/[\\^$*+?|[\]().{}]/g;function bP(a,b,c){var d=a<0?"-":"",e=(d?-a:a)+"",f=e.length;return d+(f<c?Array(c-f+1).join(b)+e:e)}function bQ(a){return a.replace(bO,"\\$&")}function bR(a){return RegExp("^(?:"+a.map(bQ).join("|")+")","i")}function bS(a){return new Map(a.map((a,b)=>[a.toLowerCase(),b]))}function bT(a,b,c){var d=bM.exec(b.slice(c,c+1));return d?(a.w=+d[0],c+d[0].length):-1}function bU(a,b,c){var d=bM.exec(b.slice(c,c+1));return d?(a.u=+d[0],c+d[0].length):-1}function bV(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.U=+d[0],c+d[0].length):-1}function bW(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.V=+d[0],c+d[0].length):-1}function bX(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.W=+d[0],c+d[0].length):-1}function bY(a,b,c){var d=bM.exec(b.slice(c,c+4));return d?(a.y=+d[0],c+d[0].length):-1}function bZ(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.y=+d[0]+(+d[0]>68?1900:2e3),c+d[0].length):-1}function b$(a,b,c){var d=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(b.slice(c,c+6));return d?(a.Z=d[1]?0:-(d[2]+(d[3]||"00")),c+d[0].length):-1}function b_(a,b,c){var d=bM.exec(b.slice(c,c+1));return d?(a.q=3*d[0]-3,c+d[0].length):-1}function b0(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.m=d[0]-1,c+d[0].length):-1}function b1(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.d=+d[0],c+d[0].length):-1}function b2(a,b,c){var d=bM.exec(b.slice(c,c+3));return d?(a.m=0,a.d=+d[0],c+d[0].length):-1}function b3(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.H=+d[0],c+d[0].length):-1}function b4(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.M=+d[0],c+d[0].length):-1}function b5(a,b,c){var d=bM.exec(b.slice(c,c+2));return d?(a.S=+d[0],c+d[0].length):-1}function b6(a,b,c){var d=bM.exec(b.slice(c,c+3));return d?(a.L=+d[0],c+d[0].length):-1}function b7(a,b,c){var d=bM.exec(b.slice(c,c+6));return d?(a.L=Math.floor(d[0]/1e3),c+d[0].length):-1}function b8(a,b,c){var d=bN.exec(b.slice(c,c+1));return d?c+d[0].length:-1}function b9(a,b,c){var d=bM.exec(b.slice(c));return d?(a.Q=+d[0],c+d[0].length):-1}function ca(a,b,c){var d=bM.exec(b.slice(c));return d?(a.s=+d[0],c+d[0].length):-1}function cb(a,b){return bP(a.getDate(),b,2)}function cc(a,b){return bP(a.getHours(),b,2)}function cd(a,b){return bP(a.getHours()%12||12,b,2)}function ce(a,b){return bP(1+bg.count(bB(a),a),b,3)}function cf(a,b){return bP(a.getMilliseconds(),b,3)}function cg(a,b){return cf(a,b)+"000"}function ch(a,b){return bP(a.getMonth()+1,b,2)}function ci(a,b){return bP(a.getMinutes(),b,2)}function cj(a,b){return bP(a.getSeconds(),b,2)}function ck(a){var b=a.getDay();return 0===b?7:b}function cl(a,b){return bP(bk.count(bB(a)-1,a),b,2)}function cm(a){var b=a.getDay();return b>=4||0===b?bo(a):bo.ceil(a)}function cn(a,b){return a=cm(a),bP(bo.count(bB(a),a)+(4===bB(a).getDay()),b,2)}function co(a){return a.getDay()}function cp(a,b){return bP(bl.count(bB(a)-1,a),b,2)}function cq(a,b){return bP(a.getFullYear()%100,b,2)}function cr(a,b){return bP((a=cm(a)).getFullYear()%100,b,2)}function cs(a,b){return bP(a.getFullYear()%1e4,b,4)}function ct(a,b){var c=a.getDay();return bP((a=c>=4||0===c?bo(a):bo.ceil(a)).getFullYear()%1e4,b,4)}function cu(a){var b=a.getTimezoneOffset();return(b>0?"-":(b*=-1,"+"))+bP(b/60|0,"0",2)+bP(b%60,"0",2)}function cv(a,b){return bP(a.getUTCDate(),b,2)}function cw(a,b){return bP(a.getUTCHours(),b,2)}function cx(a,b){return bP(a.getUTCHours()%12||12,b,2)}function cy(a,b){return bP(1+bh.count(bC(a),a),b,3)}function cz(a,b){return bP(a.getUTCMilliseconds(),b,3)}function cA(a,b){return cz(a,b)+"000"}function cB(a,b){return bP(a.getUTCMonth()+1,b,2)}function cC(a,b){return bP(a.getUTCMinutes(),b,2)}function cD(a,b){return bP(a.getUTCSeconds(),b,2)}function cE(a){var b=a.getUTCDay();return 0===b?7:b}function cF(a,b){return bP(bs.count(bC(a)-1,a),b,2)}function cG(a){var b=a.getUTCDay();return b>=4||0===b?bw(a):bw.ceil(a)}function cH(a,b){return a=cG(a),bP(bw.count(bC(a),a)+(4===bC(a).getUTCDay()),b,2)}function cI(a){return a.getUTCDay()}function cJ(a,b){return bP(bt.count(bC(a)-1,a),b,2)}function cK(a,b){return bP(a.getUTCFullYear()%100,b,2)}function cL(a,b){return bP((a=cG(a)).getUTCFullYear()%100,b,2)}function cM(a,b){return bP(a.getUTCFullYear()%1e4,b,4)}function cN(a,b){var c=a.getUTCDay();return bP((a=c>=4||0===c?bw(a):bw.ceil(a)).getUTCFullYear()%1e4,b,4)}function cO(){return"+0000"}function cP(){return"%"}function cQ(a){return+a}function cR(a){return Math.floor(a/1e3)}function cS(a){return new Date(a)}function cT(a){return a instanceof Date?+a:+new Date(+a)}function cU(a,b,c,d,e,f,g,h,i,j){var k=aA(),l=k.invert,m=k.domain,n=j(".%L"),o=j(":%S"),p=j("%I:%M"),q=j("%I %p"),r=j("%a %d"),s=j("%b %d"),t=j("%B"),u=j("%Y");function v(a){return(i(a)<a?n:h(a)<a?o:g(a)<a?p:f(a)<a?q:d(a)<a?e(a)<a?r:s:c(a)<a?t:u)(a)}return k.invert=function(a){return new Date(l(a))},k.domain=function(a){return arguments.length?m(Array.from(a,cT)):m().map(cS)},k.ticks=function(b){var c=m();return a(c[0],c[c.length-1],null==b?10:b)},k.tickFormat=function(a,b){return null==b?v:j(b)},k.nice=function(a){var c=m();return a&&"function"==typeof a.range||(a=b(c[0],c[c.length-1],null==a?10:a)),a?m(aN(c,a)):k},k.copy=function(){return ay(k,cU(a,b,c,d,e,f,g,h,i,j))},k}function cV(){return h.apply(cU(bG,bH,bB,bz,bk,bg,be,bc,bb,de).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cW(){return h.apply(cU(bE,bF,bC,bA,bs,bh,bf,bd,bb,df).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cX(){var a,b,c,d,e,f=0,g=1,h=au,i=!1;function j(b){return null==b||isNaN(b*=1)?e:h(0===c?.5:(b=(d(b)-a)*c,i?Math.max(0,Math.min(1,b)):b))}function k(a){return function(b){var c,d;return arguments.length?([c,d]=b,h=a(c,d),j):[h(0),h(1)]}}return j.domain=function(e){return arguments.length?([f,g]=e,a=d(f*=1),b=d(g*=1),c=a===b?0:1/(b-a),j):[f,g]},j.clamp=function(a){return arguments.length?(i=!!a,j):i},j.interpolator=function(a){return arguments.length?(h=a,j):h},j.range=k(aq),j.rangeRound=k(ar),j.unknown=function(a){return arguments.length?(e=a,j):e},function(e){return d=e,a=e(f),b=e(g),c=a===b?0:1/(b-a),j}}function cY(a,b){return b.domain(a.domain()).interpolator(a.interpolator()).clamp(a.clamp()).unknown(a.unknown())}function cZ(){var a=a_(cX());return a.copy=function(){return cY(a,cZ()).exponent(a.exponent())},i.apply(a,arguments)}function c$(){return cZ.apply(null,arguments).exponent(.5)}function c_(){var a,b,c,d,e,f,g,h=0,i=.5,j=1,k=1,l=au,m=!1;function n(a){return isNaN(a*=1)?g:(a=.5+((a=+f(a))-b)*(k*a<k*b?d:e),l(m?Math.max(0,Math.min(1,a)):a))}function o(a){return function(b){var c,d,e;return arguments.length?([c,d,e]=b,l=function(a,b){void 0===b&&(b=a,a=aq);for(var c=0,d=b.length-1,e=b[0],f=Array(d<0?0:d);c<d;)f[c]=a(e,e=b[++c]);return function(a){var b=Math.max(0,Math.min(d-1,Math.floor(a*=d)));return f[b](a-b)}}(a,[c,d,e]),n):[l(0),l(.5),l(1)]}}return n.domain=function(g){return arguments.length?([h,i,j]=g,a=f(h*=1),b=f(i*=1),c=f(j*=1),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n):[h,i,j]},n.clamp=function(a){return arguments.length?(m=!!a,n):m},n.interpolator=function(a){return arguments.length?(l=a,n):l},n.range=o(aq),n.rangeRound=o(ar),n.unknown=function(a){return arguments.length?(g=a,n):g},function(g){return f=g,a=g(h),b=g(i),c=g(j),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n}}function c0(){var a=a_(c_());return a.copy=function(){return cY(a,c0()).exponent(a.exponent())},i.apply(a,arguments)}function c1(){return c0.apply(null,arguments).exponent(.5)}de=(dd=function(a){var b=a.dateTime,c=a.date,d=a.time,e=a.periods,f=a.days,g=a.shortDays,h=a.months,i=a.shortMonths,j=bR(e),k=bS(e),l=bR(f),m=bS(f),n=bR(g),o=bS(g),p=bR(h),q=bS(h),r=bR(i),s=bS(i),t={a:function(a){return g[a.getDay()]},A:function(a){return f[a.getDay()]},b:function(a){return i[a.getMonth()]},B:function(a){return h[a.getMonth()]},c:null,d:cb,e:cb,f:cg,g:cr,G:ct,H:cc,I:cd,j:ce,L:cf,m:ch,M:ci,p:function(a){return e[+(a.getHours()>=12)]},q:function(a){return 1+~~(a.getMonth()/3)},Q:cQ,s:cR,S:cj,u:ck,U:cl,V:cn,w:co,W:cp,x:null,X:null,y:cq,Y:cs,Z:cu,"%":cP},u={a:function(a){return g[a.getUTCDay()]},A:function(a){return f[a.getUTCDay()]},b:function(a){return i[a.getUTCMonth()]},B:function(a){return h[a.getUTCMonth()]},c:null,d:cv,e:cv,f:cA,g:cL,G:cN,H:cw,I:cx,j:cy,L:cz,m:cB,M:cC,p:function(a){return e[+(a.getUTCHours()>=12)]},q:function(a){return 1+~~(a.getUTCMonth()/3)},Q:cQ,s:cR,S:cD,u:cE,U:cF,V:cH,w:cI,W:cJ,x:null,X:null,y:cK,Y:cM,Z:cO,"%":cP},v={a:function(a,b,c){var d=n.exec(b.slice(c));return d?(a.w=o.get(d[0].toLowerCase()),c+d[0].length):-1},A:function(a,b,c){var d=l.exec(b.slice(c));return d?(a.w=m.get(d[0].toLowerCase()),c+d[0].length):-1},b:function(a,b,c){var d=r.exec(b.slice(c));return d?(a.m=s.get(d[0].toLowerCase()),c+d[0].length):-1},B:function(a,b,c){var d=p.exec(b.slice(c));return d?(a.m=q.get(d[0].toLowerCase()),c+d[0].length):-1},c:function(a,c,d){return y(a,b,c,d)},d:b1,e:b1,f:b7,g:bZ,G:bY,H:b3,I:b3,j:b2,L:b6,m:b0,M:b4,p:function(a,b,c){var d=j.exec(b.slice(c));return d?(a.p=k.get(d[0].toLowerCase()),c+d[0].length):-1},q:b_,Q:b9,s:ca,S:b5,u:bU,U:bV,V:bW,w:bT,W:bX,x:function(a,b,d){return y(a,c,b,d)},X:function(a,b,c){return y(a,d,b,c)},y:bZ,Y:bY,Z:b$,"%":b8};function w(a,b){return function(c){var d,e,f,g=[],h=-1,i=0,j=a.length;for(c instanceof Date||(c=new Date(+c));++h<j;)37===a.charCodeAt(h)&&(g.push(a.slice(i,h)),null!=(e=bL[d=a.charAt(++h)])?d=a.charAt(++h):e="e"===d?" ":"0",(f=b[d])&&(d=f(c,e)),g.push(d),i=h+1);return g.push(a.slice(i,h)),g.join("")}}function x(a,b){return function(c){var d,e,f=bK(1900,void 0,1);if(y(f,a,c+="",0)!=c.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(1e3*f.s+("L"in f?f.L:0));if(!b||"Z"in f||(f.Z=0),"p"in f&&(f.H=f.H%12+12*f.p),void 0===f.m&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(d=(e=(d=bJ(bK(f.y,0,1))).getUTCDay())>4||0===e?bt.ceil(d):bt(d),d=bh.offset(d,(f.V-1)*7),f.y=d.getUTCFullYear(),f.m=d.getUTCMonth(),f.d=d.getUTCDate()+(f.w+6)%7):(d=(e=(d=bI(bK(f.y,0,1))).getDay())>4||0===e?bl.ceil(d):bl(d),d=bg.offset(d,(f.V-1)*7),f.y=d.getFullYear(),f.m=d.getMonth(),f.d=d.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:+("W"in f)),e="Z"in f?bJ(bK(f.y,0,1)).getUTCDay():bI(bK(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+7*f.W-(e+5)%7:f.w+7*f.U-(e+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,bJ(f)):bI(f)}}function y(a,b,c,d){for(var e,f,g=0,h=b.length,i=c.length;g<h;){if(d>=i)return -1;if(37===(e=b.charCodeAt(g++))){if(!(f=v[(e=b.charAt(g++))in bL?b.charAt(g++):e])||(d=f(a,c,d))<0)return -1}else if(e!=c.charCodeAt(d++))return -1}return d}return t.x=w(c,t),t.X=w(d,t),t.c=w(b,t),u.x=w(c,u),u.X=w(d,u),u.c=w(b,u),{format:function(a){var b=w(a+="",t);return b.toString=function(){return a},b},parse:function(a){var b=x(a+="",!1);return b.toString=function(){return a},b},utcFormat:function(a){var b=w(a+="",u);return b.toString=function(){return a},b},utcParse:function(a){var b=x(a+="",!0);return b.toString=function(){return a},b}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,dd.parse,df=dd.utcFormat,dd.utcParse;var c2=c(51426),c3=c(64279),c4=c(57282),c5=c(22989),c6=c(12128);function c7(a){if(Array.isArray(a)&&2===a.length){var[b,c]=a;if((0,c6.H)(b)&&(0,c6.H)(c))return!0}return!1}function c8(a,b,c){return c?a:[Math.min(a[0],b[0]),Math.max(a[1],b[1])]}var c9,da,db,dc,dd,de,df,dg,dh,di=!0,dj="[DecimalError] ",dk=dj+"Invalid argument: ",dl=dj+"Exponent out of range: ",dm=Math.floor,dn=Math.pow,dp=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,dq=dm(1286742750677284.5),dr={};function ds(a,b){var c,d,e,f,g,h,i,j,k=a.constructor,l=k.precision;if(!a.s||!b.s)return b.s||(b=new k(a)),di?dC(b,l):b;if(i=a.d,j=b.d,g=a.e,e=b.e,i=i.slice(),f=g-e){for(f<0?(d=i,f=-f,h=j.length):(d=j,e=g,h=i.length),f>(h=(g=Math.ceil(l/7))>h?g+1:h+1)&&(f=h,d.length=1),d.reverse();f--;)d.push(0);d.reverse()}for((h=i.length)-(f=j.length)<0&&(f=h,d=j,j=i,i=d),c=0;f;)c=(i[--f]=i[f]+j[f]+c)/1e7|0,i[f]%=1e7;for(c&&(i.unshift(c),++e),h=i.length;0==i[--h];)i.pop();return b.d=i,b.e=e,di?dC(b,l):b}function dt(a,b,c){if(a!==~~a||a<b||a>c)throw Error(dk+a)}function du(a){var b,c,d,e=a.length-1,f="",g=a[0];if(e>0){for(f+=g,b=1;b<e;b++)(c=7-(d=a[b]+"").length)&&(f+=dz(c)),f+=d;(c=7-(d=(g=a[b])+"").length)&&(f+=dz(c))}else if(0===g)return"0";for(;g%10==0;)g/=10;return f+g}dr.absoluteValue=dr.abs=function(){var a=new this.constructor(this);return a.s&&(a.s=1),a},dr.comparedTo=dr.cmp=function(a){var b,c,d,e;if(a=new this.constructor(a),this.s!==a.s)return this.s||-a.s;if(this.e!==a.e)return this.e>a.e^this.s<0?1:-1;for(b=0,c=(d=this.d.length)<(e=a.d.length)?d:e;b<c;++b)if(this.d[b]!==a.d[b])return this.d[b]>a.d[b]^this.s<0?1:-1;return d===e?0:d>e^this.s<0?1:-1},dr.decimalPlaces=dr.dp=function(){var a=this.d.length-1,b=(a-this.e)*7;if(a=this.d[a])for(;a%10==0;a/=10)b--;return b<0?0:b},dr.dividedBy=dr.div=function(a){return dv(this,new this.constructor(a))},dr.dividedToIntegerBy=dr.idiv=function(a){var b=this.constructor;return dC(dv(this,new b(a),0,1),b.precision)},dr.equals=dr.eq=function(a){return!this.cmp(a)},dr.exponent=function(){return dx(this)},dr.greaterThan=dr.gt=function(a){return this.cmp(a)>0},dr.greaterThanOrEqualTo=dr.gte=function(a){return this.cmp(a)>=0},dr.isInteger=dr.isint=function(){return this.e>this.d.length-2},dr.isNegative=dr.isneg=function(){return this.s<0},dr.isPositive=dr.ispos=function(){return this.s>0},dr.isZero=function(){return 0===this.s},dr.lessThan=dr.lt=function(a){return 0>this.cmp(a)},dr.lessThanOrEqualTo=dr.lte=function(a){return 1>this.cmp(a)},dr.logarithm=dr.log=function(a){var b,c=this.constructor,d=c.precision,e=d+5;if(void 0===a)a=new c(10);else if((a=new c(a)).s<1||a.eq(dh))throw Error(dj+"NaN");if(this.s<1)throw Error(dj+(this.s?"NaN":"-Infinity"));return this.eq(dh)?new c(0):(di=!1,b=dv(dA(this,e),dA(a,e),e),di=!0,dC(b,d))},dr.minus=dr.sub=function(a){return a=new this.constructor(a),this.s==a.s?dD(this,a):ds(this,(a.s=-a.s,a))},dr.modulo=dr.mod=function(a){var b,c=this.constructor,d=c.precision;if(!(a=new c(a)).s)throw Error(dj+"NaN");return this.s?(di=!1,b=dv(this,a,0,1).times(a),di=!0,this.minus(b)):dC(new c(this),d)},dr.naturalExponential=dr.exp=function(){return dw(this)},dr.naturalLogarithm=dr.ln=function(){return dA(this)},dr.negated=dr.neg=function(){var a=new this.constructor(this);return a.s=-a.s||0,a},dr.plus=dr.add=function(a){return a=new this.constructor(a),this.s==a.s?ds(this,a):dD(this,(a.s=-a.s,a))},dr.precision=dr.sd=function(a){var b,c,d;if(void 0!==a&&!!a!==a&&1!==a&&0!==a)throw Error(dk+a);if(b=dx(this)+1,c=7*(d=this.d.length-1)+1,d=this.d[d]){for(;d%10==0;d/=10)c--;for(d=this.d[0];d>=10;d/=10)c++}return a&&b>c?b:c},dr.squareRoot=dr.sqrt=function(){var a,b,c,d,e,f,g,h=this.constructor;if(this.s<1){if(!this.s)return new h(0);throw Error(dj+"NaN")}for(a=dx(this),di=!1,0==(e=Math.sqrt(+this))||e==1/0?(((b=du(this.d)).length+a)%2==0&&(b+="0"),e=Math.sqrt(b),a=dm((a+1)/2)-(a<0||a%2),d=new h(b=e==1/0?"5e"+a:(b=e.toExponential()).slice(0,b.indexOf("e")+1)+a)):d=new h(e.toString()),e=g=(c=h.precision)+3;;)if(d=(f=d).plus(dv(this,f,g+2)).times(.5),du(f.d).slice(0,g)===(b=du(d.d)).slice(0,g)){if(b=b.slice(g-3,g+1),e==g&&"4999"==b){if(dC(f,c+1,0),f.times(f).eq(this)){d=f;break}}else if("9999"!=b)break;g+=4}return di=!0,dC(d,c)},dr.times=dr.mul=function(a){var b,c,d,e,f,g,h,i,j,k=this.constructor,l=this.d,m=(a=new k(a)).d;if(!this.s||!a.s)return new k(0);for(a.s*=this.s,c=this.e+a.e,(i=l.length)<(j=m.length)&&(f=l,l=m,m=f,g=i,i=j,j=g),f=[],d=g=i+j;d--;)f.push(0);for(d=j;--d>=0;){for(b=0,e=i+d;e>d;)h=f[e]+m[d]*l[e-d-1]+b,f[e--]=h%1e7|0,b=h/1e7|0;f[e]=(f[e]+b)%1e7|0}for(;!f[--g];)f.pop();return b?++c:f.shift(),a.d=f,a.e=c,di?dC(a,k.precision):a},dr.toDecimalPlaces=dr.todp=function(a,b){var c=this,d=c.constructor;return(c=new d(c),void 0===a)?c:(dt(a,0,1e9),void 0===b?b=d.rounding:dt(b,0,8),dC(c,a+dx(c)+1,b))},dr.toExponential=function(a,b){var c,d=this,e=d.constructor;return void 0===a?c=dE(d,!0):(dt(a,0,1e9),void 0===b?b=e.rounding:dt(b,0,8),c=dE(d=dC(new e(d),a+1,b),!0,a+1)),c},dr.toFixed=function(a,b){var c,d,e=this.constructor;return void 0===a?dE(this):(dt(a,0,1e9),void 0===b?b=e.rounding:dt(b,0,8),c=dE((d=dC(new e(this),a+dx(this)+1,b)).abs(),!1,a+dx(d)+1),this.isneg()&&!this.isZero()?"-"+c:c)},dr.toInteger=dr.toint=function(){var a=this.constructor;return dC(new a(this),dx(this)+1,a.rounding)},dr.toNumber=function(){return+this},dr.toPower=dr.pow=function(a){var b,c,d,e,f,g,h=this,i=h.constructor,j=+(a=new i(a));if(!a.s)return new i(dh);if(!(h=new i(h)).s){if(a.s<1)throw Error(dj+"Infinity");return h}if(h.eq(dh))return h;if(d=i.precision,a.eq(dh))return dC(h,d);if(g=(b=a.e)>=(c=a.d.length-1),f=h.s,g){if((c=j<0?-j:j)<=0x1fffffffffffff){for(e=new i(dh),b=Math.ceil(d/7+4),di=!1;c%2&&dF((e=e.times(h)).d,b),0!==(c=dm(c/2));)dF((h=h.times(h)).d,b);return di=!0,a.s<0?new i(dh).div(e):dC(e,d)}}else if(f<0)throw Error(dj+"NaN");return f=f<0&&1&a.d[Math.max(b,c)]?-1:1,h.s=1,di=!1,e=a.times(dA(h,d+12)),di=!0,(e=dw(e)).s=f,e},dr.toPrecision=function(a,b){var c,d,e=this,f=e.constructor;return void 0===a?(c=dx(e),d=dE(e,c<=f.toExpNeg||c>=f.toExpPos)):(dt(a,1,1e9),void 0===b?b=f.rounding:dt(b,0,8),c=dx(e=dC(new f(e),a,b)),d=dE(e,a<=c||c<=f.toExpNeg,a)),d},dr.toSignificantDigits=dr.tosd=function(a,b){var c=this.constructor;return void 0===a?(a=c.precision,b=c.rounding):(dt(a,1,1e9),void 0===b?b=c.rounding:dt(b,0,8)),dC(new c(this),a,b)},dr.toString=dr.valueOf=dr.val=dr.toJSON=dr[Symbol.for("nodejs.util.inspect.custom")]=function(){var a=dx(this),b=this.constructor;return dE(this,a<=b.toExpNeg||a>=b.toExpPos)};var dv=function(){function a(a,b){var c,d=0,e=a.length;for(a=a.slice();e--;)c=a[e]*b+d,a[e]=c%1e7|0,d=c/1e7|0;return d&&a.unshift(d),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c){for(var d=0;c--;)a[c]-=d,d=+(a[c]<b[c]),a[c]=1e7*d+a[c]-b[c];for(;!a[0]&&a.length>1;)a.shift()}return function(d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=d.constructor,A=d.s==e.s?1:-1,B=d.d,C=e.d;if(!d.s)return new z(d);if(!e.s)throw Error(dj+"Division by zero");for(j=0,i=d.e-e.e,x=C.length,v=B.length,o=(n=new z(A)).d=[];C[j]==(B[j]||0);)++j;if(C[j]>(B[j]||0)&&--i,(s=null==f?f=z.precision:g?f+(dx(d)-dx(e))+1:f)<0)return new z(0);if(s=s/7+2|0,j=0,1==x)for(k=0,C=C[0],s++;(j<v||k)&&s--;j++)t=1e7*k+(B[j]||0),o[j]=t/C|0,k=t%C|0;else{for((k=1e7/(C[0]+1)|0)>1&&(C=a(C,k),B=a(B,k),x=C.length,v=B.length),u=x,q=(p=B.slice(0,x)).length;q<x;)p[q++]=0;(y=C.slice()).unshift(0),w=C[0],C[1]>=1e7/2&&++w;do k=0,(h=b(C,p,x,q))<0?(r=p[0],x!=q&&(r=1e7*r+(p[1]||0)),(k=r/w|0)>1?(k>=1e7&&(k=1e7-1),m=(l=a(C,k)).length,q=p.length,1==(h=b(l,p,m,q))&&(k--,c(l,x<m?y:C,m))):(0==k&&(h=k=1),l=C.slice()),(m=l.length)<q&&l.unshift(0),c(p,l,q),-1==h&&(q=p.length,(h=b(C,p,x,q))<1&&(k++,c(p,x<q?y:C,q))),q=p.length):0===h&&(k++,p=[0]),o[j++]=k,h&&p[0]?p[q++]=B[u]||0:(p=[B[u]],q=1);while((u++<v||void 0!==p[0])&&s--)}return o[0]||o.shift(),n.e=i,dC(n,g?f+dx(n)+1:f)}}();function dw(a,b){var c,d,e,f,g,h=0,i=0,j=a.constructor,k=j.precision;if(dx(a)>16)throw Error(dl+dx(a));if(!a.s)return new j(dh);for(null==b?(di=!1,g=k):g=b,f=new j(.03125);a.abs().gte(.1);)a=a.times(f),i+=5;for(g+=Math.log(dn(2,i))/Math.LN10*2+5|0,c=d=e=new j(dh),j.precision=g;;){if(d=dC(d.times(a),g),c=c.times(++h),du((f=e.plus(dv(d,c,g))).d).slice(0,g)===du(e.d).slice(0,g)){for(;i--;)e=dC(e.times(e),g);return j.precision=k,null==b?(di=!0,dC(e,k)):e}e=f}}function dx(a){for(var b=7*a.e,c=a.d[0];c>=10;c/=10)b++;return b}function dy(a,b,c){if(b>a.LN10.sd())throw di=!0,c&&(a.precision=c),Error(dj+"LN10 precision limit exceeded");return dC(new a(a.LN10),b)}function dz(a){for(var b="";a--;)b+="0";return b}function dA(a,b){var c,d,e,f,g,h,i,j,k,l=1,m=a,n=m.d,o=m.constructor,p=o.precision;if(m.s<1)throw Error(dj+(m.s?"NaN":"-Infinity"));if(m.eq(dh))return new o(0);if(null==b?(di=!1,j=p):j=b,m.eq(10))return null==b&&(di=!0),dy(o,j);if(o.precision=j+=10,d=(c=du(n)).charAt(0),!(15e14>Math.abs(f=dx(m))))return i=dy(o,j+2,p).times(f+""),m=dA(new o(d+"."+c.slice(1)),j-10).plus(i),o.precision=p,null==b?(di=!0,dC(m,p)):m;for(;d<7&&1!=d||1==d&&c.charAt(1)>3;)d=(c=du((m=m.times(a)).d)).charAt(0),l++;for(f=dx(m),d>1?(m=new o("0."+c),f++):m=new o(d+"."+c.slice(1)),h=g=m=dv(m.minus(dh),m.plus(dh),j),k=dC(m.times(m),j),e=3;;){if(g=dC(g.times(k),j),du((i=h.plus(dv(g,new o(e),j))).d).slice(0,j)===du(h.d).slice(0,j))return h=h.times(2),0!==f&&(h=h.plus(dy(o,j+2,p).times(f+""))),h=dv(h,new o(l),j),o.precision=p,null==b?(di=!0,dC(h,p)):h;h=i,e+=2}}function dB(a,b){var c,d,e;for((c=b.indexOf("."))>-1&&(b=b.replace(".","")),(d=b.search(/e/i))>0?(c<0&&(c=d),c+=+b.slice(d+1),b=b.substring(0,d)):c<0&&(c=b.length),d=0;48===b.charCodeAt(d);)++d;for(e=b.length;48===b.charCodeAt(e-1);)--e;if(b=b.slice(d,e)){if(e-=d,a.e=dm((c=c-d-1)/7),a.d=[],d=(c+1)%7,c<0&&(d+=7),d<e){for(d&&a.d.push(+b.slice(0,d)),e-=7;d<e;)a.d.push(+b.slice(d,d+=7));d=7-(b=b.slice(d)).length}else d-=e;for(;d--;)b+="0";if(a.d.push(+b),di&&(a.e>dq||a.e<-dq))throw Error(dl+c)}else a.s=0,a.e=0,a.d=[0];return a}function dC(a,b,c){var d,e,f,g,h,i,j,k,l=a.d;for(g=1,f=l[0];f>=10;f/=10)g++;if((d=b-g)<0)d+=7,e=b,j=l[k=0];else{if((k=Math.ceil((d+1)/7))>=(f=l.length))return a;for(g=1,j=f=l[k];f>=10;f/=10)g++;d%=7,e=d-7+g}if(void 0!==c&&(h=j/(f=dn(10,g-e-1))%10|0,i=b<0||void 0!==l[k+1]||j%f,i=c<4?(h||i)&&(0==c||c==(a.s<0?3:2)):h>5||5==h&&(4==c||i||6==c&&(d>0?e>0?j/dn(10,g-e):0:l[k-1])%10&1||c==(a.s<0?8:7))),b<1||!l[0])return i?(f=dx(a),l.length=1,b=b-f-1,l[0]=dn(10,(7-b%7)%7),a.e=dm(-b/7)||0):(l.length=1,l[0]=a.e=a.s=0),a;if(0==d?(l.length=k,f=1,k--):(l.length=k+1,f=dn(10,7-d),l[k]=e>0?(j/dn(10,g-e)%dn(10,e)|0)*f:0),i)for(;;)if(0==k){1e7==(l[0]+=f)&&(l[0]=1,++a.e);break}else{if(l[k]+=f,1e7!=l[k])break;l[k--]=0,f=1}for(d=l.length;0===l[--d];)l.pop();if(di&&(a.e>dq||a.e<-dq))throw Error(dl+dx(a));return a}function dD(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.constructor,n=m.precision;if(!a.s||!b.s)return b.s?b.s=-b.s:b=new m(a),di?dC(b,n):b;if(i=a.d,l=b.d,d=b.e,j=a.e,i=i.slice(),g=j-d){for((k=g<0)?(c=i,g=-g,h=l.length):(c=l,d=j,h=i.length),g>(e=Math.max(Math.ceil(n/7),h)+2)&&(g=e,c.length=1),c.reverse(),e=g;e--;)c.push(0);c.reverse()}else{for((k=(e=i.length)<(h=l.length))&&(h=e),e=0;e<h;e++)if(i[e]!=l[e]){k=i[e]<l[e];break}g=0}for(k&&(c=i,i=l,l=c,b.s=-b.s),h=i.length,e=l.length-h;e>0;--e)i[h++]=0;for(e=l.length;e>g;){if(i[--e]<l[e]){for(f=e;f&&0===i[--f];)i[f]=1e7-1;--i[f],i[e]+=1e7}i[e]-=l[e]}for(;0===i[--h];)i.pop();for(;0===i[0];i.shift())--d;return i[0]?(b.d=i,b.e=d,di?dC(b,n):b):new m(0)}function dE(a,b,c){var d,e=dx(a),f=du(a.d),g=f.length;return b?(c&&(d=c-g)>0?f=f.charAt(0)+"."+f.slice(1)+dz(d):g>1&&(f=f.charAt(0)+"."+f.slice(1)),f=f+(e<0?"e":"e+")+e):e<0?(f="0."+dz(-e-1)+f,c&&(d=c-g)>0&&(f+=dz(d))):e>=g?(f+=dz(e+1-g),c&&(d=c-e-1)>0&&(f=f+"."+dz(d))):((d=e+1)<g&&(f=f.slice(0,d)+"."+f.slice(d)),c&&(d=c-g)>0&&(e+1===g&&(f+="."),f+=dz(d))),a.s<0?"-"+f:f}function dF(a,b){if(a.length>b)return a.length=b,!0}function dG(a){if(!a||"object"!=typeof a)throw Error(dj+"Object expected");var b,c,d,e=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(b=0;b<e.length;b+=3)if(void 0!==(d=a[c=e[b]]))if(dm(d)===d&&d>=e[b+1]&&d<=e[b+2])this[c]=d;else throw Error(dk+c+": "+d);if(void 0!==(d=a[c="LN10"]))if(d==Math.LN10)this[c]=new this(d);else throw Error(dk+c+": "+d);return this}var dg=function a(b){var c,d,e;function f(a){if(!(this instanceof f))return new f(a);if(this.constructor=f,a instanceof f){this.s=a.s,this.e=a.e,this.d=(a=a.d)?a.slice():a;return}if("number"==typeof a){if(0*a!=0)throw Error(dk+a);if(a>0)this.s=1;else if(a<0)a=-a,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(a===~~a&&a<1e7){this.e=0,this.d=[a];return}return dB(this,a.toString())}if("string"!=typeof a)throw Error(dk+a);if(45===a.charCodeAt(0)?(a=a.slice(1),this.s=-1):this.s=1,dp.test(a))dB(this,a);else throw Error(dk+a)}if(f.prototype=dr,f.ROUND_UP=0,f.ROUND_DOWN=1,f.ROUND_CEIL=2,f.ROUND_FLOOR=3,f.ROUND_HALF_UP=4,f.ROUND_HALF_DOWN=5,f.ROUND_HALF_EVEN=6,f.ROUND_HALF_CEIL=7,f.ROUND_HALF_FLOOR=8,f.clone=a,f.config=f.set=dG,void 0===b&&(b={}),b)for(c=0,e=["precision","rounding","toExpNeg","toExpPos","LN10"];c<e.length;)b.hasOwnProperty(d=e[c++])||(b[d]=this[d]);return f.config(b),f}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});dh=new dg(1);let dH=dg;var dI=a=>a,dJ={},dK=a=>function b(){let c;return 0==arguments.length||1==arguments.length&&(c=arguments.length<=0?void 0:arguments[0],c===dJ)?b:a(...arguments)},dL=(a,b)=>1===a?b:dK(function(){for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];var f=d.filter(a=>a!==dJ).length;return f>=a?b(...d):dL(a-f,dK(function(){for(var a=arguments.length,c=Array(a),e=0;e<a;e++)c[e]=arguments[e];return b(...d.map(a=>a===dJ?c.shift():a),...c)}))}),dM=a=>dL(a.length,a),dN=(a,b)=>{for(var c=[],d=a;d<b;++d)c[d-a]=d;return c},dO=dM((a,b)=>Array.isArray(b)?b.map(a):Object.keys(b).map(a=>b[a]).map(a)),dP=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];if(!b.length)return dI;var d=b.reverse(),e=d[0],f=d.slice(1);return function(){return f.reduce((a,b)=>b(a),e(...arguments))}},dQ=a=>Array.isArray(a)?a.reverse():a.split("").reverse().join(""),dR=a=>{var b=null,c=null;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return b&&e.every((a,c)=>{var d;return a===(null==(d=b)?void 0:d[c])})?c:(b=e,c=a(...e))}};function dS(a){return 0===a?1:Math.floor(new dH(a).abs().log(10).toNumber())+1}function dT(a,b,c){for(var d=new dH(a),e=0,f=[];d.lt(b)&&e<1e5;)f.push(d.toNumber()),d=d.add(c),e++;return f}dM((a,b,c)=>{var d=+a;return d+c*(b-d)}),dM((a,b,c)=>{var d=b-a;return(c-a)/(d=d||1/0)}),dM((a,b,c)=>{var d=b-a;return Math.max(0,Math.min(1,(c-a)/(d=d||1/0)))});var dU=a=>{var[b,c]=a,[d,e]=[b,c];return b>c&&([d,e]=[c,b]),[d,e]},dV=(a,b,c)=>{if(a.lte(0))return new dH(0);var d=dS(a.toNumber()),e=new dH(10).pow(d),f=a.div(e),g=1!==d?.05:.1,h=new dH(Math.ceil(f.div(g).toNumber())).add(c).mul(g).mul(e);return new dH(b?h.toNumber():Math.ceil(h.toNumber()))},dW=function(a,b,c,d){var e,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((b-a)/(c-1)))return{step:new dH(0),tickMin:new dH(0),tickMax:new dH(0)};var g=dV(new dH(b).sub(a).div(c-1),d,f),h=Math.ceil((e=a<=0&&b>=0?new dH(0):(e=new dH(a).add(b).div(2)).sub(new dH(e).mod(g))).sub(a).div(g).toNumber()),i=Math.ceil(new dH(b).sub(e).div(g).toNumber()),j=h+i+1;return j>c?dW(a,b,c,d,f+1):(j<c&&(i=b>0?i+(c-j):i,h=b>0?h:h+(c-j)),{step:g,tickMin:e.sub(new dH(h).mul(g)),tickMax:e.add(new dH(i).mul(g))})},dX=dR(function(a){var[b,c]=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],f=Math.max(d,2),[g,h]=dU([b,c]);if(g===-1/0||h===1/0){var i=h===1/0?[g,...dN(0,d-1).map(()=>1/0)]:[...dN(0,d-1).map(()=>-1/0),h];return b>c?dQ(i):i}if(g===h){var j=new dH(1),k=new dH(g);if(!k.isint()&&e){var l=Math.abs(g);l<1?(j=new dH(10).pow(dS(g)-1),k=new dH(Math.floor(k.div(j).toNumber())).mul(j)):l>1&&(k=new dH(Math.floor(g)))}else 0===g?k=new dH(Math.floor((d-1)/2)):e||(k=new dH(Math.floor(g)));var m=Math.floor((d-1)/2);return dP(dO(a=>k.add(new dH(a-m).mul(j)).toNumber()),dN)(0,d)}var{step:n,tickMin:o,tickMax:p}=dW(g,h,f,e,0),q=dT(o,p.add(new dH(.1).mul(n)),n);return b>c?dQ(q):q}),dY=dR(function(a,b){var[c,d]=a,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[f,g]=dU([c,d]);if(f===-1/0||g===1/0)return[c,d];if(f===g)return[f];var h=Math.max(b,2),i=dV(new dH(g).sub(f).div(h-1),e,0),j=[...dT(new dH(f),new dH(g),i),g];return!1===e&&(j=j.map(a=>Math.round(a))),c>d?dQ(j):j}),dZ=c(86445),d$=c(23814),d_=c(76966),d0=c(94728),d1=c(97350),d2=c(8920),d3=c(36166),d4=c(60559),d5=c(53416),d6=c(75601);function d7(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function d8(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?d7(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):d7(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var d9=[0,"auto"],ea={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},eb=(a,b)=>{var c=a.cartesianAxis.xAxis[b];return null==c?ea:c},ec={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:d9,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:d6.tQ},ed=(a,b)=>{var c=a.cartesianAxis.yAxis[b];return null==c?ec:c},ee={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ef=(a,b)=>{var c=a.cartesianAxis.zAxis[b];return null==c?ee:c},eg=(a,b,c)=>{switch(b){case"xAxis":return eb(a,c);case"yAxis":return ed(a,c);case"zAxis":return ef(a,c);case"angleAxis":return(0,d2.Be)(a,c);case"radiusAxis":return(0,d2.Gl)(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},eh=(a,b,c)=>{switch(b){case"xAxis":return eb(a,c);case"yAxis":return ed(a,c);case"angleAxis":return(0,d2.Be)(a,c);case"radiusAxis":return(0,d2.Gl)(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},ei=a=>a.graphicalItems.countOfBars>0;function ej(a,b){return c=>{switch(a){case"xAxis":return"xAxisId"in c&&c.xAxisId===b;case"yAxis":return"yAxisId"in c&&c.yAxisId===b;case"zAxis":return"zAxisId"in c&&c.zAxisId===b;case"angleAxis":return"angleAxisId"in c&&c.angleAxisId===b;case"radiusAxis":return"radiusAxisId"in c&&c.radiusAxisId===b;default:return!1}}}var ek=a=>a.graphicalItems.cartesianItems,el=(0,e.Mz)([d3.N,d4.E],ej),em=(a,b,c)=>a.filter(c).filter(a=>(null==b?void 0:b.includeHidden)===!0||!a.hide),en=(0,e.Mz)([ek,eg,el],em),eo=a=>a.filter(a=>void 0===a.stackId),ep=(0,e.Mz)([en],eo),eq=a=>a.map(a=>a.data).filter(Boolean).flat(1),er=(0,e.Mz)([en],eq),es=(a,b)=>{var{chartData:c=[],dataStartIndex:d,dataEndIndex:e}=b;return a.length>0?a:c.slice(d,e+1)},et=(0,e.Mz)([er,c4.HS],es),eu=(a,b,c)=>(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:(0,c3.kr)(a,b.dataKey)})):c.length>0?c.map(a=>a.dataKey).flatMap(b=>a.map(a=>({value:(0,c3.kr)(a,b)}))):a.map(a=>({value:a})),ev=(0,e.Mz)([et,eg,en],eu);function ew(a,b){switch(a){case"xAxis":return"x"===b.direction;case"yAxis":return"y"===b.direction;default:return!1}}function ex(a){return a.filter(a=>(0,c5.vh)(a)||a instanceof Date).map(Number).filter(a=>!1===(0,c5.M8)(a))}var ey=(a,b,c)=>Object.fromEntries(Object.entries(b.reduce((a,b)=>(null==b.stackId||(null==a[b.stackId]&&(a[b.stackId]=[]),a[b.stackId].push(b)),a),{})).map(b=>{var[d,e]=b,f=e.map(a=>a.dataKey);return[d,{stackedData:(0,c3.yy)(a,f,c),graphicalItems:e}]})),ez=(0,e.Mz)([et,en,d1.eC],ey),eA=(a,b,c)=>{var{dataStartIndex:d,dataEndIndex:e}=b;if("zAxis"!==c){var f=(0,c3.Mk)(a,d,e);if(null==f||0!==f[0]||0!==f[1])return f}},eB=(0,e.Mz)([ez,c4.LF,d3.N],eA),eC=(a,b,c,d)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var e,f,g=null==(e=c.errorBars)?void 0:e.filter(a=>ew(d,a)),h=(0,c3.kr)(a,null!=(f=b.dataKey)?f:c.dataKey);return{value:h,errorDomain:function(a,b,c){return!c||"number"!=typeof b||(0,c5.M8)(b)||!c.length?[]:ex(c.flatMap(c=>{var d,e,f=(0,c3.kr)(a,c.dataKey);if(Array.isArray(f)?[d,e]=f:d=e=f,(0,c6.H)(d)&&(0,c6.H)(e))return[b-d,b+e]}))}(a,h,g)}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:(0,c3.kr)(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]})),eD=(0,e.Mz)(et,eg,ep,d3.N,eC);function eE(a){var{value:b}=a;if((0,c5.vh)(b)||b instanceof Date)return b}var eF=a=>{var b;if(null==a||!("domain"in a))return d9;if(null!=a.domain)return a.domain;if(null!=a.ticks){if("number"===a.type){var c=ex(a.ticks);return[Math.min(...c),Math.max(...c)]}if("category"===a.type)return a.ticks.map(String)}return null!=(b=null==a?void 0:a.domain)?b:d9},eG=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];var d=b.filter(Boolean);if(0!==d.length){var e=d.flat();return[Math.min(...e),Math.max(...e)]}},eH=a=>a.referenceElements.dots,eI=(a,b,c)=>a.filter(a=>"extendDomain"===a.ifOverflow).filter(a=>"xAxis"===b?a.xAxisId===c:a.yAxisId===c),eJ=(0,e.Mz)([eH,d3.N,d4.E],eI),eK=a=>a.referenceElements.areas,eL=(0,e.Mz)([eK,d3.N,d4.E],eI),eM=a=>a.referenceElements.lines,eN=(0,e.Mz)([eM,d3.N,d4.E],eI),eO=(a,b)=>{var c=ex(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},eP=(0,e.Mz)(eJ,d3.N,eO),eQ=(a,b)=>{var c=ex(a.flatMap(a=>["xAxis"===b?a.x1:a.y1,"xAxis"===b?a.x2:a.y2]));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},eR=(0,e.Mz)([eL,d3.N],eQ),eS=(a,b)=>{var c=ex(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},eT=(0,e.Mz)(eN,d3.N,eS),eU=(0,e.Mz)(eP,eT,eR,(a,b,c)=>eG(a,c,b)),eV=(0,e.Mz)([eg],eF),eW=(a,b,c,d,e)=>{var f=function(a,b){if(b&&"function"!=typeof a&&Array.isArray(a)&&2===a.length){var c,d,[e,f]=a;if((0,c6.H)(e))c=e;else if("function"==typeof e)return;if((0,c6.H)(f))d=f;else if("function"==typeof f)return;var g=[c,d];if(c7(g))return g}}(b,a.allowDataOverflow);return null!=f?f:function(a,b,c){if(c||null!=b){if("function"==typeof a&&null!=b)try{var d=a(b,c);if(c7(d))return c8(d,b,c)}catch(a){}if(Array.isArray(a)&&2===a.length){var e,f,[g,h]=a;if("auto"===g)null!=b&&(e=Math.min(...b));else if((0,c5.Et)(g))e=g;else if("function"==typeof g)try{null!=b&&(e=g(null==b?void 0:b[0]))}catch(a){}else if("string"==typeof g&&c3.IH.test(g)){var i=c3.IH.exec(g);if(null==i||null==b)e=void 0;else{var j=+i[1];e=b[0]-j}}else e=null==b?void 0:b[0];if("auto"===h)null!=b&&(f=Math.max(...b));else if((0,c5.Et)(h))f=h;else if("function"==typeof h)try{null!=b&&(f=h(null==b?void 0:b[1]))}catch(a){}else if("string"==typeof h&&c3.qx.test(h)){var k=c3.qx.exec(h);if(null==k||null==b)f=void 0;else{var l=+k[1];f=b[1]+l}}else f=null==b?void 0:b[1];var m=[e,f];if(c7(m))return null==b?m:c8(m,b,c)}}}(b,eG(c,e,(a=>{var b=ex(a.flatMap(a=>[a.value,a.errorDomain]).flat(1));if(0!==b.length)return[Math.min(...b),Math.max(...b)]})(d)),a.allowDataOverflow)},eX=(0,e.Mz)([eg,eV,eB,eD,eU],eW),eY=[0,1],eZ=(a,b,c,d,e,f,h)=>{if(null!=a&&null!=c&&0!==c.length){var{dataKey:i,type:j}=a,k=(0,c3._L)(b,f);return k&&null==i?g()(0,c.length):"category"===j?((a,b,c)=>{var d=a.map(eE).filter(a=>null!=a);return c&&(null==b.dataKey||b.allowDuplicatedCategory&&(0,c5.CG)(d))?g()(0,a.length):b.allowDuplicatedCategory?d:Array.from(new Set(d))})(d,a,k):"expand"===e?eY:h}},e$=(0,e.Mz)([eg,c2.fz,et,ev,d1.eC,d3.N,eX],eZ),e_=(a,b,c,e,f)=>{if(null!=a){var{scale:g,type:h}=a;if("auto"===g)return"radial"===b&&"radiusAxis"===f?"band":"radial"===b&&"angleAxis"===f?"linear":"category"===h&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!c)?"point":"category"===h?"band":"linear";if("string"==typeof g){var i="scale".concat((0,c5.Zb)(g));return i in d?i:"point"}}},e0=(0,e.Mz)([eg,c2.fz,ei,d1.iO,d3.N],e_);function e1(a,b,c,e){if(null!=c&&null!=e){if("function"==typeof a.scale)return a.scale.copy().domain(c).range(e);var f=function(a){if(null!=a){if(a in d)return d[a]();var b="scale".concat((0,c5.Zb)(a));if(b in d)return d[b]()}}(b);if(null!=f){var g=f.domain(c).range(e);return(0,c3.YB)(g),g}}}var e2=(a,b,c)=>{var d=eF(b);if("auto"===c||"linear"===c){if(null!=b&&b.tickCount&&Array.isArray(d)&&("auto"===d[0]||"auto"===d[1])&&c7(a))return dX(a,b.tickCount,b.allowDecimals);if(null!=b&&b.tickCount&&"number"===b.type&&c7(a))return dY(a,b.tickCount,b.allowDecimals)}},e3=(0,e.Mz)([e$,eh,e0],e2),e4=(a,b,c,d)=>"angleAxis"!==d&&(null==a?void 0:a.type)==="number"&&c7(b)&&Array.isArray(c)&&c.length>0?[Math.min(b[0],c[0]),Math.max(b[1],c[c.length-1])]:b,e5=(0,e.Mz)([eg,e$,e3,d3.N],e4),e6=(0,e.Mz)(ev,eg,(a,b)=>{if(b&&"number"===b.type){var c=1/0,d=Array.from(ex(a.map(a=>a.value))).sort((a,b)=>a-b);if(d.length<2)return 1/0;var e=d[d.length-1]-d[0];if(0===e)return 1/0;for(var f=0;f<d.length-1;f++)c=Math.min(c,d[f+1]-d[f]);return c/e}}),e7=(0,e.Mz)(e6,c2.fz,d1.gY,d_.HZ,(a,b,c,d)=>d,(a,b,c,d,e)=>{if(!(0,c6.H)(a))return 0;var f="vertical"===b?d.height:d.width;if("gap"===e)return a*f/2;if("no-gap"===e){var g=(0,c5.F4)(c,a*f),h=a*f/2;return h-g-(h-g)/f*g}return 0}),e8=(0,e.Mz)(eb,(a,b)=>{var c=eb(a,b);return null==c||"string"!=typeof c.padding?0:e7(a,"xAxis",b,c.padding)},(a,b)=>{if(null==a)return{left:0,right:0};var c,d,{padding:e}=a;return"string"==typeof e?{left:b,right:b}:{left:(null!=(c=e.left)?c:0)+b,right:(null!=(d=e.right)?d:0)+b}}),e9=(0,e.Mz)(ed,(a,b)=>{var c=ed(a,b);return null==c||"string"!=typeof c.padding?0:e7(a,"yAxis",b,c.padding)},(a,b)=>{if(null==a)return{top:0,bottom:0};var c,d,{padding:e}=a;return"string"==typeof e?{top:b,bottom:b}:{top:(null!=(c=e.top)?c:0)+b,bottom:(null!=(d=e.bottom)?d:0)+b}}),fa=(0,e.Mz)([d_.HZ,e8,d0.U,d0.C,(a,b,c)=>c],(a,b,c,d,e)=>{var{padding:f}=d;return e?[f.left,c.width-f.right]:[a.left+b.left,a.left+a.width-b.right]}),fb=(0,e.Mz)([d_.HZ,c2.fz,e9,d0.U,d0.C,(a,b,c)=>c],(a,b,c,d,e,f)=>{var{padding:g}=e;return f?[d.height-g.bottom,g.top]:"horizontal"===b?[a.top+a.height-c.bottom,a.top+c.top]:[a.top+c.top,a.top+a.height-c.bottom]}),fc=(a,b,c,d)=>{var e;switch(b){case"xAxis":return fa(a,c,d);case"yAxis":return fb(a,c,d);case"zAxis":return null==(e=ef(a,c))?void 0:e.range;case"angleAxis":return(0,d2.Cv)(a);case"radiusAxis":return(0,d2.Dc)(a,c);default:return}},fd=(0,e.Mz)([eg,fc],d5.I),fe=(0,e.Mz)([eg,e0,e5,fd],e1);function ff(a,b){return a.id<b.id?-1:+(a.id>b.id)}(0,e.Mz)(en,d3.N,(a,b)=>a.flatMap(a=>{var b;return null!=(b=a.errorBars)?b:[]}).filter(a=>ew(b,a)));var fg=(a,b)=>b,fh=(a,b,c)=>c,fi=(0,e.Mz)(d$.h,fg,fh,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(ff)),fj=(0,e.Mz)(d$.W,fg,fh,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(ff)),fk=(a,b)=>({width:a.width,height:b.height}),fl=(0,e.Mz)(d_.HZ,eb,fk),fm=(0,e.Mz)(dZ.A$,d_.HZ,fi,fg,fh,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=fk(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"top":return a.top;case"bottom":return c-a.bottom;default:return 0}})(b,d,a));var i="top"===d&&!e||"bottom"===d&&e;g[c.id]=f-Number(i)*h.height,f+=(i?-1:1)*h.height}),g}),fn=(0,e.Mz)(dZ.Lp,d_.HZ,fj,fg,fh,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=((a,b)=>({width:"number"==typeof b.width?b.width:d6.tQ,height:a.height}))(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"left":return a.left;case"right":return c-a.right;default:return 0}})(b,d,a));var i="left"===d&&!e||"right"===d&&e;g[c.id]=f-Number(i)*h.width,f+=(i?-1:1)*h.width}),g}),fo=(a,b)=>{var c=(0,d_.HZ)(a),d=eb(a,b);if(null!=d){var e=fm(a,d.orientation,d.mirror)[b];return null==e?{x:c.left,y:0}:{x:c.left,y:e}}},fp=(a,b)=>{var c=(0,d_.HZ)(a),d=ed(a,b);if(null!=d){var e=fn(a,d.orientation,d.mirror)[b];return null==e?{x:0,y:c.top}:{x:e,y:c.top}}},fq=(0,e.Mz)(d_.HZ,ed,(a,b)=>({width:"number"==typeof b.width?b.width:d6.tQ,height:a.height})),fr=(a,b,c)=>{switch(b){case"xAxis":return fl(a,c).width;case"yAxis":return fq(a,c).height;default:return}},fs=(a,b,c,d)=>{if(null!=c){var{allowDuplicatedCategory:e,type:f,dataKey:g}=c,h=(0,c3._L)(a,d),i=b.map(a=>a.value);if(g&&h&&"category"===f&&e&&(0,c5.CG)(i))return i}},ft=(0,e.Mz)([c2.fz,ev,eg,d3.N],fs),fu=(a,b,c,d)=>{if(null!=c&&null!=c.dataKey){var{type:e,scale:f}=c;if((0,c3._L)(a,d)&&("number"===e||"auto"!==f))return b.map(a=>a.value)}},fv=(0,e.Mz)([c2.fz,ev,eh,d3.N],fu),fw=(0,e.Mz)([c2.fz,(a,b,c)=>{switch(b){case"xAxis":return eb(a,c);case"yAxis":return ed(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},e0,fe,ft,fv,fc,e3,d3.N],(a,b,c,d,e,f,g,h,i)=>{if(null==b)return null;var j=(0,c3._L)(a,i);return{angle:b.angle,interval:b.interval,minTickGap:b.minTickGap,orientation:b.orientation,tick:b.tick,tickCount:b.tickCount,tickFormatter:b.tickFormatter,ticks:b.ticks,type:b.type,unit:b.unit,axisType:i,categoricalDomain:f,duplicateDomain:e,isCategorical:j,niceTicks:h,range:g,realScaleType:c,scale:d}}),fx=(0,e.Mz)([c2.fz,eh,e0,fe,e3,fc,ft,fv,d3.N],(a,b,c,d,e,f,g,h,i)=>{if(null!=b&&null!=d){var j=(0,c3._L)(a,i),{type:k,ticks:l,tickCount:m}=b,n="scaleBand"===c&&"function"==typeof d.bandwidth?d.bandwidth()/2:2,o="category"===k&&d.bandwidth?d.bandwidth()/n:0;o="angleAxis"===i&&null!=f&&f.length>=2?2*(0,c5.sA)(f[0]-f[1])*o:o;var p=l||e;return p?p.map((a,b)=>({index:b,coordinate:d(g?g.indexOf(a):a)+o,value:a,offset:o})).filter(a=>!(0,c5.M8)(a.coordinate)):j&&h?h.map((a,b)=>({coordinate:d(a)+o,value:a,index:b,offset:o})):d.ticks?d.ticks(m).map(a=>({coordinate:d(a)+o,value:a,offset:o})):d.domain().map((a,b)=>({coordinate:d(a)+o,value:g?g[a]:a,index:b,offset:o}))}}),fy=(0,e.Mz)([c2.fz,eh,fe,fc,ft,fv,d3.N],(a,b,c,d,e,f,g)=>{if(null!=b&&null!=c&&null!=d&&d[0]!==d[1]){var h=(0,c3._L)(a,g),{tickCount:i}=b,j=0;return(j="angleAxis"===g&&(null==d?void 0:d.length)>=2?2*(0,c5.sA)(d[0]-d[1])*j:j,h&&f)?f.map((a,b)=>({coordinate:c(a)+j,value:a,index:b,offset:j})):c.ticks?c.ticks(i).map(a=>({coordinate:c(a)+j,value:a,offset:j})):c.domain().map((a,b)=>({coordinate:c(a)+j,value:e?e[a]:a,index:b,offset:j}))}}),fz=(0,e.Mz)(eg,fe,(a,b)=>{if(null!=a&&null!=b)return d8(d8({},a),{},{scale:b})}),fA=(0,e.Mz)([eg,e0,e$,fd],e1);(0,e.Mz)((a,b,c)=>ef(a,c),fA,(a,b)=>{if(null!=a&&null!=b)return d8(d8({},a),{},{scale:b})});var fB=(0,e.Mz)([c2.fz,d$.h,d$.W],(a,b,c)=>{switch(a){case"horizontal":return b.some(a=>a.reversed)?"right-to-left":"left-to-right";case"vertical":return c.some(a=>a.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},86445:(a,b,c)=>{"use strict";c.d(b,{A$:()=>e,HK:()=>g,Lp:()=>d,et:()=>f});var d=a=>a.layout.width,e=a=>a.layout.height,f=a=>a.layout.scale,g=a=>a.layout.margin},87509:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(35314),e=c(76431),f=c(30657),g=c(43574);b.get=function a(b,c,h){if(null==b)return h;switch(typeof c){case"string":{if(d.isUnsafeProperty(c))return h;let f=b[c];if(void 0===f)if(e.isDeepKey(c))return a(b,g.toPath(c),h);else return h;return f}case"number":case"symbol":{"number"==typeof c&&(c=f.toKey(c));let a=b[c];if(void 0===a)return h;return a}default:{if(Array.isArray(c)){var i=b,j=c,k=h;if(0===j.length)return k;let a=i;for(let b=0;b<j.length;b++){if(null==a||d.isUnsafeProperty(j[b]))return k;a=a[j[b]]}return void 0===a?k:a}if(c=Object.is(c?.valueOf(),-0)?"-0":String(c),d.isUnsafeProperty(c))return h;let a=b[c];if(void 0===a)return h;return a}}}},90015:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.noop=function(){}},90830:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(26349),e=c(49899);b.isArrayLikeObject=function(a){return e.isObjectLike(a)&&d.isArrayLike(a)}},91428:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getTag=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":Object.prototype.toString.call(a)}},91653:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getSymbols=function(a){return Object.getOwnPropertySymbols(a).filter(b=>Object.prototype.propertyIsEnumerable.call(a,b))}},92292:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(69404),e=c(90015);b.isEqual=function(a,b){return d.isEqualWith(a,b,e.noop)}},92681:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.identity=function(a){return a}},92867:(a,b,c)=>{a.exports=c(60324).isPlainObject},92923:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(91653),e=c(91428),f=c(27469),g=c(23457),h=c(21251);function i(a,b,c,d=new Map,k){let l=k?.(a,b,c,d);if(null!=l)return l;if(g.isPrimitive(a))return a;if(d.has(a))return d.get(a);if(Array.isArray(a)){let b=Array(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return Object.hasOwn(a,"index")&&(b.index=a.index),Object.hasOwn(a,"input")&&(b.input=a.input),b}if(a instanceof Date)return new Date(a.getTime());if(a instanceof RegExp){let b=new RegExp(a.source,a.flags);return b.lastIndex=a.lastIndex,b}if(a instanceof Map){let b=new Map;for(let[e,f]of(d.set(a,b),a))b.set(e,i(f,e,c,d,k));return b}if(a instanceof Set){let b=new Set;for(let e of(d.set(a,b),a))b.add(i(e,void 0,c,d,k));return b}if("undefined"!=typeof Buffer&&Buffer.isBuffer(a))return a.subarray();if(h.isTypedArray(a)){let b=new(Object.getPrototypeOf(a)).constructor(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return b}if(a instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&a instanceof SharedArrayBuffer)return a.slice(0);if(a instanceof DataView){let b=new DataView(a.buffer.slice(0),a.byteOffset,a.byteLength);return d.set(a,b),j(b,a,c,d,k),b}if("undefined"!=typeof File&&a instanceof File){let b=new File([a],a.name,{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Blob){let b=new Blob([a],{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Error){let b=new a.constructor;return d.set(a,b),b.message=a.message,b.name=a.name,b.stack=a.stack,b.cause=a.cause,j(b,a,c,d,k),b}if("object"==typeof a&&function(a){switch(e.getTag(a)){case f.argumentsTag:case f.arrayTag:case f.arrayBufferTag:case f.dataViewTag:case f.booleanTag:case f.dateTag:case f.float32ArrayTag:case f.float64ArrayTag:case f.int8ArrayTag:case f.int16ArrayTag:case f.int32ArrayTag:case f.mapTag:case f.numberTag:case f.objectTag:case f.regexpTag:case f.setTag:case f.stringTag:case f.symbolTag:case f.uint8ArrayTag:case f.uint8ClampedArrayTag:case f.uint16ArrayTag:case f.uint32ArrayTag:return!0;default:return!1}}(a)){let b=Object.create(Object.getPrototypeOf(a));return d.set(a,b),j(b,a,c,d,k),b}return a}function j(a,b,c=a,e,f){let g=[...Object.keys(b),...d.getSymbols(b)];for(let d=0;d<g.length;d++){let h=g[d],j=Object.getOwnPropertyDescriptor(a,h);(null==j||j.writable)&&(a[h]=i(b[h],h,c,e,f))}}b.cloneDeepWith=function(a,b){return i(a,void 0,a,new Map,b)},b.cloneDeepWithImpl=i,b.copyProperties=j},93464:(a,b,c)=>{"use strict";c.d(b,{Gk:()=>ai,Vf:()=>ah});var d=c(43210),e=c(49384),f=c(81888),g=c(4057),h=c(54186);function i(){return(i=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var j=a=>{var{cx:b,cy:c,r:f,className:j}=a,k=(0,e.$)("recharts-dot",j);return b===+b&&c===+c&&f===+f?d.createElement("circle",i({},(0,h.J9)(a,!1),(0,g._U)(a),{className:k,cx:b,cy:c,r:f})):null},k=c(98986),l=c(98845),m=c(20237),n=c(22989),o=c(64279),p=c(43209),q=c(69009),r=c(27934);function s(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function t(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?s(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):s(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function u(a){var{points:b,mainColor:c,activeDot:e,itemDataKey:f}=a,i=(0,p.G)(q.A2),l=(0,r.EI)();if(null==b||null==l)return null;var m=b.find(a=>l.includes(a.payload));return(0,n.uy)(m)?null:(a=>{var b,{point:c,childIndex:e,mainColor:f,activeDot:i,dataKey:l}=a;if(!1===i||null==c.x||null==c.y)return null;var m=t(t({index:e,dataKey:l,cx:c.x,cy:c.y,r:4,fill:null!=f?f:"none",strokeWidth:2,stroke:"#fff",payload:c.payload,value:c.value},(0,h.J9)(i,!1)),(0,g._U)(i));return b=(0,d.isValidElement)(i)?(0,d.cloneElement)(i,m):"function"==typeof i?i(m):d.createElement(j,m),d.createElement(k.W,{className:"recharts-active-dot"},b)})({point:m,childIndex:Number(i),mainColor:c,dataKey:f,activeDot:e})}var v=c(37625),w=c(4236),x=c(46993),y=c(84648),z=c(85621),A=c(51426),B=c(57282),C=(a,b,c,d)=>(0,z.Gx)(a,"xAxis",b,d),D=(a,b,c,d)=>(0,z.CR)(a,"xAxis",b,d),E=(a,b,c,d)=>(0,z.Gx)(a,"yAxis",c,d),F=(a,b,c,d)=>(0,z.CR)(a,"yAxis",c,d),G=(0,y.Mz)([A.fz,C,E,D,F],(a,b,c,d,e)=>(0,o._L)(a,"xAxis")?(0,o.Hj)(b,d,!1):(0,o.Hj)(c,e,!1)),H=(0,y.Mz)([z.ld,(a,b,c,d,e)=>e],(a,b)=>{if(a.some(a=>"area"===a.type&&b.dataKey===a.dataKey&&(0,o.$8)(b.stackId)===a.stackId&&b.data===a.data))return b}),I=(0,y.Mz)([A.fz,C,E,D,F,(a,b,c,d,e)=>{var f,g,h=(0,A.fz)(a);if(null!=(g=(0,o._L)(h,"xAxis")?(0,z.TC)(a,"yAxis",c,d):(0,z.TC)(a,"xAxis",b,d))){var{dataKey:i,stackId:j}=e;if(null!=j){var k=null==(f=g[j])?void 0:f.stackedData;return null==k?void 0:k.find(a=>a.key===i)}}},B.HS,G,H],(a,b,c,d,e,f,g,h,i)=>{var j,{chartData:k,dataStartIndex:l,dataEndIndex:m}=g;if(null!=i&&("horizontal"===a||"vertical"===a)&&null!=b&&null!=c&&null!=d&&null!=e&&0!==d.length&&0!==e.length&&null!=h){var{data:n}=i;if(null!=(j=n&&n.length>0?n:null==k?void 0:k.slice(l,m+1)))return ah({layout:a,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,dataStartIndex:l,areaSettings:i,stackedData:f,displayedData:j,chartBaseValue:void 0,bandSize:h})}}),J=c(83409),K=c(21426),L=c(14956),M=c(36304),N=c(73865),O=c(12128),P=c(19420),Q=["layout","type","stroke","connectNulls","isRange"],R=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function S(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function T(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function U(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?T(Object(c),!0).forEach(function(b){V(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):T(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function V(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function W(){return(W=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function X(a,b){return a&&"none"!==a?a:b}function Y(a){var{dataKey:b,data:c,stroke:d,strokeWidth:e,fill:f,name:g,hide:h,unit:i}=a;return{dataDefinedOnItem:c,positions:void 0,settings:{stroke:d,strokeWidth:e,fill:f,dataKey:b,nameKey:void 0,name:(0,o.uM)(g,b),hide:h,type:a.tooltipType,color:X(d,f),unit:i}}}function Z(a){var{clipPathId:b,points:c,props:f}=a,{needClip:g,dot:i,dataKey:l}=f;if(null==c||!i&&1!==c.length)return null;var m=(0,h.y$)(i),n=(0,h.J9)(f,!1),o=(0,h.J9)(i,!0),p=c.map((a,b)=>{var f,g=U(U(U({key:"dot-".concat(b),r:3},n),o),{},{index:b,cx:a.x,cy:a.y,dataKey:l,value:a.value,payload:a.payload,points:c});if(d.isValidElement(i))f=d.cloneElement(i,g);else if("function"==typeof i)f=i(g);else{var h=(0,e.$)("recharts-area-dot","boolean"!=typeof i?i.className:"");f=d.createElement(j,W({},g,{className:h}))}return f}),q={clipPath:g?"url(#clipPath-".concat(m?"":"dots-").concat(b,")"):void 0};return d.createElement(k.W,W({className:"recharts-area-dots"},q),p)}function $(a){var{points:b,baseLine:c,needClip:e,clipPathId:g,props:i,showLabels:j}=a,{layout:m,type:n,stroke:o,connectNulls:p,isRange:q}=i,r=S(i,Q);return d.createElement(d.Fragment,null,(null==b?void 0:b.length)>1&&d.createElement(k.W,{clipPath:e?"url(#clipPath-".concat(g,")"):void 0},d.createElement(f.I,W({},(0,h.J9)(r,!0),{points:b,connectNulls:p,type:n,baseLine:c,layout:m,stroke:"none",className:"recharts-area-area"})),"none"!==o&&d.createElement(f.I,W({},(0,h.J9)(i,!1),{className:"recharts-area-curve",layout:m,type:n,connectNulls:p,fill:"none",points:b})),"none"!==o&&q&&d.createElement(f.I,W({},(0,h.J9)(i,!1),{className:"recharts-area-curve",layout:m,type:n,connectNulls:p,fill:"none",points:c}))),d.createElement(Z,{points:b,props:i,clipPathId:g}),j&&l.Z.renderCallByParent(i,b))}function _(a){var{alpha:b,baseLine:c,points:e,strokeWidth:f}=a,g=e[0].y,h=e[e.length-1].y;if(!(0,O.H)(g)||!(0,O.H)(h))return null;var i=b*Math.abs(g-h),j=Math.max(...e.map(a=>a.x||0));return((0,n.Et)(c)?j=Math.max(c,j):c&&Array.isArray(c)&&c.length&&(j=Math.max(...c.map(a=>a.x||0),j)),(0,n.Et)(j))?d.createElement("rect",{x:0,y:g<h?g:g-i,width:j+(f?parseInt("".concat(f),10):1),height:Math.floor(i)}):null}function aa(a){var{alpha:b,baseLine:c,points:e,strokeWidth:f}=a,g=e[0].x,h=e[e.length-1].x;if(!(0,O.H)(g)||!(0,O.H)(h))return null;var i=b*Math.abs(g-h),j=Math.max(...e.map(a=>a.y||0));return((0,n.Et)(c)?j=Math.max(c,j):c&&Array.isArray(c)&&c.length&&(j=Math.max(...c.map(a=>a.y||0),j)),(0,n.Et)(j))?d.createElement("rect",{x:g<h?g:g-i,y:0,width:i,height:Math.floor(j+(f?parseInt("".concat(f),10):1))}):null}function ab(a){var{alpha:b,layout:c,points:e,baseLine:f,strokeWidth:g}=a;return"vertical"===c?d.createElement(_,{alpha:b,points:e,baseLine:f,strokeWidth:g}):d.createElement(aa,{alpha:b,points:e,baseLine:f,strokeWidth:g})}function ac(a){var{needClip:b,clipPathId:c,props:e,previousPointsRef:f,previousBaselineRef:g}=a,{points:h,baseLine:i,isAnimationActive:j,animationBegin:l,animationDuration:m,animationEasing:o,onAnimationStart:p,onAnimationEnd:q}=e,r=(0,M.n)(e,"recharts-area-"),[s,t]=(0,d.useState)(!0),u=(0,d.useCallback)(()=>{"function"==typeof q&&q(),t(!1)},[q]),v=(0,d.useCallback)(()=>{"function"==typeof p&&p(),t(!0)},[p]),w=f.current,x=g.current;return d.createElement(P.i,{begin:l,duration:m,isActive:j,easing:o,from:{t:0},to:{t:1},onAnimationEnd:u,onAnimationStart:v,key:r},a=>{var{t:j}=a;if(w){var l,m=w.length/h.length,o=1===j?h:h.map((a,b)=>{var c=Math.floor(b*m);if(w[c]){var d=w[c];return U(U({},a),{},{x:(0,n.GW)(d.x,a.x,j),y:(0,n.GW)(d.y,a.y,j)})}return a});return l=(0,n.Et)(i)?(0,n.GW)(x,i,j):(0,n.uy)(i)||(0,n.M8)(i)?(0,n.GW)(x,0,j):i.map((a,b)=>{var c=Math.floor(b*m);if(Array.isArray(x)&&x[c]){var d=x[c];return U(U({},a),{},{x:(0,n.GW)(d.x,a.x,j),y:(0,n.GW)(d.y,a.y,j)})}return a}),j>0&&(f.current=o,g.current=l),d.createElement($,{points:o,baseLine:l,needClip:b,clipPathId:c,props:e,showLabels:!s})}return j>0&&(f.current=h,g.current=i),d.createElement(k.W,null,d.createElement("defs",null,d.createElement("clipPath",{id:"animationClipPath-".concat(c)},d.createElement(ab,{alpha:j,points:h,baseLine:i,layout:e.layout,strokeWidth:e.strokeWidth}))),d.createElement(k.W,{clipPath:"url(#animationClipPath-".concat(c,")")},d.createElement($,{points:h,baseLine:i,needClip:b,clipPathId:c,props:e,showLabels:!0})))})}function ad(a){var{needClip:b,clipPathId:c,props:e}=a,{points:f,baseLine:g,isAnimationActive:h}=e,i=(0,d.useRef)(null),j=(0,d.useRef)(),k=i.current,l=j.current;return h&&f&&f.length&&(k!==f||l!==g)?d.createElement(ac,{needClip:b,clipPathId:c,props:e,previousPointsRef:i,previousBaselineRef:j}):d.createElement($,{points:f,baseLine:g,needClip:b,clipPathId:c,props:e,showLabels:!0})}class ae extends d.PureComponent{constructor(){super(...arguments),V(this,"id",(0,n.NF)("recharts-area-"))}render(){var a,{hide:b,dot:c,points:f,className:g,top:i,left:j,needClip:l,xAxisId:m,yAxisId:o,width:p,height:q,id:r,baseLine:s}=this.props;if(b)return null;var t=(0,e.$)("recharts-area",g),v=(0,n.uy)(r)?this.id:r,{r:w=3,strokeWidth:y=2}=null!=(a=(0,h.J9)(c,!1))?a:{r:3,strokeWidth:2},z=(0,h.y$)(c),A=2*w+y;return d.createElement(d.Fragment,null,d.createElement(k.W,{className:t},l&&d.createElement("defs",null,d.createElement(x.Q,{clipPathId:v,xAxisId:m,yAxisId:o}),!z&&d.createElement("clipPath",{id:"clipPath-dots-".concat(v)},d.createElement("rect",{x:j-A/2,y:i-A/2,width:p+A,height:q+A}))),d.createElement(ad,{needClip:l,clipPathId:v,props:this.props})),d.createElement(u,{points:f,mainColor:X(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(s)&&d.createElement(u,{points:s,mainColor:X(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var af={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!m.m.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function ag(a){var b,c=(0,N.e)(a,af),{activeDot:e,animationBegin:f,animationDuration:g,animationEasing:h,connectNulls:i,dot:j,fill:k,fillOpacity:l,hide:m,isAnimationActive:n,legendType:o,stroke:q,xAxisId:s,yAxisId:t}=c,u=S(c,R),v=(0,A.WX)(),w=(0,K.fW)(),{needClip:y}=(0,x.l)(s,t),z=(0,J.r)(),B=(0,d.useMemo)(()=>({baseValue:a.baseValue,stackId:a.stackId,connectNulls:i,data:a.data,dataKey:a.dataKey}),[a.baseValue,a.stackId,i,a.data,a.dataKey]),{points:C,isRange:D,baseLine:E}=null!=(b=(0,p.G)(a=>I(a,s,t,z,B)))?b:{},{height:F,width:G,x:H,y:L}=(0,r.oM)();return"horizontal"!==v&&"vertical"!==v||"AreaChart"!==w&&"ComposedChart"!==w?null:d.createElement(ae,W({},u,{activeDot:e,animationBegin:f,animationDuration:g,animationEasing:h,baseLine:E,connectNulls:i,dot:j,fill:k,fillOpacity:l,height:F,hide:m,layout:v,isAnimationActive:n,isRange:D,legendType:o,needClip:y,points:C,stroke:q,width:G,left:H,top:L,xAxisId:s,yAxisId:t}))}function ah(a){var b,{areaSettings:{connectNulls:c,baseValue:d,dataKey:e},stackedData:f,layout:g,chartBaseValue:h,xAxis:i,yAxis:j,displayedData:k,dataStartIndex:l,xAxisTicks:m,yAxisTicks:p,bandSize:q}=a,r=f&&f.length,s=((a,b,c,d,e)=>{var f=null!=c?c:b;if((0,n.Et)(f))return f;var g="horizontal"===a?e:d,h=g.scale.domain();if("number"===g.type){var i=Math.max(h[0],h[1]),j=Math.min(h[0],h[1]);return"dataMin"===f?j:"dataMax"===f||i<0?i:Math.max(Math.min(h[0],h[1]),0)}return"dataMin"===f?h[0]:"dataMax"===f?h[1]:h[0]})(g,h,d,i,j),t="horizontal"===g,u=!1,v=k.map((a,b)=>{r?d=f[l+b]:Array.isArray(d=(0,o.kr)(a,e))?u=!0:d=[s,d];var d,g=null==d[1]||r&&!c&&null==(0,o.kr)(a,e);return t?{x:(0,o.nb)({axis:i,ticks:m,bandSize:q,entry:a,index:b}),y:g?null:j.scale(d[1]),value:d,payload:a}:{x:g?null:i.scale(d[1]),y:(0,o.nb)({axis:j,ticks:p,bandSize:q,entry:a,index:b}),value:d,payload:a}});return b=r||u?v.map(a=>{var b=Array.isArray(a.value)?a.value[0]:null;return t?{x:a.x,y:null!=b&&null!=a.y?j.scale(b):null}:{x:null!=b?i.scale(b):null,y:a.y}}):t?j.scale(s):i.scale(s),{points:v,baseLine:b,isRange:u}}class ai extends d.PureComponent{render(){return d.createElement(w._S,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},d.createElement(L.A,{legendPayload:(a=>{var{dataKey:b,name:c,stroke:d,fill:e,legendType:f,hide:g}=a;return[{inactive:g,dataKey:b,type:f,color:X(d,e),value:(0,o.uM)(c,b),payload:a}]})(this.props)}),d.createElement(v.r,{fn:Y,args:this.props}),d.createElement(ag,this.props))}}V(ai,"displayName","Area"),V(ai,"defaultProps",af)},94728:(a,b,c)=>{"use strict";c.d(b,{C:()=>h,U:()=>i});var d=c(84648),e=c(76966),f=c(86445),g=c(22989),h=a=>a.brush,i=(0,d.Mz)([h,e.HZ,f.HK],(a,b,c)=>({height:a.height,x:(0,g.Et)(a.x)?a.x:b.left,y:(0,g.Et)(a.y)?a.y:b.top+b.height+b.brushBottom-((null==c?void 0:c.bottom)||0),width:(0,g.Et)(a.width)?a.width:b.width}))},95819:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isSymbol=function(a){return"symbol"==typeof a||a instanceof Symbol}},96075:(a,b,c)=>{"use strict";c.d(b,{P:()=>j});var d=c(20237);function e(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function f(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?e(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):e(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var g={widthCache:{},cacheCount:0},h={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},i="recharts_measurement_span",j=function(a){var b,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==a||d.m.isSsr)return{width:0,height:0};var e=(Object.keys(b=f({},c)).forEach(a=>{b[a]||delete b[a]}),b),j=JSON.stringify({text:a,copyStyle:e});if(g.widthCache[j])return g.widthCache[j];try{var k=document.getElementById(i);k||((k=document.createElement("span")).setAttribute("id",i),k.setAttribute("aria-hidden","true"),document.body.appendChild(k));var l=f(f({},h),e);Object.assign(k.style,l),k.textContent="".concat(a);var m=k.getBoundingClientRect(),n={width:m.width,height:m.height};return g.widthCache[j]=n,++g.cacheCount>2e3&&(g.cacheCount=0,g.widthCache={}),n}catch(a){return{width:0,height:0}}}},97350:(a,b,c)=>{"use strict";c.d(b,{JN:()=>d,_5:()=>e,eC:()=>h,gY:()=>f,hX:()=>k,iO:()=>i,lZ:()=>j,pH:()=>l,x3:()=>g});var d=a=>a.rootProps.maxBarSize,e=a=>a.rootProps.barGap,f=a=>a.rootProps.barCategoryGap,g=a=>a.rootProps.barSize,h=a=>a.rootProps.stackOffset,i=a=>a.options.chartName,j=a=>a.rootProps.syncId,k=a=>a.rootProps.syncMethod,l=a=>a.options.eventEmitter},97371:(a,b,c)=>{"use strict";c.d(b,{P:()=>e});var d=c(12128),e=(a,b)=>{var c=null==a?void 0:a.index;if(null==c)return null;var e=Number(c);if(!(0,d.H)(e))return c;var f=Infinity;return b.length>0&&(f=b.length-1),String(Math.max(0,Math.min(e,f)))}},97633:(a,b,c)=>{"use strict";c.d(b,{J:()=>r,Z:()=>q});var d=c(43210),e=c(49384),f=c(23561),g=c(54186),h=c(22989),i=c(19335),j=c(51426),k=["offset"],l=["labelRef"];function m(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function n(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function o(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function p(){return(p=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var q=a=>null!=a&&"function"==typeof a;function r(a){var b,{offset:c=5}=a,n=o({offset:c},m(a,k)),{viewBox:q,position:r,value:s,children:t,content:u,className:v="",textBreakAll:w,labelRef:x}=n,y=(0,j.sk)(),z=q||y;if(!z||(0,h.uy)(s)&&(0,h.uy)(t)&&!(0,d.isValidElement)(u)&&"function"!=typeof u)return null;if((0,d.isValidElement)(u)){var{labelRef:A}=n,B=m(n,l);return(0,d.cloneElement)(u,B)}if("function"==typeof u){if(b=(0,d.createElement)(u,n),(0,d.isValidElement)(b))return b}else b=(a=>{var{value:b,formatter:c}=a,d=(0,h.uy)(a.children)?b:a.children;return"function"==typeof c?c(d):d})(n);var C="cx"in z&&(0,h.Et)(z.cx),D=(0,g.J9)(n,!0);if(C&&("insideStart"===r||"insideEnd"===r||"end"===r))return((a,b,c)=>{let f,g;var j,k,{position:l,viewBox:m,offset:n,className:o}=a,{cx:q,cy:r,innerRadius:s,outerRadius:t,startAngle:u,endAngle:v,clockWise:w}=m,x=(s+t)/2,y=(f=u,g=v,(0,h.sA)(g-f)*Math.min(Math.abs(g-f),360)),z=y>=0?1:-1;"insideStart"===l?(j=u+z*n,k=w):"insideEnd"===l?(j=v-z*n,k=!w):"end"===l&&(j=v+z*n,k=w),k=y<=0?k:!k;var A=(0,i.IZ)(q,r,x,j),B=(0,i.IZ)(q,r,x,j+(k?1:-1)*359),C="M".concat(A.x,",").concat(A.y,"\n    A").concat(x,",").concat(x,",0,1,").concat(+!k,",\n    ").concat(B.x,",").concat(B.y),D=(0,h.uy)(a.id)?(0,h.NF)("recharts-radial-line-"):a.id;return d.createElement("text",p({},c,{dominantBaseline:"central",className:(0,e.$)("recharts-radial-bar-label",o)}),d.createElement("defs",null,d.createElement("path",{id:D,d:C})),d.createElement("textPath",{xlinkHref:"#".concat(D)},b))})(n,b,D);var E=C?(a=>{var{viewBox:b,offset:c,position:d}=a,{cx:e,cy:f,innerRadius:g,outerRadius:h,startAngle:j,endAngle:k}=b,l=(j+k)/2;if("outside"===d){var{x:m,y:n}=(0,i.IZ)(e,f,h+c,l);return{x:m,y:n,textAnchor:m>=e?"start":"end",verticalAnchor:"middle"}}if("center"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"end"};var{x:o,y:p}=(0,i.IZ)(e,f,(g+h)/2,l);return{x:o,y:p,textAnchor:"middle",verticalAnchor:"middle"}})(n):((a,b)=>{var{parentViewBox:c,offset:d,position:e}=a,{x:f,y:g,width:i,height:j}=b,k=j>=0?1:-1,l=k*d,m=k>0?"end":"start",n=k>0?"start":"end",p=i>=0?1:-1,q=p*d,r=p>0?"end":"start",s=p>0?"start":"end";if("top"===e)return o(o({},{x:f+i/2,y:g-k*d,textAnchor:"middle",verticalAnchor:m}),c?{height:Math.max(g-c.y,0),width:i}:{});if("bottom"===e)return o(o({},{x:f+i/2,y:g+j+l,textAnchor:"middle",verticalAnchor:n}),c?{height:Math.max(c.y+c.height-(g+j),0),width:i}:{});if("left"===e){var t={x:f-q,y:g+j/2,textAnchor:r,verticalAnchor:"middle"};return o(o({},t),c?{width:Math.max(t.x-c.x,0),height:j}:{})}if("right"===e){var u={x:f+i+q,y:g+j/2,textAnchor:s,verticalAnchor:"middle"};return o(o({},u),c?{width:Math.max(c.x+c.width-u.x,0),height:j}:{})}var v=c?{width:i,height:j}:{};return"insideLeft"===e?o({x:f+q,y:g+j/2,textAnchor:s,verticalAnchor:"middle"},v):"insideRight"===e?o({x:f+i-q,y:g+j/2,textAnchor:r,verticalAnchor:"middle"},v):"insideTop"===e?o({x:f+i/2,y:g+l,textAnchor:"middle",verticalAnchor:n},v):"insideBottom"===e?o({x:f+i/2,y:g+j-l,textAnchor:"middle",verticalAnchor:m},v):"insideTopLeft"===e?o({x:f+q,y:g+l,textAnchor:s,verticalAnchor:n},v):"insideTopRight"===e?o({x:f+i-q,y:g+l,textAnchor:r,verticalAnchor:n},v):"insideBottomLeft"===e?o({x:f+q,y:g+j-l,textAnchor:s,verticalAnchor:m},v):"insideBottomRight"===e?o({x:f+i-q,y:g+j-l,textAnchor:r,verticalAnchor:m},v):e&&"object"==typeof e&&((0,h.Et)(e.x)||(0,h._3)(e.x))&&((0,h.Et)(e.y)||(0,h._3)(e.y))?o({x:f+(0,h.F4)(e.x,i),y:g+(0,h.F4)(e.y,j),textAnchor:"end",verticalAnchor:"end"},v):o({x:f+i/2,y:g+j/2,textAnchor:"middle",verticalAnchor:"middle"},v)})(n,z);return d.createElement(f.E,p({ref:x,className:(0,e.$)("recharts-label",v)},D,E,{breakAll:w}),b)}r.displayName="Label";var s=a=>{var{cx:b,cy:c,angle:d,startAngle:e,endAngle:f,r:g,radius:i,innerRadius:j,outerRadius:k,x:l,y:m,top:n,left:o,width:p,height:q,clockWise:r,labelViewBox:s}=a;if(s)return s;if((0,h.Et)(p)&&(0,h.Et)(q)){if((0,h.Et)(l)&&(0,h.Et)(m))return{x:l,y:m,width:p,height:q};if((0,h.Et)(n)&&(0,h.Et)(o))return{x:n,y:o,width:p,height:q}}return(0,h.Et)(l)&&(0,h.Et)(m)?{x:l,y:m,width:0,height:0}:(0,h.Et)(b)&&(0,h.Et)(c)?{cx:b,cy:c,startAngle:e||d||0,endAngle:f||d||0,innerRadius:j||0,outerRadius:k||i||g||0,clockWise:r}:a.viewBox?a.viewBox:void 0};r.parseViewBox=s,r.renderCallByParent=function(a,b){var c=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&c&&!a.label)return null;var{children:e,labelRef:f}=a,i=s(a),j=(0,g.aS)(e,r).map((a,c)=>(0,d.cloneElement)(a,{viewBox:b||i,key:"label-".concat(c)}));return c?[((a,b,c)=>{if(!a)return null;var e={viewBox:b,labelRef:c};return!0===a?d.createElement(r,p({key:"label-implicit"},e)):(0,h.vh)(a)?d.createElement(r,p({key:"label-implicit",value:a},e)):(0,d.isValidElement)(a)?a.type===r?(0,d.cloneElement)(a,o({key:"label-implicit"},e)):d.createElement(r,p({key:"label-implicit",content:a},e)):q(a)?d.createElement(r,p({key:"label-implicit",content:a},e)):a&&"object"==typeof a?d.createElement(r,p({},a,{key:"label-implicit"},e)):null})(a.label,b||i,f),...j]:j}},97668:(a,b)=>{"use strict";var c="function"==typeof Symbol&&Symbol.for,d=c?Symbol.for("react.element"):60103,e=c?Symbol.for("react.portal"):60106,f=c?Symbol.for("react.fragment"):60107,g=c?Symbol.for("react.strict_mode"):60108,h=c?Symbol.for("react.profiler"):60114,i=c?Symbol.for("react.provider"):60109,j=c?Symbol.for("react.context"):60110,k=c?Symbol.for("react.async_mode"):60111,l=c?Symbol.for("react.concurrent_mode"):60111,m=c?Symbol.for("react.forward_ref"):60112,n=c?Symbol.for("react.suspense"):60113,o=(c&&Symbol.for("react.suspense_list"),c?Symbol.for("react.memo"):60115),p=c?Symbol.for("react.lazy"):60116;function q(a){if("object"==typeof a&&null!==a){var b=a.$$typeof;switch(b){case d:switch(a=a.type){case k:case l:case f:case h:case g:case n:return a;default:switch(a=a&&a.$$typeof){case j:case m:case p:case o:case i:return a;default:return b}}case e:return b}}}c&&Symbol.for("react.block"),c&&Symbol.for("react.fundamental"),c&&Symbol.for("react.responder"),c&&Symbol.for("react.scope");b.isFragment=function(a){return q(a)===f}},97711:(a,b,c)=>{"use strict";c.d(b,{$:()=>e,X:()=>f});var d=c(43210),e=(0,d.createContext)(null),f=()=>(0,d.useContext)(e)},97766:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.last=function(a){return a[a.length-1]}},98009:(a,b,c)=>{"use strict";c.d(b,{l3:()=>j,m7:()=>k}),c(43210);var d=c(43209),e=c(97350);new(c(11117)),c(49605),c(17118);var f=c(21426),g=c(69009);function h(a){return a.tooltip.syncInteraction}var i=c(51426);function j(){(0,d.j)(),(0,d.G)(e.lZ),(0,d.G)(e.pH),(0,d.j)(),(0,d.G)(e.hX),(0,d.G)(g.R4),(0,i.WX)(),(0,i.sk)(),(0,d.G)(a=>a.rootProps.className),(0,d.G)(e.lZ),(0,d.G)(e.pH),(0,d.j)()}function k(a,b,c,g,i,j){(0,d.G)(c=>(0,f.dp)(c,a,b)),(0,d.G)(e.pH),(0,d.G)(e.lZ),(0,d.G)(e.hX);var k=(0,d.G)(h);null==k||k.active}c(64267)},98150:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let c=/^(?:0|[1-9]\d*)$/;b.isIndex=function(a,b=Number.MAX_SAFE_INTEGER){switch(typeof a){case"number":return Number.isInteger(a)&&a>=0&&a<b;case"symbol":return!1;case"string":return c.test(a)}}},98845:(a,b,c)=>{"use strict";c.d(b,{Z:()=>s});var d=c(43210),e=c(9474),f=c.n(e),g=c(97633),h=c(98986),i=c(54186),j=c(64279),k=c(22989),l=["valueAccessor"],m=["data","dataKey","clockWise","id","textBreakAll"];function n(){return(n=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function o(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function q(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var r=a=>Array.isArray(a.value)?f()(a.value):a.value;function s(a){var{valueAccessor:b=r}=a,c=q(a,l),{data:e,dataKey:f,clockWise:o,id:s,textBreakAll:t}=c,u=q(c,m);return e&&e.length?d.createElement(h.W,{className:"recharts-label-list"},e.map((a,c)=>{var e=(0,k.uy)(f)?b(a,c):(0,j.kr)(a&&a.payload,f),h=(0,k.uy)(s)?{}:{id:"".concat(s,"-").concat(c)};return d.createElement(g.J,n({},(0,i.J9)(a,!0),u,h,{parentViewBox:a.parentViewBox,value:e,textBreakAll:t,viewBox:g.J.parseViewBox((0,k.uy)(o)?a:p(p({},a),{},{clockWise:o})),key:"label-".concat(c),index:c}))})):null}s.displayName="LabelList",s.renderCallByParent=function(a,b){var c,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&e&&!a.label)return null;var{children:f}=a,h=(0,i.aS)(f,s).map((a,c)=>(0,d.cloneElement)(a,{data:b,key:"labelList-".concat(c)}));return e?[(c=a.label,c?!0===c?d.createElement(s,{key:"labelList-implicit",data:b}):d.isValidElement(c)||(0,g.Z)(c)?d.createElement(s,{key:"labelList-implicit",data:b,content:c}):"object"==typeof c?d.createElement(s,n({data:b},c,{key:"labelList-implicit"})):null:null),...h]:h}},98986:(a,b,c)=>{"use strict";c.d(b,{W:()=>i});var d=c(43210),e=c(49384),f=c(54186),g=["children","className"];function h(){return(h=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var i=d.forwardRef((a,b)=>{var{children:c,className:i}=a,j=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,g),k=(0,e.$)("recharts-layer",i);return d.createElement("g",h({className:k},(0,f.J9)(j,!0),{ref:b}),c)})}};