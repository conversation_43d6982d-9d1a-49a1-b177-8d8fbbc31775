{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/dashboard/attendance-stats.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Users, Clock, Calendar, TrendingUp, TrendingDown } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface AttendanceStatsProps {\n  totalStudents: number\n  presentToday: number\n  lateToday: number\n  absentToday: number\n  attendanceRate: number\n  className?: string\n}\n\nexport function AttendanceStats({\n  totalStudents,\n  presentToday,\n  lateToday,\n  absentToday,\n  attendanceRate,\n  className\n}: AttendanceStatsProps) {\n  const stats = [\n    {\n      title: \"Total Students\",\n      value: totalStudents.toLocaleString(),\n      icon: Users,\n      description: \"Enrolled students\",\n      color: \"text-blue-600\",\n      bgColor: \"bg-blue-50\",\n      change: \"+2.5%\",\n      trend: \"up\"\n    },\n    {\n      title: \"Present Today\",\n      value: presentToday.toLocaleString(),\n      icon: Calendar,\n      description: `${attendanceRate}% attendance rate`,\n      color: \"text-green-600\",\n      bgColor: \"bg-green-50\",\n      change: \"+1.2%\",\n      trend: \"up\"\n    },\n    {\n      title: \"Late Arrivals\",\n      value: lateToday.toString(),\n      icon: Clock,\n      description: \"Students arrived late\",\n      color: \"text-yellow-600\",\n      bgColor: \"bg-yellow-50\",\n      change: \"-0.8%\",\n      trend: \"down\"\n    },\n    {\n      title: \"Absent Today\",\n      value: absentToday.toString(),\n      icon: Users,\n      description: `${((absentToday / totalStudents) * 100).toFixed(1)}% of total`,\n      color: \"text-red-600\",\n      bgColor: \"bg-red-50\",\n      change: \"+0.3%\",\n      trend: \"up\"\n    }\n  ]\n\n  return (\n    <div className={cn(\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\", className)}>\n      {stats.map((stat, index) => {\n        const Icon = stat.icon\n        const TrendIcon = stat.trend === \"up\" ? TrendingUp : TrendingDown\n        \n        return (\n          <Card key={index} className=\"relative overflow-hidden\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                {stat.title}\n              </CardTitle>\n              <div className={cn(\"p-2 rounded-lg\", stat.bgColor)}>\n                <Icon className={cn(\"h-4 w-4\", stat.color)} />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"text-2xl font-bold\">{stat.value}</div>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    {stat.description}\n                  </p>\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  <TrendIcon \n                    className={cn(\n                      \"h-3 w-3\",\n                      stat.trend === \"up\" ? \"text-green-500\" : \"text-red-500\"\n                    )} \n                  />\n                  <span \n                    className={cn(\n                      \"text-xs font-medium\",\n                      stat.trend === \"up\" ? \"text-green-500\" : \"text-red-500\"\n                    )}\n                  >\n                    {stat.change}\n                  </span>\n                </div>\n              </div>\n              \n              {/* Progress bar for attendance rate */}\n              {index === 1 && (\n                <div className=\"mt-3\">\n                  <Progress value={attendanceRate} className=\"h-2\" />\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        )\n      })}\n    </div>\n  )\n}\n\n// Progress Ring Component\ninterface ProgressRingProps {\n  value: number\n  max: number\n  size?: number\n  strokeWidth?: number\n  className?: string\n  children?: React.ReactNode\n}\n\nexport function ProgressRing({\n  value,\n  max,\n  size = 120,\n  strokeWidth = 8,\n  className,\n  children\n}: ProgressRingProps) {\n  const radius = (size - strokeWidth) / 2\n  const circumference = radius * 2 * Math.PI\n  const percentage = (value / max) * 100\n  const strokeDasharray = circumference\n  const strokeDashoffset = circumference - (percentage / 100) * circumference\n\n  return (\n    <div className={cn(\"relative inline-flex items-center justify-center\", className)}>\n      <svg\n        width={size}\n        height={size}\n        className=\"transform -rotate-90\"\n      >\n        {/* Background circle */}\n        <circle\n          cx={size / 2}\n          cy={size / 2}\n          r={radius}\n          stroke=\"currentColor\"\n          strokeWidth={strokeWidth}\n          fill=\"none\"\n          className=\"text-muted-foreground/20\"\n        />\n        {/* Progress circle */}\n        <circle\n          cx={size / 2}\n          cy={size / 2}\n          r={radius}\n          stroke=\"currentColor\"\n          strokeWidth={strokeWidth}\n          fill=\"none\"\n          strokeDasharray={strokeDasharray}\n          strokeDashoffset={strokeDashoffset}\n          className=\"text-primary transition-all duration-300 ease-in-out\"\n          strokeLinecap=\"round\"\n        />\n      </svg>\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        {children || (\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold\">{percentage.toFixed(0)}%</div>\n            <div className=\"text-xs text-muted-foreground\">\n              {value}/{max}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\n// Real-time Counter Component\ninterface RealTimeCounterProps {\n  value: number\n  label: string\n  trend?: \"up\" | \"down\" | \"stable\"\n  className?: string\n}\n\nexport function RealTimeCounter({\n  value,\n  label,\n  trend = \"stable\",\n  className\n}: RealTimeCounterProps) {\n  return (\n    <div className={cn(\"text-center\", className)}>\n      <div className=\"text-3xl font-bold tabular-nums\">\n        {value.toLocaleString()}\n      </div>\n      <div className=\"text-sm text-muted-foreground\">{label}</div>\n      {trend !== \"stable\" && (\n        <div className=\"flex items-center justify-center mt-1\">\n          {trend === \"up\" ? (\n            <TrendingUp className=\"h-3 w-3 text-green-500 mr-1\" />\n          ) : (\n            <TrendingDown className=\"h-3 w-3 text-red-500 mr-1\" />\n          )}\n          <Badge \n            variant={trend === \"up\" ? \"default\" : \"destructive\"}\n            className=\"text-xs\"\n          >\n            {trend === \"up\" ? \"↑\" : \"↓\"}\n          </Badge>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAiBO,SAAS,gBAAgB,KAOT;QAPS,EAC9B,aAAa,EACb,YAAY,EACZ,SAAS,EACT,WAAW,EACX,cAAc,EACd,SAAS,EACY,GAPS;IAQ9B,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,cAAc,cAAc;YACnC,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;YACb,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,aAAa,cAAc;YAClC,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa,AAAC,GAAiB,OAAf,gBAAe;YAC/B,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,UAAU,QAAQ;YACzB,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;YACb,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,YAAY,QAAQ;YAC3B,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa,AAAC,GAAmD,OAAjD,CAAC,AAAC,cAAc,gBAAiB,GAAG,EAAE,OAAO,CAAC,IAAG;YACjE,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;kBAC5D,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,OAAO,KAAK,IAAI;YACtB,MAAM,YAAY,KAAK,KAAK,KAAK,OAAO,qNAAA,CAAA,aAAU,GAAG,yNAAA,CAAA,eAAY;YAEjE,qBACE,6LAAC,4HAAA,CAAA,OAAI;gBAAa,WAAU;;kCAC1B,6LAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;0CAClB,KAAK,KAAK;;;;;;0CAEb,6LAAC;gCAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB,KAAK,OAAO;0CAC/C,cAAA,6LAAC;oCAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,KAAK,KAAK;;;;;;;;;;;;;;;;;kCAG7C,6LAAC,4HAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAsB,KAAK,KAAK;;;;;;0DAC/C,6LAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;kDAGrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,WACA,KAAK,KAAK,KAAK,OAAO,mBAAmB;;;;;;0DAG7C,6LAAC;gDACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uBACA,KAAK,KAAK,KAAK,OAAO,mBAAmB;0DAG1C,KAAK,MAAM;;;;;;;;;;;;;;;;;;4BAMjB,UAAU,mBACT,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,WAAQ;oCAAC,OAAO;oCAAgB,WAAU;;;;;;;;;;;;;;;;;;eAtCxC;;;;;QA4Cf;;;;;;AAGN;KAzGgB;AAqHT,SAAS,aAAa,KAOT;QAPS,EAC3B,KAAK,EACL,GAAG,EACH,OAAO,GAAG,EACV,cAAc,CAAC,EACf,SAAS,EACT,QAAQ,EACU,GAPS;IAQ3B,MAAM,SAAS,CAAC,OAAO,WAAW,IAAI;IACtC,MAAM,gBAAgB,SAAS,IAAI,KAAK,EAAE;IAC1C,MAAM,aAAa,AAAC,QAAQ,MAAO;IACnC,MAAM,kBAAkB;IACxB,MAAM,mBAAmB,gBAAgB,AAAC,aAAa,MAAO;IAE9D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;;0BACrE,6LAAC;gBACC,OAAO;gBACP,QAAQ;gBACR,WAAU;;kCAGV,6LAAC;wBACC,IAAI,OAAO;wBACX,IAAI,OAAO;wBACX,GAAG;wBACH,QAAO;wBACP,aAAa;wBACb,MAAK;wBACL,WAAU;;;;;;kCAGZ,6LAAC;wBACC,IAAI,OAAO;wBACX,IAAI,OAAO;wBACX,GAAG;wBACH,QAAO;wBACP,aAAa;wBACb,MAAK;wBACL,iBAAiB;wBACjB,kBAAkB;wBAClB,WAAU;wBACV,eAAc;;;;;;;;;;;;0BAGlB,6LAAC;gBAAI,WAAU;0BACZ,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAAsB,WAAW,OAAO,CAAC;gCAAG;;;;;;;sCAC3D,6LAAC;4BAAI,WAAU;;gCACZ;gCAAM;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;MAzDgB;AAmET,SAAS,gBAAgB,KAKT;QALS,EAC9B,KAAK,EACL,KAAK,EACL,QAAQ,QAAQ,EAChB,SAAS,EACY,GALS;IAM9B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;;0BAChC,6LAAC;gBAAI,WAAU;0BACZ,MAAM,cAAc;;;;;;0BAEvB,6LAAC;gBAAI,WAAU;0BAAiC;;;;;;YAC/C,UAAU,0BACT,6LAAC;gBAAI,WAAU;;oBACZ,UAAU,qBACT,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;6CAEtB,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCAE1B,6LAAC,6HAAA,CAAA,QAAK;wBACJ,SAAS,UAAU,OAAO,YAAY;wBACtC,WAAU;kCAET,UAAU,OAAO,MAAM;;;;;;;;;;;;;;;;;;AAMpC;MA7BgB", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/dashboard/weather-widget.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  Cloud, \n  CloudRain, \n  Sun, \n  CloudSnow, \n  Wind, \n  Droplets, \n  Thermometer,\n  Eye,\n  Gauge\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface WeatherData {\n  location: string\n  temperature: number\n  condition: string\n  humidity: number\n  windSpeed: number\n  visibility: number\n  pressure: number\n  feelsLike: number\n  uvIndex: number\n  icon: string\n  lastUpdated: string\n}\n\n// Fallback weather data for Tanauan, Leyte\nconst fallbackWeatherData: WeatherData = {\n  location: \"Tanauan, Leyte\",\n  temperature: 28,\n  condition: \"Partly Cloudy\",\n  humidity: 75,\n  windSpeed: 12,\n  visibility: 10,\n  pressure: 1013,\n  feelsLike: 32,\n  uvIndex: 7,\n  icon: \"partly-cloudy\",\n  lastUpdated: new Date().toLocaleTimeString()\n}\n\nconst weatherIcons = {\n  \"sunny\": Sun,\n  \"partly-cloudy\": Cloud,\n  \"cloudy\": Cloud,\n  \"rainy\": CloudRain,\n  \"stormy\": CloudRain,\n  \"snowy\": CloudSnow,\n  \"default\": Sun\n}\n\ninterface WeatherWidgetProps {\n  className?: string\n}\n\nexport function WeatherWidget({ className }: WeatherWidgetProps) {\n  const [weather, setWeather] = useState<WeatherData>(fallbackWeatherData)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    // Simulate weather data updates every 30 minutes\n    const interval = setInterval(() => {\n      // In a real implementation, this would fetch from a weather API\n      setWeather(prev => ({\n        ...prev,\n        temperature: Math.round(26 + Math.random() * 6), // 26-32°C range\n        humidity: Math.round(65 + Math.random() * 20), // 65-85% range\n        windSpeed: Math.round(8 + Math.random() * 8), // 8-16 km/h range\n        lastUpdated: new Date().toLocaleTimeString()\n      }))\n    }, 30 * 60 * 1000) // 30 minutes\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const WeatherIcon = weatherIcons[weather.icon as keyof typeof weatherIcons] || weatherIcons.default\n\n  const getTemperatureColor = (temp: number) => {\n    if (temp >= 30) return \"text-red-500\"\n    if (temp >= 25) return \"text-orange-500\"\n    if (temp >= 20) return \"text-yellow-500\"\n    return \"text-blue-500\"\n  }\n\n  const getUVIndexColor = (uv: number) => {\n    if (uv >= 8) return \"text-red-500\"\n    if (uv >= 6) return \"text-orange-500\"\n    if (uv >= 3) return \"text-yellow-500\"\n    return \"text-green-500\"\n  }\n\n  return (\n    <Card className={cn(\"\", className)}>\n      <CardHeader className=\"pb-3\">\n        <CardTitle className=\"text-lg flex items-center justify-between\">\n          <span>Weather</span>\n          <Badge variant=\"outline\" className=\"text-xs\">\n            {weather.location}\n          </Badge>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Main weather display */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"p-2 rounded-lg bg-blue-50\">\n              <WeatherIcon className=\"h-8 w-8 text-blue-600\" />\n            </div>\n            <div>\n              <div className={cn(\"text-3xl font-bold\", getTemperatureColor(weather.temperature))}>\n                {weather.temperature}°C\n              </div>\n              <div className=\"text-sm text-muted-foreground\">\n                {weather.condition}\n              </div>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-sm text-muted-foreground\">Feels like</div>\n            <div className=\"text-lg font-semibold\">\n              {weather.feelsLike}°C\n            </div>\n          </div>\n        </div>\n\n        {/* Weather details grid */}\n        <div className=\"grid grid-cols-2 gap-3 text-sm\">\n          <div className=\"flex items-center space-x-2\">\n            <Droplets className=\"h-4 w-4 text-blue-500\" />\n            <div>\n              <div className=\"font-medium\">{weather.humidity}%</div>\n              <div className=\"text-muted-foreground\">Humidity</div>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <Wind className=\"h-4 w-4 text-gray-500\" />\n            <div>\n              <div className=\"font-medium\">{weather.windSpeed} km/h</div>\n              <div className=\"text-muted-foreground\">Wind</div>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <Eye className=\"h-4 w-4 text-green-500\" />\n            <div>\n              <div className=\"font-medium\">{weather.visibility} km</div>\n              <div className=\"text-muted-foreground\">Visibility</div>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <Gauge className=\"h-4 w-4 text-purple-500\" />\n            <div>\n              <div className=\"font-medium\">{weather.pressure} hPa</div>\n              <div className=\"text-muted-foreground\">Pressure</div>\n            </div>\n          </div>\n        </div>\n\n        {/* UV Index */}\n        <div className=\"flex items-center justify-between p-2 rounded-lg bg-muted/50\">\n          <div className=\"flex items-center space-x-2\">\n            <Sun className=\"h-4 w-4 text-yellow-500\" />\n            <span className=\"text-sm font-medium\">UV Index</span>\n          </div>\n          <Badge \n            variant=\"outline\" \n            className={cn(\"font-bold\", getUVIndexColor(weather.uvIndex))}\n          >\n            {weather.uvIndex}\n          </Badge>\n        </div>\n\n        {/* Last updated */}\n        <div className=\"text-xs text-muted-foreground text-center pt-2 border-t\">\n          Last updated: {weather.lastUpdated}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Compact weather widget for smaller spaces\nexport function CompactWeatherWidget({ className }: WeatherWidgetProps) {\n  const [weather] = useState<WeatherData>(fallbackWeatherData)\n  const WeatherIcon = weatherIcons[weather.icon as keyof typeof weatherIcons] || weatherIcons.default\n\n  return (\n    <div className={cn(\"flex items-center space-x-3 p-3 rounded-lg bg-muted/50\", className)}>\n      <WeatherIcon className=\"h-6 w-6 text-blue-600\" />\n      <div>\n        <div className=\"font-semibold\">{weather.temperature}°C</div>\n        <div className=\"text-xs text-muted-foreground\">{weather.condition}</div>\n      </div>\n      <div className=\"text-xs text-muted-foreground\">\n        {weather.location}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAhBA;;;;;;AAgCA,2CAA2C;AAC3C,MAAM,sBAAmC;IACvC,UAAU;IACV,aAAa;IACb,WAAW;IACX,UAAU;IACV,WAAW;IACX,YAAY;IACZ,UAAU;IACV,WAAW;IACX,SAAS;IACT,MAAM;IACN,aAAa,IAAI,OAAO,kBAAkB;AAC5C;AAEA,MAAM,eAAe;IACnB,SAAS,mMAAA,CAAA,MAAG;IACZ,iBAAiB,uMAAA,CAAA,QAAK;IACtB,UAAU,uMAAA,CAAA,QAAK;IACf,SAAS,mNAAA,CAAA,YAAS;IAClB,UAAU,mNAAA,CAAA,YAAS;IACnB,SAAS,mNAAA,CAAA,YAAS;IAClB,WAAW,mMAAA,CAAA,MAAG;AAChB;AAMO,SAAS,cAAc,KAAiC;QAAjC,EAAE,SAAS,EAAsB,GAAjC;;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,iDAAiD;YACjD,MAAM,WAAW;oDAAY;oBAC3B,gEAAgE;oBAChE;4DAAW,CAAA,OAAQ,CAAC;gCAClB,GAAG,IAAI;gCACP,aAAa,KAAK,KAAK,CAAC,KAAK,KAAK,MAAM,KAAK;gCAC7C,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,MAAM,KAAK;gCAC1C,WAAW,KAAK,KAAK,CAAC,IAAI,KAAK,MAAM,KAAK;gCAC1C,aAAa,IAAI,OAAO,kBAAkB;4BAC5C,CAAC;;gBACH;mDAAG,KAAK,KAAK,MAAM,aAAa;;YAEhC;2CAAO,IAAM,cAAc;;QAC7B;kCAAG,EAAE;IAEL,MAAM,cAAc,YAAY,CAAC,QAAQ,IAAI,CAA8B,IAAI,aAAa,OAAO;IAEnG,MAAM,sBAAsB,CAAC;QAC3B,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,QAAQ,IAAI,OAAO;QACvB,IAAI,QAAQ,IAAI,OAAO;QACvB,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,GAAG,OAAO;QACpB,IAAI,MAAM,GAAG,OAAO;QACpB,IAAI,MAAM,GAAG,OAAO;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,IAAI;;0BACtB,6LAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,4HAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;sCAAK;;;;;;sCACN,6LAAC,6HAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;sCAChC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;0BAIvB,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAY,WAAU;;;;;;;;;;;kDAEzB,6LAAC;;0DACC,6LAAC;gDAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB,oBAAoB,QAAQ,WAAW;;oDAC7E,QAAQ,WAAW;oDAAC;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,SAAS;;;;;;;;;;;;;;;;;;0CAIxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgC;;;;;;kDAC/C,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,SAAS;4CAAC;;;;;;;;;;;;;;;;;;;kCAMzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,QAAQ,QAAQ;oDAAC;;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,QAAQ,SAAS;oDAAC;;;;;;;0DAChD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,QAAQ,UAAU;oDAAC;;;;;;;0DACjD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,QAAQ,QAAQ;oDAAC;;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAExC,6LAAC,6HAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa,gBAAgB,QAAQ,OAAO;0CAEzD,QAAQ,OAAO;;;;;;;;;;;;kCAKpB,6LAAC;wBAAI,WAAU;;4BAA0D;4BACxD,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;AAK5C;GA/HgB;KAAA;AAkIT,SAAS,qBAAqB,KAAiC;QAAjC,EAAE,SAAS,EAAsB,GAAjC;;IACnC,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxC,MAAM,cAAc,YAAY,CAAC,QAAQ,IAAI,CAA8B,IAAI,aAAa,OAAO;IAEnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;;0BAC3E,6LAAC;gBAAY,WAAU;;;;;;0BACvB,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;4BAAiB,QAAQ,WAAW;4BAAC;;;;;;;kCACpD,6LAAC;wBAAI,WAAU;kCAAiC,QAAQ,SAAS;;;;;;;;;;;;0BAEnE,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,QAAQ;;;;;;;;;;;;AAIzB;IAhBgB;MAAA", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/dashboard/attendance-charts.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  LineChart,\n  Line,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  <PERSON>lt<PERSON>,\n  Legend,\n  ResponsiveContainer\n} from \"recharts\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\n\n// Weekly trend chart\ninterface WeeklyTrendChartProps {\n  data: Array<{\n    day: string\n    present: number\n    late: number\n    absent: number\n  }>\n}\n\nexport function WeeklyTrendChart({ data }: WeeklyTrendChartProps) {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Weekly Attendance Trend</CardTitle>\n        <CardDescription>Daily attendance patterns for this week</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <ResponsiveContainer width=\"100%\" height={300}>\n          <AreaChart data={data}>\n            <CartesianGrid strokeDasharray=\"3 3\" />\n            <XAxis dataKey=\"day\" />\n            <YAxis />\n            <Tooltip />\n            <Legend />\n            <Area\n              type=\"monotone\"\n              dataKey=\"present\"\n              stackId=\"1\"\n              stroke=\"#22c55e\"\n              fill=\"#22c55e\"\n              fillOpacity={0.8}\n            />\n            <Area\n              type=\"monotone\"\n              dataKey=\"late\"\n              stackId=\"1\"\n              stroke=\"#f59e0b\"\n              fill=\"#f59e0b\"\n              fillOpacity={0.8}\n            />\n            <Area\n              type=\"monotone\"\n              dataKey=\"absent\"\n              stackId=\"1\"\n              stroke=\"#ef4444\"\n              fill=\"#ef4444\"\n              fillOpacity={0.8}\n            />\n          </AreaChart>\n        </ResponsiveContainer>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Grade level breakdown chart\ninterface GradeBreakdownChartProps {\n  data: Array<{\n    grade: string\n    total: number\n    present: number\n    late: number\n    absent: number\n  }>\n}\n\nexport function GradeBreakdownChart({ data }: GradeBreakdownChartProps) {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Grade Level Breakdown</CardTitle>\n        <CardDescription>Attendance by grade level</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <ResponsiveContainer width=\"100%\" height={300}>\n          <BarChart data={data}>\n            <CartesianGrid strokeDasharray=\"3 3\" />\n            <XAxis dataKey=\"grade\" />\n            <YAxis />\n            <Tooltip />\n            <Legend />\n            <Bar dataKey=\"present\" fill=\"#22c55e\" name=\"Present\" />\n            <Bar dataKey=\"late\" fill=\"#f59e0b\" name=\"Late\" />\n            <Bar dataKey=\"absent\" fill=\"#ef4444\" name=\"Absent\" />\n          </BarChart>\n        </ResponsiveContainer>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Attendance rate pie chart\ninterface AttendanceRatePieChartProps {\n  present: number\n  late: number\n  absent: number\n}\n\nexport function AttendanceRatePieChart({ present, late, absent }: AttendanceRatePieChartProps) {\n  const data = [\n    { name: \"Present\", value: present, color: \"#22c55e\" },\n    { name: \"Late\", value: late, color: \"#f59e0b\" },\n    { name: \"Absent\", value: absent, color: \"#ef4444\" }\n  ]\n\n  const total = present + late + absent\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Today&apos;s Attendance Distribution</CardTitle>\n        <CardDescription>Current attendance breakdown</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"flex items-center justify-between\">\n          <ResponsiveContainer width=\"60%\" height={200}>\n            <PieChart>\n              <Pie\n                data={data}\n                cx=\"50%\"\n                cy=\"50%\"\n                innerRadius={40}\n                outerRadius={80}\n                paddingAngle={5}\n                dataKey=\"value\"\n              >\n                {data.map((entry, index) => (\n                  <Cell key={`cell-${index}`} fill={entry.color} />\n                ))}\n              </Pie>\n              <Tooltip />\n            </PieChart>\n          </ResponsiveContainer>\n          <div className=\"space-y-2\">\n            {data.map((entry, index) => (\n              <div key={index} className=\"flex items-center space-x-2\">\n                <div\n                  className=\"w-3 h-3 rounded-full\"\n                  style={{ backgroundColor: entry.color }}\n                />\n                <div className=\"text-sm\">\n                  <div className=\"font-medium\">{entry.name}</div>\n                  <div className=\"text-muted-foreground\">\n                    {entry.value} ({((entry.value / total) * 100).toFixed(1)}%)\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Monthly trend line chart\ninterface MonthlyTrendChartProps {\n  data: Array<{\n    date: string\n    attendanceRate: number\n    totalStudents: number\n  }>\n}\n\nexport function MonthlyTrendChart({ data }: MonthlyTrendChartProps) {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Monthly Attendance Trend</CardTitle>\n        <CardDescription>Attendance rate over the past month</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <ResponsiveContainer width=\"100%\" height={300}>\n          <LineChart data={data}>\n            <CartesianGrid strokeDasharray=\"3 3\" />\n            <XAxis dataKey=\"date\" />\n            <YAxis domain={[80, 100]} />\n            <Tooltip />\n            <Line\n              type=\"monotone\"\n              dataKey=\"attendanceRate\"\n              stroke=\"#3b82f6\"\n              strokeWidth={2}\n              dot={{ fill: \"#3b82f6\", strokeWidth: 2, r: 4 }}\n            />\n          </LineChart>\n        </ResponsiveContainer>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Attendance heat map component\ninterface AttendanceHeatMapProps {\n  data: Array<{\n    day: string\n    hour: number\n    count: number\n  }>\n}\n\nexport function AttendanceHeatMap({ data }: AttendanceHeatMapProps) {\n  const days = [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\"]\n  const hours = Array.from({ length: 10 }, (_, i) => i + 7) // 7 AM to 4 PM\n\n  const getIntensity = (day: string, hour: number) => {\n    const entry = data.find(d => d.day === day && d.hour === hour)\n    return entry ? entry.count : 0\n  }\n\n  const maxCount = Math.max(...data.map(d => d.count))\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Attendance Heat Map</CardTitle>\n        <CardDescription>Student check-in patterns by day and hour</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-2\">\n          <div className=\"flex space-x-1\">\n            <div className=\"w-12\"></div>\n            {hours.map(hour => (\n              <div key={hour} className=\"w-8 text-xs text-center text-muted-foreground\">\n                {hour}\n              </div>\n            ))}\n          </div>\n          {days.map(day => (\n            <div key={day} className=\"flex items-center space-x-1\">\n              <div className=\"w-12 text-xs text-muted-foreground\">{day}</div>\n              {hours.map(hour => {\n                const count = getIntensity(day, hour)\n                const intensity = count / maxCount\n                return (\n                  <div\n                    key={`${day}-${hour}`}\n                    className=\"w-8 h-6 rounded-sm border border-border\"\n                    style={{\n                      backgroundColor: `rgba(34, 197, 94, ${intensity})`,\n                    }}\n                    title={`${day} ${hour}:00 - ${count} students`}\n                  />\n                )\n              })}\n            </div>\n          ))}\n        </div>\n        <div className=\"flex items-center justify-between mt-4 text-xs text-muted-foreground\">\n          <span>Less</span>\n          <div className=\"flex space-x-1\">\n            {[0, 0.2, 0.4, 0.6, 0.8, 1].map(intensity => (\n              <div\n                key={intensity}\n                className=\"w-3 h-3 rounded-sm border border-border\"\n                style={{\n                  backgroundColor: `rgba(34, 197, 94, ${intensity})`,\n                }}\n              />\n            ))}\n          </div>\n          <span>More</span>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAnBA;;;;AAgCO,SAAS,iBAAiB,KAA+B;QAA/B,EAAE,IAAI,EAAyB,GAA/B;IAC/B,qBACE,6LAAC,4HAAA,CAAA,OAAI;;0BACH,6LAAC,4HAAA,CAAA,aAAU;;kCACT,6LAAC,4HAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,4HAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,6LAAC,4HAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;;;;;;0CAC/B,6LAAC,wJAAA,CAAA,QAAK;gCAAC,SAAQ;;;;;;0CACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0CACN,6LAAC,0JAAA,CAAA,UAAO;;;;;0CACR,6LAAC,yJAAA,CAAA,SAAM;;;;;0CACP,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,SAAQ;gCACR,QAAO;gCACP,MAAK;gCACL,aAAa;;;;;;0CAEf,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,SAAQ;gCACR,QAAO;gCACP,MAAK;gCACL,aAAa;;;;;;0CAEf,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,SAAQ;gCACR,QAAO;gCACP,MAAK;gCACL,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;KA5CgB;AAyDT,SAAS,oBAAoB,KAAkC;QAAlC,EAAE,IAAI,EAA4B,GAAlC;IAClC,qBACE,6LAAC,4HAAA,CAAA,OAAI;;0BACH,6LAAC,4HAAA,CAAA,aAAU;;kCACT,6LAAC,4HAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,4HAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,6LAAC,4HAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wBAAC,MAAM;;0CACd,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;;;;;;0CAC/B,6LAAC,wJAAA,CAAA,QAAK;gCAAC,SAAQ;;;;;;0CACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0CACN,6LAAC,0JAAA,CAAA,UAAO;;;;;0CACR,6LAAC,yJAAA,CAAA,SAAM;;;;;0CACP,6LAAC,sJAAA,CAAA,MAAG;gCAAC,SAAQ;gCAAU,MAAK;gCAAU,MAAK;;;;;;0CAC3C,6LAAC,sJAAA,CAAA,MAAG;gCAAC,SAAQ;gCAAO,MAAK;gCAAU,MAAK;;;;;;0CACxC,6LAAC,sJAAA,CAAA,MAAG;gCAAC,SAAQ;gCAAS,MAAK;gCAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtD;MAvBgB;AAgCT,SAAS,uBAAuB,KAAsD;QAAtD,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAA+B,GAAtD;IACrC,MAAM,OAAO;QACX;YAAE,MAAM;YAAW,OAAO;YAAS,OAAO;QAAU;QACpD;YAAE,MAAM;YAAQ,OAAO;YAAM,OAAO;QAAU;QAC9C;YAAE,MAAM;YAAU,OAAO;YAAQ,OAAO;QAAU;KACnD;IAED,MAAM,QAAQ,UAAU,OAAO;IAE/B,qBACE,6LAAC,4HAAA,CAAA,OAAI;;0BACH,6LAAC,4HAAA,CAAA,aAAU;;kCACT,6LAAC,4HAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,4HAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,6LAAC,4HAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,sKAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAM,QAAQ;sCACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;kDACP,6LAAC,kJAAA,CAAA,MAAG;wCACF,MAAM;wCACN,IAAG;wCACH,IAAG;wCACH,aAAa;wCACb,aAAa;wCACb,cAAc;wCACd,SAAQ;kDAEP,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC,uJAAA,CAAA,OAAI;gDAAuB,MAAM,MAAM,KAAK;+CAAlC,AAAC,QAAa,OAAN;;;;;;;;;;kDAGvB,6LAAC,0JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;sCAGZ,6LAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,MAAM,KAAK;4CAAC;;;;;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAe,MAAM,IAAI;;;;;;8DACxC,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,KAAK;wDAAC;wDAAG,CAAC,AAAC,MAAM,KAAK,GAAG,QAAS,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;mCARrD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBxB;MAvDgB;AAkET,SAAS,kBAAkB,KAAgC;QAAhC,EAAE,IAAI,EAA0B,GAAhC;IAChC,qBACE,6LAAC,4HAAA,CAAA,OAAI;;0BACH,6LAAC,4HAAA,CAAA,aAAU;;kCACT,6LAAC,4HAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,4HAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,6LAAC,4HAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;;;;;;0CAC/B,6LAAC,wJAAA,CAAA,QAAK;gCAAC,SAAQ;;;;;;0CACf,6LAAC,wJAAA,CAAA,QAAK;gCAAC,QAAQ;oCAAC;oCAAI;iCAAI;;;;;;0CACxB,6LAAC,0JAAA,CAAA,UAAO;;;;;0CACR,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3D;MA1BgB;AAqCT,SAAS,kBAAkB,KAAgC;QAAhC,EAAE,IAAI,EAA0B,GAAhC;IAChC,MAAM,OAAO;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IAChD,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,eAAe;;IAEzE,MAAM,eAAe,CAAC,KAAa;QACjC,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,OAAO,EAAE,IAAI,KAAK;QACzD,OAAO,QAAQ,MAAM,KAAK,GAAG;IAC/B;IAEA,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IAElD,qBACE,6LAAC,4HAAA,CAAA,OAAI;;0BACH,6LAAC,4HAAA,CAAA,aAAU;;kCACT,6LAAC,4HAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,4HAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,6LAAC,4HAAA,CAAA,cAAW;;kCACV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;oCACd,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC;4CAAe,WAAU;sDACvB;2CADO;;;;;;;;;;;4BAKb,KAAK,GAAG,CAAC,CAAA,oBACR,6LAAC;oCAAc,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;wCACpD,MAAM,GAAG,CAAC,CAAA;4CACT,MAAM,QAAQ,aAAa,KAAK;4CAChC,MAAM,YAAY,QAAQ;4CAC1B,qBACE,6LAAC;gDAEC,WAAU;gDACV,OAAO;oDACL,iBAAiB,AAAC,qBAA8B,OAAV,WAAU;gDAClD;gDACA,OAAO,AAAC,GAAS,OAAP,KAAI,KAAgB,OAAb,MAAK,UAAc,OAAN,OAAM;+CAL/B,AAAC,GAAS,OAAP,KAAI,KAAQ,OAAL;;;;;wCAQrB;;mCAfQ;;;;;;;;;;;kCAmBd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAK;oCAAK;oCAAK;oCAAK;iCAAE,CAAC,GAAG,CAAC,CAAA,0BAC9B,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,iBAAiB,AAAC,qBAA8B,OAAV,WAAU;wCAClD;uCAJK;;;;;;;;;;0CAQX,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;MAjEgB", "debugId": null}}, {"offset": {"line": 1753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/data/mock-data.ts"], "sourcesContent": ["import { Student, Subject, TimePeriod, AttendanceRecord } from \"@/lib/types/scanner\"\n\n// Enhanced mock student data with Philippine names and Grade 7-12 structure\nexport const mockStudents: Student[] = [\n  // Grade 7 Students\n  {\n    id: \"STU001\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-A\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU001_2025\"\n  },\n  {\n    id: \"STU002\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-B\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU002_2025\"\n  },\n  {\n    id: \"STU003\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-A\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU003_2025\"\n  },\n  // Grade 8 Students\n  {\n    id: \"STU004\",\n    name: \"Jose Miguel Rodriguez\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"2nd Year\",\n    section: \"Grade 8-A\",\n    grade: \"8\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU004_2025\"\n  },\n  {\n    id: \"STU005\",\n    name: \"Princess Mae Garcia\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"2nd Year\",\n    section: \"Grade 8-B\",\n    grade: \"8\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU005_2025\"\n  },\n  // Grade 9 Students\n  {\n    id: \"STU006\",\n    name: \"Mark Anthony Villanueva\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"3rd Year\",\n    section: \"Grade 9-A\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU006_2025\"\n  },\n  {\n    id: \"STU007\",\n    name: \"Angelica Mae Torres\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"3rd Year\",\n    section: \"Grade 9-B\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU007_2025\"\n  },\n  // Grade 10 Students\n  {\n    id: \"STU008\",\n    name: \"Christian Paul Mendoza\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"4th Year\",\n    section: \"Grade 10-A\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU008_2025\"\n  },\n  {\n    id: \"STU009\",\n    name: \"Kimberly Rose Flores\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"4th Year\",\n    section: \"Grade 10-B\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU009_2025\"\n  },\n  // Grade 11 Students (Senior High School)\n  {\n    id: \"STU010\",\n    name: \"John Michael Cruz\",\n    email: \"<EMAIL>\",\n    course: \"Information and Communications Technology\",\n    year: \"1st Year Senior High\",\n    section: \"ICT 11-A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU010_2025\"\n  },\n  {\n    id: \"STU011\",\n    name: \"Mary Grace Aquino\",\n    email: \"<EMAIL>\",\n    course: \"Accountancy, Business and Management\",\n    year: \"1st Year Senior High\",\n    section: \"ABM 11-A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU011_2025\"\n  },\n  // Grade 12 Students (Senior High School)\n  {\n    id: \"STU012\",\n    name: \"Ryan James Bautista\",\n    email: \"<EMAIL>\",\n    course: \"Information and Communications Technology\",\n    year: \"2nd Year Senior High\",\n    section: \"ICT 12-A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU012_2025\"\n  },\n  {\n    id: \"STU013\",\n    name: \"Sarah Jane Morales\",\n    email: \"<EMAIL>\",\n    course: \"Humanities and Social Sciences\",\n    year: \"2nd Year Senior High\",\n    section: \"HUMSS 12-A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU013_2025\"\n  }\n]\n\n// Mock subjects\nexport const mockSubjects: Subject[] = [\n  {\n    id: \"SUBJ001\",\n    name: \"Programming Fundamentals\",\n    code: \"IT101\",\n    instructor: \"Prof. Martinez\",\n    schedule: [\n      { day: \"Monday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Wednesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Friday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ002\",\n    name: \"Database Management\",\n    code: \"IT201\",\n    instructor: \"Prof. Rodriguez\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"10:00\", endTime: \"12:00\" },\n      { day: \"Thursday\", startTime: \"10:00\", endTime: \"12:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ003\",\n    name: \"Web Development\",\n    code: \"IT301\",\n    instructor: \"Prof. Santos\",\n    schedule: [\n      { day: \"Monday\", startTime: \"13:00\", endTime: \"15:00\" },\n      { day: \"Wednesday\", startTime: \"13:00\", endTime: \"15:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ004\",\n    name: \"Data Structures\",\n    code: \"CS201\",\n    instructor: \"Prof. Reyes\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Thursday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ005\",\n    name: \"Software Engineering\",\n    code: \"CS301\",\n    instructor: \"Prof. Cruz\",\n    schedule: [\n      { day: \"Monday\", startTime: \"15:00\", endTime: \"17:00\" },\n      { day: \"Friday\", startTime: \"15:00\", endTime: \"17:00\" }\n    ]\n  }\n]\n\n// Mock time periods\nexport const mockTimePeriods: TimePeriod[] = [\n  {\n    id: \"PERIOD001\",\n    name: \"1st Period\",\n    startTime: \"08:00\",\n    endTime: \"10:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD002\",\n    name: \"2nd Period\",\n    startTime: \"10:00\",\n    endTime: \"12:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD003\",\n    name: \"3rd Period\",\n    startTime: \"13:00\",\n    endTime: \"15:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD004\",\n    name: \"4th Period\",\n    startTime: \"15:00\",\n    endTime: \"17:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD005\",\n    name: \"Evening Class\",\n    startTime: \"18:00\",\n    endTime: \"20:00\",\n    type: \"evening\"\n  }\n]\n\n// Enhanced mock attendance records with realistic patterns\nexport const mockAttendanceRecords: AttendanceRecord[] = [\n  // Today's attendance records\n  {\n    id: \"ATT001\",\n    studentId: \"STU001\",\n    studentName: \"Maria Cristina Santos\",\n    course: \"Junior High School\",\n    checkIn: \"7:45 AM\",\n    checkOut: \"4:30 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 45, 0))\n  },\n  {\n    id: \"ATT002\",\n    studentId: \"STU002\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    course: \"Junior High School\",\n    checkIn: \"7:50 AM\",\n    checkOut: \"4:25 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 50, 0))\n  },\n  {\n    id: \"ATT003\",\n    studentId: \"STU003\",\n    studentName: \"Ana Marie Reyes\",\n    course: \"Junior High School\",\n    checkIn: \"8:15 AM\",\n    checkOut: \"4:35 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(8, 15, 0))\n  },\n  {\n    id: \"ATT004\",\n    studentId: \"STU004\",\n    studentName: \"Jose Miguel Rodriguez\",\n    course: \"Junior High School\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Absent\",\n    type: \"subject\",\n    subject: \"Mathematics\",\n    period: \"1st Period\",\n    timestamp: new Date(new Date().setHours(8, 0, 0))\n  },\n  {\n    id: \"ATT005\",\n    studentId: \"STU005\",\n    studentName: \"Princess Mae Garcia\",\n    course: \"Junior High School\",\n    checkIn: \"7:55 AM\",\n    checkOut: \"4:20 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 55, 0))\n  },\n  {\n    id: \"ATT006\",\n    studentId: \"STU010\",\n    studentName: \"John Michael Cruz\",\n    course: \"Information and Communications Technology\",\n    checkIn: \"7:40 AM\",\n    checkOut: \"5:00 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 40, 0))\n  },\n  {\n    id: \"ATT007\",\n    studentId: \"STU012\",\n    studentName: \"Ryan James Bautista\",\n    course: \"Information and Communications Technology\",\n    checkIn: \"8:10 AM\",\n    checkOut: \"5:05 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(8, 10, 0))\n  }\n]\n\n// Dashboard statistics data\nexport const mockDashboardStats = {\n  totalStudents: 1234,\n  presentToday: 1105,\n  lateToday: 23,\n  absentToday: 106,\n  attendanceRate: 89.5,\n  weeklyTrend: [\n    { day: 'Mon', present: 1150, late: 15, absent: 69 },\n    { day: 'Tue', present: 1120, late: 28, absent: 86 },\n    { day: 'Wed', present: 1105, late: 23, absent: 106 },\n    { day: 'Thu', present: 1140, late: 18, absent: 76 },\n    { day: 'Fri', present: 1095, late: 35, absent: 104 }\n  ],\n  gradeBreakdown: [\n    { grade: '7', total: 180, present: 165, late: 3, absent: 12 },\n    { grade: '8', total: 175, present: 158, late: 4, absent: 13 },\n    { grade: '9', total: 170, present: 155, late: 2, absent: 13 },\n    { grade: '10', total: 165, present: 148, late: 5, absent: 12 },\n    { grade: '11', total: 272, present: 245, late: 6, absent: 21 },\n    { grade: '12', total: 272, present: 234, late: 3, absent: 35 }\n  ]\n}\n\n// Recent activity feed\nexport const mockRecentActivity = [\n  {\n    id: \"ACT001\",\n    type: \"scan\",\n    studentName: \"Maria Cristina Santos\",\n    action: \"Check In\",\n    time: \"2 minutes ago\",\n    status: \"success\"\n  },\n  {\n    id: \"ACT002\",\n    type: \"scan\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    action: \"Check Out\",\n    time: \"5 minutes ago\",\n    status: \"success\"\n  },\n  {\n    id: \"ACT003\",\n    type: \"alert\",\n    studentName: \"Jose Miguel Rodriguez\",\n    action: \"Marked Absent\",\n    time: \"15 minutes ago\",\n    status: \"warning\"\n  },\n  {\n    id: \"ACT004\",\n    type: \"scan\",\n    studentName: \"Princess Mae Garcia\",\n    action: \"Late Arrival\",\n    time: \"25 minutes ago\",\n    status: \"warning\"\n  }\n]\n\n// Helper functions\nexport function findStudentById(id: string): Student | undefined {\n  return mockStudents.find(student => student.id === id)\n}\n\nexport function findStudentByQRCode(qrCode: string): Student | undefined {\n  return mockStudents.find(student => student.qrCode === qrCode)\n}\n\nexport function findSubjectById(id: string): Subject | undefined {\n  return mockSubjects.find(subject => subject.id === id)\n}\n\nexport function findPeriodById(id: string): TimePeriod | undefined {\n  return mockTimePeriods.find(period => period.id === id)\n}\n\nexport function getStudentAttendanceRecords(studentId: string): AttendanceRecord[] {\n  return mockAttendanceRecords.filter(record => record.studentId === studentId)\n}\n\nexport function getTodayAttendanceRecord(studentId: string): AttendanceRecord | undefined {\n  const today = new Date().toISOString().split('T')[0]\n  return mockAttendanceRecords.find(record => \n    record.studentId === studentId && record.date === today\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGO,MAAM,eAA0B;IACrC,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;YACzD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;SAC1D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;CACD;AAGM,MAAM,kBAAgC;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;CACD;AAGM,MAAM,wBAA4C;IACvD,6BAA6B;IAC7B;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,SAAS;QACT,QAAQ;QACR,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,GAAG;IAChD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;CACD;AAGM,MAAM,qBAAqB;IAChC,eAAe;IACf,cAAc;IACd,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,aAAa;QACX;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAI;QACnD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAI;KACpD;IACD,gBAAgB;QACd;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC7D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC7D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;KAC9D;AACH;AAGO,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;CACD;AAGM,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,oBAAoB,MAAc;IAChD,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;AACzD;AAEO,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AACtD;AAEO,SAAS,4BAA4B,SAAiB;IAC3D,OAAO,sBAAsB,MAAM,CAAC,CAAA,SAAU,OAAO,SAAS,KAAK;AACrE;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACpD,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAChC,OAAO,SAAS,KAAK,aAAa,OAAO,IAAI,KAAK;AAEtD", "debugId": null}}, {"offset": {"line": 2297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from \"@/components/ui/tabs\"\nimport { AttendanceStats, ProgressRing, RealTimeCounter } from \"@/components/dashboard/attendance-stats\"\nimport { WeatherWidget, CompactWeatherWidget } from \"@/components/dashboard/weather-widget\"\nimport {\n  WeeklyTrendChart,\n  GradeBreakdownChart,\n  AttendanceRatePieChart,\n  MonthlyTrendChart,\n  AttendanceHeatMap\n} from \"@/components/dashboard/attendance-charts\"\nimport { mockDashboardStats, mockRecentActivity } from \"@/lib/data/mock-data\"\nimport {\n  QrCode,\n  Bell,\n  Settings,\n  RefreshCw,\n  Download,\n  Printer,\n  Activity,\n  AlertTriangle\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\nexport default function DashboardPage() {\n  const [currentTime, setCurrentTime] = useState(new Date())\n  const [isRefreshing, setIsRefreshing] = useState(false)\n\n  // Update time every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date())\n    }, 1000)\n\n    return () => clearInterval(timer)\n  }, [])\n\n  const handleRefresh = async () => {\n    setIsRefreshing(true)\n    // Simulate data refresh\n    await new Promise(resolve => setTimeout(resolve, 1000))\n    setIsRefreshing(false)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header Section */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Dashboard</h1>\n          <p className=\"text-muted-foreground\">\n            Welcome to QRSAMS - Real-time Student Attendance Monitoring\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          {/* Current Time Display */}\n          <div className=\"text-right\">\n            <div className=\"text-2xl font-bold tabular-nums\">\n              {format(currentTime, \"HH:mm:ss\")}\n            </div>\n            <div className=\"text-sm text-muted-foreground\">\n              {format(currentTime, \"EEEE, MMMM d, yyyy\")}\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"flex space-x-2\">\n            <Button variant=\"outline\" size=\"sm\" onClick={handleRefresh} disabled={isRefreshing}>\n              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />\n              Refresh\n            </Button>\n            <Button size=\"sm\">\n              <QrCode className=\"mr-2 h-4 w-4\" />\n              Open Scanner\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Statistics */}\n      <AttendanceStats\n        totalStudents={mockDashboardStats.totalStudents}\n        presentToday={mockDashboardStats.presentToday}\n        lateToday={mockDashboardStats.lateToday}\n        absentToday={mockDashboardStats.absentToday}\n        attendanceRate={mockDashboardStats.attendanceRate}\n      />\n\n      {/* Main Content Grid */}\n      <div className=\"grid gap-6 lg:grid-cols-3\">\n        {/* Left Column - Charts and Analytics */}\n        <div className=\"lg:col-span-2 space-y-6\">\n          <Tabs defaultValue=\"overview\" className=\"space-y-4\">\n            <TabsList className=\"grid w-full grid-cols-4\">\n              <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n              <TabsTrigger value=\"trends\">Trends</TabsTrigger>\n              <TabsTrigger value=\"grades\">Grades</TabsTrigger>\n              <TabsTrigger value=\"patterns\">Patterns</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"overview\" className=\"space-y-4\">\n              <div className=\"grid gap-4 md:grid-cols-2\">\n                <AttendanceRatePieChart\n                  present={mockDashboardStats.presentToday}\n                  late={mockDashboardStats.lateToday}\n                  absent={mockDashboardStats.absentToday}\n                />\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Real-time Counters</CardTitle>\n                    <CardDescription>Live attendance tracking</CardDescription>\n                  </CardHeader>\n                  <CardContent className=\"space-y-6\">\n                    <div className=\"grid grid-cols-2 gap-4\">\n                      <RealTimeCounter\n                        value={mockDashboardStats.presentToday}\n                        label=\"Present\"\n                        trend=\"up\"\n                      />\n                      <RealTimeCounter\n                        value={mockDashboardStats.lateToday}\n                        label=\"Late\"\n                        trend=\"down\"\n                      />\n                    </div>\n                    <div className=\"flex justify-center\">\n                      <ProgressRing\n                        value={mockDashboardStats.presentToday + mockDashboardStats.lateToday}\n                        max={mockDashboardStats.totalStudents}\n                        size={120}\n                      />\n                    </div>\n                  </CardContent>\n                </Card>\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"trends\">\n              <WeeklyTrendChart data={mockDashboardStats.weeklyTrend} />\n            </TabsContent>\n\n            <TabsContent value=\"grades\">\n              <GradeBreakdownChart data={mockDashboardStats.gradeBreakdown} />\n            </TabsContent>\n\n            <TabsContent value=\"patterns\">\n              <AttendanceHeatMap data={[\n                { day: \"Mon\", hour: 7, count: 45 },\n                { day: \"Mon\", hour: 8, count: 120 },\n                { day: \"Tue\", hour: 7, count: 38 },\n                { day: \"Tue\", hour: 8, count: 95 },\n                { day: \"Wed\", hour: 7, count: 52 },\n                { day: \"Wed\", hour: 8, count: 110 },\n                { day: \"Thu\", hour: 7, count: 41 },\n                { day: \"Thu\", hour: 8, count: 88 },\n                { day: \"Fri\", hour: 7, count: 35 },\n                { day: \"Fri\", hour: 8, count: 75 }\n              ]} />\n            </TabsContent>\n          </Tabs>\n        </div>\n\n        {/* Right Column - Sidebar Content */}\n        <div className=\"space-y-6\">\n          {/* Weather Widget */}\n          <WeatherWidget />\n\n          {/* Recent Activity Feed */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between\">\n              <div>\n                <CardTitle className=\"text-lg\">Recent Activity</CardTitle>\n                <CardDescription>Latest scans and alerts</CardDescription>\n              </div>\n              <Button variant=\"ghost\" size=\"sm\">\n                <Bell className=\"h-4 w-4\" />\n              </Button>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {mockRecentActivity.map((activity) => (\n                <div key={activity.id} className=\"flex items-center space-x-3\">\n                  <div className={`w-2 h-2 rounded-full ${\n                    activity.status === 'success' ? 'bg-green-500' :\n                    activity.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'\n                  }`} />\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium truncate\">\n                      {activity.studentName}\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {activity.action} • {activity.time}\n                    </p>\n                  </div>\n                  <Badge\n                    variant={\n                      activity.status === 'success' ? 'default' :\n                      activity.status === 'warning' ? 'secondary' : 'destructive'\n                    }\n                    className=\"text-xs\"\n                  >\n                    {activity.type === 'scan' ? <Activity className=\"h-3 w-3\" /> :\n                     <AlertTriangle className=\"h-3 w-3\" />}\n                  </Badge>\n                </div>\n              ))}\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                View All Activity\n              </Button>\n            </CardContent>\n          </Card>\n\n          {/* System Health */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">System Health</CardTitle>\n              <CardDescription>Real-time system status</CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm\">Scanner Status</span>\n                <Badge variant=\"default\">Online</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm\">Database</span>\n                <Badge variant=\"default\">Connected</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm\">SMS Gateway</span>\n                <Badge variant=\"secondary\">Pending</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm\">Last Sync</span>\n                <span className=\"text-xs text-muted-foreground\">2 min ago</span>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Quick Actions Panel */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Quick Actions</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-2\">\n              <Button className=\"w-full justify-start\" variant=\"outline\">\n                <Download className=\"mr-2 h-4 w-4\" />\n                Export Today&apos;s Data\n              </Button>\n              <Button className=\"w-full justify-start\" variant=\"outline\">\n                <Printer className=\"mr-2 h-4 w-4\" />\n                Print Daily Report\n              </Button>\n              <Button className=\"w-full justify-start\" variant=\"outline\">\n                <Settings className=\"mr-2 h-4 w-4\" />\n                System Settings\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AA3BA;;;;;;;;;;;;AA6Be,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ;iDAAY;oBACxB,eAAe,IAAI;gBACrB;gDAAG;YAEH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,wBAAwB;QACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;kDACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;;;;;;;;;;;;0CAKzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;wCAAe,UAAU;;0DACpE,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAW,AAAC,gBAAkD,OAAnC,eAAe,iBAAiB;;;;;;4CAAQ;;;;;;;kDAGhF,6LAAC,8HAAA,CAAA,SAAM;wCAAC,MAAK;;0DACX,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC,kJAAA,CAAA,kBAAe;gBACd,eAAe,8HAAA,CAAA,qBAAkB,CAAC,aAAa;gBAC/C,cAAc,8HAAA,CAAA,qBAAkB,CAAC,YAAY;gBAC7C,WAAW,8HAAA,CAAA,qBAAkB,CAAC,SAAS;gBACvC,aAAa,8HAAA,CAAA,qBAAkB,CAAC,WAAW;gBAC3C,gBAAgB,8HAAA,CAAA,qBAAkB,CAAC,cAAc;;;;;;0BAInD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4HAAA,CAAA,OAAI;4BAAC,cAAa;4BAAW,WAAU;;8CACtC,6LAAC,4HAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6LAAC,4HAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;sDAC9B,6LAAC,4HAAA,CAAA,cAAW;4CAAC,OAAM;sDAAS;;;;;;sDAC5B,6LAAC,4HAAA,CAAA,cAAW;4CAAC,OAAM;sDAAS;;;;;;sDAC5B,6LAAC,4HAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;;;;;;;8CAGhC,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mJAAA,CAAA,yBAAsB;gDACrB,SAAS,8HAAA,CAAA,qBAAkB,CAAC,YAAY;gDACxC,MAAM,8HAAA,CAAA,qBAAkB,CAAC,SAAS;gDAClC,QAAQ,8HAAA,CAAA,qBAAkB,CAAC,WAAW;;;;;;0DAExC,6LAAC,4HAAA,CAAA,OAAI;;kEACH,6LAAC,4HAAA,CAAA,aAAU;;0EACT,6LAAC,4HAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,4HAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;kEAEnB,6LAAC,4HAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,kJAAA,CAAA,kBAAe;wEACd,OAAO,8HAAA,CAAA,qBAAkB,CAAC,YAAY;wEACtC,OAAM;wEACN,OAAM;;;;;;kFAER,6LAAC,kJAAA,CAAA,kBAAe;wEACd,OAAO,8HAAA,CAAA,qBAAkB,CAAC,SAAS;wEACnC,OAAM;wEACN,OAAM;;;;;;;;;;;;0EAGV,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,kJAAA,CAAA,eAAY;oEACX,OAAO,8HAAA,CAAA,qBAAkB,CAAC,YAAY,GAAG,8HAAA,CAAA,qBAAkB,CAAC,SAAS;oEACrE,KAAK,8HAAA,CAAA,qBAAkB,CAAC,aAAa;oEACrC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQlB,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,mJAAA,CAAA,mBAAgB;wCAAC,MAAM,8HAAA,CAAA,qBAAkB,CAAC,WAAW;;;;;;;;;;;8CAGxD,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,mJAAA,CAAA,sBAAmB;wCAAC,MAAM,8HAAA,CAAA,qBAAkB,CAAC,cAAc;;;;;;;;;;;8CAG9D,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,mJAAA,CAAA,oBAAiB;wCAAC,MAAM;4CACvB;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAG;4CACjC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAI;4CAClC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAG;4CACjC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAG;4CACjC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAG;4CACjC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAI;4CAClC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAG;4CACjC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAG;4CACjC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAG;4CACjC;gDAAE,KAAK;gDAAO,MAAM;gDAAG,OAAO;4CAAG;yCAClC;;;;;;;;;;;;;;;;;;;;;;kCAMP,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,gJAAA,CAAA,gBAAa;;;;;0CAGd,6LAAC,4HAAA,CAAA,OAAI;;kDACH,6LAAC,4HAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;;kEACC,6LAAC,4HAAA,CAAA,YAAS;wDAAC,WAAU;kEAAU;;;;;;kEAC/B,6LAAC,4HAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,6LAAC,8HAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;4CACpB,8HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,yBACvB,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC;4DAAI,WAAW,AAAC,wBAGhB,OAFC,SAAS,MAAM,KAAK,YAAY,iBAChC,SAAS,MAAM,KAAK,YAAY,kBAAkB;;;;;;sEAEpD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,SAAS,WAAW;;;;;;8EAEvB,6LAAC;oEAAE,WAAU;;wEACV,SAAS,MAAM;wEAAC;wEAAI,SAAS,IAAI;;;;;;;;;;;;;sEAGtC,6LAAC,6HAAA,CAAA,QAAK;4DACJ,SACE,SAAS,MAAM,KAAK,YAAY,YAChC,SAAS,MAAM,KAAK,YAAY,cAAc;4DAEhD,WAAU;sEAET,SAAS,IAAI,KAAK,uBAAS,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;qFAC/C,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;;mDArBpB,SAAS,EAAE;;;;;0DAyBvB,6LAAC,8HAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;0CAO3D,6LAAC,4HAAA,CAAA,OAAI;;kDACH,6LAAC,4HAAA,CAAA,aAAU;;0DACT,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,6LAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;;;;;;;0DAE7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAK,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;0CAMtD,6LAAC,4HAAA,CAAA,OAAI;;kDACH,6LAAC,4HAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;;;;;;kDAEjC,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,8HAAA,CAAA,SAAM;gDAAC,WAAU;gDAAuB,SAAQ;;kEAC/C,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,6LAAC,8HAAA,CAAA,SAAM;gDAAC,WAAU;gDAAuB,SAAQ;;kEAC/C,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGtC,6LAAC,8HAAA,CAAA,SAAM;gDAAC,WAAU;gDAAuB,SAAQ;;kEAC/C,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;GA7OwB;KAAA", "debugId": null}}]}