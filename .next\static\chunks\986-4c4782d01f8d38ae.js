"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[986],{1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},15452:(e,t,n)=>{n.d(t,{G$:()=>Y,Hs:()=>w,UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(12115),o=n(85185),a=n(6101),i=n(46081),l=n(61285),u=n(5845),c=n(19178),s=n(25519),d=n(34378),f=n(28905),p=n(63655),v=n(92293),h=n(93795),m=n(38168),y=n(99708),g=n(95155),b="Dialog",[E,w]=(0,i.A)(b),[C,k]=E(b),x=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:b});return(0,g.jsx)(C,{scope:t,triggerRef:s,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};x.displayName=b;var A="DialogTrigger",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=k(A,n),l=(0,a.s)(t,i.triggerRef);return(0,g.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":H(i.open),...r,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});R.displayName=A;var N="DialogPortal",[D,L]=E(N,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,i=k(N,t);return(0,g.jsx)(D,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,g.jsx)(f.C,{present:n||i.open,children:(0,g.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};M.displayName=N;var S="DialogOverlay",O=r.forwardRef((e,t)=>{let n=L(S,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=k(S,e.__scopeDialog);return a.modal?(0,g.jsx)(f.C,{present:r||a.open,children:(0,g.jsx)(P,{...o,ref:t})}):null});O.displayName=S;var j=(0,y.TL)("DialogOverlay.RemoveScroll"),P=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(S,n);return(0,g.jsx)(h.A,{as:j,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(p.sG.div,{"data-state":H(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),T="DialogContent",I=r.forwardRef((e,t)=>{let n=L(T,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=k(T,e.__scopeDialog);return(0,g.jsx)(f.C,{present:r||a.open,children:a.modal?(0,g.jsx)(F,{...o,ref:t}):(0,g.jsx)(W,{...o,ref:t})})});I.displayName=T;var F=r.forwardRef((e,t)=>{let n=k(T,e.__scopeDialog),i=r.useRef(null),l=(0,a.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,m.Eq)(e)},[]),(0,g.jsx)(_,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),W=r.forwardRef((e,t)=>{let n=k(T,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,g.jsx)(_,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(i=n.triggerRef.current)||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,i;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let l=t.target;(null==(i=n.triggerRef.current)?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,d=k(T,n),f=r.useRef(null),p=(0,a.s)(t,f);return(0,v.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,g.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":H(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)($,{titleId:d.titleId}),(0,g.jsx)(V,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(B,n);return(0,g.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});q.displayName=B;var z="DialogDescription",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(z,n);return(0,g.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});G.displayName=z;var K="DialogClose",Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=k(K,n);return(0,g.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function H(e){return e?"open":"closed"}Z.displayName=K;var U="DialogTitleWarning",[Y,X]=(0,i.q)(U,{contentName:T,titleName:B,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,n=X(U),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},V=e=>{let{contentRef:t,descriptionId:n}=e,o=X("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(a))},[a,t,n]),null},J=x,Q=R,ee=M,et=O,en=I,er=q,eo=G,ea=Z},17580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(12115),a=n(85185),i=n(63655),l=n(6101),u=n(39033),c=n(95155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:m,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:b,onDismiss:E,...w}=e,C=o.useContext(d),[k,x]=o.useState(null),A=null!=(f=null==k?void 0:k.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,R]=o.useState({}),N=(0,l.s)(t,e=>x(e)),D=Array.from(C.layers),[L]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),M=D.indexOf(L),S=k?D.indexOf(k):-1,O=C.layersWithOutsidePointerEventsDisabled.size>0,j=S>=M,P=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){v("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));j&&!n&&(null==y||y(e),null==b||b(e),e.defaultPrevented||null==E||E())},A),T=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&v("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==g||g(e),null==b||b(e),e.defaultPrevented||null==E||E())},A);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{S===C.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},A),o.useEffect(()=>{if(k)return h&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(k)),C.layers.add(k),p(),()=>{h&&1===C.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=r)}},[k,A,h,C]),o.useEffect(()=>()=>{k&&(C.layers.delete(k),C.layersWithOutsidePointerEventsDisabled.delete(k),p())},[k,C]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(i.sG.div,{...w,ref:N,style:{pointerEvents:O?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,T.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,P.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function v(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.hO)(a,l):a.dispatchEvent(l)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(i.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},19946:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(12115);let o=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:u,className:c="",children:s,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:n,strokeWidth:u?24*Number(l)/Number(o):l,className:a("lucide",c),...!s&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:u,...c}=n;return(0,r.createElement)(l,{ref:i,iconNode:t,className:a("lucide-".concat(o(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),u),...c})});return n.displayName=o(e),n}},25519:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(12115),o=n(6101),a=n(63655),i=n(39033),l=n(95155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:y,...g}=e,[b,E]=r.useState(null),w=(0,i.c)(m),C=(0,i.c)(y),k=r.useRef(null),x=(0,o.s)(t,e=>E(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(A.paused||!b)return;let t=e.target;b.contains(t)?k.current=t:v(k.current,{select:!0})},t=function(e){if(A.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||v(k.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,b,A.paused]),r.useEffect(()=>{if(b){h.add(A);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,s);b.addEventListener(u,w),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(b))}return()=>{b.removeEventListener(u,w),setTimeout(()=>{let t=new CustomEvent(c,s);b.addEventListener(c,C),b.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),b.removeEventListener(c,C),h.remove(A)},0)}}},[b,w,C,A]);let R=r.useCallback(e=>{if(!n&&!d||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&v(a,{select:!0})):(e.preventDefault(),n&&v(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,A.paused]);return(0,l.jsx)(a.sG.div,{tabIndex:-1,...g,ref:x,onKeyDown:R})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null==(n=(e=m(e,t))[0])||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},34378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(12115),o=n(47650),a=n(63655),i=n(52712),l=n(95155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,i.N)(()=>f(!0),[]);let p=c||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,l.jsx)(a.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},38168:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=new WeakMap,o=new WeakMap,a={},i=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};c.forEach(v);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(u),a=null!==t&&"false"!==t,i=(r.get(e)||0)+1,l=(s.get(e)||0)+1;r.set(e,i),s.set(e,l),d.push(e),1===i&&a&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),a||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),i++,function(){d.forEach(function(e){var t=r.get(e)-1,a=s.get(e)-1;r.set(e,t),s.set(e,a),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),a||e.removeAttribute(n)}),--i||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),u(r,o,n,"aria-hidden")):function(){return null}}},40646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},74466:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:l}=t,u=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let a=o(t)||o(r);return i[e][a]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return a(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...c}[t]):({...l,...c})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},89367:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},92293:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(12115),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:i()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93795:(e,t,n)=>{n.d(t,{A:()=>H});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(12115)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function p(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return o.options=a({async:!0,ssr:!1},e),o}(),h=function(){},m=l.forwardRef(function(e,t){var n,r,o,u,c=l.useRef(null),p=l.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=p[0],y=p[1],g=e.forwardProps,b=e.children,E=e.className,w=e.removeScrollBar,C=e.enabled,k=e.shards,x=e.sideCar,A=e.noRelative,R=e.noIsolation,N=e.inert,D=e.allowPinchZoom,L=e.as,M=e.gapMode,S=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),j=a(a({},S),m);return l.createElement(l.Fragment,null,C&&l.createElement(x,{sideCar:v,removeScrollBar:w,shards:k,noRelative:A,noIsolation:R,inert:N,setCallbacks:y,allowPinchZoom:!!D,lockRef:c,gapMode:M}),g?l.cloneElement(l.Children.only(b),a(a({},j),{ref:O})):l.createElement(void 0===L?"div":L,a({},j,{className:E,ref:O}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:c,zeroRight:u};var y=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,a({},n))};y.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=g();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},E=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},C=function(e){return parseInt(e||"",10)||0},k=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[C(n),C(r),C(o)]},x=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=k(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=E(),R="data-scroll-locked",N=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},D=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},L=function(){l.useEffect(function(){return document.body.setAttribute(R,(D()+1).toString()),function(){var e=D()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},M=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var a=l.useMemo(function(){return x(o)},[o]);return l.createElement(A,{styles:N(a,!t,o,n?"":"!important")})},S=!1;if("undefined"!=typeof window)try{var O=Object.defineProperty({},"passive",{get:function(){return S=!0,!0}});window.addEventListener("test",O,O),window.removeEventListener("test",O,O)}catch(e){S=!1}var j=!!S&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},T=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var v=F(e,u),h=v[0],m=v[1]-v[2]-i*h;(h||m)&&I(e,u)&&(f+=m,p+=h);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},_=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},q=function(e){return e&&"current"in e?e.current:e},z=0,G=[];let K=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(z++)[0],a=l.useState(E)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(q),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=_(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-a[0],c="deltaY"in e?e.deltaY:l[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=T(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=T(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(G.length&&G[G.length-1]===a){var n="deltaY"in e?B(e):_(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(q).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=l.useCallback(function(e){n.current=_(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,_(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return G.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){G=G.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var v=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?l.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},v.useMedium(r),y);var Z=l.forwardRef(function(e,t){return l.createElement(m,a({},e,{ref:t,sideCar:K}))});Z.classNames=m.classNames;let H=Z},97939:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("qr-code",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])}}]);