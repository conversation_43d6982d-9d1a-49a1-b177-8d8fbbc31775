(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{16:(a,b,c)=>{"use strict";c.d(b,{F:()=>e,h:()=>f});let d="DYNAMIC_SERVER_USAGE";class e extends Error{constructor(a){super("Dynamic server usage: "+a),this.description=a,this.digest=d}}function f(a){return"object"==typeof a&&null!==a&&"digest"in a&&"string"==typeof a.digest&&a.digest===d}},35:(a,b)=>{"use strict";var c={H:null,A:null};function d(a){var b="https://react.dev/errors/"+a;if(1<arguments.length){b+="?args[]="+encodeURIComponent(arguments[1]);for(var c=2;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c])}return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var e=Array.isArray;function f(){}var g=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),j=Symbol.for("react.strict_mode"),k=Symbol.for("react.profiler"),l=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),n=Symbol.for("react.memo"),o=Symbol.for("react.lazy"),p=Symbol.iterator,q=Object.prototype.hasOwnProperty,r=Object.assign;function s(a,b,c,d,e,f){return{$$typeof:g,type:a,key:b,ref:void 0!==(c=f.ref)?c:null,props:f}}function t(a){return"object"==typeof a&&null!==a&&a.$$typeof===g}var u=/\/+/g;function v(a,b){var c,d;return"object"==typeof a&&null!==a&&null!=a.key?(c=""+a.key,d={"=":"=0",":":"=2"},"$"+c.replace(/[=:]/g,function(a){return d[a]})):b.toString(36)}function w(a,b,c){if(null==a)return a;var i=[],j=0;return!function a(b,c,i,j,k){var l,m,n,q=typeof b;("undefined"===q||"boolean"===q)&&(b=null);var r=!1;if(null===b)r=!0;else switch(q){case"bigint":case"string":case"number":r=!0;break;case"object":switch(b.$$typeof){case g:case h:r=!0;break;case o:return a((r=b._init)(b._payload),c,i,j,k)}}if(r)return k=k(b),r=""===j?"."+v(b,0):j,e(k)?(i="",null!=r&&(i=r.replace(u,"$&/")+"/"),a(k,c,i,"",function(a){return a})):null!=k&&(t(k)&&(l=k,m=i+(null==k.key||b&&b.key===k.key?"":(""+k.key).replace(u,"$&/")+"/")+r,k=s(l.type,m,void 0,void 0,void 0,l.props)),c.push(k)),1;r=0;var w=""===j?".":j+":";if(e(b))for(var x=0;x<b.length;x++)q=w+v(j=b[x],x),r+=a(j,c,i,q,k);else if("function"==typeof(x=null===(n=b)||"object"!=typeof n?null:"function"==typeof(n=p&&n[p]||n["@@iterator"])?n:null))for(b=x.call(b),x=0;!(j=b.next()).done;)q=w+v(j=j.value,x++),r+=a(j,c,i,q,k);else if("object"===q){if("function"==typeof b.then)return a(function(a){switch(a.status){case"fulfilled":return a.value;case"rejected":throw a.reason;default:switch("string"==typeof a.status?a.then(f,f):(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)})),a.status){case"fulfilled":return a.value;case"rejected":throw a.reason}}throw a}(b),c,i,j,k);throw Error(d(31,"[object Object]"===(c=String(b))?"object with keys {"+Object.keys(b).join(", ")+"}":c))}return r}(a,i,"","",function(a){return b.call(c,a,j++)}),i}function x(a){if(-1===a._status){var b=a._result;(b=b()).then(function(b){(0===a._status||-1===a._status)&&(a._status=1,a._result=b)},function(b){(0===a._status||-1===a._status)&&(a._status=2,a._result=b)}),-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result.default;throw a._result}function y(){return new WeakMap}function z(){return{s:0,v:void 0,o:null,p:null}}b.Children={map:w,forEach:function(a,b,c){w(a,function(){b.apply(this,arguments)},c)},count:function(a){var b=0;return w(a,function(){b++}),b},toArray:function(a){return w(a,function(a){return a})||[]},only:function(a){if(!t(a))throw Error(d(143));return a}},b.Fragment=i,b.Profiler=k,b.StrictMode=j,b.Suspense=m,b.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,b.cache=function(a){return function(){var b=c.A;if(!b)return a.apply(null,arguments);var d=b.getCacheForType(y);void 0===(b=d.get(a))&&(b=z(),d.set(a,b)),d=0;for(var e=arguments.length;d<e;d++){var f=arguments[d];if("function"==typeof f||"object"==typeof f&&null!==f){var g=b.o;null===g&&(b.o=g=new WeakMap),void 0===(b=g.get(f))&&(b=z(),g.set(f,b))}else null===(g=b.p)&&(b.p=g=new Map),void 0===(b=g.get(f))&&(b=z(),g.set(f,b))}if(1===b.s)return b.v;if(2===b.s)throw b.v;try{var h=a.apply(null,arguments);return(d=b).s=1,d.v=h}catch(a){throw(h=b).s=2,h.v=a,a}}},b.cacheSignal=function(){var a=c.A;return a?a.cacheSignal():null},b.captureOwnerStack=function(){return null},b.cloneElement=function(a,b,c){if(null==a)throw Error(d(267,a));var e=r({},a.props),f=a.key,g=void 0;if(null!=b)for(h in void 0!==b.ref&&(g=void 0),void 0!==b.key&&(f=""+b.key),b)q.call(b,h)&&"key"!==h&&"__self"!==h&&"__source"!==h&&("ref"!==h||void 0!==b.ref)&&(e[h]=b[h]);var h=arguments.length-2;if(1===h)e.children=c;else if(1<h){for(var i=Array(h),j=0;j<h;j++)i[j]=arguments[j+2];e.children=i}return s(a.type,f,void 0,void 0,g,e)},b.createElement=function(a,b,c){var d,e={},f=null;if(null!=b)for(d in void 0!==b.key&&(f=""+b.key),b)q.call(b,d)&&"key"!==d&&"__self"!==d&&"__source"!==d&&(e[d]=b[d]);var g=arguments.length-2;if(1===g)e.children=c;else if(1<g){for(var h=Array(g),i=0;i<g;i++)h[i]=arguments[i+2];e.children=h}if(a&&a.defaultProps)for(d in g=a.defaultProps)void 0===e[d]&&(e[d]=g[d]);return s(a,f,void 0,void 0,null,e)},b.createRef=function(){return{current:null}},b.forwardRef=function(a){return{$$typeof:l,render:a}},b.isValidElement=t,b.lazy=function(a){return{$$typeof:o,_payload:{_status:-1,_result:a},_init:x}},b.memo=function(a,b){return{$$typeof:n,type:a,compare:void 0===b?null:b}},b.use=function(a){return c.H.use(a)},b.useCallback=function(a,b){return c.H.useCallback(a,b)},b.useDebugValue=function(){},b.useId=function(){return c.H.useId()},b.useMemo=function(a,b){return c.H.useMemo(a,b)},b.version="19.2.0-canary-97cdd5d3-20250710"},58:(a,b,c)=>{"use strict";c.d(b,{xl:()=>g});let d=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class e{disable(){throw d}getStore(){}run(){throw d}exit(){throw d}enterWith(){throw d}static bind(a){return a}}let f="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function g(){return f?new f:new e}},115:(a,b,c)=>{"use strict";c.d(b,{XN:()=>e,FP:()=>d});let d=(0,c(58).xl)();function e(a){let b=d.getStore();switch(!b&&function(a){throw Object.defineProperty(Error(`\`${a}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(a),b.type){case"request":default:return b;case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},159:(a,b,c)=>{"use strict";c.d(b,{RM:()=>f,s8:()=>e});let d=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401})),e="NEXT_HTTP_ERROR_FALLBACK";function f(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let[b,c]=a.digest.split(";");return b===e&&d.has(Number(c))}},167:(a,b,c)=>{"use strict";c.d(b,{nJ:()=>g,oJ:()=>e,zB:()=>f});var d=c(821);let e="NEXT_REDIRECT";var f=function(a){return a.push="push",a.replace="replace",a}({});function g(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let b=a.digest.split(";"),[c,f]=b,g=b.slice(2,-2).join(";"),h=Number(b.at(-2));return c===e&&("replace"===f||"push"===f)&&"string"==typeof g&&!isNaN(h)&&h in d.Q}},199:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(159),e=c(167);function f(a){return(0,e.nJ)(a)||(0,d.RM)(a)}},201:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getTestReqInfo:function(){return g},withRequest:function(){return f}});let d=new(c(521)).AsyncLocalStorage;function e(a,b){let c=b.header(a,"next-test-proxy-port");if(!c)return;let d=b.url(a);return{url:d,proxyPort:Number(c),testData:b.header(a,"next-test-data")||""}}function f(a,b,c){let f=e(a,b);return f?d.run(f,c):c()}function g(a,b){let c=d.getStore();return c||(a&&b?e(a,b):void 0)}},280:(a,b,c)=>{var d;(()=>{var e={226:function(e,f){!function(g,h){"use strict";var i="function",j="undefined",k="object",l="string",m="major",n="model",o="name",p="type",q="vendor",r="version",s="architecture",t="console",u="mobile",v="tablet",w="smarttv",x="wearable",y="embedded",z="Amazon",A="Apple",B="ASUS",C="BlackBerry",D="Browser",E="Chrome",F="Firefox",G="Google",H="Huawei",I="Microsoft",J="Motorola",K="Opera",L="Samsung",M="Sharp",N="Sony",O="Xiaomi",P="Zebra",Q="Facebook",R="Chromium OS",S="Mac OS",T=function(a,b){var c={};for(var d in a)b[d]&&b[d].length%2==0?c[d]=b[d].concat(a[d]):c[d]=a[d];return c},U=function(a){for(var b={},c=0;c<a.length;c++)b[a[c].toUpperCase()]=a[c];return b},V=function(a,b){return typeof a===l&&-1!==W(b).indexOf(W(a))},W=function(a){return a.toLowerCase()},X=function(a,b){if(typeof a===l)return a=a.replace(/^\s\s*/,""),typeof b===j?a:a.substring(0,350)},Y=function(a,b){for(var c,d,e,f,g,j,l=0;l<b.length&&!g;){var m=b[l],n=b[l+1];for(c=d=0;c<m.length&&!g&&m[c];)if(g=m[c++].exec(a))for(e=0;e<n.length;e++)j=g[++d],typeof(f=n[e])===k&&f.length>0?2===f.length?typeof f[1]==i?this[f[0]]=f[1].call(this,j):this[f[0]]=f[1]:3===f.length?typeof f[1]!==i||f[1].exec&&f[1].test?this[f[0]]=j?j.replace(f[1],f[2]):void 0:this[f[0]]=j?f[1].call(this,j,f[2]):void 0:4===f.length&&(this[f[0]]=j?f[3].call(this,j.replace(f[1],f[2])):h):this[f]=j||h;l+=2}},Z=function(a,b){for(var c in b)if(typeof b[c]===k&&b[c].length>0){for(var d=0;d<b[c].length;d++)if(V(b[c][d],a))return"?"===c?h:c}else if(V(b[c],a))return"?"===c?h:c;return a},$={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},_={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[r,[o,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[r,[o,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[o,r],[/opios[\/ ]+([\w\.]+)/i],[r,[o,K+" Mini"]],[/\bopr\/([\w\.]+)/i],[r,[o,K]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[o,r],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[r,[o,"UC"+D]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[r,[o,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[r,[o,"WeChat"]],[/konqueror\/([\w\.]+)/i],[r,[o,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[r,[o,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[r,[o,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[o,/(.+)/,"$1 Secure "+D],r],[/\bfocus\/([\w\.]+)/i],[r,[o,F+" Focus"]],[/\bopt\/([\w\.]+)/i],[r,[o,K+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[r,[o,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[r,[o,"Dolphin"]],[/coast\/([\w\.]+)/i],[r,[o,K+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[r,[o,"MIUI "+D]],[/fxios\/([-\w\.]+)/i],[r,[o,F]],[/\bqihu|(qi?ho?o?|360)browser/i],[[o,"360 "+D]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[o,/(.+)/,"$1 "+D],r],[/(comodo_dragon)\/([\w\.]+)/i],[[o,/_/g," "],r],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[o,r],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[o],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[o,Q],r],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[o,r],[/\bgsa\/([\w\.]+) .*safari\//i],[r,[o,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[r,[o,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[r,[o,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[o,E+" WebView"],r],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[r,[o,"Android "+D]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[o,r],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[r,[o,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[r,o],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[o,[r,Z,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[o,r],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[o,"Netscape"],r],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[r,[o,F+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[o,r],[/(cobalt)\/([\w\.]+)/i],[o,[r,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[s,"amd64"]],[/(ia32(?=;))/i],[[s,W]],[/((?:i[346]|x)86)[;\)]/i],[[s,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[s,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[s,"armhf"]],[/windows (ce|mobile); ppc;/i],[[s,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[s,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[s,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[s,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[n,[q,L],[p,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[n,[q,L],[p,u]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[n,[q,A],[p,u]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[n,[q,A],[p,v]],[/(macintosh);/i],[n,[q,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[n,[q,M],[p,u]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[n,[q,H],[p,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[n,[q,H],[p,u]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,u]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[n,[q,"OPPO"],[p,u]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[n,[q,"Vivo"],[p,u]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[n,[q,"Realme"],[p,u]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[n,[q,J],[p,u]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[n,[q,J],[p,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[n,[q,"LG"],[p,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[n,[q,"LG"],[p,u]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[n,[q,"Lenovo"],[p,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[n,/_/g," "],[q,"Nokia"],[p,u]],[/(pixel c)\b/i],[n,[q,G],[p,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[n,[q,G],[p,u]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[n,[q,N],[p,u]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[n,"Xperia Tablet"],[q,N],[p,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[n,[q,"OnePlus"],[p,u]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[n,[q,z],[p,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[n,/(.+)/g,"Fire Phone $1"],[q,z],[p,u]],[/(playbook);[-\w\),; ]+(rim)/i],[n,q,[p,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[n,[q,C],[p,u]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[n,[q,B],[p,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[n,[q,B],[p,u]],[/(nexus 9)/i],[n,[q,"HTC"],[p,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[q,[n,/_/g," "],[p,u]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[n,[q,"Acer"],[p,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[n,[q,"Meizu"],[p,u]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[q,n,[p,u]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[q,n,[p,v]],[/(surface duo)/i],[n,[q,I],[p,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[n,[q,"Fairphone"],[p,u]],[/(u304aa)/i],[n,[q,"AT&T"],[p,u]],[/\bsie-(\w*)/i],[n,[q,"Siemens"],[p,u]],[/\b(rct\w+) b/i],[n,[q,"RCA"],[p,v]],[/\b(venue[\d ]{2,7}) b/i],[n,[q,"Dell"],[p,v]],[/\b(q(?:mv|ta)\w+) b/i],[n,[q,"Verizon"],[p,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[n,[q,"Barnes & Noble"],[p,v]],[/\b(tm\d{3}\w+) b/i],[n,[q,"NuVision"],[p,v]],[/\b(k88) b/i],[n,[q,"ZTE"],[p,v]],[/\b(nx\d{3}j) b/i],[n,[q,"ZTE"],[p,u]],[/\b(gen\d{3}) b.+49h/i],[n,[q,"Swiss"],[p,u]],[/\b(zur\d{3}) b/i],[n,[q,"Swiss"],[p,v]],[/\b((zeki)?tb.*\b) b/i],[n,[q,"Zeki"],[p,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[q,"Dragon Touch"],n,[p,v]],[/\b(ns-?\w{0,9}) b/i],[n,[q,"Insignia"],[p,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[n,[q,"NextBook"],[p,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[q,"Voice"],n,[p,u]],[/\b(lvtel\-)?(v1[12]) b/i],[[q,"LvTel"],n,[p,u]],[/\b(ph-1) /i],[n,[q,"Essential"],[p,u]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[n,[q,"Envizen"],[p,v]],[/\b(trio[-\w\. ]+) b/i],[n,[q,"MachSpeed"],[p,v]],[/\btu_(1491) b/i],[n,[q,"Rotor"],[p,v]],[/(shield[\w ]+) b/i],[n,[q,"Nvidia"],[p,v]],[/(sprint) (\w+)/i],[q,n,[p,u]],[/(kin\.[onetw]{3})/i],[[n,/\./g," "],[q,I],[p,u]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[n,[q,P],[p,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[n,[q,P],[p,u]],[/smart-tv.+(samsung)/i],[q,[p,w]],[/hbbtv.+maple;(\d+)/i],[[n,/^/,"SmartTV"],[q,L],[p,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[q,"LG"],[p,w]],[/(apple) ?tv/i],[q,[n,A+" TV"],[p,w]],[/crkey/i],[[n,E+"cast"],[q,G],[p,w]],[/droid.+aft(\w)( bui|\))/i],[n,[q,z],[p,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[n,[q,M],[p,w]],[/(bravia[\w ]+)( bui|\))/i],[n,[q,N],[p,w]],[/(mitv-\w{5}) bui/i],[n,[q,O],[p,w]],[/Hbbtv.*(technisat) (.*);/i],[q,n,[p,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[q,X],[n,X],[p,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[q,n,[p,t]],[/droid.+; (shield) bui/i],[n,[q,"Nvidia"],[p,t]],[/(playstation [345portablevi]+)/i],[n,[q,N],[p,t]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[n,[q,I],[p,t]],[/((pebble))app/i],[q,n,[p,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[n,[q,A],[p,x]],[/droid.+; (glass) \d/i],[n,[q,G],[p,x]],[/droid.+; (wt63?0{2,3})\)/i],[n,[q,P],[p,x]],[/(quest( 2| pro)?)/i],[n,[q,Q],[p,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[q,[p,y]],[/(aeobc)\b/i],[n,[q,z],[p,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[n,[p,u]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[n,[p,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,u]],[/(android[-\w\. ]{0,9});.+buil/i],[n,[q,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[r,[o,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[r,[o,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[o,r],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[r,o]],os:[[/microsoft (windows) (vista|xp)/i],[o,r],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[o,[r,Z,$]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[o,"Windows"],[r,Z,$]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[r,/_/g,"."],[o,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[o,S],[r,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[r,o],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[o,r],[/\(bb(10);/i],[r,[o,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[r,[o,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[r,[o,F+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[r,[o,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[r,[o,"watchOS"]],[/crkey\/([\d\.]+)/i],[r,[o,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[o,R],r],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[o,r],[/(sunos) ?([\w\.\d]*)/i],[[o,"Solaris"],r],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[o,r]]},aa=function(a,b){if(typeof a===k&&(b=a,a=h),!(this instanceof aa))return new aa(a,b).getResult();var c=typeof g!==j&&g.navigator?g.navigator:h,d=a||(c&&c.userAgent?c.userAgent:""),e=c&&c.userAgentData?c.userAgentData:h,f=b?T(_,b):_,t=c&&c.userAgent==d;return this.getBrowser=function(){var a,b={};return b[o]=h,b[r]=h,Y.call(b,d,f.browser),b[m]=typeof(a=b[r])===l?a.replace(/[^\d\.]/g,"").split(".")[0]:h,t&&c&&c.brave&&typeof c.brave.isBrave==i&&(b[o]="Brave"),b},this.getCPU=function(){var a={};return a[s]=h,Y.call(a,d,f.cpu),a},this.getDevice=function(){var a={};return a[q]=h,a[n]=h,a[p]=h,Y.call(a,d,f.device),t&&!a[p]&&e&&e.mobile&&(a[p]=u),t&&"Macintosh"==a[n]&&c&&typeof c.standalone!==j&&c.maxTouchPoints&&c.maxTouchPoints>2&&(a[n]="iPad",a[p]=v),a},this.getEngine=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.engine),a},this.getOS=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.os),t&&!a[o]&&e&&"Unknown"!=e.platform&&(a[o]=e.platform.replace(/chrome os/i,R).replace(/macos/i,S)),a},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return d},this.setUA=function(a){return d=typeof a===l&&a.length>350?X(a,350):a,this},this.setUA(d),this};aa.VERSION="1.0.35",aa.BROWSER=U([o,r,m]),aa.CPU=U([s]),aa.DEVICE=U([n,q,p,t,u,w,v,x,y]),aa.ENGINE=aa.OS=U([o,r]),typeof f!==j?(e.exports&&(f=e.exports=aa),f.UAParser=aa):c.amdO?void 0===(d=(function(){return aa}).call(b,c,b,a))||(a.exports=d):typeof g!==j&&(g.UAParser=aa);var ab=typeof g!==j&&(g.jQuery||g.Zepto);if(ab&&!ab.ua){var ac=new aa;ab.ua=ac.getResult(),ab.ua.get=function(){return ac.getUA()},ab.ua.set=function(a){ac.setUA(a);var b=ac.getResult();for(var c in b)ab.ua[c]=b[c]}}}("object"==typeof window?window:this)}},f={};function g(a){var b=f[a];if(void 0!==b)return b.exports;var c=f[a]={exports:{}},d=!0;try{e[a].call(c.exports,c,c.exports,g),d=!1}finally{d&&delete f[a]}return c.exports}g.ab="//",a.exports=g(226)})()},305:(a,b,c)=>{"use strict";let d,e,f,g,h;c.r(b),c.d(b,{default:()=>hX});var i={};c.r(i),c.d(i,{q:()=>dw,l:()=>dz});var j={};async function k(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}c.r(j),c.d(j,{config:()=>hT,default:()=>hS});let l=null;async function m(){if("phase-production-build"===process.env.NEXT_PHASE)return;l||(l=k());let a=await l;if(null==a?void 0:a.register)try{await a.register()}catch(a){throw a.message=`An error occurred while loading instrumentation hook: ${a.message}`,a}}async function n(...a){let b=await k();try{var c;await (null==b||null==(c=b.onRequestError)?void 0:c.call(b,...a))}catch(a){console.error("Error in instrumentation.onRequestError:",a)}}let o=null;function p(){return o||(o=m()),o}function q(a){return`The edge runtime does not support Node.js '${a}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==c.g.process&&(process.env=c.g.process.env,c.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(a){let b=new Proxy(function(){},{get(b,c){if("then"===c)return{};throw Object.defineProperty(Error(q(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(q(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(c,d,e){if("function"==typeof e[0])return e[0](b);throw Object.defineProperty(Error(q(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>b})},enumerable:!1,configurable:!1}),p();class r extends Error{constructor({page:a}){super(`The middleware "${a}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class s extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class t extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let u="_N_T_",v={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function w(a){var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}function x(a){let b={},c=[];if(a)for(let[d,e]of a.entries())"set-cookie"===d.toLowerCase()?(c.push(...w(e)),b[d]=1===c.length?c[0]:c):b[d]=e;return b}function y(a){try{return String(new URL(String(a)))}catch(b){throw Object.defineProperty(Error(`URL is malformed "${String(a)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:b}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...v,GROUP:{builtinReact:[v.reactServerComponents,v.actionBrowser],serverOnly:[v.reactServerComponents,v.actionBrowser,v.instrument,v.middleware],neutralTarget:[v.apiNode,v.apiEdge],clientOnly:[v.serverSideRendering,v.appPagesBrowser],bundled:[v.reactServerComponents,v.actionBrowser,v.serverSideRendering,v.appPagesBrowser,v.shared,v.instrument,v.middleware],appPages:[v.reactServerComponents,v.serverSideRendering,v.appPagesBrowser,v.actionBrowser]}});let z=Symbol("response"),A=Symbol("passThrough"),B=Symbol("waitUntil");class C{constructor(a,b){this[A]=!1,this[B]=b?{kind:"external",function:b}:{kind:"internal",promises:[]}}respondWith(a){this[z]||(this[z]=Promise.resolve(a))}passThroughOnException(){this[A]=!0}waitUntil(a){if("external"===this[B].kind)return(0,this[B].function)(a);this[B].promises.push(a)}}class D extends C{constructor(a){var b;super(a.request,null==(b=a.context)?void 0:b.waitUntil),this.sourcePage=a.page}get request(){throw Object.defineProperty(new r({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new r({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function E(a){return a.replace(/\/$/,"")||"/"}function F(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}function G(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:d,hash:e}=F(a);return""+b+c+d+e}function H(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:d,hash:e}=F(a);return""+c+b+d+e}function I(a,b){if("string"!=typeof a)return!1;let{pathname:c}=F(a);return c===b||c.startsWith(b+"/")}let J=new WeakMap;function K(a,b){let c;if(!b)return{pathname:a};let d=J.get(b);d||(d=b.map(a=>a.toLowerCase()),J.set(b,d));let e=a.split("/",2);if(!e[1])return{pathname:a};let f=e[1].toLowerCase(),g=d.indexOf(f);return g<0?{pathname:a}:(c=b[g],{pathname:a=a.slice(c.length+1)||"/",detectedLocale:c})}let L=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function M(a,b){return new URL(String(a).replace(L,"localhost"),b&&String(b).replace(L,"localhost"))}let N=Symbol("NextURLInternal");class O{constructor(a,b,c){let d,e;"object"==typeof b&&"pathname"in b||"string"==typeof b?(d=b,e=c||{}):e=c||b||{},this[N]={url:M(a,d??e.base),options:e,basePath:""},this.analyze()}analyze(){var a,b,c,d,e;let f=function(a,b){var c,d;let{basePath:e,i18n:f,trailingSlash:g}=null!=(c=b.nextConfig)?c:{},h={pathname:a,trailingSlash:"/"!==a?a.endsWith("/"):g};e&&I(h.pathname,e)&&(h.pathname=function(a,b){if(!I(a,b))return a;let c=a.slice(b.length);return c.startsWith("/")?c:"/"+c}(h.pathname,e),h.basePath=e);let i=h.pathname;if(h.pathname.startsWith("/_next/data/")&&h.pathname.endsWith(".json")){let a=h.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");h.buildId=a[0],i="index"!==a[1]?"/"+a.slice(1).join("/"):"/",!0===b.parseData&&(h.pathname=i)}if(f){let a=b.i18nProvider?b.i18nProvider.analyze(h.pathname):K(h.pathname,f.locales);h.locale=a.detectedLocale,h.pathname=null!=(d=a.pathname)?d:h.pathname,!a.detectedLocale&&h.buildId&&(a=b.i18nProvider?b.i18nProvider.analyze(i):K(i,f.locales)).detectedLocale&&(h.locale=a.detectedLocale)}return h}(this[N].url.pathname,{nextConfig:this[N].options.nextConfig,parseData:!0,i18nProvider:this[N].options.i18nProvider}),g=function(a,b){let c;if((null==b?void 0:b.host)&&!Array.isArray(b.host))c=b.host.toString().split(":",1)[0];else{if(!a.hostname)return;c=a.hostname}return c.toLowerCase()}(this[N].url,this[N].options.headers);this[N].domainLocale=this[N].options.i18nProvider?this[N].options.i18nProvider.detectDomainLocale(g):function(a,b,c){if(a)for(let f of(c&&(c=c.toLowerCase()),a)){var d,e;if(b===(null==(d=f.domain)?void 0:d.split(":",1)[0].toLowerCase())||c===f.defaultLocale.toLowerCase()||(null==(e=f.locales)?void 0:e.some(a=>a.toLowerCase()===c)))return f}}(null==(b=this[N].options.nextConfig)||null==(a=b.i18n)?void 0:a.domains,g);let h=(null==(c=this[N].domainLocale)?void 0:c.defaultLocale)||(null==(e=this[N].options.nextConfig)||null==(d=e.i18n)?void 0:d.defaultLocale);this[N].url.pathname=f.pathname,this[N].defaultLocale=h,this[N].basePath=f.basePath??"",this[N].buildId=f.buildId,this[N].locale=f.locale??h,this[N].trailingSlash=f.trailingSlash}formatPathname(){var a;let b;return b=function(a,b,c,d){if(!b||b===c)return a;let e=a.toLowerCase();return!d&&(I(e,"/api")||I(e,"/"+b.toLowerCase()))?a:G(a,"/"+b)}((a={basePath:this[N].basePath,buildId:this[N].buildId,defaultLocale:this[N].options.forceLocale?void 0:this[N].defaultLocale,locale:this[N].locale,pathname:this[N].url.pathname,trailingSlash:this[N].trailingSlash}).pathname,a.locale,a.buildId?void 0:a.defaultLocale,a.ignorePrefix),(a.buildId||!a.trailingSlash)&&(b=E(b)),a.buildId&&(b=H(G(b,"/_next/data/"+a.buildId),"/"===a.pathname?"index.json":".json")),b=G(b,a.basePath),!a.buildId&&a.trailingSlash?b.endsWith("/")?b:H(b,"/"):E(b)}formatSearch(){return this[N].url.search}get buildId(){return this[N].buildId}set buildId(a){this[N].buildId=a}get locale(){return this[N].locale??""}set locale(a){var b,c;if(!this[N].locale||!(null==(c=this[N].options.nextConfig)||null==(b=c.i18n)?void 0:b.locales.includes(a)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${a}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[N].locale=a}get defaultLocale(){return this[N].defaultLocale}get domainLocale(){return this[N].domainLocale}get searchParams(){return this[N].url.searchParams}get host(){return this[N].url.host}set host(a){this[N].url.host=a}get hostname(){return this[N].url.hostname}set hostname(a){this[N].url.hostname=a}get port(){return this[N].url.port}set port(a){this[N].url.port=a}get protocol(){return this[N].url.protocol}set protocol(a){this[N].url.protocol=a}get href(){let a=this.formatPathname(),b=this.formatSearch();return`${this.protocol}//${this.host}${a}${b}${this.hash}`}set href(a){this[N].url=M(a),this.analyze()}get origin(){return this[N].url.origin}get pathname(){return this[N].url.pathname}set pathname(a){this[N].url.pathname=a}get hash(){return this[N].url.hash}set hash(a){this[N].url.hash=a}get search(){return this[N].url.search}set search(a){this[N].url.search=a}get password(){return this[N].url.password}set password(a){this[N].url.password=a}get username(){return this[N].url.username}set username(a){this[N].url.username=a}get basePath(){return this[N].basePath}set basePath(a){this[N].basePath=a.startsWith("/")?a:`/${a}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new O(String(this),this[N].options)}}var P=c(724);let Q=Symbol("internal request");class R extends Request{constructor(a,b={}){let c="string"!=typeof a&&"url"in a?a.url:String(a);y(c),a instanceof Request?super(a,b):super(c,b);let d=new O(c,{headers:x(this.headers),nextConfig:b.nextConfig});this[Q]={cookies:new P.RequestCookies(this.headers),nextUrl:d,url:d.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[Q].cookies}get nextUrl(){return this[Q].nextUrl}get page(){throw new s}get ua(){throw new t}get url(){return this[Q].url}}class S{static get(a,b,c){let d=Reflect.get(a,b,c);return"function"==typeof d?d.bind(a):d}static set(a,b,c,d){return Reflect.set(a,b,c,d)}static has(a,b){return Reflect.has(a,b)}static deleteProperty(a,b){return Reflect.deleteProperty(a,b)}}let T=Symbol("internal response"),U=new Set([301,302,303,307,308]);function V(a,b){var c;if(null==a||null==(c=a.request)?void 0:c.headers){if(!(a.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let c=[];for(let[d,e]of a.request.headers)b.set("x-middleware-request-"+d,e),c.push(d);b.set("x-middleware-override-headers",c.join(","))}}class W extends Response{constructor(a,b={}){super(a,b);let c=this.headers,d=new Proxy(new P.ResponseCookies(c),{get(a,d,e){switch(d){case"delete":case"set":return(...e)=>{let f=Reflect.apply(a[d],a,e),g=new Headers(c);return f instanceof P.ResponseCookies&&c.set("x-middleware-set-cookie",f.getAll().map(a=>(0,P.stringifyCookie)(a)).join(",")),V(b,g),f};default:return S.get(a,d,e)}}});this[T]={cookies:d,url:b.url?new O(b.url,{headers:x(c),nextConfig:b.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[T].cookies}static json(a,b){let c=Response.json(a,b);return new W(c.body,c)}static redirect(a,b){let c="number"==typeof b?b:(null==b?void 0:b.status)??307;if(!U.has(c))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let d="object"==typeof b?b:{},e=new Headers(null==d?void 0:d.headers);return e.set("Location",y(a)),new W(null,{...d,headers:e,status:c})}static rewrite(a,b){let c=new Headers(null==b?void 0:b.headers);return c.set("x-middleware-rewrite",y(a)),V(b,c),new W(null,{...b,headers:c})}static next(a){let b=new Headers(null==a?void 0:a.headers);return b.set("x-middleware-next","1"),V(a,b),new W(null,{...a,headers:b})}}function X(a,b){let c="string"==typeof b?new URL(b):b,d=new URL(a,b),e=d.origin===c.origin;return{url:e?d.toString().slice(c.origin.length):d.toString(),isRelative:e}}let Y="Next-Router-Prefetch",Z=["RSC","Next-Router-State-Tree",Y,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"];class $ extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new $}}class _ extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,c,d){if("symbol"==typeof c)return S.get(b,c,d);let e=c.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);if(void 0!==f)return S.get(b,f,d)},set(b,c,d,e){if("symbol"==typeof c)return S.set(b,c,d,e);let f=c.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);return S.set(b,g??c,d,e)},has(b,c){if("symbol"==typeof c)return S.has(b,c);let d=c.toLowerCase(),e=Object.keys(a).find(a=>a.toLowerCase()===d);return void 0!==e&&S.has(b,e)},deleteProperty(b,c){if("symbol"==typeof c)return S.deleteProperty(b,c);let d=c.toLowerCase(),e=Object.keys(a).find(a=>a.toLowerCase()===d);return void 0===e||S.deleteProperty(b,e)}})}static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"append":case"delete":case"set":return $.callable;default:return S.get(a,b,c)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new _(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}var aa=c(535),ab=c(115);class ac extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new ac}}class ad{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return ac.callable;default:return S.get(a,b,c)}}})}}let ae=Symbol.for("next.mutated.cookies");class af{static wrap(a,b){let c=new P.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let d=[],e=new Set,f=()=>{let a=aa.J.getStore();if(a&&(a.pathWasRevalidated=!0),d=c.getAll().filter(a=>e.has(a.name)),b){let a=[];for(let b of d){let c=new P.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},g=new Proxy(c,{get(a,b,c){switch(b){case ae:return d;case"delete":return function(...b){e.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),g}finally{f()}};case"set":return function(...b){e.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),g}finally{f()}};default:return S.get(a,b,c)}}});return g}}function ag(a){return"action"===a.phase}function ah(a){if(!ag((0,ab.XN)(a)))throw new ac}var ai=function(a){return a.handleRequest="BaseServer.handleRequest",a.run="BaseServer.run",a.pipe="BaseServer.pipe",a.getStaticHTML="BaseServer.getStaticHTML",a.render="BaseServer.render",a.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",a.renderToResponse="BaseServer.renderToResponse",a.renderToHTML="BaseServer.renderToHTML",a.renderError="BaseServer.renderError",a.renderErrorToResponse="BaseServer.renderErrorToResponse",a.renderErrorToHTML="BaseServer.renderErrorToHTML",a.render404="BaseServer.render404",a}(ai||{}),aj=function(a){return a.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",a.loadComponents="LoadComponents.loadComponents",a}(aj||{}),ak=function(a){return a.getRequestHandler="NextServer.getRequestHandler",a.getServer="NextServer.getServer",a.getServerRequestHandler="NextServer.getServerRequestHandler",a.createServer="createServer.createServer",a}(ak||{}),al=function(a){return a.compression="NextNodeServer.compression",a.getBuildId="NextNodeServer.getBuildId",a.createComponentTree="NextNodeServer.createComponentTree",a.clientComponentLoading="NextNodeServer.clientComponentLoading",a.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",a.generateStaticRoutes="NextNodeServer.generateStaticRoutes",a.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",a.generatePublicRoutes="NextNodeServer.generatePublicRoutes",a.generateImageRoutes="NextNodeServer.generateImageRoutes.route",a.sendRenderResult="NextNodeServer.sendRenderResult",a.proxyRequest="NextNodeServer.proxyRequest",a.runApi="NextNodeServer.runApi",a.render="NextNodeServer.render",a.renderHTML="NextNodeServer.renderHTML",a.imageOptimizer="NextNodeServer.imageOptimizer",a.getPagePath="NextNodeServer.getPagePath",a.getRoutesManifest="NextNodeServer.getRoutesManifest",a.findPageComponents="NextNodeServer.findPageComponents",a.getFontManifest="NextNodeServer.getFontManifest",a.getServerComponentManifest="NextNodeServer.getServerComponentManifest",a.getRequestHandler="NextNodeServer.getRequestHandler",a.renderToHTML="NextNodeServer.renderToHTML",a.renderError="NextNodeServer.renderError",a.renderErrorToHTML="NextNodeServer.renderErrorToHTML",a.render404="NextNodeServer.render404",a.startResponse="NextNodeServer.startResponse",a.route="route",a.onProxyReq="onProxyReq",a.apiResolver="apiResolver",a.internalFetch="internalFetch",a}(al||{}),am=function(a){return a.startServer="startServer.startServer",a}(am||{}),an=function(a){return a.getServerSideProps="Render.getServerSideProps",a.getStaticProps="Render.getStaticProps",a.renderToString="Render.renderToString",a.renderDocument="Render.renderDocument",a.createBodyResult="Render.createBodyResult",a}(an||{}),ao=function(a){return a.renderToString="AppRender.renderToString",a.renderToReadableStream="AppRender.renderToReadableStream",a.getBodyResult="AppRender.getBodyResult",a.fetch="AppRender.fetch",a}(ao||{}),ap=function(a){return a.executeRoute="Router.executeRoute",a}(ap||{}),aq=function(a){return a.runHandler="Node.runHandler",a}(aq||{}),ar=function(a){return a.runHandler="AppRouteRouteHandlers.runHandler",a}(ar||{}),as=function(a){return a.generateMetadata="ResolveMetadata.generateMetadata",a.generateViewport="ResolveMetadata.generateViewport",a}(as||{}),at=function(a){return a.execute="Middleware.execute",a}(at||{});let au=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],av=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function aw(a){return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then}let{context:ax,propagation:ay,trace:az,SpanStatusCode:aA,SpanKind:aB,ROOT_CONTEXT:aC}=d=c(956);class aD extends Error{constructor(a,b){super(),this.bubble=a,this.result=b}}let aE=(a,b)=>{(function(a){return"object"==typeof a&&null!==a&&a instanceof aD})(b)&&b.bubble?a.setAttribute("next.bubble",!0):(b&&a.recordException(b),a.setStatus({code:aA.ERROR,message:null==b?void 0:b.message})),a.end()},aF=new Map,aG=d.createContextKey("next.rootSpanId"),aH=0,aI={set(a,b,c){a.push({key:b,value:c})}};class aJ{getTracerInstance(){return az.getTracer("next.js","0.0.1")}getContext(){return ax}getTracePropagationData(){let a=ax.active(),b=[];return ay.inject(a,b,aI),b}getActiveScopeSpan(){return az.getSpan(null==ax?void 0:ax.active())}withPropagatedContext(a,b,c){let d=ax.active();if(az.getSpanContext(d))return b();let e=ay.extract(d,a,c);return ax.with(e,b)}trace(...a){var b;let[c,d,e]=a,{fn:f,options:g}="function"==typeof d?{fn:d,options:{}}:{fn:e,options:{...d}},h=g.spanName??c;if(!au.includes(c)&&"1"!==process.env.NEXT_OTEL_VERBOSE||g.hideSpan)return f();let i=this.getSpanContext((null==g?void 0:g.parentSpan)??this.getActiveScopeSpan()),j=!1;i?(null==(b=az.getSpanContext(i))?void 0:b.isRemote)&&(j=!0):(i=(null==ax?void 0:ax.active())??aC,j=!0);let k=aH++;return g.attributes={"next.span_name":h,"next.span_type":c,...g.attributes},ax.with(i.setValue(aG,k),()=>this.getTracerInstance().startActiveSpan(h,g,a=>{let b="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,d=()=>{aF.delete(k),b&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&av.includes(c||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(c.split(".").pop()||"").replace(/[A-Z]/g,a=>"-"+a.toLowerCase())}`,{start:b,end:performance.now()})};j&&aF.set(k,new Map(Object.entries(g.attributes??{})));try{if(f.length>1)return f(a,b=>aE(a,b));let b=f(a);if(aw(b))return b.then(b=>(a.end(),b)).catch(b=>{throw aE(a,b),b}).finally(d);return a.end(),d(),b}catch(b){throw aE(a,b),d(),b}}))}wrap(...a){let b=this,[c,d,e]=3===a.length?a:[a[0],{},a[1]];return au.includes(c)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let a=d;"function"==typeof a&&"function"==typeof e&&(a=a.apply(this,arguments));let f=arguments.length-1,g=arguments[f];if("function"!=typeof g)return b.trace(c,a,()=>e.apply(this,arguments));{let d=b.getContext().bind(ax.active(),g);return b.trace(c,a,(a,b)=>(arguments[f]=function(a){return null==b||b(a),d.apply(this,arguments)},e.apply(this,arguments)))}}:e}startSpan(...a){let[b,c]=a,d=this.getSpanContext((null==c?void 0:c.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(b,c,d)}getSpanContext(a){return a?az.setSpan(ax.active(),a):void 0}getRootSpanAttributes(){let a=ax.active().getValue(aG);return aF.get(a)}setRootSpanAttribute(a,b){let c=ax.active().getValue(aG),d=aF.get(c);d&&d.set(a,b)}}let aK=(()=>{let a=new aJ;return()=>a})(),aL="__prerender_bypass";Symbol("__next_preview_data"),Symbol(aL);class aM{constructor(a,b,c,d){var e;let f=a&&function(a,b){let c=_.from(a.headers);return{isOnDemandRevalidate:c.get("x-prerender-revalidate")===b.previewModeId,revalidateOnlyGenerated:c.has("x-prerender-revalidate-if-generated")}}(b,a).isOnDemandRevalidate,g=null==(e=c.get(aL))?void 0:e.value;this._isEnabled=!!(!f&&g&&a&&g===a.previewModeId),this._previewModeId=null==a?void 0:a.previewModeId,this._mutableCookies=d}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:aL,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:aL,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function aN(a,b){if("x-middleware-set-cookie"in a.headers&&"string"==typeof a.headers["x-middleware-set-cookie"]){let c=a.headers["x-middleware-set-cookie"],d=new Headers;for(let a of w(c))d.append("set-cookie",a);for(let a of new P.ResponseCookies(d).getAll())b.set(a)}}var aO=c(802),aP=c.n(aO);class aQ extends Error{constructor(a,b){super("Invariant: "+(a.endsWith(".")?a:a+".")+" This is a bug in Next.js.",b),this.name="InvariantError"}}class aR{constructor(a,b){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=a,this.calculateSize=b||(()=>1)}set(a,b){if(!a||!b)return;let c=this.calculateSize(b);if(c>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(a)&&(this.totalSize-=this.sizes.get(a)||0),this.cache.set(a,b),this.sizes.set(a,c),this.totalSize+=c,this.touch(a)}has(a){return!!a&&(this.touch(a),!!this.cache.get(a))}get(a){if(!a)return;let b=this.cache.get(a);if(void 0!==b)return this.touch(a),b}touch(a){let b=this.cache.get(a);void 0!==b&&(this.cache.delete(a),this.cache.set(a,b),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let a=this.cache.keys().next().value;if(void 0!==a){let b=this.sizes.get(a)||0;this.totalSize-=b,this.cache.delete(a),this.sizes.delete(a)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(a){this.cache.has(a)&&(this.totalSize-=this.sizes.get(a)||0,this.cache.delete(a),this.sizes.delete(a))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}c(356).Buffer,new aR(0x3200000,a=>a.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE&&((a,...b)=>{console.log(`use-cache: ${a}`,...b)}),Symbol.for("@next/cache-handlers");let aS=Symbol.for("@next/cache-handlers-map"),aT=Symbol.for("@next/cache-handlers-set"),aU=globalThis;function aV(){if(aU[aS])return aU[aS].entries()}async function aW(a,b){if(!a)return b();let c=aX(a);try{return await b()}finally{let b=function(a,b){let c=new Set(a.pendingRevalidatedTags),d=new Set(a.pendingRevalidateWrites);return{pendingRevalidatedTags:b.pendingRevalidatedTags.filter(a=>!c.has(a)),pendingRevalidates:Object.fromEntries(Object.entries(b.pendingRevalidates).filter(([b])=>!(b in a.pendingRevalidates))),pendingRevalidateWrites:b.pendingRevalidateWrites.filter(a=>!d.has(a))}}(c,aX(a));await aZ(a,b)}}function aX(a){return{pendingRevalidatedTags:a.pendingRevalidatedTags?[...a.pendingRevalidatedTags]:[],pendingRevalidates:{...a.pendingRevalidates},pendingRevalidateWrites:a.pendingRevalidateWrites?[...a.pendingRevalidateWrites]:[]}}async function aY(a,b){if(0===a.length)return;let c=[];b&&c.push(b.revalidateTag(a));let d=function(){if(aU[aT])return aU[aT].values()}();if(d)for(let b of d)c.push(b.expireTags(...a));await Promise.all(c)}async function aZ(a,b){let c=(null==b?void 0:b.pendingRevalidatedTags)??a.pendingRevalidatedTags??[],d=(null==b?void 0:b.pendingRevalidates)??a.pendingRevalidates??{},e=(null==b?void 0:b.pendingRevalidateWrites)??a.pendingRevalidateWrites??[];return Promise.all([aY(c,a.incrementalCache),...Object.values(d),...e])}let a$=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a_{disable(){throw a$}getStore(){}run(){throw a$}exit(){throw a$}enterWith(){throw a$}static bind(a){return a}}let a0="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,a1=a0?new a0:new a_;class a2{constructor({waitUntil:a,onClose:b,onTaskError:c}){this.workUnitStores=new Set,this.waitUntil=a,this.onClose=b,this.onTaskError=c,this.callbackQueue=new(aP()),this.callbackQueue.pause()}after(a){if(aw(a))this.waitUntil||a3(),this.waitUntil(a.catch(a=>this.reportTaskError("promise",a)));else if("function"==typeof a)this.addCallback(a);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(a){var b;this.waitUntil||a3();let c=ab.FP.getStore();c&&this.workUnitStores.add(c);let d=a1.getStore(),e=d?d.rootTaskSpawnPhase:null==c?void 0:c.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let f=(b=async()=>{try{await a1.run({rootTaskSpawnPhase:e},()=>a())}catch(a){this.reportTaskError("function",a)}},a0?a0.bind(b):a_.bind(b));this.callbackQueue.add(f)}async runCallbacksOnClose(){return await new Promise(a=>this.onClose(a)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let a of this.workUnitStores)a.phase="after";let a=aa.J.getStore();if(!a)throw Object.defineProperty(new aQ("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return aW(a,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(a,b){if(console.error("promise"===a?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",b),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,b)}catch(a){console.error(Object.defineProperty(new aQ("`onTaskError` threw while handling an error thrown from an `after` task",{cause:a}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function a3(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function a4(a){let b,c={then:(d,e)=>(b||(b=a()),b.then(a=>{c.value=a}).catch(()=>{}),b.then(d,e))};return c}class a5{onClose(a){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",a),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function a6(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID||"",previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let a7=Symbol.for("@next/request-context");async function a8(a,b,c){let d=[],e=c&&c.size>0;for(let b of(a=>{let b=["/layout"];if(a.startsWith("/")){let c=a.split("/");for(let a=1;a<c.length+1;a++){let d=c.slice(0,a).join("/");d&&(d.endsWith("/page")||d.endsWith("/route")||(d=`${d}${!d.endsWith("/")?"/":""}layout`),b.push(d))}}return b})(a))b=`${u}${b}`,d.push(b);if(b.pathname&&!e){let a=`${u}${b.pathname}`;d.push(a)}return{tags:d,expirationsByCacheKind:function(a){let b=new Map,c=aV();if(c)for(let[d,e]of c)"getExpiration"in e&&b.set(d,a4(async()=>e.getExpiration(...a)));return b}(d)}}class a9 extends R{constructor(a){super(a.input,a.init),this.sourcePage=a.page}get request(){throw Object.defineProperty(new r({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new r({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new r({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let ba={keys:a=>Array.from(a.keys()),get:(a,b)=>a.get(b)??void 0},bb=(a,b)=>aK().withPropagatedContext(a.headers,b,ba),bc=!1;async function bd(a){var b;let d,e;if(!bc&&(bc=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:a,wrapRequestHandler:b}=c(905);a(),bb=b(bb)}await p();let f=void 0!==globalThis.__BUILD_MANIFEST;a.request.url=a.request.url.replace(/\.rsc($|\?)/,"$1");let g=a.bypassNextUrl?new URL(a.request.url):new O(a.request.url,{headers:a.request.headers,nextConfig:a.request.nextConfig});for(let a of[...g.searchParams.keys()]){let b=g.searchParams.getAll(a),c=function(a){for(let b of["nxtP","nxtI"])if(a!==b&&a.startsWith(b))return a.substring(b.length);return null}(a);if(c){for(let a of(g.searchParams.delete(c),b))g.searchParams.append(c,a);g.searchParams.delete(a)}}let h=process.env.__NEXT_BUILD_ID||"";"buildId"in g&&(h=g.buildId||"",g.buildId="");let i=function(a){let b=new Headers;for(let[c,d]of Object.entries(a))for(let a of Array.isArray(d)?d:[d])void 0!==a&&("number"==typeof a&&(a=a.toString()),b.append(c,a));return b}(a.request.headers),j=i.has("x-nextjs-data"),k="1"===i.get("RSC");j&&"/index"===g.pathname&&(g.pathname="/");let l=new Map;if(!f)for(let a of Z){let b=a.toLowerCase(),c=i.get(b);null!==c&&(l.set(b,c),i.delete(b))}let m=new a9({page:a.page,input:(function(a){let b="string"==typeof a,c=b?new URL(a):a;return c.searchParams.delete("_rsc"),b?c.toString():c})(g).toString(),init:{body:a.request.body,headers:i,method:a.request.method,nextConfig:a.request.nextConfig,signal:a.request.signal}});j&&Object.defineProperty(m,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCacheShared&&a.IncrementalCache&&(globalThis.__incrementalCache=new a.IncrementalCache({CurCacheHandler:a.incrementalCacheHandler,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:a.request.headers,getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:a6()})}));let n=a.request.waitUntil??(null==(b=function(){let a=globalThis[a7];return null==a?void 0:a.get()}())?void 0:b.waitUntil),o=new D({request:m,page:a.page,context:n?{waitUntil:n}:void 0});if((d=await bb(m,()=>{if("/middleware"===a.page||"/src/middleware"===a.page){let b=o.waitUntil.bind(o),c=new a5;return aK().trace(at.execute,{spanName:`middleware ${m.method} ${m.nextUrl.pathname}`,attributes:{"http.target":m.nextUrl.pathname,"http.method":m.method}},async()=>{try{var d,f,g,i,j,k;let l=a6(),n=await a8("/",m.nextUrl,null),p=(j=m.nextUrl,k=a=>{e=a},function(a,b,c,d,e,f,g,h,i,j,k){function l(a){c&&c.setHeader("Set-Cookie",a)}let m={};return{type:"request",phase:a,implicitTags:f,url:{pathname:d.pathname,search:d.search??""},rootParams:e,get headers(){return m.headers||(m.headers=function(a){let b=_.from(a);for(let a of Z)b.delete(a.toLowerCase());return _.seal(b)}(b.headers)),m.headers},get cookies(){if(!m.cookies){let a=new P.RequestCookies(_.from(b.headers));aN(b,a),m.cookies=ad.seal(a)}return m.cookies},set cookies(value){m.cookies=value},get mutableCookies(){if(!m.mutableCookies){let a=function(a,b){let c=new P.RequestCookies(_.from(a));return af.wrap(c,b)}(b.headers,g||(c?l:void 0));aN(b,a),m.mutableCookies=a}return m.mutableCookies},get userspaceMutableCookies(){return m.userspaceMutableCookies||(m.userspaceMutableCookies=function(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return ah("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return ah("cookies().set"),a.set(...c),b};default:return S.get(a,c,d)}}});return b}(this.mutableCookies)),m.userspaceMutableCookies},get draftMode(){return m.draftMode||(m.draftMode=new aM(i,b,this.cookies,this.mutableCookies)),m.draftMode},renderResumeDataCache:h??null,isHmrRefresh:j,serverComponentsHmrCache:k||globalThis.__serverComponentsHmrCache}}("action",m,void 0,j,{},n,k,void 0,l,!1,void 0)),q=function({page:a,fallbackRouteParams:b,renderOpts:c,requestEndedState:d,isPrefetchRequest:e,buildId:f,previouslyRevalidatedTags:g}){var h;let i={isStaticGeneration:!c.shouldWaitOnAllReady&&!c.supportsDynamicResponse&&!c.isDraftMode&&!c.isPossibleServerAction,page:a,fallbackRouteParams:b,route:(h=a.split("/").reduce((a,b,c,d)=>b?"("===b[0]&&b.endsWith(")")||"@"===b[0]||("page"===b||"route"===b)&&c===d.length-1?a:a+"/"+b:a,"")).startsWith("/")?h:"/"+h,incrementalCache:c.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:c.cacheLifeProfiles,isRevalidate:c.isRevalidate,isBuildTimePrerendering:c.nextExport,hasReadableErrorStacks:c.hasReadableErrorStacks,fetchCache:c.fetchCache,isOnDemandRevalidate:c.isOnDemandRevalidate,isDraftMode:c.isDraftMode,requestEndedState:d,isPrefetchRequest:e,buildId:f,reactLoadableManifest:(null==c?void 0:c.reactLoadableManifest)||{},assetPrefix:(null==c?void 0:c.assetPrefix)||"",afterContext:function(a){let{waitUntil:b,onClose:c,onAfterTaskError:d}=a;return new a2({waitUntil:b,onClose:c,onTaskError:d})}(c),dynamicIOEnabled:c.experimental.dynamicIO,dev:c.dev??!1,previouslyRevalidatedTags:g,refreshTagsByCacheKind:function(){let a=new Map,b=aV();if(b)for(let[c,d]of b)"refreshTags"in d&&a.set(c,a4(async()=>d.refreshTags()));return a}(),runInCleanSnapshot:a0?a0.snapshot():function(a,...b){return a(...b)}};return c.store=i,i}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(f=a.request.nextConfig)||null==(d=f.experimental)?void 0:d.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(i=a.request.nextConfig)||null==(g=i.experimental)?void 0:g.authInterrupts)},supportsDynamicResponse:!0,waitUntil:b,onClose:c.onClose.bind(c),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:m.headers.has(Y),buildId:h??"",previouslyRevalidatedTags:[]});return await aa.J.run(q,()=>ab.FP.run(p,a.handler,m,o))}finally{setTimeout(()=>{c.dispatchClose()},0)}})}return a.handler(m,o)}))&&!(d instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});d&&e&&d.headers.set("set-cookie",e);let q=null==d?void 0:d.headers.get("x-middleware-rewrite");if(d&&q&&(k||!f)){let b=new O(q,{forceLocale:!0,headers:a.request.headers,nextConfig:a.request.nextConfig});f||b.host!==m.nextUrl.host||(b.buildId=h||b.buildId,d.headers.set("x-middleware-rewrite",String(b)));let{url:c,isRelative:e}=X(b.toString(),g.toString());!f&&j&&d.headers.set("x-nextjs-rewrite",c),k&&e&&(g.pathname!==b.pathname&&d.headers.set("x-nextjs-rewritten-path",b.pathname),g.search!==b.search&&d.headers.set("x-nextjs-rewritten-query",b.search.slice(1)))}let r=null==d?void 0:d.headers.get("Location");if(d&&r&&!f){let b=new O(r,{forceLocale:!1,headers:a.request.headers,nextConfig:a.request.nextConfig});d=new Response(d.body,d),b.host===g.host&&(b.buildId=h||b.buildId,d.headers.set("Location",b.toString())),j&&(d.headers.delete("Location"),d.headers.set("x-nextjs-redirect",X(b.toString(),g.toString()).url))}let s=d||W.next(),t=s.headers.get("x-middleware-override-headers"),u=[];if(t){for(let[a,b]of l)s.headers.set(`x-middleware-request-${a}`,b),u.push(a);u.length>0&&s.headers.set("x-middleware-override-headers",t+","+u.join(","))}return{response:s,waitUntil:("internal"===o[B].kind?Promise.all(o[B].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:m.fetchMetrics}}var be=function(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c},bf=function(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)};function bg(a){let b=a?"__Secure-":"";return{sessionToken:{name:`${b}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},callbackUrl:{name:`${b}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},csrfToken:{name:`${a?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},pkceCodeVerifier:{name:`${b}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},state:{name:`${b}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},nonce:{name:`${b}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},webauthnChallenge:{name:`${b}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}}}}class bh{constructor(a,b,c){if(d5.add(this),d6.set(this,{}),d7.set(this,void 0),d8.set(this,void 0),be(this,d8,c,"f"),be(this,d7,a,"f"),!b)return;let{name:d}=a;for(let[a,c]of Object.entries(b))a.startsWith(d)&&c&&(bf(this,d6,"f")[a]=c)}get value(){return Object.keys(bf(this,d6,"f")).sort((a,b)=>parseInt(a.split(".").pop()||"0")-parseInt(b.split(".").pop()||"0")).map(a=>bf(this,d6,"f")[a]).join("")}chunk(a,b){let c=bf(this,d5,"m",ea).call(this);for(let d of bf(this,d5,"m",d9).call(this,{name:bf(this,d7,"f").name,value:a,options:{...bf(this,d7,"f").options,...b}}))c[d.name]=d;return Object.values(c)}clean(){return Object.values(bf(this,d5,"m",ea).call(this))}}d6=new WeakMap,d7=new WeakMap,d8=new WeakMap,d5=new WeakSet,d9=function(a){let b=Math.ceil(a.value.length/3936);if(1===b)return bf(this,d6,"f")[a.name]=a.value,[a];let c=[];for(let d=0;d<b;d++){let b=`${a.name}.${d}`,e=a.value.substr(3936*d,3936);c.push({...a,name:b,value:e}),bf(this,d6,"f")[b]=e}return bf(this,d8,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:a.value.length,chunks:c.map(a=>a.value.length+160)}),c},ea=function(){let a={};for(let b in bf(this,d6,"f"))delete bf(this,d6,"f")?.[b],a[b]={name:b,value:"",options:{...bf(this,d7,"f").options,maxAge:0}};return a};class bi extends Error{constructor(a,b){a instanceof Error?super(void 0,{cause:{err:a,...a.cause,...b}}):"string"==typeof a?(b instanceof Error&&(b={err:b,...b.cause}),super(a,b)):super(void 0,a),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let c=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${c}`}}class bj extends bi{}bj.kind="signIn";class bk extends bi{}bk.type="AdapterError";class bl extends bi{}bl.type="AccessDenied";class bm extends bi{}bm.type="CallbackRouteError";class bn extends bi{}bn.type="ErrorPageLoop";class bo extends bi{}bo.type="EventError";class bp extends bi{}bp.type="InvalidCallbackUrl";class bq extends bj{constructor(){super(...arguments),this.code="credentials"}}bq.type="CredentialsSignin";class br extends bi{}br.type="InvalidEndpoints";class bs extends bi{}bs.type="InvalidCheck";class bt extends bi{}bt.type="JWTSessionError";class bu extends bi{}bu.type="MissingAdapter";class bv extends bi{}bv.type="MissingAdapterMethods";class bw extends bi{}bw.type="MissingAuthorize";class bx extends bi{}bx.type="MissingSecret";class by extends bj{}by.type="OAuthAccountNotLinked";class bz extends bj{}bz.type="OAuthCallbackError";class bA extends bi{}bA.type="OAuthProfileParseError";class bB extends bi{}bB.type="SessionTokenError";class bC extends bj{}bC.type="OAuthSignInError";class bD extends bj{}bD.type="EmailSignInError";class bE extends bi{}bE.type="SignOutError";class bF extends bi{}bF.type="UnknownAction";class bG extends bi{}bG.type="UnsupportedStrategy";class bH extends bi{}bH.type="InvalidProvider";class bI extends bi{}bI.type="UntrustedHost";class bJ extends bi{}bJ.type="Verification";class bK extends bj{}bK.type="MissingCSRF";let bL=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class bM extends bi{}bM.type="DuplicateConditionalUI";class bN extends bi{}bN.type="MissingWebAuthnAutocomplete";class bO extends bi{}bO.type="WebAuthnVerificationError";class bP extends bj{}bP.type="AccountNotLinked";class bQ extends bi{}bQ.type="ExperimentalFeatureNotEnabled";let bR=!1;function bS(a,b){try{return/^https?:/.test(new URL(a,a.startsWith("/")?b:void 0).protocol)}catch{return!1}}let bT=!1,bU=!1,bV=!1,bW=["createVerificationToken","useVerificationToken","getUserByEmail"],bX=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],bY=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"],bZ=async(a,b,c,d,e)=>{let{crypto:{subtle:f}}=(()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")})();return new Uint8Array(await f.deriveBits({name:"HKDF",hash:`SHA-${a.substr(3)}`,salt:c,info:d},await f.importKey("raw",b,"HKDF",!1,["deriveBits"]),e<<3))};function b$(a,b){if("string"==typeof a)return new TextEncoder().encode(a);if(!(a instanceof Uint8Array))throw TypeError(`"${b}"" must be an instance of Uint8Array or a string`);return a}async function b_(a,b,c,d,e){return bZ(function(a){switch(a){case"sha256":case"sha384":case"sha512":case"sha1":return a;default:throw TypeError('unsupported "digest" value')}}(a),function(a){let b=b$(a,"ikm");if(!b.byteLength)throw TypeError('"ikm" must be at least one byte in length');return b}(b),b$(c,"salt"),function(a){let b=b$(a,"info");if(b.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return b}(d),function(a,b){if("number"!=typeof a||!Number.isInteger(a)||a<1)throw TypeError('"keylen" must be a positive integer');if(a>255*(parseInt(b.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return a}(e,a))}let b0=async(a,b)=>{let c=`SHA-${a.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(c,b))},b1=new TextEncoder,b2=new TextDecoder;function b3(...a){let b=new Uint8Array(a.reduce((a,{length:b})=>a+b,0)),c=0;for(let d of a)b.set(d,c),c+=d.length;return b}function b4(a,b,c){if(b<0||b>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${b}`);a.set([b>>>24,b>>>16,b>>>8,255&b],c)}function b5(a){let b=Math.floor(a/0x100000000),c=new Uint8Array(8);return b4(c,b,0),b4(c,a%0x100000000,4),c}function b6(a){let b=new Uint8Array(4);return b4(b,a),b}function b7(a){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof a?a:b2.decode(a),{alphabet:"base64url"});let b=a;b instanceof Uint8Array&&(b=b2.decode(b)),b=b.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var c=b;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(c);let a=atob(c),d=new Uint8Array(a.length);for(let b=0;b<a.length;b++)d[b]=a.charCodeAt(b);return d}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function b8(a){let b=a;return("string"==typeof b&&(b=b1.encode(b)),Uint8Array.prototype.toBase64)?b.toBase64({alphabet:"base64url",omitPadding:!0}):(function(a){if(Uint8Array.prototype.toBase64)return a.toBase64();let b=[];for(let c=0;c<a.length;c+=32768)b.push(String.fromCharCode.apply(null,a.subarray(c,c+32768)));return btoa(b.join(""))})(b).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class b9 extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(a,b){super(a,b),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ca extends b9{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class cb extends b9{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class cc extends b9{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class cd extends b9{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class ce extends b9{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(a="decryption operation failed",b){super(a,b)}}class cf extends b9{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class cg extends b9{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class ch extends b9{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class ci extends b9{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(a="multiple matching keys found in the JSON Web Key Set",b){super(a,b)}}function cj(a){if(!ck(a))throw Error("CryptoKey instance expected")}function ck(a){return a?.[Symbol.toStringTag]==="CryptoKey"}function cl(a){return a?.[Symbol.toStringTag]==="KeyObject"}let cm=a=>ck(a)||cl(a),cn=a=>{if(!function(a){return"object"==typeof a&&null!==a}(a)||"[object Object]"!==Object.prototype.toString.call(a))return!1;if(null===Object.getPrototypeOf(a))return!0;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b};function co(a){return cn(a)&&"string"==typeof a.kty}function cp(a,b,...c){if((c=c.filter(Boolean)).length>2){let b=c.pop();a+=`one of type ${c.join(", ")}, or ${b}.`}else 2===c.length?a+=`one of type ${c[0]} or ${c[1]}.`:a+=`of type ${c[0]}.`;return null==b?a+=` Received ${b}`:"function"==typeof b&&b.name?a+=` Received function ${b.name}`:"object"==typeof b&&null!=b&&b.constructor?.name&&(a+=` Received an instance of ${b.constructor.name}`),a}let cq=(a,...b)=>cp("Key must be ",a,...b);function cr(a,b,...c){return cp(`Key for the ${a} algorithm must be `,b,...c)}async function cs(a){if(cl(a))if("secret"!==a.type)return a.export({format:"jwk"});else a=a.export();if(a instanceof Uint8Array)return{kty:"oct",k:b8(a)};if(!ck(a))throw TypeError(cq(a,"CryptoKey","KeyObject","Uint8Array"));if(!a.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:b,key_ops:c,alg:d,use:e,...f}=await crypto.subtle.exportKey("jwk",a);return f}async function ct(a){return cs(a)}let cu=(a,b)=>{if("string"!=typeof a||!a)throw new ch(`${b} missing or invalid`)};async function cv(a,b){let c,d;if(co(a))c=a;else if(cm(a))c=await ct(a);else throw TypeError(cq(a,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(b??="sha256")&&"sha384"!==b&&"sha512"!==b)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(c.kty){case"EC":cu(c.crv,'"crv" (Curve) Parameter'),cu(c.x,'"x" (X Coordinate) Parameter'),cu(c.y,'"y" (Y Coordinate) Parameter'),d={crv:c.crv,kty:c.kty,x:c.x,y:c.y};break;case"OKP":cu(c.crv,'"crv" (Subtype of Key Pair) Parameter'),cu(c.x,'"x" (Public Key) Parameter'),d={crv:c.crv,kty:c.kty,x:c.x};break;case"RSA":cu(c.e,'"e" (Exponent) Parameter'),cu(c.n,'"n" (Modulus) Parameter'),d={e:c.e,kty:c.kty,n:c.n};break;case"oct":cu(c.k,'"k" (Key Value) Parameter'),d={k:c.k,kty:c.kty};break;default:throw new cd('"kty" (Key Type) Parameter missing or unsupported')}let e=b1.encode(JSON.stringify(d));return b8(await b0(b,e))}let cw=Symbol();function cx(a){switch(a){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new cd(`Unsupported JWE Algorithm: ${a}`)}}let cy=(a,b)=>{if(b.length<<3!==cx(a))throw new cf("Invalid Initialization Vector length")},cz=(a,b)=>{let c=a.byteLength<<3;if(c!==b)throw new cf(`Invalid Content Encryption Key length. Expected ${b} bits, got ${c} bits`)};function cA(a,b="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${b} must be ${a}`)}function cB(a,b){return a.name===b}function cC(a,b,c){switch(b){case"A128GCM":case"A192GCM":case"A256GCM":{if(!cB(a.algorithm,"AES-GCM"))throw cA("AES-GCM");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw cA(c,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!cB(a.algorithm,"AES-KW"))throw cA("AES-KW");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw cA(c,"algorithm.length");break}case"ECDH":switch(a.algorithm.name){case"ECDH":case"X25519":break;default:throw cA("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!cB(a.algorithm,"PBKDF2"))throw cA("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!cB(a.algorithm,"RSA-OAEP"))throw cA("RSA-OAEP");let c=parseInt(b.slice(9),10)||1;if(parseInt(a.algorithm.hash.name.slice(4),10)!==c)throw cA(`SHA-${c}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}if(c&&!a.usages.includes(c))throw TypeError(`CryptoKey does not support this operation, its usages must include ${c}.`)}async function cD(a,b,c,d,e){if(!(c instanceof Uint8Array))throw TypeError(cq(c,"Uint8Array"));let f=parseInt(a.slice(1,4),10),g=await crypto.subtle.importKey("raw",c.subarray(f>>3),"AES-CBC",!1,["encrypt"]),h=await crypto.subtle.importKey("raw",c.subarray(0,f>>3),{hash:`SHA-${f<<1}`,name:"HMAC"},!1,["sign"]),i=new Uint8Array(await crypto.subtle.encrypt({iv:d,name:"AES-CBC"},g,b)),j=b3(e,d,i,b5(e.length<<3));return{ciphertext:i,tag:new Uint8Array((await crypto.subtle.sign("HMAC",h,j)).slice(0,f>>3)),iv:d}}async function cE(a,b,c,d,e){let f;c instanceof Uint8Array?f=await crypto.subtle.importKey("raw",c,"AES-GCM",!1,["encrypt"]):(cC(c,a,"encrypt"),f=c);let g=new Uint8Array(await crypto.subtle.encrypt({additionalData:e,iv:d,name:"AES-GCM",tagLength:128},f,b)),h=g.slice(-16);return{ciphertext:g.slice(0,-16),tag:h,iv:d}}let cF=async(a,b,c,d,e)=>{if(!ck(c)&&!(c instanceof Uint8Array))throw TypeError(cq(c,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(d)cy(a,d);else d=crypto.getRandomValues(new Uint8Array(cx(a)>>3));switch(a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return c instanceof Uint8Array&&cz(c,parseInt(a.slice(-3),10)),cD(a,b,c,d,e);case"A128GCM":case"A192GCM":case"A256GCM":return c instanceof Uint8Array&&cz(c,parseInt(a.slice(1,4),10)),cE(a,b,c,d,e);default:throw new cd("Unsupported JWE Content Encryption Algorithm")}};function cG(a,b){if(a.algorithm.length!==parseInt(b.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${b}`)}function cH(a,b,c){return a instanceof Uint8Array?crypto.subtle.importKey("raw",a,"AES-KW",!0,[c]):(cC(a,b,c),a)}async function cI(a,b,c){let d=await cH(b,a,"wrapKey");cG(d,a);let e=await crypto.subtle.importKey("raw",c,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",e,d,"AES-KW"))}async function cJ(a,b,c){let d=await cH(b,a,"unwrapKey");cG(d,a);let e=await crypto.subtle.unwrapKey("raw",c,d,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",e))}function cK(a){return b3(b6(a.length),a)}async function cL(a,b,c){let d=Math.ceil((b>>3)/32),e=new Uint8Array(32*d);for(let b=0;b<d;b++){let d=new Uint8Array(4+a.length+c.length);d.set(b6(b+1)),d.set(a,4),d.set(c,4+a.length),e.set(await b0("sha256",d),32*b)}return e.slice(0,b>>3)}async function cM(a,b,c,d,e=new Uint8Array(0),f=new Uint8Array(0)){let g;cC(a,"ECDH"),cC(b,"ECDH","deriveBits");let h=b3(cK(b1.encode(c)),cK(e),cK(f),b6(d));return g="X25519"===a.algorithm.name?256:Math.ceil(parseInt(a.algorithm.namedCurve.slice(-3),10)/8)<<3,cL(new Uint8Array(await crypto.subtle.deriveBits({name:a.algorithm.name,public:a},b,g)),d,h)}function cN(a){switch(a.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===a.algorithm.name}}async function cO(a,b,c,d){if(!(a instanceof Uint8Array)||a.length<8)throw new cf("PBES2 Salt Input must be 8 or more octets");let e=b3(b1.encode(b),new Uint8Array([0]),a),f=parseInt(b.slice(13,16),10),g={hash:`SHA-${b.slice(8,11)}`,iterations:c,name:"PBKDF2",salt:e},h=await (d instanceof Uint8Array?crypto.subtle.importKey("raw",d,"PBKDF2",!1,["deriveBits"]):(cC(d,b,"deriveBits"),d));return new Uint8Array(await crypto.subtle.deriveBits(g,h,f))}async function cP(a,b,c,d=2048,e=crypto.getRandomValues(new Uint8Array(16))){let f=await cO(e,a,d,b);return{encryptedKey:await cI(a.slice(-6),f,c),p2c:d,p2s:b8(e)}}async function cQ(a,b,c,d,e){let f=await cO(e,a,d,b);return cJ(a.slice(-6),f,c)}let cR=(a,b)=>{if(a.startsWith("RS")||a.startsWith("PS")){let{modulusLength:c}=b.algorithm;if("number"!=typeof c||c<2048)throw TypeError(`${a} requires key modulusLength to be 2048 bits or larger`)}},cS=a=>{switch(a){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new cd(`alg ${a} is not supported either by JOSE or your javascript runtime`)}};async function cT(a,b,c){return cC(b,a,"encrypt"),cR(a,b),new Uint8Array(await crypto.subtle.encrypt(cS(a),b,c))}async function cU(a,b,c){return cC(b,a,"decrypt"),cR(a,b),new Uint8Array(await crypto.subtle.decrypt(cS(a),b,c))}let cV=async a=>{if(!a.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:b,keyUsages:c}=function(a){let b,c;switch(a.kty){case"RSA":switch(a.alg){case"PS256":case"PS384":case"PS512":b={name:"RSA-PSS",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":b={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":b={name:"RSA-OAEP",hash:`SHA-${parseInt(a.alg.slice(-3),10)||1}`},c=a.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new cd('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(a.alg){case"ES256":b={name:"ECDSA",namedCurve:"P-256"},c=a.d?["sign"]:["verify"];break;case"ES384":b={name:"ECDSA",namedCurve:"P-384"},c=a.d?["sign"]:["verify"];break;case"ES512":b={name:"ECDSA",namedCurve:"P-521"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:"ECDH",namedCurve:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new cd('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(a.alg){case"Ed25519":case"EdDSA":b={name:"Ed25519"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new cd('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new cd('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:b,keyUsages:c}}(a),d={...a};return delete d.alg,delete d.use,crypto.subtle.importKey("jwk",d,b,a.ext??!a.d,a.key_ops??c)},cW=async(a,b,c,d=!1)=>{let f=(e||=new WeakMap).get(a);if(f?.[c])return f[c];let g=await cV({...b,alg:c});return d&&Object.freeze(a),f?f[c]=g:e.set(a,{[c]:g}),g},cX=async(a,b)=>{if(a instanceof Uint8Array||ck(a))return a;if(cl(a)){if("secret"===a.type)return a.export();if("toCryptoKey"in a&&"function"==typeof a.toCryptoKey)try{return((a,b)=>{let c,d=(e||=new WeakMap).get(a);if(d?.[b])return d[b];let f="public"===a.type,g=!!f;if("x25519"===a.asymmetricKeyType){switch(b){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}c=a.toCryptoKey(a.asymmetricKeyType,g,f?[]:["deriveBits"])}if("ed25519"===a.asymmetricKeyType){if("EdDSA"!==b&&"Ed25519"!==b)throw TypeError("given KeyObject instance cannot be used for this algorithm");c=a.toCryptoKey(a.asymmetricKeyType,g,[f?"verify":"sign"])}if("rsa"===a.asymmetricKeyType){let d;switch(b){case"RSA-OAEP":d="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":d="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":d="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":d="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(b.startsWith("RSA-OAEP"))return a.toCryptoKey({name:"RSA-OAEP",hash:d},g,f?["encrypt"]:["decrypt"]);c=a.toCryptoKey({name:b.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:d},g,[f?"verify":"sign"])}if("ec"===a.asymmetricKeyType){let d=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(a.asymmetricKeyDetails?.namedCurve);if(!d)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===b&&"P-256"===d&&(c=a.toCryptoKey({name:"ECDSA",namedCurve:d},g,[f?"verify":"sign"])),"ES384"===b&&"P-384"===d&&(c=a.toCryptoKey({name:"ECDSA",namedCurve:d},g,[f?"verify":"sign"])),"ES512"===b&&"P-521"===d&&(c=a.toCryptoKey({name:"ECDSA",namedCurve:d},g,[f?"verify":"sign"])),b.startsWith("ECDH-ES")&&(c=a.toCryptoKey({name:"ECDH",namedCurve:d},g,f?[]:["deriveBits"]))}if(!c)throw TypeError("given KeyObject instance cannot be used for this algorithm");return d?d[b]=c:e.set(a,{[b]:c}),c})(a,b)}catch(a){if(a instanceof TypeError)throw a}let c=a.export({format:"jwk"});return cW(a,c,b)}if(co(a))return a.k?b7(a.k):cW(a,a,b,!0);throw Error("unreachable")};function cY(a){switch(a){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new cd(`Unsupported JWE Algorithm: ${a}`)}}let cZ=a=>crypto.getRandomValues(new Uint8Array(cY(a)>>3));async function c$(a,b){if(!(a instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(b instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let c={name:"HMAC",hash:"SHA-256"},d=await crypto.subtle.generateKey(c,!1,["sign"]),e=new Uint8Array(await crypto.subtle.sign(c,d,a)),f=new Uint8Array(await crypto.subtle.sign(c,d,b)),g=0,h=-1;for(;++h<32;)g|=e[h]^f[h];return 0===g}async function c_(a,b,c,d,e,f){let g,h;if(!(b instanceof Uint8Array))throw TypeError(cq(b,"Uint8Array"));let i=parseInt(a.slice(1,4),10),j=await crypto.subtle.importKey("raw",b.subarray(i>>3),"AES-CBC",!1,["decrypt"]),k=await crypto.subtle.importKey("raw",b.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),l=b3(f,d,c,b5(f.length<<3)),m=new Uint8Array((await crypto.subtle.sign("HMAC",k,l)).slice(0,i>>3));try{g=await c$(e,m)}catch{}if(!g)throw new ce;try{h=new Uint8Array(await crypto.subtle.decrypt({iv:d,name:"AES-CBC"},j,c))}catch{}if(!h)throw new ce;return h}async function c0(a,b,c,d,e,f){let g;b instanceof Uint8Array?g=await crypto.subtle.importKey("raw",b,"AES-GCM",!1,["decrypt"]):(cC(b,a,"decrypt"),g=b);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:f,iv:d,name:"AES-GCM",tagLength:128},g,b3(c,e)))}catch{throw new ce}}let c1=async(a,b,c,d,e,f)=>{if(!ck(b)&&!(b instanceof Uint8Array))throw TypeError(cq(b,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!d)throw new cf("JWE Initialization Vector missing");if(!e)throw new cf("JWE Authentication Tag missing");switch(cy(a,d),a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return b instanceof Uint8Array&&cz(b,parseInt(a.slice(-3),10)),c_(a,b,c,d,e,f);case"A128GCM":case"A192GCM":case"A256GCM":return b instanceof Uint8Array&&cz(b,parseInt(a.slice(1,4),10)),c0(a,b,c,d,e,f);default:throw new cd("Unsupported JWE Content Encryption Algorithm")}};async function c2(a,b,c,d){let e=a.slice(0,7),f=await cF(e,c,b,d,new Uint8Array(0));return{encryptedKey:f.ciphertext,iv:b8(f.iv),tag:b8(f.tag)}}async function c3(a,b,c,d,e){return c1(a.slice(0,7),b,c,d,e,new Uint8Array(0))}let c4=async(a,b,c,d,e={})=>{let f,g,h;switch(a){case"dir":h=c;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i;if(cj(c),!cN(c))throw new cd("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:j,apv:k}=e;i=e.epk?await cX(e.epk,a):(await crypto.subtle.generateKey(c.algorithm,!0,["deriveBits"])).privateKey;let{x:l,y:m,crv:n,kty:o}=await ct(i),p=await cM(c,i,"ECDH-ES"===a?b:a,"ECDH-ES"===a?cY(b):parseInt(a.slice(-5,-2),10),j,k);if(g={epk:{x:l,crv:n,kty:o}},"EC"===o&&(g.epk.y=m),j&&(g.apu=b8(j)),k&&(g.apv=b8(k)),"ECDH-ES"===a){h=p;break}h=d||cZ(b);let q=a.slice(-6);f=await cI(q,p,h);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":h=d||cZ(b),cj(c),f=await cT(a,c,h);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{h=d||cZ(b);let{p2c:i,p2s:j}=e;({encryptedKey:f,...g}=await cP(a,c,h,i,j));break}case"A128KW":case"A192KW":case"A256KW":h=d||cZ(b),f=await cI(a,c,h);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{h=d||cZ(b);let{iv:i}=e;({encryptedKey:f,...g}=await c2(a,c,h,i));break}default:throw new cd('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:h,encryptedKey:f,parameters:g}},c5=(...a)=>{let b,c=a.filter(Boolean);if(0===c.length||1===c.length)return!0;for(let a of c){let c=Object.keys(a);if(!b||0===b.size){b=new Set(c);continue}for(let a of c){if(b.has(a))return!1;b.add(a)}}return!0},c6=(a,b,c,d,e)=>{let f;if(void 0!==e.crit&&d?.crit===void 0)throw new a('"crit" (Critical) Header Parameter MUST be integrity protected');if(!d||void 0===d.crit)return new Set;if(!Array.isArray(d.crit)||0===d.crit.length||d.crit.some(a=>"string"!=typeof a||0===a.length))throw new a('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let g of(f=void 0!==c?new Map([...Object.entries(c),...b.entries()]):b,d.crit)){if(!f.has(g))throw new cd(`Extension Header Parameter "${g}" is not recognized`);if(void 0===e[g])throw new a(`Extension Header Parameter "${g}" is missing`);if(f.get(g)&&void 0===d[g])throw new a(`Extension Header Parameter "${g}" MUST be integrity protected`)}return new Set(d.crit)},c7=a=>a?.[Symbol.toStringTag],c8=(a,b,c)=>{if(void 0!==b.use){let a;switch(c){case"sign":case"verify":a="sig";break;case"encrypt":case"decrypt":a="enc"}if(b.use!==a)throw TypeError(`Invalid key for this operation, its "use" must be "${a}" when present`)}if(void 0!==b.alg&&b.alg!==a)throw TypeError(`Invalid key for this operation, its "alg" must be "${a}" when present`);if(Array.isArray(b.key_ops)){let d;switch(!0){case"sign"===c||"verify"===c:case"dir"===a:case a.includes("CBC-HS"):d=c;break;case a.startsWith("PBES2"):d="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(a):d=!a.includes("GCM")&&a.endsWith("KW")?"encrypt"===c?"wrapKey":"unwrapKey":c;break;case"encrypt"===c&&a.startsWith("RSA"):d="wrapKey";break;case"decrypt"===c:d=a.startsWith("RSA")?"unwrapKey":"deriveBits"}if(d&&b.key_ops?.includes?.(d)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${d}" when present`)}return!0},c9=(a,b,c)=>{a.startsWith("HS")||"dir"===a||a.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(a)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(a)?((a,b,c)=>{if(!(b instanceof Uint8Array)){if(co(b)){if(function(a){return"oct"===a.kty&&"string"==typeof a.k}(b)&&c8(a,b,c))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!cm(b))throw TypeError(cr(a,b,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==b.type)throw TypeError(`${c7(b)} instances for symmetric algorithms must be of type "secret"`)}})(a,b,c):((a,b,c)=>{if(co(b))switch(c){case"decrypt":case"sign":if(function(a){return"oct"!==a.kty&&"string"==typeof a.d}(b)&&c8(a,b,c))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(a){return"oct"!==a.kty&&void 0===a.d}(b)&&c8(a,b,c))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!cm(b))throw TypeError(cr(a,b,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===b.type)throw TypeError(`${c7(b)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===b.type)switch(c){case"sign":throw TypeError(`${c7(b)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${c7(b)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===b.type)switch(c){case"verify":throw TypeError(`${c7(b)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${c7(b)} instances for asymmetric algorithm encryption must be of type "public"`)}})(a,b,c)};class da{#a;#b;#c;#d;#e;#f;#g;#h;constructor(a){if(!(a instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#a=a}setKeyManagementParameters(a){if(this.#h)throw TypeError("setKeyManagementParameters can only be called once");return this.#h=a,this}setProtectedHeader(a){if(this.#b)throw TypeError("setProtectedHeader can only be called once");return this.#b=a,this}setSharedUnprotectedHeader(a){if(this.#c)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#c=a,this}setUnprotectedHeader(a){if(this.#d)throw TypeError("setUnprotectedHeader can only be called once");return this.#d=a,this}setAdditionalAuthenticatedData(a){return this.#e=a,this}setContentEncryptionKey(a){if(this.#f)throw TypeError("setContentEncryptionKey can only be called once");return this.#f=a,this}setInitializationVector(a){if(this.#g)throw TypeError("setInitializationVector can only be called once");return this.#g=a,this}async encrypt(a,b){let c,d,e,f,g;if(!this.#b&&!this.#d&&!this.#c)throw new cf("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!c5(this.#b,this.#d,this.#c))throw new cf("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let h={...this.#b,...this.#d,...this.#c};if(c6(cf,new Map,b?.crit,this.#b,h),void 0!==h.zip)throw new cd('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:i,enc:j}=h;if("string"!=typeof i||!i)throw new cf('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof j||!j)throw new cf('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#f&&("dir"===i||"ECDH-ES"===i))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${i}`);c9("dir"===i?j:i,a,"encrypt");{let e,f=await cX(a,i);({cek:d,encryptedKey:c,parameters:e}=await c4(i,j,f,this.#f,this.#h)),e&&(b&&cw in b?this.#d?this.#d={...this.#d,...e}:this.setUnprotectedHeader(e):this.#b?this.#b={...this.#b,...e}:this.setProtectedHeader(e))}f=this.#b?b1.encode(b8(JSON.stringify(this.#b))):b1.encode(""),this.#e?(g=b8(this.#e),e=b3(f,b1.encode("."),b1.encode(g))):e=f;let{ciphertext:k,tag:l,iv:m}=await cF(j,this.#a,d,this.#g,e),n={ciphertext:b8(k)};return m&&(n.iv=b8(m)),l&&(n.tag=b8(l)),c&&(n.encrypted_key=b8(c)),g&&(n.aad=g),this.#b&&(n.protected=b2.decode(f)),this.#c&&(n.unprotected=this.#c),this.#d&&(n.header=this.#d),n}}class db{#i;constructor(a){this.#i=new da(a)}setContentEncryptionKey(a){return this.#i.setContentEncryptionKey(a),this}setInitializationVector(a){return this.#i.setInitializationVector(a),this}setProtectedHeader(a){return this.#i.setProtectedHeader(a),this}setKeyManagementParameters(a){return this.#i.setKeyManagementParameters(a),this}async encrypt(a,b){let c=await this.#i.encrypt(a,b);return[c.protected,c.encrypted_key,c.iv,c.ciphertext,c.tag].join(".")}}let dc=a=>Math.floor(a.getTime()/1e3),dd=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,de=a=>{let b,c=dd.exec(a);if(!c||c[4]&&c[1])throw TypeError("Invalid time period format");let d=parseFloat(c[2]);switch(c[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":b=Math.round(d);break;case"minute":case"minutes":case"min":case"mins":case"m":b=Math.round(60*d);break;case"hour":case"hours":case"hr":case"hrs":case"h":b=Math.round(3600*d);break;case"day":case"days":case"d":b=Math.round(86400*d);break;case"week":case"weeks":case"w":b=Math.round(604800*d);break;default:b=Math.round(0x1e187e0*d)}return"-"===c[1]||"ago"===c[4]?-b:b};function df(a,b){if(!Number.isFinite(b))throw TypeError(`Invalid ${a} input`);return b}let dg=a=>a.includes("/")?a.toLowerCase():`application/${a.toLowerCase()}`;class dh{#j;constructor(a){if(!cn(a))throw TypeError("JWT Claims Set MUST be an object");this.#j=structuredClone(a)}data(){return b1.encode(JSON.stringify(this.#j))}get iss(){return this.#j.iss}set iss(a){this.#j.iss=a}get sub(){return this.#j.sub}set sub(a){this.#j.sub=a}get aud(){return this.#j.aud}set aud(a){this.#j.aud=a}set jti(a){this.#j.jti=a}set nbf(a){"number"==typeof a?this.#j.nbf=df("setNotBefore",a):a instanceof Date?this.#j.nbf=df("setNotBefore",dc(a)):this.#j.nbf=dc(new Date)+de(a)}set exp(a){"number"==typeof a?this.#j.exp=df("setExpirationTime",a):a instanceof Date?this.#j.exp=df("setExpirationTime",dc(a)):this.#j.exp=dc(new Date)+de(a)}set iat(a){void 0===a?this.#j.iat=dc(new Date):a instanceof Date?this.#j.iat=df("setIssuedAt",dc(a)):"string"==typeof a?this.#j.iat=df("setIssuedAt",dc(new Date)+de(a)):this.#j.iat=df("setIssuedAt",a)}}class di{#f;#g;#h;#b;#k;#l;#m;#n;constructor(a={}){this.#n=new dh(a)}setIssuer(a){return this.#n.iss=a,this}setSubject(a){return this.#n.sub=a,this}setAudience(a){return this.#n.aud=a,this}setJti(a){return this.#n.jti=a,this}setNotBefore(a){return this.#n.nbf=a,this}setExpirationTime(a){return this.#n.exp=a,this}setIssuedAt(a){return this.#n.iat=a,this}setProtectedHeader(a){if(this.#b)throw TypeError("setProtectedHeader can only be called once");return this.#b=a,this}setKeyManagementParameters(a){if(this.#h)throw TypeError("setKeyManagementParameters can only be called once");return this.#h=a,this}setContentEncryptionKey(a){if(this.#f)throw TypeError("setContentEncryptionKey can only be called once");return this.#f=a,this}setInitializationVector(a){if(this.#g)throw TypeError("setInitializationVector can only be called once");return this.#g=a,this}replicateIssuerAsHeader(){return this.#k=!0,this}replicateSubjectAsHeader(){return this.#l=!0,this}replicateAudienceAsHeader(){return this.#m=!0,this}async encrypt(a,b){let c=new db(this.#n.data());return this.#b&&(this.#k||this.#l||this.#m)&&(this.#b={...this.#b,iss:this.#k?this.#n.iss:void 0,sub:this.#l?this.#n.sub:void 0,aud:this.#m?this.#n.aud:void 0}),c.setProtectedHeader(this.#b),this.#g&&c.setInitializationVector(this.#g),this.#f&&c.setContentEncryptionKey(this.#f),this.#h&&c.setKeyManagementParameters(this.#h),c.encrypt(a,b)}}async function dj(a,b,c){let d;if(!cn(a))throw TypeError("JWK must be an object");switch(b??=a.alg,d??=c?.extractable??a.ext,a.kty){case"oct":if("string"!=typeof a.k||!a.k)throw TypeError('missing "k" (Key Value) Parameter value');return b7(a.k);case"RSA":if("oth"in a&&void 0!==a.oth)throw new cd('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return cV({...a,alg:b,ext:d});default:throw new cd('Unsupported "kty" (Key Type) Parameter value')}}let dk=async(a,b,c,d,e)=>{switch(a){case"dir":if(void 0!==c)throw new cf("Encountered unexpected JWE Encrypted Key");return b;case"ECDH-ES":if(void 0!==c)throw new cf("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let e,f;if(!cn(d.epk))throw new cf('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(cj(b),!cN(b))throw new cd("ECDH with the provided key is not allowed or not supported by your javascript runtime");let g=await dj(d.epk,a);if(cj(g),void 0!==d.apu){if("string"!=typeof d.apu)throw new cf('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{e=b7(d.apu)}catch{throw new cf("Failed to base64url decode the apu")}}if(void 0!==d.apv){if("string"!=typeof d.apv)throw new cf('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{f=b7(d.apv)}catch{throw new cf("Failed to base64url decode the apv")}}let h=await cM(g,b,"ECDH-ES"===a?d.enc:a,"ECDH-ES"===a?cY(d.enc):parseInt(a.slice(-5,-2),10),e,f);if("ECDH-ES"===a)return h;if(void 0===c)throw new cf("JWE Encrypted Key missing");return cJ(a.slice(-6),h,c)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===c)throw new cf("JWE Encrypted Key missing");return cj(b),cU(a,b,c);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let f;if(void 0===c)throw new cf("JWE Encrypted Key missing");if("number"!=typeof d.p2c)throw new cf('JOSE Header "p2c" (PBES2 Count) missing or invalid');let g=e?.maxPBES2Count||1e4;if(d.p2c>g)throw new cf('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof d.p2s)throw new cf('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{f=b7(d.p2s)}catch{throw new cf("Failed to base64url decode the p2s")}return cQ(a,b,c,d.p2c,f)}case"A128KW":case"A192KW":case"A256KW":if(void 0===c)throw new cf("JWE Encrypted Key missing");return cJ(a,b,c);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let e,f;if(void 0===c)throw new cf("JWE Encrypted Key missing");if("string"!=typeof d.iv)throw new cf('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof d.tag)throw new cf('JOSE Header "tag" (Authentication Tag) missing or invalid');try{e=b7(d.iv)}catch{throw new cf("Failed to base64url decode the iv")}try{f=b7(d.tag)}catch{throw new cf("Failed to base64url decode the tag")}return c3(a,b,c,e,f)}default:throw new cd('Invalid or unsupported "alg" (JWE Algorithm) header value')}},dl=(a,b)=>{if(void 0!==b&&(!Array.isArray(b)||b.some(a=>"string"!=typeof a)))throw TypeError(`"${a}" option must be an array of strings`);if(b)return new Set(b)};async function dm(a,b,c){let d,e,f,g,h,i,j;if(!cn(a))throw new cf("Flattened JWE must be an object");if(void 0===a.protected&&void 0===a.header&&void 0===a.unprotected)throw new cf("JOSE Header missing");if(void 0!==a.iv&&"string"!=typeof a.iv)throw new cf("JWE Initialization Vector incorrect type");if("string"!=typeof a.ciphertext)throw new cf("JWE Ciphertext missing or incorrect type");if(void 0!==a.tag&&"string"!=typeof a.tag)throw new cf("JWE Authentication Tag incorrect type");if(void 0!==a.protected&&"string"!=typeof a.protected)throw new cf("JWE Protected Header incorrect type");if(void 0!==a.encrypted_key&&"string"!=typeof a.encrypted_key)throw new cf("JWE Encrypted Key incorrect type");if(void 0!==a.aad&&"string"!=typeof a.aad)throw new cf("JWE AAD incorrect type");if(void 0!==a.header&&!cn(a.header))throw new cf("JWE Shared Unprotected Header incorrect type");if(void 0!==a.unprotected&&!cn(a.unprotected))throw new cf("JWE Per-Recipient Unprotected Header incorrect type");if(a.protected)try{let b=b7(a.protected);d=JSON.parse(b2.decode(b))}catch{throw new cf("JWE Protected Header is invalid")}if(!c5(d,a.header,a.unprotected))throw new cf("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let k={...d,...a.header,...a.unprotected};if(c6(cf,new Map,c?.crit,d,k),void 0!==k.zip)throw new cd('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:l,enc:m}=k;if("string"!=typeof l||!l)throw new cf("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof m||!m)throw new cf("missing JWE Encryption Algorithm (enc) in JWE Header");let n=c&&dl("keyManagementAlgorithms",c.keyManagementAlgorithms),o=c&&dl("contentEncryptionAlgorithms",c.contentEncryptionAlgorithms);if(n&&!n.has(l)||!n&&l.startsWith("PBES2"))throw new cc('"alg" (Algorithm) Header Parameter value not allowed');if(o&&!o.has(m))throw new cc('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==a.encrypted_key)try{e=b7(a.encrypted_key)}catch{throw new cf("Failed to base64url decode the encrypted_key")}let p=!1;"function"==typeof b&&(b=await b(d,a),p=!0),c9("dir"===l?m:l,b,"decrypt");let q=await cX(b,l);try{f=await dk(l,q,e,k,c)}catch(a){if(a instanceof TypeError||a instanceof cf||a instanceof cd)throw a;f=cZ(m)}if(void 0!==a.iv)try{g=b7(a.iv)}catch{throw new cf("Failed to base64url decode the iv")}if(void 0!==a.tag)try{h=b7(a.tag)}catch{throw new cf("Failed to base64url decode the tag")}let r=b1.encode(a.protected??"");i=void 0!==a.aad?b3(r,b1.encode("."),b1.encode(a.aad)):r;try{j=b7(a.ciphertext)}catch{throw new cf("Failed to base64url decode the ciphertext")}let s={plaintext:await c1(m,f,j,g,h,i)};if(void 0!==a.protected&&(s.protectedHeader=d),void 0!==a.aad)try{s.additionalAuthenticatedData=b7(a.aad)}catch{throw new cf("Failed to base64url decode the aad")}return(void 0!==a.unprotected&&(s.sharedUnprotectedHeader=a.unprotected),void 0!==a.header&&(s.unprotectedHeader=a.header),p)?{...s,key:q}:s}async function dn(a,b,c){if(a instanceof Uint8Array&&(a=b2.decode(a)),"string"!=typeof a)throw new cf("Compact JWE must be a string or Uint8Array");let{0:d,1:e,2:f,3:g,4:h,length:i}=a.split(".");if(5!==i)throw new cf("Invalid Compact JWE");let j=await dm({ciphertext:g,iv:f||void 0,protected:d,tag:h||void 0,encrypted_key:e||void 0},b,c),k={plaintext:j.plaintext,protectedHeader:j.protectedHeader};return"function"==typeof b?{...k,key:j.key}:k}async function dp(a,b,c){let d=await dn(a,b,c),e=function(a,b,c={}){var d,e;let f,g;try{f=JSON.parse(b2.decode(b))}catch{}if(!cn(f))throw new cg("JWT Claims Set must be a top-level JSON object");let{typ:h}=c;if(h&&("string"!=typeof a.typ||dg(a.typ)!==dg(h)))throw new ca('unexpected "typ" JWT header value',f,"typ","check_failed");let{requiredClaims:i=[],issuer:j,subject:k,audience:l,maxTokenAge:m}=c,n=[...i];for(let a of(void 0!==m&&n.push("iat"),void 0!==l&&n.push("aud"),void 0!==k&&n.push("sub"),void 0!==j&&n.push("iss"),new Set(n.reverse())))if(!(a in f))throw new ca(`missing required "${a}" claim`,f,a,"missing");if(j&&!(Array.isArray(j)?j:[j]).includes(f.iss))throw new ca('unexpected "iss" claim value',f,"iss","check_failed");if(k&&f.sub!==k)throw new ca('unexpected "sub" claim value',f,"sub","check_failed");if(l&&(d=f.aud,e="string"==typeof l?[l]:l,"string"==typeof d?!e.includes(d):!(Array.isArray(d)&&e.some(Set.prototype.has.bind(new Set(d))))))throw new ca('unexpected "aud" claim value',f,"aud","check_failed");switch(typeof c.clockTolerance){case"string":g=de(c.clockTolerance);break;case"number":g=c.clockTolerance;break;case"undefined":g=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:o}=c,p=dc(o||new Date);if((void 0!==f.iat||m)&&"number"!=typeof f.iat)throw new ca('"iat" claim must be a number',f,"iat","invalid");if(void 0!==f.nbf){if("number"!=typeof f.nbf)throw new ca('"nbf" claim must be a number',f,"nbf","invalid");if(f.nbf>p+g)throw new ca('"nbf" claim timestamp check failed',f,"nbf","check_failed")}if(void 0!==f.exp){if("number"!=typeof f.exp)throw new ca('"exp" claim must be a number',f,"exp","invalid");if(f.exp<=p-g)throw new cb('"exp" claim timestamp check failed',f,"exp","check_failed")}if(m){let a=p-f.iat;if(a-g>("number"==typeof m?m:de(m)))throw new cb('"iat" claim timestamp check failed (too far in the past)',f,"iat","check_failed");if(a<0-g)throw new ca('"iat" claim timestamp check failed (it should be in the past)',f,"iat","check_failed")}return f}(d.protectedHeader,d.plaintext,c),{protectedHeader:f}=d;if(void 0!==f.iss&&f.iss!==e.iss)throw new ca('replicated "iss" claim header parameter mismatch',e,"iss","mismatch");if(void 0!==f.sub&&f.sub!==e.sub)throw new ca('replicated "sub" claim header parameter mismatch',e,"sub","mismatch");if(void 0!==f.aud&&JSON.stringify(f.aud)!==JSON.stringify(e.aud))throw new ca('replicated "aud" claim header parameter mismatch',e,"aud","mismatch");let g={payload:e,protectedHeader:f};return"function"==typeof b?{...g,key:d.key}:g}let dq=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,dr=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,ds=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,dt=/^[\u0020-\u003A\u003D-\u007E]*$/,du=Object.prototype.toString,dv=(()=>{let a=function(){};return a.prototype=Object.create(null),a})();function dw(a,b){let c=new dv,d=a.length;if(d<2)return c;let e=b?.decode||dA,f=0;do{let b=a.indexOf("=",f);if(-1===b)break;let g=a.indexOf(";",f),h=-1===g?d:g;if(b>h){f=a.lastIndexOf(";",b-1)+1;continue}let i=dx(a,f,b),j=dy(a,b,i),k=a.slice(i,j);if(void 0===c[k]){let d=dx(a,b+1,h),f=dy(a,h,d),g=e(a.slice(d,f));c[k]=g}f=h+1}while(f<d);return c}function dx(a,b,c){do{let c=a.charCodeAt(b);if(32!==c&&9!==c)return b}while(++b<c);return c}function dy(a,b,c){for(;b>c;){let c=a.charCodeAt(--b);if(32!==c&&9!==c)return b+1}return c}function dz(a,b,c){let d=c?.encode||encodeURIComponent;if(!dq.test(a))throw TypeError(`argument name is invalid: ${a}`);let e=d(b);if(!dr.test(e))throw TypeError(`argument val is invalid: ${b}`);let f=a+"="+e;if(!c)return f;if(void 0!==c.maxAge){if(!Number.isInteger(c.maxAge))throw TypeError(`option maxAge is invalid: ${c.maxAge}`);f+="; Max-Age="+c.maxAge}if(c.domain){if(!ds.test(c.domain))throw TypeError(`option domain is invalid: ${c.domain}`);f+="; Domain="+c.domain}if(c.path){if(!dt.test(c.path))throw TypeError(`option path is invalid: ${c.path}`);f+="; Path="+c.path}if(c.expires){var g;if(g=c.expires,"[object Date]"!==du.call(g)||!Number.isFinite(c.expires.valueOf()))throw TypeError(`option expires is invalid: ${c.expires}`);f+="; Expires="+c.expires.toUTCString()}if(c.httpOnly&&(f+="; HttpOnly"),c.secure&&(f+="; Secure"),c.partitioned&&(f+="; Partitioned"),c.priority)switch("string"==typeof c.priority?c.priority.toLowerCase():void 0){case"low":f+="; Priority=Low";break;case"medium":f+="; Priority=Medium";break;case"high":f+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${c.priority}`)}if(c.sameSite)switch("string"==typeof c.sameSite?c.sameSite.toLowerCase():c.sameSite){case!0:case"strict":f+="; SameSite=Strict";break;case"lax":f+="; SameSite=Lax";break;case"none":f+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${c.sameSite}`)}return f}function dA(a){if(-1===a.indexOf("%"))return a;try{return decodeURIComponent(a)}catch(b){return a}}let{q:dB}=i,dC="A256CBC-HS512";async function dD(a){let{token:b={},secret:c,maxAge:d=2592e3,salt:e}=a,f=Array.isArray(c)?c:[c],g=await dF(dC,f[0],e),h=await cv({kty:"oct",k:b8(g)},`sha${g.byteLength<<3}`);return await new di(b).setProtectedHeader({alg:"dir",enc:dC,kid:h}).setIssuedAt().setExpirationTime((Date.now()/1e3|0)+d).setJti(crypto.randomUUID()).encrypt(g)}async function dE(a){let{token:b,secret:c,salt:d}=a,e=Array.isArray(c)?c:[c];if(!b)return null;let{payload:f}=await dp(b,async({kid:a,enc:b})=>{for(let c of e){let e=await dF(b,c,d);if(void 0===a||a===await cv({kty:"oct",k:b8(e)},`sha${e.byteLength<<3}`))return e}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[dC,"A256GCM"]});return f}async function dF(a,b,c){let d;switch(a){case"A256CBC-HS512":d=64;break;case"A256GCM":d=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await b_("sha256",b,c,`Auth.js Generated Encryption Key (${c})`,d)}async function dG({options:a,paramValue:b,cookieValue:c}){let{url:d,callbacks:e}=a,f=d.origin;return b?f=await e.redirect({url:b,baseUrl:d.origin}):c&&(f=await e.redirect({url:c,baseUrl:d.origin})),{callbackUrl:f,callbackUrlCookie:f!==c?f:void 0}}let dH="\x1b[31m",dI="\x1b[0m",dJ={error(a){let b=a instanceof bi?a.type:a.name;if(console.error(`${dH}[auth][error]${dI} ${b}: ${a.message}`),a.cause&&"object"==typeof a.cause&&"err"in a.cause&&a.cause.err instanceof Error){let{err:b,...c}=a.cause;console.error(`${dH}[auth][cause]${dI}:`,b.stack),c&&console.error(`${dH}[auth][details]${dI}:`,JSON.stringify(c,null,2))}else a.stack&&console.error(a.stack.replace(/.*/,"").substring(1))},warn(a){console.warn(`\x1b[33m[auth][warn][${a}]${dI}`,"Read more: https://warnings.authjs.dev")},debug(a,b){console.log(`\x1b[90m[auth][debug]:${dI} ${a}`,JSON.stringify(b,null,2))}};function dK(a){let b={...dJ};return a.debug||(b.debug=()=>{}),a.logger?.error&&(b.error=a.logger.error),a.logger?.warn&&(b.warn=a.logger.warn),a.logger?.debug&&(b.debug=a.logger.debug),a.logger??(a.logger=b),b}let dL=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:dM,l:dN}=i;async function dO(a){if(!("body"in a)||!a.body||"POST"!==a.method)return;let b=a.headers.get("content-type");return b?.includes("application/json")?await a.json():b?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await a.text())):void 0}async function dP(a,b){try{if("GET"!==a.method&&"POST"!==a.method)throw new bF("Only GET and POST requests are supported");b.basePath??(b.basePath="/auth");let c=new URL(a.url),{action:d,providerId:e}=function(a,b){let c=a.match(RegExp(`^${b}(.+)`));if(null===c)throw new bF(`Cannot parse action at ${a}`);let d=c.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==d.length&&2!==d.length)throw new bF(`Cannot parse action at ${a}`);let[e,f]=d;if(!dL.includes(e)||f&&!["signin","callback","webauthn-options"].includes(e))throw new bF(`Cannot parse action at ${a}`);return{action:e,providerId:"undefined"==f?void 0:f}}(c.pathname,b.basePath);return{url:c,action:d,providerId:e,method:a.method,headers:Object.fromEntries(a.headers),body:a.body?await dO(a):void 0,cookies:dM(a.headers.get("cookie")??"")??{},error:c.searchParams.get("error")??void 0,query:Object.fromEntries(c.searchParams)}}catch(d){let c=dK(b);c.error(d),c.debug("request",a)}}function dQ(a){let b=new Headers(a.headers);a.cookies?.forEach(a=>{let{name:c,value:d,options:e}=a,f=dN(c,d,e);b.has("Set-Cookie")?b.append("Set-Cookie",f):b.set("Set-Cookie",f)});let c=a.body;"application/json"===b.get("content-type")?c=JSON.stringify(a.body):"application/x-www-form-urlencoded"===b.get("content-type")&&(c=new URLSearchParams(a.body).toString());let d=new Response(c,{headers:b,status:a.redirect?302:a.status??200});return a.redirect&&d.headers.set("Location",a.redirect),d}async function dR(a){let b=new TextEncoder().encode(a);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",b))).map(a=>a.toString(16).padStart(2,"0")).join("").toString()}function dS(a){return Array.from(crypto.getRandomValues(new Uint8Array(a))).reduce((a,b)=>a+("0"+b.toString(16)).slice(-2),"")}async function dT({options:a,cookieValue:b,isPost:c,bodyValue:d}){if(b){let[e,f]=b.split("|");if(f===await dR(`${e}${a.secret}`))return{csrfTokenVerified:c&&e===d,csrfToken:e}}let e=dS(32),f=await dR(`${e}${a.secret}`);return{cookie:`${e}|${f}`,csrfToken:e}}function dU(a,b){if(!b)throw new bK(`CSRF token was missing during an action ${a}`)}function dV(a){return null!==a&&"object"==typeof a}function dW(a,...b){if(!b.length)return a;let c=b.shift();if(dV(a)&&dV(c))for(let b in c)dV(c[b])?(dV(a[b])||(a[b]=Array.isArray(c[b])?[]:{}),dW(a[b],c[b])):void 0!==c[b]&&(a[b]=c[b]);return dW(a,...b)}let dX=Symbol("skip-csrf-check"),dY=Symbol("return-type-raw"),dZ=Symbol("custom-fetch"),d$=Symbol("conform-internal"),d_=a=>d1({id:a.sub??a.id??crypto.randomUUID(),name:a.name??a.nickname??a.preferred_username,email:a.email,image:a.picture}),d0=a=>d1({access_token:a.access_token,id_token:a.id_token,refresh_token:a.refresh_token,expires_at:a.expires_at,scope:a.scope,token_type:a.token_type,session_state:a.session_state});function d1(a){let b={};for(let[c,d]of Object.entries(a))void 0!==d&&(b[c]=d);return b}function d2(a,b){if(!a&&b)return;if("string"==typeof a)return{url:new URL(a)};let c=new URL(a?.url??"https://authjs.dev");if(a?.params!=null)for(let[b,d]of Object.entries(a.params))"claims"===b&&(d=JSON.stringify(d)),c.searchParams.set(b,String(d));return{url:c,request:a?.request,conform:a?.conform,...a?.clientPrivateKey?{clientPrivateKey:a?.clientPrivateKey}:null}}let d3={signIn:()=>!0,redirect:({url:a,baseUrl:b})=>a.startsWith("/")?`${b}${a}`:new URL(a).origin===b?a:b,session:({session:a})=>({user:{name:a.user?.name,email:a.user?.email,image:a.user?.image},expires:a.expires?.toISOString?.()??a.expires}),jwt:({token:a})=>a};async function d4({authOptions:a,providerId:b,action:c,url:d,cookies:e,callbackUrl:f,csrfToken:g,csrfDisabled:h,isPost:i}){var j,k;let l=dK(a),{providers:m,provider:n}=function(a){let{providerId:b,config:c}=a,d=new URL(c.basePath??"/auth",a.url.origin),e=c.providers.map(a=>{let b="function"==typeof a?a():a,{options:e,...f}=b,g=e?.id??f.id,h=dW(f,e,{signinUrl:`${d}/signin/${g}`,callbackUrl:`${d}/callback/${g}`});if("oauth"===b.type||"oidc"===b.type){h.redirectProxyUrl??(h.redirectProxyUrl=e?.redirectProxyUrl??c.redirectProxyUrl);let a=function(a){a.issuer&&(a.wellKnown??(a.wellKnown=`${a.issuer}/.well-known/openid-configuration`));let b=d2(a.authorization,a.issuer);b&&!b.url?.searchParams.has("scope")&&b.url.searchParams.set("scope","openid profile email");let c=d2(a.token,a.issuer),d=d2(a.userinfo,a.issuer),e=a.checks??["pkce"];return a.redirectProxyUrl&&(e.includes("state")||e.push("state"),a.redirectProxyUrl=`${a.redirectProxyUrl}/callback/${a.id}`),{...a,authorization:b,token:c,checks:e,userinfo:d,profile:a.profile??d_,account:a.account??d0}}(h);return a.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete a.redirectProxyUrl,a[dZ]??(a[dZ]=e?.[dZ]),a}return h}),f=e.find(({id:a})=>a===b);if(b&&!f){let a=e.map(a=>a.id).join(", ");throw Error(`Provider with id "${b}" not found. Available providers: [${a}].`)}return{providers:e,provider:f}}({url:d,providerId:b,config:a}),o=!1;if((n?.type==="oauth"||n?.type==="oidc")&&n.redirectProxyUrl)try{o=new URL(n.redirectProxyUrl).origin===d.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${n.redirectProxyUrl}`)}let p={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...a,url:d,action:c,provider:n,cookies:dW(bg(a.useSecureCookies??"https:"===d.protocol),a.cookies),providers:m,session:{strategy:a.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...a.session},jwt:{secret:a.secret,maxAge:a.session?.maxAge??2592e3,encode:dD,decode:dE,...a.jwt},events:(j=a.events??{},k=l,Object.keys(j).reduce((a,b)=>(a[b]=async(...a)=>{try{let c=j[b];return await c(...a)}catch(a){k.error(new bo(a))}},a),{})),adapter:function(a,b){if(a)return Object.keys(a).reduce((c,d)=>(c[d]=async(...c)=>{try{b.debug(`adapter_${d}`,{args:c});let e=a[d];return await e(...c)}catch(c){let a=new bk(c);throw b.error(a),a}},c),{})}(a.adapter,l),callbacks:{...d3,...a.callbacks},logger:l,callbackUrl:d.origin,isOnRedirectProxy:o,experimental:{...a.experimental}},q=[];if(h)p.csrfTokenVerified=!0;else{let{csrfToken:a,cookie:b,csrfTokenVerified:c}=await dT({options:p,cookieValue:e?.[p.cookies.csrfToken.name],isPost:i,bodyValue:g});p.csrfToken=a,p.csrfTokenVerified=c,b&&q.push({name:p.cookies.csrfToken.name,value:b,options:p.cookies.csrfToken.options})}let{callbackUrl:r,callbackUrlCookie:s}=await dG({options:p,cookieValue:e?.[p.cookies.callbackUrl.name],paramValue:f});return p.callbackUrl=r,s&&q.push({name:p.cookies.callbackUrl.name,value:s,options:p.cookies.callbackUrl.options}),{options:p,cookies:q}}var d5,d6,d7,d8,d9,ea,eb,ec,ed,ee,ef,eg,eh,ei,ej,ek,el={},em=[],en=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,eo=Array.isArray;function ep(a,b){for(var c in b)a[c]=b[c];return a}function eq(a){a&&a.parentNode&&a.parentNode.removeChild(a)}function er(a,b,c,d,e){var f={type:a,props:b,key:c,ref:d,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==e?++ed:e,__i:-1,__u:0};return null==e&&null!=ec.vnode&&ec.vnode(f),f}function es(a){return a.children}function et(a,b){this.props=a,this.context=b}function eu(a,b){if(null==b)return a.__?eu(a.__,a.__i+1):null;for(var c;b<a.__k.length;b++)if(null!=(c=a.__k[b])&&null!=c.__e)return c.__e;return"function"==typeof a.type?eu(a):null}function ev(a){(!a.__d&&(a.__d=!0)&&ee.push(a)&&!ew.__r++||ef!==ec.debounceRendering)&&((ef=ec.debounceRendering)||eg)(ew)}function ew(){var a,b,c,d,e,f,g,h;for(ee.sort(eh);a=ee.shift();)a.__d&&(b=ee.length,d=void 0,f=(e=(c=a).__v).__e,g=[],h=[],c.__P&&((d=ep({},e)).__v=e.__v+1,ec.vnode&&ec.vnode(d),eB(c.__P,d,e,c.__n,c.__P.namespaceURI,32&e.__u?[f]:null,g,null==f?eu(e):f,!!(32&e.__u),h),d.__v=e.__v,d.__.__k[d.__i]=d,function(a,b,c){b.__d=void 0;for(var d=0;d<c.length;d++)eC(c[d],c[++d],c[++d]);ec.__c&&ec.__c(b,a),a.some(function(b){try{a=b.__h,b.__h=[],a.some(function(a){a.call(b)})}catch(a){ec.__e(a,b.__v)}})}(g,d,h),d.__e!=f&&function a(b){var c,d;if(null!=(b=b.__)&&null!=b.__c){for(b.__e=b.__c.base=null,c=0;c<b.__k.length;c++)if(null!=(d=b.__k[c])&&null!=d.__e){b.__e=b.__c.base=d.__e;break}return a(b)}}(d)),ee.length>b&&ee.sort(eh));ew.__r=0}function ex(a,b,c,d,e,f,g,h,i,j,k){var l,m,n,o,p,q=d&&d.__k||em,r=b.length;for(c.__d=i,function(a,b,c){var d,e,f,g,h,i=b.length,j=c.length,k=j,l=0;for(a.__k=[],d=0;d<i;d++)null!=(e=b[d])&&"boolean"!=typeof e&&"function"!=typeof e?(g=d+l,(e=a.__k[d]="string"==typeof e||"number"==typeof e||"bigint"==typeof e||e.constructor==String?er(null,e,null,null,null):eo(e)?er(es,{children:e},null,null,null):void 0===e.constructor&&e.__b>0?er(e.type,e.props,e.key,e.ref?e.ref:null,e.__v):e).__=a,e.__b=a.__b+1,f=null,-1!==(h=e.__i=function(a,b,c,d){var e=a.key,f=a.type,g=c-1,h=c+1,i=b[c];if(null===i||i&&e==i.key&&f===i.type&&0==(131072&i.__u))return c;if(d>+(null!=i&&0==(131072&i.__u)))for(;g>=0||h<b.length;){if(g>=0){if((i=b[g])&&0==(131072&i.__u)&&e==i.key&&f===i.type)return g;g--}if(h<b.length){if((i=b[h])&&0==(131072&i.__u)&&e==i.key&&f===i.type)return h;h++}}return -1}(e,c,g,k))&&(k--,(f=c[h])&&(f.__u|=131072)),null==f||null===f.__v?(-1==h&&l--,"function"!=typeof e.type&&(e.__u|=65536)):h!==g&&(h==g-1?l--:h==g+1?l++:(h>g?l--:l++,e.__u|=65536))):e=a.__k[d]=null;if(k)for(d=0;d<j;d++)null!=(f=c[d])&&0==(131072&f.__u)&&(f.__e==a.__d&&(a.__d=eu(f)),function a(b,c,d){var e,f;if(ec.unmount&&ec.unmount(b),(e=b.ref)&&(e.current&&e.current!==b.__e||eC(e,null,c)),null!=(e=b.__c)){if(e.componentWillUnmount)try{e.componentWillUnmount()}catch(a){ec.__e(a,c)}e.base=e.__P=null}if(e=b.__k)for(f=0;f<e.length;f++)e[f]&&a(e[f],c,d||"function"!=typeof b.type);d||eq(b.__e),b.__c=b.__=b.__e=b.__d=void 0}(f,f))}(c,b,q),i=c.__d,l=0;l<r;l++)null!=(n=c.__k[l])&&(m=-1===n.__i?el:q[n.__i]||el,n.__i=l,eB(a,n,m,e,f,g,h,i,j,k),o=n.__e,n.ref&&m.ref!=n.ref&&(m.ref&&eC(m.ref,null,n),k.push(n.ref,n.__c||o,n)),null==p&&null!=o&&(p=o),65536&n.__u||m.__k===n.__k?i=function a(b,c,d){var e,f;if("function"==typeof b.type){for(e=b.__k,f=0;e&&f<e.length;f++)e[f]&&(e[f].__=b,c=a(e[f],c,d));return c}b.__e!=c&&(c&&b.type&&!d.contains(c)&&(c=eu(b)),d.insertBefore(b.__e,c||null),c=b.__e);do c=c&&c.nextSibling;while(null!=c&&8===c.nodeType);return c}(n,i,a):"function"==typeof n.type&&void 0!==n.__d?i=n.__d:o&&(i=o.nextSibling),n.__d=void 0,n.__u&=-196609);c.__d=i,c.__e=p}function ey(a,b,c){"-"===b[0]?a.setProperty(b,null==c?"":c):a[b]=null==c?"":"number"!=typeof c||en.test(b)?c:c+"px"}function ez(a,b,c,d,e){var f;a:if("style"===b)if("string"==typeof c)a.style.cssText=c;else{if("string"==typeof d&&(a.style.cssText=d=""),d)for(b in d)c&&b in c||ey(a.style,b,"");if(c)for(b in c)d&&c[b]===d[b]||ey(a.style,b,c[b])}else if("o"===b[0]&&"n"===b[1])f=b!==(b=b.replace(/(PointerCapture)$|Capture$/i,"$1")),b=b.toLowerCase()in a||"onFocusOut"===b||"onFocusIn"===b?b.toLowerCase().slice(2):b.slice(2),a.l||(a.l={}),a.l[b+f]=c,c?d?c.u=d.u:(c.u=ei,a.addEventListener(b,f?ek:ej,f)):a.removeEventListener(b,f?ek:ej,f);else{if("http://www.w3.org/2000/svg"==e)b=b.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=b&&"height"!=b&&"href"!=b&&"list"!=b&&"form"!=b&&"tabIndex"!=b&&"download"!=b&&"rowSpan"!=b&&"colSpan"!=b&&"role"!=b&&"popover"!=b&&b in a)try{a[b]=null==c?"":c;break a}catch(a){}"function"==typeof c||(null==c||!1===c&&"-"!==b[4]?a.removeAttribute(b):a.setAttribute(b,"popover"==b&&1==c?"":c))}}function eA(a){return function(b){if(this.l){var c=this.l[b.type+a];if(null==b.t)b.t=ei++;else if(b.t<c.u)return;return c(ec.event?ec.event(b):b)}}}function eB(a,b,c,d,e,f,g,h,i,j){var k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A=b.type;if(void 0!==b.constructor)return null;128&c.__u&&(i=!!(32&c.__u),f=[h=b.__e=c.__e]),(k=ec.__b)&&k(b);a:if("function"==typeof A)try{if(r=b.props,s="prototype"in A&&A.prototype.render,t=(k=A.contextType)&&d[k.__c],u=k?t?t.props.value:k.__:d,c.__c?q=(l=b.__c=c.__c).__=l.__E:(s?b.__c=l=new A(r,u):(b.__c=l=new et(r,u),l.constructor=A,l.render=eD),t&&t.sub(l),l.props=r,l.state||(l.state={}),l.context=u,l.__n=d,m=l.__d=!0,l.__h=[],l._sb=[]),s&&null==l.__s&&(l.__s=l.state),s&&null!=A.getDerivedStateFromProps&&(l.__s==l.state&&(l.__s=ep({},l.__s)),ep(l.__s,A.getDerivedStateFromProps(r,l.__s))),n=l.props,o=l.state,l.__v=b,m)s&&null==A.getDerivedStateFromProps&&null!=l.componentWillMount&&l.componentWillMount(),s&&null!=l.componentDidMount&&l.__h.push(l.componentDidMount);else{if(s&&null==A.getDerivedStateFromProps&&r!==n&&null!=l.componentWillReceiveProps&&l.componentWillReceiveProps(r,u),!l.__e&&(null!=l.shouldComponentUpdate&&!1===l.shouldComponentUpdate(r,l.__s,u)||b.__v===c.__v)){for(b.__v!==c.__v&&(l.props=r,l.state=l.__s,l.__d=!1),b.__e=c.__e,b.__k=c.__k,b.__k.some(function(a){a&&(a.__=b)}),v=0;v<l._sb.length;v++)l.__h.push(l._sb[v]);l._sb=[],l.__h.length&&g.push(l);break a}null!=l.componentWillUpdate&&l.componentWillUpdate(r,l.__s,u),s&&null!=l.componentDidUpdate&&l.__h.push(function(){l.componentDidUpdate(n,o,p)})}if(l.context=u,l.props=r,l.__P=a,l.__e=!1,w=ec.__r,x=0,s){for(l.state=l.__s,l.__d=!1,w&&w(b),k=l.render(l.props,l.state,l.context),y=0;y<l._sb.length;y++)l.__h.push(l._sb[y]);l._sb=[]}else do l.__d=!1,w&&w(b),k=l.render(l.props,l.state,l.context),l.state=l.__s;while(l.__d&&++x<25);l.state=l.__s,null!=l.getChildContext&&(d=ep(ep({},d),l.getChildContext())),s&&!m&&null!=l.getSnapshotBeforeUpdate&&(p=l.getSnapshotBeforeUpdate(n,o)),ex(a,eo(z=null!=k&&k.type===es&&null==k.key?k.props.children:k)?z:[z],b,c,d,e,f,g,h,i,j),l.base=b.__e,b.__u&=-161,l.__h.length&&g.push(l),q&&(l.__E=l.__=null)}catch(a){if(b.__v=null,i||null!=f){for(b.__u|=i?160:128;h&&8===h.nodeType&&h.nextSibling;)h=h.nextSibling;f[f.indexOf(h)]=null,b.__e=h}else b.__e=c.__e,b.__k=c.__k;ec.__e(a,b,c)}else null==f&&b.__v===c.__v?(b.__k=c.__k,b.__e=c.__e):b.__e=function(a,b,c,d,e,f,g,h,i){var j,k,l,m,n,o,p,q=c.props,r=b.props,s=b.type;if("svg"===s?e="http://www.w3.org/2000/svg":"math"===s?e="http://www.w3.org/1998/Math/MathML":e||(e="http://www.w3.org/1999/xhtml"),null!=f){for(j=0;j<f.length;j++)if((n=f[j])&&"setAttribute"in n==!!s&&(s?n.localName===s:3===n.nodeType)){a=n,f[j]=null;break}}if(null==a){if(null===s)return document.createTextNode(r);a=document.createElementNS(e,s,r.is&&r),h&&(ec.__m&&ec.__m(b,f),h=!1),f=null}if(null===s)q===r||h&&a.data===r||(a.data=r);else{if(f=f&&eb.call(a.childNodes),q=c.props||el,!h&&null!=f)for(q={},j=0;j<a.attributes.length;j++)q[(n=a.attributes[j]).name]=n.value;for(j in q)if(n=q[j],"children"==j);else if("dangerouslySetInnerHTML"==j)l=n;else if(!(j in r)){if("value"==j&&"defaultValue"in r||"checked"==j&&"defaultChecked"in r)continue;ez(a,j,null,n,e)}for(j in r)n=r[j],"children"==j?m=n:"dangerouslySetInnerHTML"==j?k=n:"value"==j?o=n:"checked"==j?p=n:h&&"function"!=typeof n||q[j]===n||ez(a,j,n,q[j],e);if(k)h||l&&(k.__html===l.__html||k.__html===a.innerHTML)||(a.innerHTML=k.__html),b.__k=[];else if(l&&(a.innerHTML=""),ex(a,eo(m)?m:[m],b,c,d,"foreignObject"===s?"http://www.w3.org/1999/xhtml":e,f,g,f?f[0]:c.__k&&eu(c,0),h,i),null!=f)for(j=f.length;j--;)eq(f[j]);h||(j="value","progress"===s&&null==o?a.removeAttribute("value"):void 0===o||o===a[j]&&("progress"!==s||o)&&("option"!==s||o===q[j])||ez(a,j,o,q[j],e),j="checked",void 0!==p&&p!==a[j]&&ez(a,j,p,q[j],e))}return a}(c.__e,b,c,d,e,f,g,i,j);(k=ec.diffed)&&k(b)}function eC(a,b,c){try{if("function"==typeof a){var d="function"==typeof a.__u;d&&a.__u(),d&&null==b||(a.__u=a(b))}else a.current=b}catch(a){ec.__e(a,c)}}function eD(a,b,c){return this.constructor(a,c)}eb=em.slice,ec={__e:function(a,b,c,d){for(var e,f,g;b=b.__;)if((e=b.__c)&&!e.__)try{if((f=e.constructor)&&null!=f.getDerivedStateFromError&&(e.setState(f.getDerivedStateFromError(a)),g=e.__d),null!=e.componentDidCatch&&(e.componentDidCatch(a,d||{}),g=e.__d),g)return e.__E=e}catch(b){a=b}throw a}},ed=0,et.prototype.setState=function(a,b){var c;c=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=ep({},this.state),"function"==typeof a&&(a=a(ep({},c),this.props)),a&&ep(c,a),null!=a&&this.__v&&(b&&this._sb.push(b),ev(this))},et.prototype.forceUpdate=function(a){this.__v&&(this.__e=!0,a&&this.__h.push(a),ev(this))},et.prototype.render=es,ee=[],eg="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,eh=function(a,b){return a.__v.__b-b.__v.__b},ew.__r=0,ei=0,ej=eA(!1),ek=eA(!0);var eE=/[\s\n\\/='"\0<>]/,eF=/^(xlink|xmlns|xml)([A-Z])/,eG=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,eH=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,eI=new Set(["draggable","spellcheck"]),eJ=/["&<]/;function eK(a){if(0===a.length||!1===eJ.test(a))return a;for(var b=0,c=0,d="",e="";c<a.length;c++){switch(a.charCodeAt(c)){case 34:e="&quot;";break;case 38:e="&amp;";break;case 60:e="&lt;";break;default:continue}c!==b&&(d+=a.slice(b,c)),d+=e,b=c+1}return c!==b&&(d+=a.slice(b,c)),d}var eL={},eM=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),eN=/[A-Z]/g;function eO(){this.__d=!0}var eP,eQ,eR,eS,eT={},eU=[],eV=Array.isArray,eW=Object.assign;function eX(a,b){var c,d=a.type,e=!0;return a.__c?(e=!1,(c=a.__c).state=c.__s):c=new d(a.props,b),a.__c=c,c.__v=a,c.props=a.props,c.context=b,c.__d=!0,null==c.state&&(c.state=eT),null==c.__s&&(c.__s=c.state),d.getDerivedStateFromProps?c.state=eW({},c.state,d.getDerivedStateFromProps(c.props,c.state)):e&&c.componentWillMount?(c.componentWillMount(),c.state=c.__s!==c.state?c.__s:c.state):!e&&c.componentWillUpdate&&c.componentWillUpdate(),eR&&eR(a),c.render(c.props,c.state,b)}var eY=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),eZ=0;function e$(a,b,c,d,e,f){b||(b={});var g,h,i=b;"ref"in b&&(g=b.ref,delete b.ref);var j={type:a,props:i,key:c,ref:g,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--eZ,__i:-1,__u:0,__source:e,__self:f};if("function"==typeof a&&(g=a.defaultProps))for(h in g)void 0===i[h]&&(i[h]=g[h]);return ec.vnode&&ec.vnode(j),j}async function e_(a,b){let c=window.SimpleWebAuthnBrowser;async function d(c){let d=new URL(`${a}/webauthn-options/${b}`);c&&d.searchParams.append("action",c),f().forEach(a=>{d.searchParams.append(a.name,a.value)});let e=await fetch(d);return e.ok?e.json():void console.error("Failed to fetch options",e)}function e(){let a=`#${b}-form`,c=document.querySelector(a);if(!c)throw Error(`Form '${a}' not found`);return c}function f(){return Array.from(e().querySelectorAll("input[data-form-field]"))}async function g(a,b){let c=e();if(a){let b=document.createElement("input");b.type="hidden",b.name="action",b.value=a,c.appendChild(b)}if(b){let a=document.createElement("input");a.type="hidden",a.name="data",a.value=JSON.stringify(b),c.appendChild(a)}return c.submit()}async function h(a,b){let d=await c.startAuthentication(a,b);return await g("authenticate",d)}async function i(a){f().forEach(a=>{if(a.required&&!a.value)throw Error(`Missing required field: ${a.name}`)});let b=await c.startRegistration(a);return await g("register",b)}async function j(){if(!c.browserSupportsWebAuthnAutofill())return;let a=await d("authenticate");if(!a)return void console.error("Failed to fetch option for autofill authentication");try{await h(a.options,!0)}catch(a){console.error(a)}}(async function(){let a=e();if(!c.browserSupportsWebAuthn()){a.style.display="none";return}a&&a.addEventListener("submit",async a=>{a.preventDefault();let b=await d(void 0);if(!b)return void console.error("Failed to fetch options for form submission");if("authenticate"===b.action)try{await h(b.options,!1)}catch(a){console.error(a)}else if("register"===b.action)try{await i(b.options)}catch(a){console.error(a)}})})(),j()}let e0={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},e1=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function e2({html:a,title:b,status:c,cookies:d,theme:e,headTags:f}){return{cookies:d,status:c,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${e1}</style><title>${b}</title>${f??""}</head><body class="__next-auth-theme-${e?.colorScheme??"auto"}"><div class="page">${function(a,b,c){var d=ec.__s;ec.__s=!0,eP=ec.__b,eQ=ec.diffed,eR=ec.__r,eS=ec.unmount;var e=function(a,b,c){var d,e,f,g={};for(f in b)"key"==f?d=b[f]:"ref"==f?e=b[f]:g[f]=b[f];if(arguments.length>2&&(g.children=arguments.length>3?eb.call(arguments,2):c),"function"==typeof a&&null!=a.defaultProps)for(f in a.defaultProps)void 0===g[f]&&(g[f]=a.defaultProps[f]);return er(a,g,d,e,null)}(es,null);e.__k=[a];try{var f=function a(b,c,d,e,f,g,h){if(null==b||!0===b||!1===b||""===b)return"";var i=typeof b;if("object"!=i)return"function"==i?"":"string"==i?eK(b):b+"";if(eV(b)){var j,k="";f.__k=b;for(var l=0;l<b.length;l++){var m=b[l];if(null!=m&&"boolean"!=typeof m){var n,o=a(m,c,d,e,f,g,h);"string"==typeof o?k+=o:(j||(j=[]),k&&j.push(k),k="",eV(o)?(n=j).push.apply(n,o):j.push(o))}}return j?(k&&j.push(k),j):k}if(void 0!==b.constructor)return"";b.__=f,eP&&eP(b);var p=b.type,q=b.props;if("function"==typeof p){var r,s,t,u=c;if(p===es){if("tpl"in q){for(var v="",w=0;w<q.tpl.length;w++)if(v+=q.tpl[w],q.exprs&&w<q.exprs.length){var x=q.exprs[w];if(null==x)continue;"object"==typeof x&&(void 0===x.constructor||eV(x))?v+=a(x,c,d,e,b,g,h):v+=x}return v}if("UNSTABLE_comment"in q)return"\x3c!--"+eK(q.UNSTABLE_comment)+"--\x3e";s=q.children}else{if(null!=(r=p.contextType)){var y=c[r.__c];u=y?y.props.value:r.__}var z=p.prototype&&"function"==typeof p.prototype.render;if(z)s=eX(b,u),t=b.__c;else{b.__c=t={__v:b,context:u,props:b.props,setState:eO,forceUpdate:eO,__d:!0,__h:[]};for(var A=0;t.__d&&A++<25;)t.__d=!1,eR&&eR(b),s=p.call(t,q,u);t.__d=!0}if(null!=t.getChildContext&&(c=eW({},c,t.getChildContext())),z&&ec.errorBoundaries&&(p.getDerivedStateFromError||t.componentDidCatch)){s=null!=s&&s.type===es&&null==s.key&&null==s.props.tpl?s.props.children:s;try{return a(s,c,d,e,b,g,h)}catch(f){return p.getDerivedStateFromError&&(t.__s=p.getDerivedStateFromError(f)),t.componentDidCatch&&t.componentDidCatch(f,eT),t.__d?(s=eX(b,c),null!=(t=b.__c).getChildContext&&(c=eW({},c,t.getChildContext())),a(s=null!=s&&s.type===es&&null==s.key&&null==s.props.tpl?s.props.children:s,c,d,e,b,g,h)):""}finally{eQ&&eQ(b),b.__=null,eS&&eS(b)}}}s=null!=s&&s.type===es&&null==s.key&&null==s.props.tpl?s.props.children:s;try{var B=a(s,c,d,e,b,g,h);return eQ&&eQ(b),b.__=null,ec.unmount&&ec.unmount(b),B}catch(f){if(!g&&h&&h.onError){var C=h.onError(f,b,function(f){return a(f,c,d,e,b,g,h)});if(void 0!==C)return C;var D=ec.__e;return D&&D(f,b),""}if(!g||!f||"function"!=typeof f.then)throw f;return f.then(function f(){try{return a(s,c,d,e,b,g,h)}catch(i){if(!i||"function"!=typeof i.then)throw i;return i.then(function(){return a(s,c,d,e,b,g,h)},f)}})}}var E,F="<"+p,G="";for(var H in q){var I=q[H];if("function"!=typeof I||"class"===H||"className"===H){switch(H){case"children":E=I;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in q)continue;H="for";break;case"className":if("class"in q)continue;H="class";break;case"defaultChecked":H="checked";break;case"defaultSelected":H="selected";break;case"defaultValue":case"value":switch(H="value",p){case"textarea":E=I;continue;case"select":e=I;continue;case"option":e!=I||"selected"in q||(F+=" selected")}break;case"dangerouslySetInnerHTML":G=I&&I.__html;continue;case"style":"object"==typeof I&&(I=function(a){var b="";for(var c in a){var d=a[c];if(null!=d&&""!==d){var e="-"==c[0]?c:eL[c]||(eL[c]=c.replace(eN,"-$&").toLowerCase()),f=";";"number"!=typeof d||e.startsWith("--")||eM.has(e)||(f="px;"),b=b+e+":"+d+f}}return b||void 0}(I));break;case"acceptCharset":H="accept-charset";break;case"httpEquiv":H="http-equiv";break;default:if(eF.test(H))H=H.replace(eF,"$1:$2").toLowerCase();else{if(eE.test(H))continue;("-"===H[4]||eI.has(H))&&null!=I?I+="":d?eH.test(H)&&(H="panose1"===H?"panose-1":H.replace(/([A-Z])/g,"-$1").toLowerCase()):eG.test(H)&&(H=H.toLowerCase())}}null!=I&&!1!==I&&(F=!0===I||""===I?F+" "+H:F+" "+H+'="'+("string"==typeof I?eK(I):I+"")+'"')}}if(eE.test(p))throw Error(p+" is not a valid HTML tag name in "+F+">");if(G||("string"==typeof E?G=eK(E):null!=E&&!1!==E&&!0!==E&&(G=a(E,c,"svg"===p||"foreignObject"!==p&&d,e,b,g,h))),eQ&&eQ(b),b.__=null,eS&&eS(b),!G&&eY.has(p))return F+"/>";var J="</"+p+">",K=F+">";return eV(G)?[K].concat(G,[J]):"string"!=typeof G?[K,G,J]:K+G+J}(a,eT,!1,void 0,e,!1,void 0);return eV(f)?f.join(""):f}catch(a){if(a.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw a}finally{ec.__c&&ec.__c(a,eU),ec.__s=d,eU.length=0}}(a)}</div></body></html>`}}function e3(a){let{url:b,theme:c,query:d,cookies:e,pages:f,providers:g}=a;return{csrf:(a,b,c)=>a?(b.logger.warn("csrf-disabled"),c.push({name:b.cookies.csrfToken.name,value:"",options:{...b.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:c}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:b.csrfToken},cookies:c},providers:a=>({headers:{"Content-Type":"application/json"},body:a.reduce((a,{id:b,name:c,type:d,signinUrl:e,callbackUrl:f})=>(a[b]={id:b,name:c,type:d,signinUrl:e,callbackUrl:f},a),{})}),signin(b,h){if(b)throw new bF("Unsupported action");if(f?.signIn){let b=`${f.signIn}${f.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:a.callbackUrl??"/"})}`;return h&&(b=`${b}&${new URLSearchParams({error:h})}`),{redirect:b,cookies:e}}let i=g?.find(a=>"webauthn"===a.type&&a.enableConditionalUI&&!!a.simpleWebAuthnBrowserVersion),j="";if(i){let{simpleWebAuthnBrowserVersion:a}=i;j=`<script src="https://unpkg.com/@simplewebauthn/browser@${a}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return e2({cookies:e,theme:c,html:function(a){let{csrfToken:b,providers:c=[],callbackUrl:d,theme:e,email:f,error:g}=a;"undefined"!=typeof document&&e?.brandColor&&document.documentElement.style.setProperty("--brand-color",e.brandColor),"undefined"!=typeof document&&e?.buttonText&&document.documentElement.style.setProperty("--button-text-color",e.buttonText);let h=g&&(e0[g]??e0.default),i=c.find(a=>"webauthn"===a.type&&a.enableConditionalUI)?.id;return e$("div",{className:"signin",children:[e?.brandColor&&e$("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${e.brandColor}}`}}),e?.buttonText&&e$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${e.buttonText}
        }
      `}}),e$("div",{className:"card",children:[h&&e$("div",{className:"error",children:e$("p",{children:h})}),e?.logo&&e$("img",{src:e.logo,alt:"Logo",className:"logo"}),c.map((a,e)=>{let g,h,i;("oauth"===a.type||"oidc"===a.type)&&({bg:g="#fff",brandColor:h,logo:i=`https://authjs.dev/img/providers/${a.id}.svg`}=a.style??{});let j=h??g??"#fff";return e$("div",{className:"provider",children:["oauth"===a.type||"oidc"===a.type?e$("form",{action:a.signinUrl,method:"POST",children:[e$("input",{type:"hidden",name:"csrfToken",value:b}),d&&e$("input",{type:"hidden",name:"callbackUrl",value:d}),e$("button",{type:"submit",className:"button",style:{"--provider-brand-color":j},tabIndex:0,children:[e$("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",a.name]}),i&&e$("img",{loading:"lazy",height:24,src:i})]})]}):null,("email"===a.type||"credentials"===a.type||"webauthn"===a.type)&&e>0&&"email"!==c[e-1].type&&"credentials"!==c[e-1].type&&"webauthn"!==c[e-1].type&&e$("hr",{}),"email"===a.type&&e$("form",{action:a.signinUrl,method:"POST",children:[e$("input",{type:"hidden",name:"csrfToken",value:b}),e$("label",{className:"section-header",htmlFor:`input-email-for-${a.id}-provider`,children:"Email"}),e$("input",{id:`input-email-for-${a.id}-provider`,autoFocus:!0,type:"email",name:"email",value:f,placeholder:"<EMAIL>",required:!0}),e$("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),"credentials"===a.type&&e$("form",{action:a.callbackUrl,method:"POST",children:[e$("input",{type:"hidden",name:"csrfToken",value:b}),Object.keys(a.credentials).map(b=>e$("div",{children:[e$("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`,children:a.credentials[b].label??b}),e$("input",{name:b,id:`input-${b}-for-${a.id}-provider`,type:a.credentials[b].type??"text",placeholder:a.credentials[b].placeholder??"",...a.credentials[b]})]},`input-group-${a.id}`)),e$("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),"webauthn"===a.type&&e$("form",{action:a.callbackUrl,method:"POST",id:`${a.id}-form`,children:[e$("input",{type:"hidden",name:"csrfToken",value:b}),Object.keys(a.formFields).map(b=>e$("div",{children:[e$("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`,children:a.formFields[b].label??b}),e$("input",{name:b,"data-form-field":!0,id:`input-${b}-for-${a.id}-provider`,type:a.formFields[b].type??"text",placeholder:a.formFields[b].placeholder??"",...a.formFields[b]})]},`input-group-${a.id}`)),e$("button",{id:`submitButton-${a.id}`,type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),("email"===a.type||"credentials"===a.type||"webauthn"===a.type)&&e+1<c.length&&e$("hr",{})]},a.id)})]}),i&&e$(es,{children:e$("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${e_})(authURL, "${i}");
`}})})]})}({csrfToken:a.csrfToken,providers:a.providers?.filter(a=>["email","oauth","oidc"].includes(a.type)||"credentials"===a.type&&a.credentials||"webauthn"===a.type&&a.formFields||!1),callbackUrl:a.callbackUrl,theme:a.theme,error:h,...d}),title:"Sign In",headTags:j})},signout:()=>f?.signOut?{redirect:f.signOut,cookies:e}:e2({cookies:e,theme:c,html:function(a){let{url:b,csrfToken:c,theme:d}=a;return e$("div",{className:"signout",children:[d?.brandColor&&e$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${d.brandColor}
        }
      `}}),d?.buttonText&&e$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${d.buttonText}
        }
      `}}),e$("div",{className:"card",children:[d?.logo&&e$("img",{src:d.logo,alt:"Logo",className:"logo"}),e$("h1",{children:"Signout"}),e$("p",{children:"Are you sure you want to sign out?"}),e$("form",{action:b?.toString(),method:"POST",children:[e$("input",{type:"hidden",name:"csrfToken",value:c}),e$("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:a.csrfToken,url:b,theme:c}),title:"Sign Out"}),verifyRequest:a=>f?.verifyRequest?{redirect:`${f.verifyRequest}${b?.search??""}`,cookies:e}:e2({cookies:e,theme:c,html:function(a){let{url:b,theme:c}=a;return e$("div",{className:"verify-request",children:[c.brandColor&&e$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${c.brandColor}
        }
      `}}),e$("div",{className:"card",children:[c.logo&&e$("img",{src:c.logo,alt:"Logo",className:"logo"}),e$("h1",{children:"Check your email"}),e$("p",{children:"A sign in link has been sent to your email address."}),e$("p",{children:e$("a",{className:"site",href:b.origin,children:b.host})})]})]})}({url:b,theme:c,...a}),title:"Verify Request"}),error:a=>f?.error?{redirect:`${f.error}${f.error.includes("?")?"&":"?"}error=${a}`,cookies:e}:e2({cookies:e,theme:c,...function(a){let{url:b,error:c="default",theme:d}=a,e=`${b}/signin`,f={default:{status:200,heading:"Error",message:e$("p",{children:e$("a",{className:"site",href:b?.origin,children:b?.host})})},Configuration:{status:500,heading:"Server error",message:e$("div",{children:[e$("p",{children:"There is a problem with the server configuration."}),e$("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:e$("div",{children:[e$("p",{children:"You do not have permission to sign in."}),e$("p",{children:e$("a",{className:"button",href:e,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:e$("div",{children:[e$("p",{children:"The sign in link is no longer valid."}),e$("p",{children:"It may have been used already or it may have expired."})]}),signin:e$("a",{className:"button",href:e,children:"Sign in"})}},{status:g,heading:h,message:i,signin:j}=f[c]??f.default;return{status:g,html:e$("div",{className:"error",children:[d?.brandColor&&e$("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${d?.brandColor}
        }
      `}}),e$("div",{className:"card",children:[d?.logo&&e$("img",{src:d?.logo,alt:"Logo",className:"logo"}),e$("h1",{children:h}),e$("div",{className:"message",children:i}),j]})]})}}({url:b,theme:c,error:a}),title:"Error"})}}function e4(a,b=Date.now()){return new Date(b+1e3*a)}async function e5(a,b,c,d){if(!c?.providerAccountId||!c.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(c.type))throw Error("Provider not supported");let{adapter:e,jwt:f,events:g,session:{strategy:h,generateSessionToken:i}}=d;if(!e)return{user:b,account:c};let j=c,{createUser:k,updateUser:l,getUser:m,getUserByAccount:n,getUserByEmail:o,linkAccount:p,createSession:q,getSessionAndUser:r,deleteSession:s}=e,t=null,u=null,v=!1,w="jwt"===h;if(a)if(w)try{let b=d.cookies.sessionToken.name;(t=await f.decode({...f,token:a,salt:b}))&&"sub"in t&&t.sub&&(u=await m(t.sub))}catch{}else{let b=await r(a);b&&(t=b.session,u=b.user)}if("email"===j.type){let c=await o(b.email);return c?(u?.id!==c.id&&!w&&a&&await s(a),u=await l({id:c.id,emailVerified:new Date}),await g.updateUser?.({user:u})):(u=await k({...b,emailVerified:new Date}),await g.createUser?.({user:u}),v=!0),{session:t=w?{}:await q({sessionToken:i(),userId:u.id,expires:e4(d.session.maxAge)}),user:u,isNewUser:v}}if("webauthn"===j.type){let a=await n({providerAccountId:j.providerAccountId,provider:j.provider});if(a){if(u){if(a.id===u.id){let a={...j,userId:u.id};return{session:t,user:u,isNewUser:v,account:a}}throw new bP("The account is already associated with another user",{provider:j.provider})}t=w?{}:await q({sessionToken:i(),userId:a.id,expires:e4(d.session.maxAge)});let b={...j,userId:a.id};return{session:t,user:a,isNewUser:v,account:b}}{if(u){await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b});let a={...j,userId:u.id};return{session:t,user:u,isNewUser:v,account:a}}if(b.email?await o(b.email):null)throw new bP("Another account already exists with the same e-mail address",{provider:j.provider});u=await k({...b}),await g.createUser?.({user:u}),await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),t=w?{}:await q({sessionToken:i(),userId:u.id,expires:e4(d.session.maxAge)});let a={...j,userId:u.id};return{session:t,user:u,isNewUser:!0,account:a}}}let x=await n({providerAccountId:j.providerAccountId,provider:j.provider});if(x){if(u){if(x.id===u.id)return{session:t,user:u,isNewUser:v};throw new by("The account is already associated with another user",{provider:j.provider})}return{session:t=w?{}:await q({sessionToken:i(),userId:x.id,expires:e4(d.session.maxAge)}),user:x,isNewUser:v}}{let{provider:a}=d,{type:c,provider:e,providerAccountId:f,userId:h,...l}=j;if(j=Object.assign(a.account(l)??{},{providerAccountId:f,provider:e,type:c,userId:h}),u)return await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),{session:t,user:u,isNewUser:v};let m=b.email?await o(b.email):null;if(m){let a=d.provider;if(a?.allowDangerousEmailAccountLinking)u=m,v=!1;else throw new by("Another account already exists with the same e-mail address",{provider:j.provider})}else u=await k({...b,emailVerified:null}),v=!0;return await g.createUser?.({user:u}),await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),{session:t=w?{}:await q({sessionToken:i(),userId:u.id,expires:e4(d.session.maxAge)}),user:u,isNewUser:v}}}function e6(a,b){if(null==a)return!1;try{return a instanceof b||Object.getPrototypeOf(a)[Symbol.toStringTag]===b.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(f="oauth4webapi/v3.6.1");let e7="ERR_INVALID_ARG_VALUE",e8="ERR_INVALID_ARG_TYPE";function e9(a,b,c){let d=TypeError(a,{cause:c});return Object.assign(d,{code:b}),d}let fa=Symbol(),fb=Symbol(),fc=Symbol(),fd=Symbol(),fe=Symbol(),ff=Symbol();Symbol();let fg=new TextEncoder,fh=new TextDecoder;function fi(a){return"string"==typeof a?fg.encode(a):fh.decode(a)}function fj(a){return"string"==typeof a?h(a):g(a)}g=Uint8Array.prototype.toBase64?a=>(a instanceof ArrayBuffer&&(a=new Uint8Array(a)),a.toBase64({alphabet:"base64url",omitPadding:!0})):a=>{a instanceof ArrayBuffer&&(a=new Uint8Array(a));let b=[];for(let c=0;c<a.byteLength;c+=32768)b.push(String.fromCharCode.apply(null,a.subarray(c,c+32768)));return btoa(b.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},h=Uint8Array.fromBase64?a=>{try{return Uint8Array.fromBase64(a,{alphabet:"base64url"})}catch(a){throw e9("The input to be decoded is not correctly encoded.",e7,a)}}:a=>{try{let b=atob(a.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c}catch(a){throw e9("The input to be decoded is not correctly encoded.",e7,a)}};class fk extends Error{code;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=gl,Error.captureStackTrace?.(this,this.constructor)}}class fl extends Error{code;constructor(a,b){super(a,b),this.name=this.constructor.name,b?.code&&(this.code=b?.code),Error.captureStackTrace?.(this,this.constructor)}}function fm(a,b,c){return new fl(a,{code:b,cause:c})}function fn(a){return!(null===a||"object"!=typeof a||Array.isArray(a))}function fo(a){e6(a,Headers)&&(a=Object.fromEntries(a.entries()));let b=new Headers(a??{});if(f&&!b.has("user-agent")&&b.set("user-agent",f),b.has("authorization"))throw e9('"options.headers" must not include the "authorization" header name',e7);return b}function fp(a,b){if(void 0!==b){if("function"==typeof b&&(b=b(a.href)),!(b instanceof AbortSignal))throw e9('"options.signal" must return or be an instance of AbortSignal',e8);return b}}function fq(a){return a.includes("//")?a.replace("//","/"):a}async function fr(a,b,c,d){if(!(a instanceof URL))throw e9(`"${b}" must be an instance of URL`,e8);fH(a,d?.[fa]!==!0);let e=c(new URL(a.href)),f=fo(d?.headers);return f.set("accept","application/json"),(d?.[fd]||fetch)(e.href,{body:void 0,headers:Object.fromEntries(f.entries()),method:"GET",redirect:"manual",signal:fp(e,d?.signal)})}async function fs(a,b){return fr(a,"issuerIdentifier",a=>{switch(b?.algorithm){case void 0:case"oidc":a.pathname=fq(`${a.pathname}/.well-known/openid-configuration`);break;case"oauth2":!function(a,b,c=!1){"/"===a.pathname?a.pathname=b:a.pathname=fq(`${b}/${c?a.pathname:a.pathname.replace(/(\/)$/,"")}`)}(a,".well-known/oauth-authorization-server");break;default:throw e9('"options.algorithm" must be "oidc" (default), or "oauth2"',e7)}return a},b)}function ft(a,b,c,d,e){try{if("number"!=typeof a||!Number.isFinite(a))throw e9(`${c} must be a number`,e8,e);if(a>0)return;if(b){if(0!==a)throw e9(`${c} must be a non-negative number`,e7,e);return}throw e9(`${c} must be a positive number`,e7,e)}catch(a){if(d)throw fm(a.message,d,e);throw a}}function fu(a,b,c,d){try{if("string"!=typeof a)throw e9(`${b} must be a string`,e8,d);if(0===a.length)throw e9(`${b} must not be empty`,e7,d)}catch(a){if(c)throw fm(a.message,c,d);throw a}}async function fv(a,b){if(!(a instanceof URL)&&a!==gH)throw e9('"expectedIssuerIdentifier" must be an instance of URL',e8);if(!e6(b,Response))throw e9('"response" must be an instance of Response',e8);if(200!==b.status)throw fm('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',gr,b);gz(b);let c=await gG(b);if(fu(c.issuer,'"response" body "issuer" property',gp,{body:c}),a!==gH&&new URL(c.issuer).href!==a.href)throw fm('"response" body "issuer" property does not match the expected value',gw,{expected:a.href,body:c,attribute:"issuer"});return c}function fw(a){var b=a,c="application/json";if(fY(b)!==c)throw function(a,...b){let c='"response" content-type must be ';if(b.length>2){let a=b.pop();c+=`${b.join(", ")}, or ${a}`}else 2===b.length?c+=`${b[0]} or ${b[1]}`:c+=b[0];return fm(c,gq,a)}(b,c)}function fx(){return fj(crypto.getRandomValues(new Uint8Array(32)))}async function fy(a){return fu(a,"codeVerifier"),fj(await crypto.subtle.digest("SHA-256",fi(a)))}function fz(a){let b=a?.[fb];return"number"==typeof b&&Number.isFinite(b)?b:0}function fA(a){let b=a?.[fc];return"number"==typeof b&&Number.isFinite(b)&&-1!==Math.sign(b)?b:30}function fB(){return Math.floor(Date.now()/1e3)}function fC(a){if("object"!=typeof a||null===a)throw e9('"as" must be an object',e8);fu(a.issuer,'"as.issuer"')}function fD(a){if("object"!=typeof a||null===a)throw e9('"client" must be an object',e8);fu(a.client_id,'"client.client_id"')}function fE(a,b){let c=fB()+fz(b);return{jti:fx(),aud:a.issuer,exp:c+60,iat:c,nbf:c,iss:b.client_id,sub:b.client_id}}async function fF(a,b,c){if(!c.usages.includes("sign"))throw e9('CryptoKey instances used for signing assertions must include "sign" in their "usages"',e7);let d=`${fj(fi(JSON.stringify(a)))}.${fj(fi(JSON.stringify(b)))}`,e=fj(await crypto.subtle.sign(function(a){switch(a.algorithm.name){case"ECDSA":return{name:a.algorithm.name,hash:function(a){let{algorithm:b}=a;switch(b.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new fk("unsupported ECDSA namedCurve",{cause:a})}}(a)};case"RSA-PSS":switch(gA(a),a.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:a.algorithm.name,saltLength:parseInt(a.algorithm.hash.name.slice(-3),10)>>3};default:throw new fk("unsupported RSA-PSS hash name",{cause:a})}case"RSASSA-PKCS1-v1_5":return gA(a),a.algorithm.name;case"Ed25519":return a.algorithm.name}throw new fk("unsupported CryptoKey algorithm name",{cause:a})}(c),c,fi(d)));return`${d}.${e}`}let fG=URL.parse?(a,b)=>URL.parse(a,b):(a,b)=>{try{return new URL(a,b)}catch{return null}};function fH(a,b){if(b&&"https:"!==a.protocol)throw fm("only requests to HTTPS are allowed",gs,a);if("https:"!==a.protocol&&"http:"!==a.protocol)throw fm("only HTTP and HTTPS requests are allowed",gt,a)}function fI(a,b,c,d){let e;if("string"!=typeof a||!(e=fG(a)))throw fm(`authorization server metadata does not contain a valid ${c?`"as.mtls_endpoint_aliases.${b}"`:`"as.${b}"`}`,void 0===a?gx:gy,{attribute:c?`mtls_endpoint_aliases.${b}`:b});return fH(e,d),e}function fJ(a,b,c,d){return c&&a.mtls_endpoint_aliases&&b in a.mtls_endpoint_aliases?fI(a.mtls_endpoint_aliases[b],b,c,d):fI(a[b],b,c,d)}class fK extends Error{cause;code;error;status;error_description;response;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=gk,this.cause=b.cause,this.error=b.cause.error,this.status=b.response.status,this.error_description=b.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:b.response}),Error.captureStackTrace?.(this,this.constructor)}}class fL extends Error{cause;code;error;error_description;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=gm,this.cause=b.cause,this.error=b.cause.get("error"),this.error_description=b.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class fM extends Error{cause;code;response;status;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=gj,this.cause=b.cause,this.status=b.response.status,this.response=b.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let fN="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",fO=RegExp("^[,\\s]*("+fN+")\\s(.*)"),fP=RegExp("^[,\\s]*("+fN+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),fQ=RegExp("^[,\\s]*"+("("+fN+")\\s*=\\s*(")+fN+")[,\\s]*(.*)"),fR=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function fS(a){if(a.status>399&&a.status<500){gz(a),fw(a);try{let b=await a.clone().json();if(fn(b)&&"string"==typeof b.error&&b.error.length)return b}catch{}}}async function fT(a,b,c){if(a.status!==b){let b;if(b=await fS(a))throw await a.body?.cancel(),new fK("server responded with an error in the response body",{cause:b,response:a});throw fm(`"response" is not a conform ${c} response (unexpected HTTP status code)`,gr,a)}}function fU(a){if(!f9.has(a))throw e9('"options.DPoP" is not a valid DPoPHandle',e7)}async function fV(a,b,c,d,e,f){if(fu(a,'"accessToken"'),!(c instanceof URL))throw e9('"url" must be an instance of URL',e8);fH(c,f?.[fa]!==!0),d=fo(d),f?.DPoP&&(fU(f.DPoP),await f.DPoP.addProof(c,d,b.toUpperCase(),a)),d.set("authorization",`${d.has("dpop")?"DPoP":"Bearer"} ${a}`);let g=await (f?.[fd]||fetch)(c.href,{body:e,headers:Object.fromEntries(d.entries()),method:b,redirect:"manual",signal:fp(c,f?.signal)});return f?.DPoP?.cacheNonce(g),g}async function fW(a,b,c,d){fC(a),fD(b);let e=fJ(a,"userinfo_endpoint",b.use_mtls_endpoint_aliases,d?.[fa]!==!0),f=fo(d?.headers);return b.userinfo_signed_response_alg?f.set("accept","application/jwt"):(f.set("accept","application/json"),f.append("accept","application/jwt")),fV(c,"GET",e,f,null,{...d,[fb]:fz(b)})}let fX=Symbol();function fY(a){return a.headers.get("content-type")?.split(";")[0]}async function fZ(a,b,c,d,e){let f;if(fC(a),fD(b),!e6(d,Response))throw e9('"response" must be an instance of Response',e8);if(f4(d),200!==d.status)throw fm('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',gr,d);if(gz(d),"application/jwt"===fY(d)){let{claims:c,jwt:g}=await gB(await d.text(),gC.bind(void 0,b.userinfo_signed_response_alg,a.userinfo_signing_alg_values_supported,void 0),fz(b),fA(b),e?.[ff]).then(f5.bind(void 0,b.client_id)).then(f7.bind(void 0,a));f1.set(d,g),f=c}else{if(b.userinfo_signed_response_alg)throw fm("JWT UserInfo Response expected",gn,d);f=await gG(d)}if(fu(f.sub,'"response" body "sub" property',gp,{body:f}),c===fX);else if(fu(c,'"expectedSubject"'),f.sub!==c)throw fm('unexpected "response" body "sub" property value',gw,{expected:c,body:f,attribute:"sub"});return f}async function f$(a,b,c,d,e,f,g){return await c(a,b,e,f),f.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(g?.[fd]||fetch)(d.href,{body:e,headers:Object.fromEntries(f.entries()),method:"POST",redirect:"manual",signal:fp(d,g?.signal)})}async function f_(a,b,c,d,e,f){let g=fJ(a,"token_endpoint",b.use_mtls_endpoint_aliases,f?.[fa]!==!0);e.set("grant_type",d);let h=fo(f?.headers);h.set("accept","application/json"),f?.DPoP!==void 0&&(fU(f.DPoP),await f.DPoP.addProof(g,h,"POST"));let i=await f$(a,b,c,g,e,h,f);return f?.DPoP?.cacheNonce(i),i}let f0=new WeakMap,f1=new WeakMap;function f2(a){if(!a.id_token)return;let b=f0.get(a);if(!b)throw e9('"ref" was already garbage collected or did not resolve from the proper sources',e7);return b}async function f3(a,b,c,d,e){if(fC(a),fD(b),!e6(c,Response))throw e9('"response" must be an instance of Response',e8);f4(c),await fT(c,200,"Token Endpoint"),gz(c);let f=await gG(c);if(fu(f.access_token,'"response" body "access_token" property',gp,{body:f}),fu(f.token_type,'"response" body "token_type" property',gp,{body:f}),f.token_type=f.token_type.toLowerCase(),"dpop"!==f.token_type&&"bearer"!==f.token_type)throw new fk("unsupported `token_type` value",{cause:{body:f}});if(void 0!==f.expires_in){let a="number"!=typeof f.expires_in?parseFloat(f.expires_in):f.expires_in;ft(a,!0,'"response" body "expires_in" property',gp,{body:f}),f.expires_in=a}if(void 0!==f.refresh_token&&fu(f.refresh_token,'"response" body "refresh_token" property',gp,{body:f}),void 0!==f.scope&&"string"!=typeof f.scope)throw fm('"response" body "scope" property must be a string',gp,{body:f});if(void 0!==f.id_token){fu(f.id_token,'"response" body "id_token" property',gp,{body:f});let g=["aud","exp","iat","iss","sub"];!0===b.require_auth_time&&g.push("auth_time"),void 0!==b.default_max_age&&(ft(b.default_max_age,!0,'"client.default_max_age"'),g.push("auth_time")),d?.length&&g.push(...d);let{claims:h,jwt:i}=await gB(f.id_token,gC.bind(void 0,b.id_token_signed_response_alg,a.id_token_signing_alg_values_supported,"RS256"),fz(b),fA(b),e?.[ff]).then(gd.bind(void 0,g)).then(f8.bind(void 0,a)).then(f6.bind(void 0,b.client_id));if(Array.isArray(h.aud)&&1!==h.aud.length){if(void 0===h.azp)throw fm('ID Token "aud" (audience) claim includes additional untrusted audiences',gv,{claims:h,claim:"aud"});if(h.azp!==b.client_id)throw fm('unexpected ID Token "azp" (authorized party) claim value',gv,{expected:b.client_id,claims:h,claim:"azp"})}void 0!==h.auth_time&&ft(h.auth_time,!0,'ID Token "auth_time" (authentication time)',gp,{claims:h}),f1.set(c,i),f0.set(f,h)}return f}function f4(a){let b;if(b=function(a){if(!e6(a,Response))throw e9('"response" must be an instance of Response',e8);let b=a.headers.get("www-authenticate");if(null===b)return;let c=[],d=b;for(;d;){let a,b=d.match(fO),e=b?.["1"].toLowerCase();if(d=b?.["2"],!e)return;let f={};for(;d;){let c,e;if(b=d.match(fP)){if([,c,e,d]=b,e.includes("\\"))try{e=JSON.parse(`"${e}"`)}catch{}f[c.toLowerCase()]=e;continue}if(b=d.match(fQ)){[,c,e,d]=b,f[c.toLowerCase()]=e;continue}if(b=d.match(fR)){if(Object.keys(f).length)break;[,a,d]=b;break}return}let g={scheme:e,parameters:f};a&&(g.token68=a),c.push(g)}if(c.length)return c}(a))throw new fM("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:b,response:a})}function f5(a,b){return void 0!==b.claims.aud?f6(a,b):b}function f6(a,b){if(Array.isArray(b.claims.aud)){if(!b.claims.aud.includes(a))throw fm('unexpected JWT "aud" (audience) claim value',gv,{expected:a,claims:b.claims,claim:"aud"})}else if(b.claims.aud!==a)throw fm('unexpected JWT "aud" (audience) claim value',gv,{expected:a,claims:b.claims,claim:"aud"});return b}function f7(a,b){return void 0!==b.claims.iss?f8(a,b):b}function f8(a,b){let c=a[gI]?.(b)??a.issuer;if(b.claims.iss!==c)throw fm('unexpected JWT "iss" (issuer) claim value',gv,{expected:c,claims:b.claims,claim:"iss"});return b}let f9=new WeakSet,ga=Symbol();async function gb(a,b,c,d,e,f,g){if(fC(a),fD(b),!f9.has(d))throw e9('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',e7);fu(e,'"redirectUri"');let h=gD(d,"code");if(!h)throw fm('no authorization code in "callbackParameters"',gp);let i=new URLSearchParams(g?.additionalParameters);return i.set("redirect_uri",e),i.set("code",h),f!==ga&&(fu(f,'"codeVerifier"'),i.set("code_verifier",f)),f_(a,b,c,"authorization_code",i,g)}let gc={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function gd(a,b){for(let c of a)if(void 0===b.claims[c])throw fm(`JWT "${c}" (${gc[c]}) claim missing`,gp,{claims:b.claims});return b}let ge=Symbol(),gf=Symbol();async function gg(a,b,c,d){return"string"==typeof d?.expectedNonce||"number"==typeof d?.maxAge||d?.requireIdToken?gh(a,b,c,d.expectedNonce,d.maxAge,{[ff]:d[ff]}):gi(a,b,c,d)}async function gh(a,b,c,d,e,f){let g=[];switch(d){case void 0:d=ge;break;case ge:break;default:fu(d,'"expectedNonce" argument'),g.push("nonce")}switch(e??=b.default_max_age){case void 0:e=gf;break;case gf:break;default:ft(e,!0,'"maxAge" argument'),g.push("auth_time")}let h=await f3(a,b,c,g,f);fu(h.id_token,'"response" body "id_token" property',gp,{body:h});let i=f2(h);if(e!==gf){let a=fB()+fz(b),c=fA(b);if(i.auth_time+e<a-c)throw fm("too much time has elapsed since the last End-User authentication",gu,{claims:i,now:a,tolerance:c,claim:"auth_time"})}if(d===ge){if(void 0!==i.nonce)throw fm('unexpected ID Token "nonce" claim value',gv,{expected:void 0,claims:i,claim:"nonce"})}else if(i.nonce!==d)throw fm('unexpected ID Token "nonce" claim value',gv,{expected:d,claims:i,claim:"nonce"});return h}async function gi(a,b,c,d){let e=await f3(a,b,c,void 0,d),f=f2(e);if(f){if(void 0!==b.default_max_age){ft(b.default_max_age,!0,'"client.default_max_age"');let a=fB()+fz(b),c=fA(b);if(f.auth_time+b.default_max_age<a-c)throw fm("too much time has elapsed since the last End-User authentication",gu,{claims:f,now:a,tolerance:c,claim:"auth_time"})}if(void 0!==f.nonce)throw fm('unexpected ID Token "nonce" claim value',gv,{expected:void 0,claims:f,claim:"nonce"})}return e}let gj="OAUTH_WWW_AUTHENTICATE_CHALLENGE",gk="OAUTH_RESPONSE_BODY_ERROR",gl="OAUTH_UNSUPPORTED_OPERATION",gm="OAUTH_AUTHORIZATION_RESPONSE_ERROR",gn="OAUTH_JWT_USERINFO_EXPECTED",go="OAUTH_PARSE_ERROR",gp="OAUTH_INVALID_RESPONSE",gq="OAUTH_RESPONSE_IS_NOT_JSON",gr="OAUTH_RESPONSE_IS_NOT_CONFORM",gs="OAUTH_HTTP_REQUEST_FORBIDDEN",gt="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",gu="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",gv="OAUTH_JWT_CLAIM_COMPARISON_FAILED",gw="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",gx="OAUTH_MISSING_SERVER_METADATA",gy="OAUTH_INVALID_SERVER_METADATA";function gz(a){if(a.bodyUsed)throw e9('"response" body has been used already',e7)}function gA(a){let{algorithm:b}=a;if("number"!=typeof b.modulusLength||b.modulusLength<2048)throw new fk(`unsupported ${b.name} modulusLength`,{cause:a})}async function gB(a,b,c,d,e){let f,g,{0:h,1:i,length:j}=a.split(".");if(5===j)if(void 0!==e)a=await e(a),{0:h,1:i,length:j}=a.split(".");else throw new fk("JWE decryption is not configured",{cause:a});if(3!==j)throw fm("Invalid JWT",gp,a);try{f=JSON.parse(fi(fj(h)))}catch(a){throw fm("failed to parse JWT Header body as base64url encoded JSON",go,a)}if(!fn(f))throw fm("JWT Header must be a top level object",gp,a);if(b(f),void 0!==f.crit)throw new fk('no JWT "crit" header parameter extensions are supported',{cause:{header:f}});try{g=JSON.parse(fi(fj(i)))}catch(a){throw fm("failed to parse JWT Payload body as base64url encoded JSON",go,a)}if(!fn(g))throw fm("JWT Payload must be a top level object",gp,a);let k=fB()+c;if(void 0!==g.exp){if("number"!=typeof g.exp)throw fm('unexpected JWT "exp" (expiration time) claim type',gp,{claims:g});if(g.exp<=k-d)throw fm('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',gu,{claims:g,now:k,tolerance:d,claim:"exp"})}if(void 0!==g.iat&&"number"!=typeof g.iat)throw fm('unexpected JWT "iat" (issued at) claim type',gp,{claims:g});if(void 0!==g.iss&&"string"!=typeof g.iss)throw fm('unexpected JWT "iss" (issuer) claim type',gp,{claims:g});if(void 0!==g.nbf){if("number"!=typeof g.nbf)throw fm('unexpected JWT "nbf" (not before) claim type',gp,{claims:g});if(g.nbf>k+d)throw fm('unexpected JWT "nbf" (not before) claim value',gu,{claims:g,now:k,tolerance:d,claim:"nbf"})}if(void 0!==g.aud&&"string"!=typeof g.aud&&!Array.isArray(g.aud))throw fm('unexpected JWT "aud" (audience) claim type',gp,{claims:g});return{header:f,claims:g,jwt:a}}function gC(a,b,c,d){if(void 0!==a){if("string"==typeof a?d.alg!==a:!a.includes(d.alg))throw fm('unexpected JWT "alg" header parameter',gp,{header:d,expected:a,reason:"client configuration"});return}if(Array.isArray(b)){if(!b.includes(d.alg))throw fm('unexpected JWT "alg" header parameter',gp,{header:d,expected:b,reason:"authorization server metadata"});return}if(void 0!==c){if("string"==typeof c?d.alg!==c:"function"==typeof c?!c(d.alg):!c.includes(d.alg))throw fm('unexpected JWT "alg" header parameter',gp,{header:d,expected:c,reason:"default value"});return}throw fm('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:a,issuer:b,fallback:c})}function gD(a,b){let{0:c,length:d}=a.getAll(b);if(d>1)throw fm(`"${b}" parameter must be provided only once`,gp);return c}let gE=Symbol(),gF=Symbol();async function gG(a,b=fw){let c;try{c=await a.json()}catch(c){throw b(a),fm('failed to parse "response" body as JSON',go,c)}if(!fn(c))throw fm('"response" body must be a top level object',gp,{body:c});return c}let gH=Symbol(),gI=Symbol();async function gJ(a,b,c){let{cookies:d,logger:e}=c,f=d[a],g=new Date;g.setTime(g.getTime()+9e5),e.debug(`CREATE_${a.toUpperCase()}`,{name:f.name,payload:b,COOKIE_TTL:900,expires:g});let h=await dD({...c.jwt,maxAge:900,token:{value:b},salt:f.name}),i={...f.options,expires:g};return{name:f.name,value:h,options:i}}async function gK(a,b,c){try{let{logger:d,cookies:e,jwt:f}=c;if(d.debug(`PARSE_${a.toUpperCase()}`,{cookie:b}),!b)throw new bs(`${a} cookie was missing`);let g=await dE({...f,token:b,salt:e[a].name});if(g?.value)return g.value;throw Error("Invalid cookie")}catch(b){throw new bs(`${a} value could not be parsed`,{cause:b})}}function gL(a,b,c){let{logger:d,cookies:e}=b,f=e[a];d.debug(`CLEAR_${a.toUpperCase()}`,{cookie:f}),c.push({name:f.name,value:"",options:{...e[a].options,maxAge:0}})}function gM(a,b){return async function(c,d,e){let{provider:f,logger:g}=e;if(!f?.checks?.includes(a))return;let h=c?.[e.cookies[b].name];g.debug(`USE_${b.toUpperCase()}`,{value:h});let i=await gK(b,h,e);return gL(b,e,d),i}}let gN={async create(a){let b=fx(),c=await fy(b);return{cookie:await gJ("pkceCodeVerifier",b,a),value:c}},use:gM("pkce","pkceCodeVerifier")},gO="encodedState",gP={async create(a,b){let{provider:c}=a;if(!c.checks.includes("state")){if(b)throw new bs("State data was provided but the provider is not configured to use state");return}let d={origin:b,random:fx()},e=await dD({secret:a.jwt.secret,token:d,salt:gO,maxAge:900});return{cookie:await gJ("state",e,a),value:e}},use:gM("state","state"),async decode(a,b){try{b.logger.debug("DECODE_STATE",{state:a});let c=await dE({secret:b.jwt.secret,token:a,salt:gO});if(c)return c;throw Error("Invalid state")}catch(a){throw new bs("State could not be decoded",{cause:a})}}},gQ={async create(a){if(!a.provider.checks.includes("nonce"))return;let b=fx();return{cookie:await gJ("nonce",b,a),value:b}},use:gM("nonce","nonce")},gR="encodedWebauthnChallenge",gS={create:async(a,b,c)=>({cookie:await gJ("webauthnChallenge",await dD({secret:a.jwt.secret,token:{challenge:b,registerData:c},salt:gR,maxAge:900}),a)}),async use(a,b,c){let d=b?.[a.cookies.webauthnChallenge.name],e=await gK("webauthnChallenge",d,a),f=await dE({secret:a.jwt.secret,token:e,salt:gR});if(gL("webauthnChallenge",a,c),!f)throw new bs("WebAuthn challenge was missing");return f}};function gT(a){return encodeURIComponent(a).replace(/%20/g,"+")}async function gU(a,b,c){let d,e,f,{logger:g,provider:h}=c,{token:i,userinfo:j}=h;if(i?.url&&"authjs.dev"!==i.url.host||j?.url&&"authjs.dev"!==j.url.host)d={issuer:h.issuer??"https://authjs.dev",token_endpoint:i?.url.toString(),userinfo_endpoint:j?.url.toString()};else{let a=new URL(h.issuer),b=await fs(a,{[fa]:!0,[fd]:h[dZ]});if(!(d=await fv(a,b)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!d.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let k={client_id:h.clientId,...h.client};switch(k.token_endpoint_auth_method){case void 0:case"client_secret_basic":e=(a,b,c,d)=>{d.set("authorization",function(a,b){let c=gT(a),d=gT(b),e=btoa(`${c}:${d}`);return`Basic ${e}`}(h.clientId,h.clientSecret))};break;case"client_secret_post":var l;fu(l=h.clientSecret,'"clientSecret"'),e=(a,b,c,d)=>{c.set("client_id",b.client_id),c.set("client_secret",l)};break;case"client_secret_jwt":e=function(a,b){let c;fu(a,'"clientSecret"');let d=void 0;return async(b,e,f,g)=>{c||=await crypto.subtle.importKey("raw",fi(a),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let h={alg:"HS256"},i=fE(b,e);d?.(h,i);let j=`${fj(fi(JSON.stringify(h)))}.${fj(fi(JSON.stringify(i)))}`,k=await crypto.subtle.sign(c.algorithm,c,fi(j));f.set("client_id",e.client_id),f.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),f.set("client_assertion",`${j}.${fj(new Uint8Array(k))}`)}}(h.clientSecret);break;case"private_key_jwt":e=function(a,b){let{key:c,kid:d}=a instanceof CryptoKey?{key:a}:a?.key instanceof CryptoKey?(void 0!==a.kid&&fu(a.kid,'"kid"'),{key:a.key,kid:a.kid}):{};var e='"clientPrivateKey.key"';if(!(c instanceof CryptoKey))throw e9(`${e} must be a CryptoKey`,e8);if("private"!==c.type)throw e9(`${e} must be a private CryptoKey`,e7);return async(a,e,f,g)=>{let h={alg:function(a){switch(a.algorithm.name){case"RSA-PSS":switch(a.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new fk("unsupported RsaHashedKeyAlgorithm hash name",{cause:a})}case"RSASSA-PKCS1-v1_5":switch(a.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new fk("unsupported RsaHashedKeyAlgorithm hash name",{cause:a})}case"ECDSA":switch(a.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new fk("unsupported EcKeyAlgorithm namedCurve",{cause:a})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new fk("unsupported CryptoKey algorithm name",{cause:a})}}(c),kid:d},i=fE(a,e);b?.[fe]?.(h,i),f.set("client_id",e.client_id),f.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),f.set("client_assertion",await fF(h,i,c))}}(h.token.clientPrivateKey,{[fe](a,b){b.aud=[d.issuer,d.token_endpoint]}});break;case"none":e=(a,b,c,d)=>{c.set("client_id",b.client_id)};break;default:throw Error("unsupported client authentication method")}let m=[],n=await gP.use(b,m,c);try{f=function(a,b,c,d){var e;if(fC(a),fD(b),c instanceof URL&&(c=c.searchParams),!(c instanceof URLSearchParams))throw e9('"parameters" must be an instance of URLSearchParams, or URL',e8);if(gD(c,"response"))throw fm('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',gp,{parameters:c});let f=gD(c,"iss"),g=gD(c,"state");if(!f&&a.authorization_response_iss_parameter_supported)throw fm('response parameter "iss" (issuer) missing',gp,{parameters:c});if(f&&f!==a.issuer)throw fm('unexpected "iss" (issuer) response parameter value',gp,{expected:a.issuer,parameters:c});switch(d){case void 0:case gF:if(void 0!==g)throw fm('unexpected "state" response parameter encountered',gp,{expected:void 0,parameters:c});break;case gE:break;default:if(fu(d,'"expectedState" argument'),g!==d)throw fm(void 0===g?'response parameter "state" missing':'unexpected "state" response parameter value',gp,{expected:d,parameters:c})}if(gD(c,"error"))throw new fL("authorization response from the server is an error",{cause:c});let h=gD(c,"id_token"),i=gD(c,"token");if(void 0!==h||void 0!==i)throw new fk("implicit and hybrid flows are not supported");return e=new URLSearchParams(c),f9.add(e),e}(d,k,new URLSearchParams(a),h.checks.includes("state")?n:gE)}catch(a){if(a instanceof fL){let b={providerId:h.id,...Object.fromEntries(a.cause.entries())};throw g.debug("OAuthCallbackError",b),new bz("OAuth Provider returned an error",b)}throw a}let o=await gN.use(b,m,c),p=h.callbackUrl;!c.isOnRedirectProxy&&h.redirectProxyUrl&&(p=h.redirectProxyUrl);let q=await gb(d,k,e,f,p,o??"decoy",{[fa]:!0,[fd]:(...a)=>(h.checks.includes("pkce")||a[1].body.delete("code_verifier"),(h[dZ]??fetch)(...a))});h.token?.conform&&(q=await h.token.conform(q.clone())??q);let r={},s="oidc"===h.type;if(h[d$])switch(h.id){case"microsoft-entra-id":case"azure-ad":{let a=await q.clone().json();if(a.error){let b={providerId:h.id,...a};throw new bz(`OAuth Provider returned an error: ${a.error}`,b)}let{tid:b}=function(a){let b,c;if("string"!=typeof a)throw new cg("JWTs must use Compact JWS serialization, JWT must be a string");let{1:d,length:e}=a.split(".");if(5===e)throw new cg("Only JWTs using Compact JWS serialization can be decoded");if(3!==e)throw new cg("Invalid JWT");if(!d)throw new cg("JWTs must contain a payload");try{b=b7(d)}catch{throw new cg("Failed to base64url decode the payload")}try{c=JSON.parse(b2.decode(b))}catch{throw new cg("Failed to parse the decoded payload as JSON")}if(!cn(c))throw new cg("Invalid JWT Claims Set");return c}(a.id_token);if("string"==typeof b){let a=d.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",c=new URL(d.issuer.replace(a,b)),e=await fs(c,{[fd]:h[dZ]});d=await fv(c,e)}}}let t=await gg(d,k,q,{expectedNonce:await gQ.use(b,m,c),requireIdToken:s});if(s){let b=f2(t);if(r=b,h[d$]&&"apple"===h.id)try{r.user=JSON.parse(a?.user)}catch{}if(!1===h.idToken){let a=await fW(d,k,t.access_token,{[fd]:h[dZ],[fa]:!0});r=await fZ(d,k,b.sub,a)}}else if(j?.request){let a=await j.request({tokens:t,provider:h});a instanceof Object&&(r=a)}else if(j?.url){let a=await fW(d,k,t.access_token,{[fd]:h[dZ],[fa]:!0});r=await a.json()}else throw TypeError("No userinfo endpoint configured");return t.expires_in&&(t.expires_at=Math.floor(Date.now()/1e3)+Number(t.expires_in)),{...await gV(r,h,t,g),profile:r,cookies:m}}async function gV(a,b,c,d){try{let d=await b.profile(a,c);return{user:{...d,id:crypto.randomUUID(),email:d.email?.toLowerCase()},account:{...c,provider:b.id,type:b.type,providerAccountId:d.id??crypto.randomUUID()}}}catch(c){d.debug("getProfile error details",a),d.error(new bA(c,{provider:b.id}))}}var gW=c(356).Buffer;async function gX(a,b,c,d){let e=await g0(a,b,c),{cookie:f}=await gS.create(a,e.challenge,c);return{status:200,cookies:[...d??[],f],body:{action:"register",options:e},headers:{"Content-Type":"application/json"}}}async function gY(a,b,c,d){let e=await g_(a,b,c),{cookie:f}=await gS.create(a,e.challenge);return{status:200,cookies:[...d??[],f],body:{action:"authenticate",options:e},headers:{"Content-Type":"application/json"}}}async function gZ(a,b,c){let d,{adapter:e,provider:f}=a,g=b.body&&"string"==typeof b.body.data?JSON.parse(b.body.data):void 0;if(!g||"object"!=typeof g||!("id"in g)||"string"!=typeof g.id)throw new bi("Invalid WebAuthn Authentication response");let h=g3(g2(g.id)),i=await e.getAuthenticator(h);if(!i)throw new bi(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:h})}`);let{challenge:j}=await gS.use(a,b.cookies,c);try{var k;let c=f.getRelayingParty(a,b);d=await f.simpleWebAuthn.verifyAuthenticationResponse({...f.verifyAuthenticationOptions,expectedChallenge:j,response:g,authenticator:{...k=i,credentialDeviceType:k.credentialDeviceType,transports:g4(k.transports),credentialID:g2(k.credentialID),credentialPublicKey:g2(k.credentialPublicKey)},expectedOrigin:c.origin,expectedRPID:c.id})}catch(a){throw new bO(a)}let{verified:l,authenticationInfo:m}=d;if(!l)throw new bO("WebAuthn authentication response could not be verified");try{let{newCounter:a}=m;await e.updateAuthenticatorCounter(i.credentialID,a)}catch(a){throw new bk(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:h,oldCounter:i.counter,newCounter:m.newCounter})}`,a)}let n=await e.getAccount(i.providerAccountId,f.id);if(!n)throw new bi(`WebAuthn account not found in database: ${JSON.stringify({credentialID:h,providerAccountId:i.providerAccountId})}`);let o=await e.getUser(n.userId);if(!o)throw new bi(`WebAuthn user not found in database: ${JSON.stringify({credentialID:h,providerAccountId:i.providerAccountId,userID:n.userId})}`);return{account:n,user:o}}async function g$(a,b,c){var d;let e,{provider:f}=a,g=b.body&&"string"==typeof b.body.data?JSON.parse(b.body.data):void 0;if(!g||"object"!=typeof g||!("id"in g)||"string"!=typeof g.id)throw new bi("Invalid WebAuthn Registration response");let{challenge:h,registerData:i}=await gS.use(a,b.cookies,c);if(!i)throw new bi("Missing user registration data in WebAuthn challenge cookie");try{let c=f.getRelayingParty(a,b);e=await f.simpleWebAuthn.verifyRegistrationResponse({...f.verifyRegistrationOptions,expectedChallenge:h,response:g,expectedOrigin:c.origin,expectedRPID:c.id})}catch(a){throw new bO(a)}if(!e.verified||!e.registrationInfo)throw new bO("WebAuthn registration response could not be verified");let j={providerAccountId:g3(e.registrationInfo.credentialID),provider:a.provider.id,type:f.type},k={providerAccountId:j.providerAccountId,counter:e.registrationInfo.counter,credentialID:g3(e.registrationInfo.credentialID),credentialPublicKey:g3(e.registrationInfo.credentialPublicKey),credentialBackedUp:e.registrationInfo.credentialBackedUp,credentialDeviceType:e.registrationInfo.credentialDeviceType,transports:(d=g.response.transports,d?.join(","))};return{user:i,account:j,authenticator:k}}async function g_(a,b,c){let{provider:d,adapter:e}=a,f=c&&c.id?await e.listAuthenticatorsByUserId(c.id):null,g=d.getRelayingParty(a,b);return await d.simpleWebAuthn.generateAuthenticationOptions({...d.authenticationOptions,rpID:g.id,allowCredentials:f?.map(a=>({id:g2(a.credentialID),type:"public-key",transports:g4(a.transports)}))})}async function g0(a,b,c){let{provider:d,adapter:e}=a,f=c.id?await e.listAuthenticatorsByUserId(c.id):null,g=dS(32),h=d.getRelayingParty(a,b);return await d.simpleWebAuthn.generateRegistrationOptions({...d.registrationOptions,userID:g,userName:c.email,userDisplayName:c.name??void 0,rpID:h.id,rpName:h.name,excludeCredentials:f?.map(a=>({id:g2(a.credentialID),type:"public-key",transports:g4(a.transports)}))})}function g1(a){let{provider:b,adapter:c}=a;if(!c)throw new bu("An adapter is required for the WebAuthn provider");if(!b||"webauthn"!==b.type)throw new bH("Provider must be WebAuthn");return{...a,provider:b,adapter:c}}function g2(a){return new Uint8Array(gW.from(a,"base64"))}function g3(a){return gW.from(a).toString("base64")}function g4(a){return a?a.split(","):void 0}async function g5(a,b,c,d){if(!b.provider)throw new bH("Callback route called without provider");let{query:e,body:f,method:g,headers:h}=a,{provider:i,adapter:j,url:k,callbackUrl:l,pages:m,jwt:n,events:o,callbacks:p,session:{strategy:q,maxAge:r},logger:s}=b,t="jwt"===q;try{if("oauth"===i.type||"oidc"===i.type){let g,h=i.authorization?.url.searchParams.get("response_mode")==="form_post"?f:e;if(b.isOnRedirectProxy&&h?.state){let a=await gP.decode(h.state,b);if(a?.origin&&new URL(a.origin).origin!==b.url.origin){let b=`${a.origin}?${new URLSearchParams(h)}`;return s.debug("Proxy redirecting to",b),{redirect:b,cookies:d}}}let q=await gU(h,a.cookies,b);q.cookies.length&&d.push(...q.cookies),s.debug("authorization result",q);let{user:u,account:v,profile:w}=q;if(!u||!v||!w)return{redirect:`${k}/signin`,cookies:d};if(j){let{getUserByAccount:a}=j;g=await a({providerAccountId:v.providerAccountId,provider:i.id})}let x=await g6({user:g??u,account:v,profile:w},b);if(x)return{redirect:x,cookies:d};let{user:y,session:z,isNewUser:A}=await e5(c.value,u,v,b);if(t){let a={name:y.name,email:y.email,picture:y.image,sub:y.id?.toString()},e=await p.jwt({token:a,user:y,account:v,profile:w,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await n.encode({...n,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*r);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:z.sessionToken,options:{...b.cookies.sessionToken.options,expires:z.expires}});if(await o.signIn?.({user:y,account:v,profile:w,isNewUser:A}),A&&m.newUser)return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}if("email"===i.type){let a=e?.token,f=e?.email;if(!a){let b=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!a}});throw b.name="Configuration",b}let g=i.secret??b.secret,h=await j.useVerificationToken({identifier:f,token:await dR(`${a}${g}`)}),k=!!h,q=k&&h.expires.valueOf()<Date.now();if(!k||q||f&&h.identifier!==f)throw new bJ({hasInvite:k,expired:q});let{identifier:s}=h,u=await j.getUserByEmail(s)??{id:crypto.randomUUID(),email:s,emailVerified:null},v={providerAccountId:u.email,userId:u.id,type:"email",provider:i.id},w=await g6({user:u,account:v},b);if(w)return{redirect:w,cookies:d};let{user:x,session:y,isNewUser:z}=await e5(c.value,u,v,b);if(t){let a={name:x.name,email:x.email,picture:x.image,sub:x.id?.toString()},e=await p.jwt({token:a,user:x,account:v,isNewUser:z,trigger:z?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await n.encode({...n,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*r);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:y.sessionToken,options:{...b.cookies.sessionToken.options,expires:y.expires}});if(await o.signIn?.({user:x,account:v,isNewUser:z}),z&&m.newUser)return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}if("credentials"===i.type&&"POST"===g){let a=f??{};Object.entries(e??{}).forEach(([a,b])=>k.searchParams.set(a,b));let j=await i.authorize(a,new Request(k,{headers:h,method:g,body:JSON.stringify(f)}));if(j)j.id=j.id?.toString()??crypto.randomUUID();else throw new bq;let m={providerAccountId:j.id,type:"credentials",provider:i.id},q=await g6({user:j,account:m,credentials:a},b);if(q)return{redirect:q,cookies:d};let s={name:j.name,email:j.email,picture:j.image,sub:j.id},t=await p.jwt({token:s,user:j,account:m,isNewUser:!1,trigger:"signIn"});if(null===t)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,e=await n.encode({...n,token:t,salt:a}),f=new Date;f.setTime(f.getTime()+1e3*r);let g=c.chunk(e,{expires:f});d.push(...g)}return await o.signIn?.({user:j,account:m}),{redirect:l,cookies:d}}else if("webauthn"===i.type&&"POST"===g){let e,f,g,h=a.body?.action;if("string"!=typeof h||"authenticate"!==h&&"register"!==h)throw new bi("Invalid action parameter");let i=g1(b);switch(h){case"authenticate":{let b=await gZ(i,a,d);e=b.user,f=b.account;break}case"register":{let c=await g$(b,a,d);e=c.user,f=c.account,g=c.authenticator}}await g6({user:e,account:f},b);let{user:j,isNewUser:k,session:q,account:s}=await e5(c.value,e,f,b);if(!s)throw new bi("Error creating or finding account");if(g&&j.id&&await i.adapter.createAuthenticator({...g,userId:j.id}),t){let a={name:j.name,email:j.email,picture:j.image,sub:j.id?.toString()},e=await p.jwt({token:a,user:j,account:s,isNewUser:k,trigger:k?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await n.encode({...n,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*r);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:q.sessionToken,options:{...b.cookies.sessionToken.options,expires:q.expires}});if(await o.signIn?.({user:j,account:s,isNewUser:k}),k&&m.newUser)return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}throw new bH(`Callback for provider type (${i.type}) is not supported`)}catch(b){if(b instanceof bi)throw b;let a=new bm(b,{provider:i.id});throw s.debug("callback route error details",{method:g,query:e,body:f}),a}}async function g6(a,b){let c,{signIn:d,redirect:e}=b.callbacks;try{c=await d(a)}catch(a){if(a instanceof bi)throw a;throw new bl(a)}if(!c)throw new bl("AccessDenied");if("string"==typeof c)return await e({url:c,baseUrl:b.url.origin})}async function g7(a,b,c,d,e){let{adapter:f,jwt:g,events:h,callbacks:i,logger:j,session:{strategy:k,maxAge:l}}=a,m={body:null,headers:{"Content-Type":"application/json",...!d&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:c},n=b.value;if(!n)return m;if("jwt"===k){try{let c=a.cookies.sessionToken.name,f=await g.decode({...g,token:n,salt:c});if(!f)throw Error("Invalid JWT");let j=await i.jwt({token:f,...d&&{trigger:"update"},session:e}),k=e4(l);if(null!==j){let a={user:{name:j.name,email:j.email,image:j.picture},expires:k.toISOString()},d=await i.session({session:a,token:j});m.body=d;let e=await g.encode({...g,token:j,salt:c}),f=b.chunk(e,{expires:k});m.cookies?.push(...f),await h.session?.({session:d,token:j})}else m.cookies?.push(...b.clean())}catch(a){j.error(new bt(a)),m.cookies?.push(...b.clean())}return m}try{let{getSessionAndUser:c,deleteSession:g,updateSession:j}=f,k=await c(n);if(k&&k.session.expires.valueOf()<Date.now()&&(await g(n),k=null),k){let{user:b,session:c}=k,f=a.session.updateAge,g=c.expires.valueOf()-1e3*l+1e3*f,o=e4(l);g<=Date.now()&&await j({sessionToken:n,expires:o});let p=await i.session({session:{...c,user:b},user:b,newSession:e,...d?{trigger:"update"}:{}});m.body=p,m.cookies?.push({name:a.cookies.sessionToken.name,value:n,options:{...a.cookies.sessionToken.options,expires:o}}),await h.session?.({session:p})}else n&&m.cookies?.push(...b.clean())}catch(a){j.error(new bB(a))}return m}async function g8(a,b){let c,d,{logger:e,provider:f}=b,g=f.authorization?.url;if(!g||"authjs.dev"===g.host){let a=new URL(f.issuer),b=await fs(a,{[fd]:f[dZ],[fa]:!0}),c=await fv(a,b).catch(b=>{if(!(b instanceof TypeError)||"Invalid URL"!==b.message)throw b;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${a}`)});if(!c.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");g=new URL(c.authorization_endpoint)}let h=g.searchParams,i=f.callbackUrl;!b.isOnRedirectProxy&&f.redirectProxyUrl&&(i=f.redirectProxyUrl,d=f.callbackUrl,e.debug("using redirect proxy",{redirect_uri:i,data:d}));let j=Object.assign({response_type:"code",client_id:f.clientId,redirect_uri:i,...f.authorization?.params},Object.fromEntries(f.authorization?.url.searchParams??[]),a);for(let a in j)h.set(a,j[a]);let k=[];f.authorization?.url.searchParams.get("response_mode")==="form_post"&&(b.cookies.state.options.sameSite="none",b.cookies.state.options.secure=!0,b.cookies.nonce.options.sameSite="none",b.cookies.nonce.options.secure=!0);let l=await gP.create(b,d);if(l&&(h.set("state",l.value),k.push(l.cookie)),f.checks?.includes("pkce"))if(c&&!c.code_challenge_methods_supported?.includes("S256"))"oidc"===f.type&&(f.checks=["nonce"]);else{let{value:a,cookie:c}=await gN.create(b);h.set("code_challenge",a),h.set("code_challenge_method","S256"),k.push(c)}let m=await gQ.create(b);return m&&(h.set("nonce",m.value),k.push(m.cookie)),"oidc"!==f.type||g.searchParams.has("scope")||g.searchParams.set("scope","openid profile email"),e.debug("authorization url is ready",{url:g,cookies:k,provider:f}),{redirect:g.toString(),cookies:k}}async function g9(a,b){let c,{body:d}=a,{provider:e,callbacks:f,adapter:g}=b,h=(e.normalizeIdentifier??function(a){if(!a)throw Error("Missing email from request body.");let[b,c]=a.toLowerCase().trim().split("@");return c=c.split(",")[0],`${b}@${c}`})(d?.email),i={id:crypto.randomUUID(),email:h,emailVerified:null},j=await g.getUserByEmail(h)??i,k={providerAccountId:h,userId:j.id,type:"email",provider:e.id};try{c=await f.signIn({user:j,account:k,email:{verificationRequest:!0}})}catch(a){throw new bl(a)}if(!c)throw new bl("AccessDenied");if("string"==typeof c)return{redirect:await f.redirect({url:c,baseUrl:b.url.origin})};let{callbackUrl:l,theme:m}=b,n=await e.generateVerificationToken?.()??dS(32),o=new Date(Date.now()+(e.maxAge??86400)*1e3),p=e.secret??b.secret,q=new URL(b.basePath,b.url.origin),r=e.sendVerificationRequest({identifier:h,token:n,expires:o,url:`${q}/callback/${e.id}?${new URLSearchParams({callbackUrl:l,token:n,email:h})}`,provider:e,theme:m,request:new Request(a.url,{headers:a.headers,method:a.method,body:"POST"===a.method?JSON.stringify(a.body??{}):void 0})}),s=g.createVerificationToken?.({identifier:h,token:await dR(`${n}${p}`),expires:o});return await Promise.all([r,s]),{redirect:`${q}/verify-request?${new URLSearchParams({provider:e.id,type:e.type})}`}}async function ha(a,b,c){let d=`${c.url.origin}${c.basePath}/signin`;if(!c.provider)return{redirect:d,cookies:b};switch(c.provider.type){case"oauth":case"oidc":{let{redirect:d,cookies:e}=await g8(a.query,c);return e&&b.push(...e),{redirect:d,cookies:b}}case"email":return{...await g9(a,c),cookies:b};default:return{redirect:d,cookies:b}}}async function hb(a,b,c){let{jwt:d,events:e,callbackUrl:f,logger:g,session:h}=c,i=b.value;if(!i)return{redirect:f,cookies:a};try{if("jwt"===h.strategy){let a=c.cookies.sessionToken.name,b=await d.decode({...d,token:i,salt:a});await e.signOut?.({token:b})}else{let a=await c.adapter?.deleteSession(i);await e.signOut?.({session:a})}}catch(a){g.error(new bE(a))}return a.push(...b.clean()),{redirect:f,cookies:a}}async function hc(a,b){let{adapter:c,jwt:d,session:{strategy:e}}=a,f=b.value;if(!f)return null;if("jwt"===e){let b=a.cookies.sessionToken.name,c=await d.decode({...d,token:f,salt:b});if(c&&c.sub)return{id:c.sub,name:c.name,email:c.email,image:c.picture}}else{let a=await c?.getSessionAndUser(f);if(a)return a.user}return null}async function hd(a,b,c,d){let e=g1(b),{provider:f}=e,{action:g}=a.query??{};if("register"!==g&&"authenticate"!==g&&void 0!==g)return{status:400,body:{error:"Invalid action"},cookies:d,headers:{"Content-Type":"application/json"}};let h=await hc(b,c),i=h?{user:h,exists:!0}:await f.getUserInfo(b,a),j=i?.user;switch(function(a,b,c){let{user:d,exists:e=!1}=c??{};switch(a){case"authenticate":return"authenticate";case"register":if(d&&b===e)return"register";break;case void 0:if(!b)if(!d)return"authenticate";else if(e)return"authenticate";else return"register"}return null}(g,!!h,i)){case"authenticate":return gY(e,a,j,d);case"register":if("string"==typeof j?.email)return gX(e,a,j,d);break;default:return{status:400,body:{error:"Invalid request"},cookies:d,headers:{"Content-Type":"application/json"}}}}async function he(a,b){let{action:c,providerId:d,error:e,method:f}=a,g=b.skipCSRFCheck===dX,{options:h,cookies:i}=await d4({authOptions:b,action:c,providerId:d,url:a.url,callbackUrl:a.body?.callbackUrl??a.query?.callbackUrl,csrfToken:a.body?.csrfToken,cookies:a.cookies,isPost:"POST"===f,csrfDisabled:g}),j=new bh(h.cookies.sessionToken,a.cookies,h.logger);if("GET"===f){let b=e3({...h,query:a.query,cookies:i});switch(c){case"callback":return await g5(a,h,j,i);case"csrf":return b.csrf(g,h,i);case"error":return b.error(e);case"providers":return b.providers(h.providers);case"session":return await g7(h,j,i);case"signin":return b.signin(d,e);case"signout":return b.signout();case"verify-request":return b.verifyRequest();case"webauthn-options":return await hd(a,h,j,i)}}else{let{csrfTokenVerified:b}=h;switch(c){case"callback":return"credentials"===h.provider.type&&dU(c,b),await g5(a,h,j,i);case"session":return dU(c,b),await g7(h,j,i,!0,a.body?.data);case"signin":return dU(c,b),await ha(a,i,h);case"signout":return dU(c,b),await hb(i,j,h)}}throw new bF(`Cannot handle action: ${c}`)}function hf(a,b,c,d,e){let f,g=e?.basePath,h=d.AUTH_URL??d.NEXTAUTH_URL;if(h)f=new URL(h),g&&"/"!==g&&"/"!==f.pathname&&(f.pathname!==g&&dK(e).warn("env-url-basepath-mismatch"),f.pathname="/");else{let a=c.get("x-forwarded-host")??c.get("host"),d=c.get("x-forwarded-proto")??b??"https",e=d.endsWith(":")?d:d+":";f=new URL(`${e}//${a}`)}let i=f.toString().replace(/\/$/,"");if(g){let b=g?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${i}/${b}/${a}`)}return new URL(`${i}/${a}`)}async function hg(a,b){let c=dK(b),d=await dP(a,b);if(!d)return Response.json("Bad request.",{status:400});let e=function(a,b){let{url:c}=a,d=[];if(!bR&&b.debug&&d.push("debug-enabled"),!b.trustHost)return new bI(`Host must be trusted. URL was: ${a.url}`);if(!b.secret?.length)return new bx("Please define a `secret`");let e=a.query?.callbackUrl;if(e&&!bS(e,c.origin))return new bp(`Invalid callback URL. Received: ${e}`);let{callbackUrl:f}=bg(b.useSecureCookies??"https:"===c.protocol),g=a.cookies?.[b.cookies?.callbackUrl?.name??f.name];if(g&&!bS(g,c.origin))return new bp(`Invalid callback URL. Received: ${g}`);let h=!1;for(let a of b.providers){let b="function"==typeof a?a():a;if(("oauth"===b.type||"oidc"===b.type)&&!(b.issuer??b.options?.issuer)){let a,{authorization:c,token:d,userinfo:e}=b;if("string"==typeof c||c?.url?"string"==typeof d||d?.url?"string"==typeof e||e?.url||(a="userinfo"):a="token":a="authorization",a)return new br(`Provider "${b.id}" is missing both \`issuer\` and \`${a}\` endpoint config. At least one of them is required`)}if("credentials"===b.type)bT=!0;else if("email"===b.type)bU=!0;else if("webauthn"===b.type){var i;if(bV=!0,b.simpleWebAuthnBrowserVersion&&(i=b.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(i)))return new bi(`Invalid provider config for "${b.id}": simpleWebAuthnBrowserVersion "${b.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(b.enableConditionalUI){if(h)return new bM("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(h=!0,!Object.values(b.formFields).some(a=>a.autocomplete&&a.autocomplete.toString().indexOf("webauthn")>-1))return new bN(`Provider "${b.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(bT){let a=b.session?.strategy==="database",c=!b.providers.some(a=>"credentials"!==("function"==typeof a?a():a).type);if(a&&c)return new bG("Signing in with credentials only supported if JWT strategy is enabled");if(b.providers.some(a=>{let b="function"==typeof a?a():a;return"credentials"===b.type&&!b.authorize}))return new bw("Must define an authorize() handler to use credentials authentication provider")}let{adapter:j,session:k}=b,l=[];if(bU||k?.strategy==="database"||!k?.strategy&&j)if(bU){if(!j)return new bu("Email login requires an adapter");l.push(...bW)}else{if(!j)return new bu("Database session requires an adapter");l.push(...bX)}if(bV){if(!b.experimental?.enableWebAuthn)return new bQ("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(d.push("experimental-webauthn"),!j)return new bu("WebAuthn requires an adapter");l.push(...bY)}if(j){let a=l.filter(a=>!(a in j));if(a.length)return new bv(`Required adapter methods were missing: ${a.join(", ")}`)}return bR||(bR=!0),d}(d,b);if(Array.isArray(e))e.forEach(c.warn);else if(e){if(c.error(e),!new Set(["signin","signout","error","verify-request"]).has(d.action)||"GET"!==d.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:a,theme:f}=b,g=a?.error&&d.url.searchParams.get("callbackUrl")?.startsWith(a.error);if(!a?.error||g)return g&&c.error(new bn(`The error page ${a?.error} should not require authentication`)),dQ(e3({theme:f}).error("Configuration"));let h=`${d.url.origin}${a.error}?error=Configuration`;return Response.redirect(h)}let f=a.headers?.has("X-Auth-Return-Redirect"),g=b.raw===dY;try{let a=await he(d,b);if(g)return a;let c=dQ(a),e=c.headers.get("Location");if(!f||!e)return c;return Response.json({url:e},{headers:c.headers})}catch(l){c.error(l);let e=l instanceof bi;if(e&&g&&!f)throw l;if("POST"===a.method&&"session"===d.action)return Response.json(null,{status:400});let h=new URLSearchParams({error:l instanceof bi&&bL.has(l.type)?l.type:"Configuration"});l instanceof bq&&h.set("code",l.code);let i=e&&l.kind||"error",j=b.pages?.[i]??`${b.basePath}/${i.toLowerCase()}`,k=`${d.url.origin}${j}?${h}`;if(f)return Response.json({url:k});return Response.redirect(k)}}c(280),"undefined"==typeof URLPattern||URLPattern;var hh=c(557),hi=c(602),hj=c(801);function hk(){let a=a1.getStore();return(null==a?void 0:a.rootTaskSpawnPhase)==="action"}function hl(a){let b=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!b)return a;let{origin:c}=new URL(b),{href:d,origin:e}=a.nextUrl;return new R(d.replace(e,c),a)}function hm(a){try{a.secret??(a.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let b=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!b)return;let{pathname:c}=new URL(b);if("/"===c)return;a.basePath||(a.basePath=c)}catch{}finally{a.basePath||(a.basePath="/api/auth"),function(a,b,c=!1){try{let d=a.AUTH_URL;d&&(b.basePath?c||dK(b).warn("env-url-basepath-redundant"):b.basePath=new URL(d).pathname)}catch{}finally{b.basePath??(b.basePath="/auth")}if(!b.secret?.length){b.secret=[];let c=a.AUTH_SECRET;for(let d of(c&&b.secret.push(c),[1,2,3])){let c=a[`AUTH_SECRET_${d}`];c&&b.secret.unshift(c)}}b.redirectProxyUrl??(b.redirectProxyUrl=a.AUTH_REDIRECT_PROXY_URL),b.trustHost??(b.trustHost=!!(a.AUTH_URL??a.AUTH_TRUST_HOST??a.VERCEL??a.CF_PAGES??"production"!==a.NODE_ENV)),b.providers=b.providers.map(b=>{let{id:c}="function"==typeof b?b({}):b,d=c.toUpperCase().replace(/-/g,"_"),e=a[`AUTH_${d}_ID`],f=a[`AUTH_${d}_SECRET`],g=a[`AUTH_${d}_ISSUER`],h=a[`AUTH_${d}_KEY`],i="function"==typeof b?b({clientId:e,clientSecret:f,issuer:g,apiKey:h}):b;return"oauth"===i.type||"oidc"===i.type?(i.clientId??(i.clientId=e),i.clientSecret??(i.clientSecret=f),i.issuer??(i.issuer=g)):"email"===i.type&&(i.apiKey??(i.apiKey=h)),i})}(process.env,a,!0)}}new WeakMap;var hn=c(815);let ho={current:null},hp="function"==typeof hn.cache?hn.cache:a=>a,hq=console.warn;function hr(a){return function(...b){hq(a(...b))}}function hs(){let a="cookies",b=aa.J.getStore(),c=ab.FP.getStore();if(b){if(c&&"after"===c.phase&&!hk())throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(b.forceStatic)return hu(ad.seal(new P.RequestCookies(new Headers({}))));if(c){if("cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(b.dynamicShouldError)throw Object.defineProperty(new hi.f(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(c)switch(c.type){case"prerender":var d=c;let e=ht.get(d);if(e)return e;let f=(0,hj.W)(d.renderSignal,"`cookies()`");return ht.set(d,f),f;case"prerender-client":let g="`cookies`";throw Object.defineProperty(new aQ(`${g} must not be used within a client component. Next.js should be preventing ${g} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,hh.Ui)(b.route,a,c.dynamicTracking);break;case"prerender-legacy":(0,hh.xI)(a,b,c)}(0,hh.Pk)(b,c)}let e=(0,ab.XN)(a);return hu(ag(e)?e.userspaceMutableCookies:e.cookies)}hp(a=>{try{hq(ho.current)}finally{ho.current=null}});let ht=new WeakMap;function hu(a){let b=ht.get(a);if(b)return b;let c=Promise.resolve(a);return ht.set(a,c),Object.defineProperties(c,{[Symbol.iterator]:{value:a[Symbol.iterator]?a[Symbol.iterator].bind(a):hv.bind(a)},size:{get:()=>a.size},get:{value:a.get.bind(a)},getAll:{value:a.getAll.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},delete:{value:a.delete.bind(a)},clear:{value:"function"==typeof a.clear?a.clear.bind(a):hw.bind(a,c)},toString:{value:a.toString.bind(a)}}),c}function hv(){return this.getAll().map(a=>[a.name,a]).values()}function hw(a){for(let a of this.getAll())this.delete(a.name);return a}function hx(){let a=aa.J.getStore(),b=ab.FP.getStore();if(a){if(b&&"after"===b.phase&&!hk())throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(a.forceStatic)return hz(_.seal(new Headers({})));if(b){if("cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(a.dynamicShouldError)throw Object.defineProperty(new hi.f(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender":var c=b;let d=hy.get(c);if(d)return d;let e=(0,hj.W)(c.renderSignal,"`headers()`");return hy.set(c,e),e;case"prerender-client":let f="`headers`";throw Object.defineProperty(new aQ(`${f} must not be used within a client component. Next.js should be preventing ${f} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,hh.Ui)(a.route,"headers",b.dynamicTracking);break;case"prerender-legacy":(0,hh.xI)("headers",a,b)}(0,hh.Pk)(a,b)}return hz((0,ab.XN)("headers").headers)}hr(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})});let hy=new WeakMap;function hz(a){let b=hy.get(a);if(b)return b;let c=Promise.resolve(a);return hy.set(a,c),Object.defineProperties(c,{append:{value:a.append.bind(a)},delete:{value:a.delete.bind(a)},get:{value:a.get.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},getSetCookie:{value:a.getSetCookie.bind(a)},forEach:{value:a.forEach.bind(a)},keys:{value:a.keys.bind(a)},values:{value:a.values.bind(a)},entries:{value:a.entries.bind(a)},[Symbol.iterator]:{value:a[Symbol.iterator].bind(a)}}),c}async function hA(a,b){return hg(new Request(hf("session",a.get("x-forwarded-proto"),a,process.env,b),{headers:{cookie:a.get("cookie")??""}}),{...b,callbacks:{...b.callbacks,async session(...a){let c=await b.callbacks?.session?.(...a)??{...a[0].session,expires:a[0].session.expires?.toISOString?.()??a[0].session.expires};return{user:a[0].user??a[0].token,...c}}}})}function hB(a){return"function"==typeof a}function hC(a,b){return"function"==typeof a?async(...c)=>{if(!c.length){let c=await hx(),d=await a(void 0);return b?.(d),hA(c,d).then(a=>a.json())}if(c[0]instanceof Request){let d=c[0],e=c[1],f=await a(d);return b?.(f),hD([d,e],f)}if(hB(c[0])){let d=c[0];return async(...c)=>{let e=await a(c[0]);return b?.(e),hD(c,e,d)}}let d="req"in c[0]?c[0].req:c[0],e="res"in c[0]?c[0].res:c[1],f=await a(d);return b?.(f),hA(new Headers(d.headers),f).then(async a=>{let b=await a.json();for(let b of a.headers.getSetCookie())"headers"in e?e.headers.append("set-cookie",b):e.appendHeader("set-cookie",b);return b})}:(...b)=>{if(!b.length)return Promise.resolve(hx()).then(b=>hA(b,a).then(a=>a.json()));if(b[0]instanceof Request)return hD([b[0],b[1]],a);if(hB(b[0])){let c=b[0];return async(...b)=>hD(b,a,c).then(a=>a)}let c="req"in b[0]?b[0].req:b[0],d="res"in b[0]?b[0].res:b[1];return hA(new Headers(c.headers),a).then(async a=>{let b=await a.json();for(let b of a.headers.getSetCookie())"headers"in d?d.headers.append("set-cookie",b):d.appendHeader("set-cookie",b);return b})}}async function hD(a,b,c){let d=hl(a[0]),e=await hA(d.headers,b),f=await e.json(),g=!0;b.callbacks?.authorized&&(g=await b.callbacks.authorized({request:d,auth:f}));let h=W.next?.();if(g instanceof Response){h=g;let a=g.headers.get("Location"),{pathname:c}=d.nextUrl;a&&function(a,b,c){let d=b.replace(`${a}/`,""),e=Object.values(c.pages??{});return(hE.has(d)||e.includes(b))&&b===a}(c,new URL(a).pathname,b)&&(g=!0)}else if(c)d.auth=f,h=await c(d,a[1])??W.next();else if(!g){let a=b.pages?.signIn??`${b.basePath}/signin`;if(d.nextUrl.pathname!==a){let b=d.nextUrl.clone();b.pathname=a,b.searchParams.set("callbackUrl",d.nextUrl.href),h=W.redirect(b)}}let i=new Response(h?.body,h);for(let a of e.headers.getSetCookie())i.headers.append("set-cookie",a);return i}hr(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}),c(16),new WeakMap,hr(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});let hE=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var hF=c(821),hG=c(167);let hH=c(830).s;function hI(a,b){var c;throw null!=b||(b=(null==hH||null==(c=hH.getStore())?void 0:c.isAction)?hG.zB.push:hG.zB.replace),function(a,b,c){void 0===c&&(c=hF.Q.TemporaryRedirect);let d=Object.defineProperty(Error(hG.oJ),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return d.digest=hG.oJ+";"+b+";"+a+";"+c+";",d}(a,b,hF.Q.TemporaryRedirect)}var hJ=c(159);async function hK(a,b={},c,d){let e=new Headers(await hx()),{redirect:f=!0,redirectTo:g,...h}=b instanceof FormData?Object.fromEntries(b):b,i=g?.toString()??e.get("Referer")??"/",j=hf("signin",e.get("x-forwarded-proto"),e,process.env,d);if(!a)return j.searchParams.append("callbackUrl",i),f&&hI(j.toString()),j.toString();let k=`${j}/${a}?${new URLSearchParams(c)}`,l={};for(let b of d.providers){let{options:c,...d}="function"==typeof b?b():b,e=c?.id??d.id;if(e===a){l={id:e,type:c?.type??d.type};break}}if(!l.id){let a=`${j}?${new URLSearchParams({callbackUrl:i})}`;return f&&hI(a),a}"credentials"===l.type&&(k=k.replace("signin","callback")),e.set("Content-Type","application/x-www-form-urlencoded");let m=new Request(k,{method:"POST",headers:e,body:new URLSearchParams({...h,callbackUrl:i})}),n=await hg(m,{...d,raw:dY,skipCSRFCheck:dX}),o=await hs();for(let a of n?.cookies??[])o.set(a.name,a.value,a.options);let p=(n instanceof Response?n.headers.get("Location"):n.redirect)??k;return f?hI(p):p}async function hL(a,b){let c=new Headers(await hx());c.set("Content-Type","application/x-www-form-urlencoded");let d=hf("signout",c.get("x-forwarded-proto"),c,process.env,b),e=new URLSearchParams({callbackUrl:a?.redirectTo??c.get("Referer")??"/"}),f=new Request(d,{method:"POST",headers:c,body:e}),g=await hg(f,{...b,raw:dY,skipCSRFCheck:dX}),h=await hs();for(let a of g?.cookies??[])h.set(a.name,a.value,a.options);return a?.redirect??!0?hI(g.redirect):g}async function hM(a,b){let c=new Headers(await hx());c.set("Content-Type","application/json");let d=new Request(hf("session",c.get("x-forwarded-proto"),c,process.env,b),{method:"POST",headers:c,body:JSON.stringify({data:a})}),e=await hg(d,{...b,raw:dY,skipCSRFCheck:dX}),f=await hs();for(let a of e?.cookies??[])f.set(a.name,a.value,a.options);return e.body}hJ.s8,hJ.s8,hJ.s8,c(792).X;let hN=[{id:"1",email:"<EMAIL>",password:"admin123",name:"Administrator",role:"admin"},{id:"2",email:"<EMAIL>",password:"teacher123",name:"Teacher",role:"teacher"}],{handlers:hO,signIn:hP,signOut:hQ,auth:hR}=function(a){if("function"==typeof a){let b=async b=>{let c=await a(b);return hm(c),hg(hl(b),c)};return{handlers:{GET:b,POST:b},auth:hC(a,a=>hm(a)),signIn:async(b,c,d)=>{let e=await a(void 0);return hm(e),hK(b,c,d,e)},signOut:async b=>{let c=await a(void 0);return hm(c),hL(b,c)},unstable_update:async b=>{let c=await a(void 0);return hm(c),hM(b,c)}}}hm(a);let b=b=>hg(hl(b),a);return{handlers:{GET:b,POST:b},auth:hC(a),signIn:(b,c,d)=>hK(b,c,d,a),signOut:b=>hL(b,a),unstable_update:b=>hM(b,a)}}({providers:[{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:{credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(a){if(!a?.email||!a?.password)return null;let b=hN.find(b=>b.email===a.email&&b.password===a.password);return b?{id:b.id,email:b.email,name:b.name,role:b.role}:null}}}],pages:{signIn:"/login"},callbacks:{jwt:async({token:a,user:b})=>(b&&(a.role=b.role),a),session:async({session:a,token:b})=>(b&&(a.user.id=b.sub,a.user.role=b.role),a)},session:{strategy:"jwt"}}),hS=hR(a=>{let{pathname:b}=a.nextUrl,c=!!a.auth,d=["/login"].includes(b);return c||d?c&&"/login"===b?W.redirect(new URL("/dashboard",a.url)):W.next():W.redirect(new URL("/login",a.url))}),hT={matcher:["/((?!api|_next/static|_next/image|favicon.ico).*)"]};c(199);let hU={...j},hV=hU.middleware||hU.default,hW="/middleware";if("function"!=typeof hV)throw Object.defineProperty(Error(`The Middleware "${hW}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function hX(a){return bd({...a,page:hW,handler:async(...a)=>{try{return await hV(...a)}catch(e){let b=a[0],c=new URL(b.url),d=c.pathname+c.search;throw await n(e,{path:d,method:b.method,headers:Object.fromEntries(b.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),e}}})}},356:a=>{"use strict";a.exports=require("node:buffer")},521:a=>{"use strict";a.exports=require("node:async_hooks")},535:(a,b,c)=>{"use strict";c.d(b,{J:()=>d});let d=(0,c(58).xl)()},552:(a,b,c)=>{"use strict";var d=c(356).Buffer;Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{handleFetch:function(){return h},interceptFetch:function(){return i},reader:function(){return f}});let e=c(201),f={url:a=>a.url,header:(a,b)=>a.headers.get(b)};async function g(a,b){let{url:c,method:e,headers:f,body:g,cache:h,credentials:i,integrity:j,mode:k,redirect:l,referrer:m,referrerPolicy:n}=b;return{testData:a,api:"fetch",request:{url:c,method:e,headers:[...Array.from(f),["next-test-stack",function(){let a=(Error().stack??"").split("\n");for(let b=1;b<a.length;b++)if(a[b].length>0){a=a.slice(b);break}return(a=(a=(a=a.filter(a=>!a.includes("/next/dist/"))).slice(0,5)).map(a=>a.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:g?d.from(await b.arrayBuffer()).toString("base64"):null,cache:h,credentials:i,integrity:j,mode:k,redirect:l,referrer:m,referrerPolicy:n}}}async function h(a,b){let c=(0,e.getTestReqInfo)(b,f);if(!c)return a(b);let{testData:h,proxyPort:i}=c,j=await g(h,b),k=await a(`http://localhost:${i}`,{method:"POST",body:JSON.stringify(j),next:{internal:!0}});if(!k.ok)throw Object.defineProperty(Error(`Proxy request failed: ${k.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let l=await k.json(),{api:m}=l;switch(m){case"continue":return a(b);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${b.method} ${b.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:n,headers:o,body:p}=l.response;return new Response(p?d.from(p,"base64"):null,{status:n,headers:new Headers(o)})}function i(a){return c.g.fetch=function(b,c){var d;return(null==c||null==(d=c.next)?void 0:d.internal)?a(b,c):h(a,new Request(b,c))},()=>{c.g.fetch=a}}},557:(a,b,c)=>{"use strict";c.d(b,{I3:()=>k,Ui:()=>i,xI:()=>g,Pk:()=>h});var d=c(815),e=c(16);c(602),c(115),c(535),c(801);let f="function"==typeof d.unstable_postpone;function g(a,b,c){let d=Object.defineProperty(new e.F(`Route ${b.route} couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw c.revalidate=0,b.dynamicUsageDescription=a,b.dynamicUsageStack=d.stack,d}function h(a,b){b&&"cache"!==b.type&&"unstable-cache"!==b.type&&("prerender"===b.type||"prerender-client"===b.type||"prerender-legacy"===b.type)&&(b.revalidate=0)}function i(a,b,c){(function(){if(!f)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:b}),d.unstable_postpone(j(a,b))}function j(a,b){return`Route ${a} needs to bail out of prerendering at this point because it used ${b}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function k(a){return"object"==typeof a&&null!==a&&"string"==typeof a.message&&l(a.message)}function l(a){return a.includes("needs to bail out of prerendering at this point because it used")&&a.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===l(j("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},602:(a,b,c)=>{"use strict";c.d(b,{f:()=>d});class d extends Error{constructor(...a){super(...a),this.code="NEXT_STATIC_GEN_BAILOUT"}}},724:a=>{"use strict";var b=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d=Object.getOwnPropertyNames,e=Object.prototype.hasOwnProperty,f={};function g(a){var b;let c=["path"in a&&a.path&&`Path=${a.path}`,"expires"in a&&(a.expires||0===a.expires)&&`Expires=${("number"==typeof a.expires?new Date(a.expires):a.expires).toUTCString()}`,"maxAge"in a&&"number"==typeof a.maxAge&&`Max-Age=${a.maxAge}`,"domain"in a&&a.domain&&`Domain=${a.domain}`,"secure"in a&&a.secure&&"Secure","httpOnly"in a&&a.httpOnly&&"HttpOnly","sameSite"in a&&a.sameSite&&`SameSite=${a.sameSite}`,"partitioned"in a&&a.partitioned&&"Partitioned","priority"in a&&a.priority&&`Priority=${a.priority}`].filter(Boolean),d=`${a.name}=${encodeURIComponent(null!=(b=a.value)?b:"")}`;return 0===c.length?d:`${d}; ${c.join("; ")}`}function h(a){let b=new Map;for(let c of a.split(/; */)){if(!c)continue;let a=c.indexOf("=");if(-1===a){b.set(c,"true");continue}let[d,e]=[c.slice(0,a),c.slice(a+1)];try{b.set(d,decodeURIComponent(null!=e?e:"true"))}catch{}}return b}function i(a){if(!a)return;let[[b,c],...d]=h(a),{domain:e,expires:f,httponly:g,maxage:i,path:l,samesite:m,secure:n,partitioned:o,priority:p}=Object.fromEntries(d.map(([a,b])=>[a.toLowerCase().replace(/-/g,""),b]));{var q,r,s={name:b,value:decodeURIComponent(c),domain:e,...f&&{expires:new Date(f)},...g&&{httpOnly:!0},..."string"==typeof i&&{maxAge:Number(i)},path:l,...m&&{sameSite:j.includes(q=(q=m).toLowerCase())?q:void 0},...n&&{secure:!0},...p&&{priority:k.includes(r=(r=p).toLowerCase())?r:void 0},...o&&{partitioned:!0}};let a={};for(let b in s)s[b]&&(a[b]=s[b]);return a}}((a,c)=>{for(var d in c)b(a,d,{get:c[d],enumerable:!0})})(f,{RequestCookies:()=>l,ResponseCookies:()=>m,parseCookie:()=>h,parseSetCookie:()=>i,stringifyCookie:()=>g}),a.exports=((a,f,g,h)=>{if(f&&"object"==typeof f||"function"==typeof f)for(let i of d(f))e.call(a,i)||i===g||b(a,i,{get:()=>f[i],enumerable:!(h=c(f,i))||h.enumerable});return a})(b({},"__esModule",{value:!0}),f);var j=["strict","lax","none"],k=["low","medium","high"],l=class{constructor(a){this._parsed=new Map,this._headers=a;let b=a.get("cookie");if(b)for(let[a,c]of h(b))this._parsed.set(a,{name:a,value:c})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed);if(!a.length)return c.map(([a,b])=>b);let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(([a])=>a===d).map(([a,b])=>b)}has(a){return this._parsed.has(a)}set(...a){let[b,c]=1===a.length?[a[0].name,a[0].value]:a,d=this._parsed;return d.set(b,{name:b,value:c}),this._headers.set("cookie",Array.from(d).map(([a,b])=>g(b)).join("; ")),this}delete(a){let b=this._parsed,c=Array.isArray(a)?a.map(a=>b.delete(a)):b.delete(a);return this._headers.set("cookie",Array.from(b).map(([a,b])=>g(b)).join("; ")),c}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a=>`${a.name}=${encodeURIComponent(a.value)}`).join("; ")}},m=class{constructor(a){var b,c,d;this._parsed=new Map,this._headers=a;let e=null!=(d=null!=(c=null==(b=a.getSetCookie)?void 0:b.call(a))?c:a.get("set-cookie"))?d:[];for(let a of Array.isArray(e)?e:function(a){if(!a)return[];var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}(e)){let b=i(a);b&&this._parsed.set(b.name,b)}}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed.values());if(!a.length)return c;let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(a=>a.name===d)}has(a){return this._parsed.has(a)}set(...a){let[b,c,d]=1===a.length?[a[0].name,a[0].value,a[0]]:a,e=this._parsed;return e.set(b,function(a={name:"",value:""}){return"number"==typeof a.expires&&(a.expires=new Date(a.expires)),a.maxAge&&(a.expires=new Date(Date.now()+1e3*a.maxAge)),(null===a.path||void 0===a.path)&&(a.path="/"),a}({name:b,value:c,...d})),function(a,b){for(let[,c]of(b.delete("set-cookie"),a)){let a=g(c);b.append("set-cookie",a)}}(e,this._headers),this}delete(...a){let[b,c]="string"==typeof a[0]?[a[0]]:[a[0].name,a[0]];return this.set({...c,name:b,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(g).join("; ")}}},792:(a,b,c)=>{"use strict";c.d(b,{X:()=>function a(b){if((0,f.p)(b)||"object"==typeof b&&null!==b&&"digest"in b&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===b.digest||(0,h.h)(b)||(0,g.I3)(b)||"object"==typeof b&&null!==b&&b.$$typeof===e||(0,d.T)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}});var d=c(801);let e=Symbol.for("react.postpone");var f=c(199),g=c(557),h=c(16)},801:(a,b,c)=>{"use strict";function d(a){return"object"==typeof a&&null!==a&&"digest"in a&&a.digest===e}c.d(b,{T:()=>d,W:()=>h});let e="HANGING_PROMISE_REJECTION";class f extends Error{constructor(a){super(`During prerendering, ${a} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${a} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=a,this.digest=e}}let g=new WeakMap;function h(a,b){if(a.aborted)return Promise.reject(new f(b));{let c=new Promise((c,d)=>{let e=d.bind(null,new f(b)),h=g.get(a);if(h)h.push(e);else{let b=[e];g.set(a,b),a.addEventListener("abort",()=>{for(let a=0;a<b.length;a++)b[a]()},{once:!0})}});return c.catch(i),c}}function i(){}},802:a=>{(()=>{"use strict";var b={993:a=>{var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),(new d).__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},213:a=>{a.exports=(a,b)=>(b=b||(()=>{}),a.then(a=>new Promise(a=>{a(b())}).then(()=>a),a=>new Promise(a=>{a(b())}).then(()=>{throw a})))},574:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a,b,c){let d=0,e=a.length;for(;e>0;){let f=e/2|0,g=d+f;0>=c(a[g],b)?(d=++g,e-=f+1):e=f}return d}},821:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0});let d=c(574);class e{constructor(){this._queue=[]}enqueue(a,b){let c={priority:(b=Object.assign({priority:0},b)).priority,run:a};if(this.size&&this._queue[this.size-1].priority>=b.priority)return void this._queue.push(c);let e=d.default(this._queue,c,(a,b)=>b.priority-a.priority);this._queue.splice(e,0,c)}dequeue(){let a=this._queue.shift();return null==a?void 0:a.run}filter(a){return this._queue.filter(b=>b.priority===a.priority).map(a=>a.run)}get size(){return this._queue.length}}b.default=e},816:(a,b,c)=>{let d=c(213);class e extends Error{constructor(a){super(a),this.name="TimeoutError"}}let f=(a,b,c)=>new Promise((f,g)=>{if("number"!=typeof b||b<0)throw TypeError("Expected `milliseconds` to be a positive number");if(b===1/0)return void f(a);let h=setTimeout(()=>{if("function"==typeof c){try{f(c())}catch(a){g(a)}return}let d="string"==typeof c?c:`Promise timed out after ${b} milliseconds`,h=c instanceof Error?c:new e(d);"function"==typeof a.cancel&&a.cancel(),g(h)},b);d(a.then(f,g),()=>{clearTimeout(h)})});a.exports=f,a.exports.default=f,a.exports.TimeoutError=e}},c={};function d(a){var e=c[a];if(void 0!==e)return e.exports;var f=c[a]={exports:{}},g=!0;try{b[a](f,f.exports,d),g=!1}finally{g&&delete c[a]}return f.exports}d.ab="//";var e={};(()=>{Object.defineProperty(e,"__esModule",{value:!0});let a=d(993),b=d(816),c=d(821),f=()=>{},g=new b.TimeoutError;class h extends a{constructor(a){var b,d,e,g;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=f,this._resolveIdle=f,!("number"==typeof(a=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:c.default},a)).intervalCap&&a.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(d=null==(b=a.intervalCap)?void 0:b.toString())?d:""}\` (${typeof a.intervalCap})`);if(void 0===a.interval||!(Number.isFinite(a.interval)&&a.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(g=null==(e=a.interval)?void 0:e.toString())?g:""}\` (${typeof a.interval})`);this._carryoverConcurrencyCount=a.carryoverConcurrencyCount,this._isIntervalIgnored=a.intervalCap===1/0||0===a.interval,this._intervalCap=a.intervalCap,this._interval=a.interval,this._queue=new a.queueClass,this._queueClass=a.queueClass,this.concurrency=a.concurrency,this._timeout=a.timeout,this._throwOnTimeout=!0===a.throwOnTimeout,this._isPaused=!1===a.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=f,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=f,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let a=Date.now();if(void 0===this._intervalId){let b=this._intervalEnd-a;if(!(b<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},b)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let a=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let b=this._queue.dequeue();return!!b&&(this.emit("active"),b(),a&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(a){if(!("number"==typeof a&&a>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${a}\` (${typeof a})`);this._concurrency=a,this._processQueue()}async add(a,c={}){return new Promise((d,e)=>{let f=async()=>{this._pendingCount++,this._intervalCount++;try{let f=void 0===this._timeout&&void 0===c.timeout?a():b.default(Promise.resolve(a()),void 0===c.timeout?this._timeout:c.timeout,()=>{(void 0===c.throwOnTimeout?this._throwOnTimeout:c.throwOnTimeout)&&e(g)});d(await f)}catch(a){e(a)}this._next()};this._queue.enqueue(f,c),this._tryToStartAnother(),this.emit("add")})}async addAll(a,b){return Promise.all(a.map(async a=>this.add(a,b)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(a=>{let b=this._resolveEmpty;this._resolveEmpty=()=>{b(),a()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(a=>{let b=this._resolveIdle;this._resolveIdle=()=>{b(),a()}})}get size(){return this._queue.size}sizeBy(a){return this._queue.filter(a).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(a){this._timeout=a}}e.default=h})(),a.exports=e})()},815:(a,b,c)=>{"use strict";a.exports=c(35)},821:(a,b,c)=>{"use strict";c.d(b,{Q:()=>d});var d=function(a){return a[a.SeeOther=303]="SeeOther",a[a.TemporaryRedirect=307]="TemporaryRedirect",a[a.PermanentRedirect=308]="PermanentRedirect",a}({})},830:(a,b,c)=>{"use strict";c.d(b,{s:()=>d});let d=(0,c(58).xl)()},890:a=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var b={};(()=>{b.parse=function(b,c){if("string"!=typeof b)throw TypeError("argument str must be a string");for(var e={},f=b.split(d),g=(c||{}).decode||a,h=0;h<f.length;h++){var i=f[h],j=i.indexOf("=");if(!(j<0)){var k=i.substr(0,j).trim(),l=i.substr(++j,i.length).trim();'"'==l[0]&&(l=l.slice(1,-1)),void 0==e[k]&&(e[k]=function(a,b){try{return b(a)}catch(b){return a}}(l,g))}}return e},b.serialize=function(a,b,d){var f=d||{},g=f.encode||c;if("function"!=typeof g)throw TypeError("option encode is invalid");if(!e.test(a))throw TypeError("argument name is invalid");var h=g(b);if(h&&!e.test(h))throw TypeError("argument val is invalid");var i=a+"="+h;if(null!=f.maxAge){var j=f.maxAge-0;if(isNaN(j)||!isFinite(j))throw TypeError("option maxAge is invalid");i+="; Max-Age="+Math.floor(j)}if(f.domain){if(!e.test(f.domain))throw TypeError("option domain is invalid");i+="; Domain="+f.domain}if(f.path){if(!e.test(f.path))throw TypeError("option path is invalid");i+="; Path="+f.path}if(f.expires){if("function"!=typeof f.expires.toUTCString)throw TypeError("option expires is invalid");i+="; Expires="+f.expires.toUTCString()}if(f.httpOnly&&(i+="; HttpOnly"),f.secure&&(i+="; Secure"),f.sameSite)switch("string"==typeof f.sameSite?f.sameSite.toLowerCase():f.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return i};var a=decodeURIComponent,c=encodeURIComponent,d=/; */,e=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),a.exports=b})()},905:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{interceptTestApis:function(){return f},wrapRequestHandler:function(){return g}});let d=c(201),e=c(552);function f(){return(0,e.interceptFetch)(c.g.fetch)}function g(a){return(b,c)=>(0,d.withRequest)(b,e.reader,()=>a(b,c))}},956:(a,b,c)=>{(()=>{"use strict";var b={491:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ContextAPI=void 0;let d=c(223),e=c(172),f=c(930),g="context",h=new d.NoopContextManager;class i{constructor(){}static getInstance(){return this._instance||(this._instance=new i),this._instance}setGlobalContextManager(a){return(0,e.registerGlobal)(g,a,f.DiagAPI.instance())}active(){return this._getContextManager().active()}with(a,b,c,...d){return this._getContextManager().with(a,b,c,...d)}bind(a,b){return this._getContextManager().bind(a,b)}_getContextManager(){return(0,e.getGlobal)(g)||h}disable(){this._getContextManager().disable(),(0,e.unregisterGlobal)(g,f.DiagAPI.instance())}}b.ContextAPI=i},930:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagAPI=void 0;let d=c(56),e=c(912),f=c(957),g=c(172);class h{constructor(){function a(a){return function(...b){let c=(0,g.getGlobal)("diag");if(c)return c[a](...b)}}let b=this;b.setLogger=(a,c={logLevel:f.DiagLogLevel.INFO})=>{var d,h,i;if(a===b){let a=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return b.error(null!=(d=a.stack)?d:a.message),!1}"number"==typeof c&&(c={logLevel:c});let j=(0,g.getGlobal)("diag"),k=(0,e.createLogLevelDiagLogger)(null!=(h=c.logLevel)?h:f.DiagLogLevel.INFO,a);if(j&&!c.suppressOverrideMessage){let a=null!=(i=Error().stack)?i:"<failed to generate stacktrace>";j.warn(`Current logger will be overwritten from ${a}`),k.warn(`Current logger will overwrite one already registered from ${a}`)}return(0,g.registerGlobal)("diag",k,b,!0)},b.disable=()=>{(0,g.unregisterGlobal)("diag",b)},b.createComponentLogger=a=>new d.DiagComponentLogger(a),b.verbose=a("verbose"),b.debug=a("debug"),b.info=a("info"),b.warn=a("warn"),b.error=a("error")}static instance(){return this._instance||(this._instance=new h),this._instance}}b.DiagAPI=h},653:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.MetricsAPI=void 0;let d=c(660),e=c(172),f=c(930),g="metrics";class h{constructor(){}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalMeterProvider(a){return(0,e.registerGlobal)(g,a,f.DiagAPI.instance())}getMeterProvider(){return(0,e.getGlobal)(g)||d.NOOP_METER_PROVIDER}getMeter(a,b,c){return this.getMeterProvider().getMeter(a,b,c)}disable(){(0,e.unregisterGlobal)(g,f.DiagAPI.instance())}}b.MetricsAPI=h},181:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.PropagationAPI=void 0;let d=c(172),e=c(874),f=c(194),g=c(277),h=c(369),i=c(930),j="propagation",k=new e.NoopTextMapPropagator;class l{constructor(){this.createBaggage=h.createBaggage,this.getBaggage=g.getBaggage,this.getActiveBaggage=g.getActiveBaggage,this.setBaggage=g.setBaggage,this.deleteBaggage=g.deleteBaggage}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalPropagator(a){return(0,d.registerGlobal)(j,a,i.DiagAPI.instance())}inject(a,b,c=f.defaultTextMapSetter){return this._getGlobalPropagator().inject(a,b,c)}extract(a,b,c=f.defaultTextMapGetter){return this._getGlobalPropagator().extract(a,b,c)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,d.unregisterGlobal)(j,i.DiagAPI.instance())}_getGlobalPropagator(){return(0,d.getGlobal)(j)||k}}b.PropagationAPI=l},997:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceAPI=void 0;let d=c(172),e=c(846),f=c(139),g=c(607),h=c(930),i="trace";class j{constructor(){this._proxyTracerProvider=new e.ProxyTracerProvider,this.wrapSpanContext=f.wrapSpanContext,this.isSpanContextValid=f.isSpanContextValid,this.deleteSpan=g.deleteSpan,this.getSpan=g.getSpan,this.getActiveSpan=g.getActiveSpan,this.getSpanContext=g.getSpanContext,this.setSpan=g.setSpan,this.setSpanContext=g.setSpanContext}static getInstance(){return this._instance||(this._instance=new j),this._instance}setGlobalTracerProvider(a){let b=(0,d.registerGlobal)(i,this._proxyTracerProvider,h.DiagAPI.instance());return b&&this._proxyTracerProvider.setDelegate(a),b}getTracerProvider(){return(0,d.getGlobal)(i)||this._proxyTracerProvider}getTracer(a,b){return this.getTracerProvider().getTracer(a,b)}disable(){(0,d.unregisterGlobal)(i,h.DiagAPI.instance()),this._proxyTracerProvider=new e.ProxyTracerProvider}}b.TraceAPI=j},277:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.deleteBaggage=b.setBaggage=b.getActiveBaggage=b.getBaggage=void 0;let d=c(491),e=(0,c(780).createContextKey)("OpenTelemetry Baggage Key");function f(a){return a.getValue(e)||void 0}b.getBaggage=f,b.getActiveBaggage=function(){return f(d.ContextAPI.getInstance().active())},b.setBaggage=function(a,b){return a.setValue(e,b)},b.deleteBaggage=function(a){return a.deleteValue(e)}},993:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.BaggageImpl=void 0;class c{constructor(a){this._entries=a?new Map(a):new Map}getEntry(a){let b=this._entries.get(a);if(b)return Object.assign({},b)}getAllEntries(){return Array.from(this._entries.entries()).map(([a,b])=>[a,b])}setEntry(a,b){let d=new c(this._entries);return d._entries.set(a,b),d}removeEntry(a){let b=new c(this._entries);return b._entries.delete(a),b}removeEntries(...a){let b=new c(this._entries);for(let c of a)b._entries.delete(c);return b}clear(){return new c}}b.BaggageImpl=c},830:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.baggageEntryMetadataSymbol=void 0,b.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.baggageEntryMetadataFromString=b.createBaggage=void 0;let d=c(930),e=c(993),f=c(830),g=d.DiagAPI.instance();b.createBaggage=function(a={}){return new e.BaggageImpl(new Map(Object.entries(a)))},b.baggageEntryMetadataFromString=function(a){return"string"!=typeof a&&(g.error(`Cannot create baggage metadata from unknown type: ${typeof a}`),a=""),{__TYPE__:f.baggageEntryMetadataSymbol,toString:()=>a}}},67:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.context=void 0,b.context=c(491).ContextAPI.getInstance()},223:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopContextManager=void 0;let d=c(780);class e{active(){return d.ROOT_CONTEXT}with(a,b,c,...d){return b.call(c,...d)}bind(a,b){return b}enable(){return this}disable(){return this}}b.NoopContextManager=e},780:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ROOT_CONTEXT=b.createContextKey=void 0,b.createContextKey=function(a){return Symbol.for(a)};class c{constructor(a){let b=this;b._currentContext=a?new Map(a):new Map,b.getValue=a=>b._currentContext.get(a),b.setValue=(a,d)=>{let e=new c(b._currentContext);return e._currentContext.set(a,d),e},b.deleteValue=a=>{let d=new c(b._currentContext);return d._currentContext.delete(a),d}}}b.ROOT_CONTEXT=new c},506:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.diag=void 0,b.diag=c(930).DiagAPI.instance()},56:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagComponentLogger=void 0;let d=c(172);class e{constructor(a){this._namespace=a.namespace||"DiagComponentLogger"}debug(...a){return f("debug",this._namespace,a)}error(...a){return f("error",this._namespace,a)}info(...a){return f("info",this._namespace,a)}warn(...a){return f("warn",this._namespace,a)}verbose(...a){return f("verbose",this._namespace,a)}}function f(a,b,c){let e=(0,d.getGlobal)("diag");if(e)return c.unshift(b),e[a](...c)}b.DiagComponentLogger=e},972:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagConsoleLogger=void 0;let c=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class d{constructor(){for(let a=0;a<c.length;a++)this[c[a].n]=function(a){return function(...b){if(console){let c=console[a];if("function"!=typeof c&&(c=console.log),"function"==typeof c)return c.apply(console,b)}}}(c[a].c)}}b.DiagConsoleLogger=d},912:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createLogLevelDiagLogger=void 0;let d=c(957);b.createLogLevelDiagLogger=function(a,b){function c(c,d){let e=b[c];return"function"==typeof e&&a>=d?e.bind(b):function(){}}return a<d.DiagLogLevel.NONE?a=d.DiagLogLevel.NONE:a>d.DiagLogLevel.ALL&&(a=d.DiagLogLevel.ALL),b=b||{},{error:c("error",d.DiagLogLevel.ERROR),warn:c("warn",d.DiagLogLevel.WARN),info:c("info",d.DiagLogLevel.INFO),debug:c("debug",d.DiagLogLevel.DEBUG),verbose:c("verbose",d.DiagLogLevel.VERBOSE)}}},957:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagLogLevel=void 0,function(a){a[a.NONE=0]="NONE",a[a.ERROR=30]="ERROR",a[a.WARN=50]="WARN",a[a.INFO=60]="INFO",a[a.DEBUG=70]="DEBUG",a[a.VERBOSE=80]="VERBOSE",a[a.ALL=9999]="ALL"}(b.DiagLogLevel||(b.DiagLogLevel={}))},172:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.unregisterGlobal=b.getGlobal=b.registerGlobal=void 0;let d=c(200),e=c(521),f=c(130),g=e.VERSION.split(".")[0],h=Symbol.for(`opentelemetry.js.api.${g}`),i=d._globalThis;b.registerGlobal=function(a,b,c,d=!1){var f;let g=i[h]=null!=(f=i[h])?f:{version:e.VERSION};if(!d&&g[a]){let b=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${a}`);return c.error(b.stack||b.message),!1}if(g.version!==e.VERSION){let b=Error(`@opentelemetry/api: Registration of version v${g.version} for ${a} does not match previously registered API v${e.VERSION}`);return c.error(b.stack||b.message),!1}return g[a]=b,c.debug(`@opentelemetry/api: Registered a global for ${a} v${e.VERSION}.`),!0},b.getGlobal=function(a){var b,c;let d=null==(b=i[h])?void 0:b.version;if(d&&(0,f.isCompatible)(d))return null==(c=i[h])?void 0:c[a]},b.unregisterGlobal=function(a,b){b.debug(`@opentelemetry/api: Unregistering a global for ${a} v${e.VERSION}.`);let c=i[h];c&&delete c[a]}},130:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.isCompatible=b._makeCompatibilityCheck=void 0;let d=c(521),e=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function f(a){let b=new Set([a]),c=new Set,d=a.match(e);if(!d)return()=>!1;let f={major:+d[1],minor:+d[2],patch:+d[3],prerelease:d[4]};if(null!=f.prerelease)return function(b){return b===a};function g(a){return c.add(a),!1}return function(a){if(b.has(a))return!0;if(c.has(a))return!1;let d=a.match(e);if(!d)return g(a);let h={major:+d[1],minor:+d[2],patch:+d[3],prerelease:d[4]};if(null!=h.prerelease||f.major!==h.major)return g(a);if(0===f.major)return f.minor===h.minor&&f.patch<=h.patch?(b.add(a),!0):g(a);return f.minor<=h.minor?(b.add(a),!0):g(a)}}b._makeCompatibilityCheck=f,b.isCompatible=f(d.VERSION)},886:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.metrics=void 0,b.metrics=c(653).MetricsAPI.getInstance()},901:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ValueType=void 0,function(a){a[a.INT=0]="INT",a[a.DOUBLE=1]="DOUBLE"}(b.ValueType||(b.ValueType={}))},102:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createNoopMeter=b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=b.NOOP_OBSERVABLE_GAUGE_METRIC=b.NOOP_OBSERVABLE_COUNTER_METRIC=b.NOOP_UP_DOWN_COUNTER_METRIC=b.NOOP_HISTOGRAM_METRIC=b.NOOP_COUNTER_METRIC=b.NOOP_METER=b.NoopObservableUpDownCounterMetric=b.NoopObservableGaugeMetric=b.NoopObservableCounterMetric=b.NoopObservableMetric=b.NoopHistogramMetric=b.NoopUpDownCounterMetric=b.NoopCounterMetric=b.NoopMetric=b.NoopMeter=void 0;class c{constructor(){}createHistogram(a,c){return b.NOOP_HISTOGRAM_METRIC}createCounter(a,c){return b.NOOP_COUNTER_METRIC}createUpDownCounter(a,c){return b.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(a,c){return b.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(a,c){return b.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(a,c){return b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(a,b){}removeBatchObservableCallback(a){}}b.NoopMeter=c;class d{}b.NoopMetric=d;class e extends d{add(a,b){}}b.NoopCounterMetric=e;class f extends d{add(a,b){}}b.NoopUpDownCounterMetric=f;class g extends d{record(a,b){}}b.NoopHistogramMetric=g;class h{addCallback(a){}removeCallback(a){}}b.NoopObservableMetric=h;class i extends h{}b.NoopObservableCounterMetric=i;class j extends h{}b.NoopObservableGaugeMetric=j;class k extends h{}b.NoopObservableUpDownCounterMetric=k,b.NOOP_METER=new c,b.NOOP_COUNTER_METRIC=new e,b.NOOP_HISTOGRAM_METRIC=new g,b.NOOP_UP_DOWN_COUNTER_METRIC=new f,b.NOOP_OBSERVABLE_COUNTER_METRIC=new i,b.NOOP_OBSERVABLE_GAUGE_METRIC=new j,b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new k,b.createNoopMeter=function(){return b.NOOP_METER}},660:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NOOP_METER_PROVIDER=b.NoopMeterProvider=void 0;let d=c(102);class e{getMeter(a,b,c){return d.NOOP_METER}}b.NoopMeterProvider=e,b.NOOP_METER_PROVIDER=new e},200:function(a,b,c){var d=this&&this.__createBinding||(Object.create?function(a,b,c,d){void 0===d&&(d=c),Object.defineProperty(a,d,{enumerable:!0,get:function(){return b[c]}})}:function(a,b,c,d){void 0===d&&(d=c),a[d]=b[c]}),e=this&&this.__exportStar||function(a,b){for(var c in a)"default"===c||Object.prototype.hasOwnProperty.call(b,c)||d(b,a,c)};Object.defineProperty(b,"__esModule",{value:!0}),e(c(46),b)},651:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b._globalThis=void 0,b._globalThis="object"==typeof globalThis?globalThis:c.g},46:function(a,b,c){var d=this&&this.__createBinding||(Object.create?function(a,b,c,d){void 0===d&&(d=c),Object.defineProperty(a,d,{enumerable:!0,get:function(){return b[c]}})}:function(a,b,c,d){void 0===d&&(d=c),a[d]=b[c]}),e=this&&this.__exportStar||function(a,b){for(var c in a)"default"===c||Object.prototype.hasOwnProperty.call(b,c)||d(b,a,c)};Object.defineProperty(b,"__esModule",{value:!0}),e(c(651),b)},939:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.propagation=void 0,b.propagation=c(181).PropagationAPI.getInstance()},874:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTextMapPropagator=void 0;class c{inject(a,b){}extract(a,b){return a}fields(){return[]}}b.NoopTextMapPropagator=c},194:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.defaultTextMapSetter=b.defaultTextMapGetter=void 0,b.defaultTextMapGetter={get(a,b){if(null!=a)return a[b]},keys:a=>null==a?[]:Object.keys(a)},b.defaultTextMapSetter={set(a,b,c){null!=a&&(a[b]=c)}}},845:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.trace=void 0,b.trace=c(997).TraceAPI.getInstance()},403:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NonRecordingSpan=void 0;let d=c(476);class e{constructor(a=d.INVALID_SPAN_CONTEXT){this._spanContext=a}spanContext(){return this._spanContext}setAttribute(a,b){return this}setAttributes(a){return this}addEvent(a,b){return this}setStatus(a){return this}updateName(a){return this}end(a){}isRecording(){return!1}recordException(a,b){}}b.NonRecordingSpan=e},614:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTracer=void 0;let d=c(491),e=c(607),f=c(403),g=c(139),h=d.ContextAPI.getInstance();class i{startSpan(a,b,c=h.active()){var d;if(null==b?void 0:b.root)return new f.NonRecordingSpan;let i=c&&(0,e.getSpanContext)(c);return"object"==typeof(d=i)&&"string"==typeof d.spanId&&"string"==typeof d.traceId&&"number"==typeof d.traceFlags&&(0,g.isSpanContextValid)(i)?new f.NonRecordingSpan(i):new f.NonRecordingSpan}startActiveSpan(a,b,c,d){let f,g,i;if(arguments.length<2)return;2==arguments.length?i=b:3==arguments.length?(f=b,i=c):(f=b,g=c,i=d);let j=null!=g?g:h.active(),k=this.startSpan(a,f,j),l=(0,e.setSpan)(j,k);return h.with(l,i,void 0,k)}}b.NoopTracer=i},124:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTracerProvider=void 0;let d=c(614);class e{getTracer(a,b,c){return new d.NoopTracer}}b.NoopTracerProvider=e},125:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ProxyTracer=void 0;let d=new(c(614)).NoopTracer;class e{constructor(a,b,c,d){this._provider=a,this.name=b,this.version=c,this.options=d}startSpan(a,b,c){return this._getTracer().startSpan(a,b,c)}startActiveSpan(a,b,c,d){let e=this._getTracer();return Reflect.apply(e.startActiveSpan,e,arguments)}_getTracer(){if(this._delegate)return this._delegate;let a=this._provider.getDelegateTracer(this.name,this.version,this.options);return a?(this._delegate=a,this._delegate):d}}b.ProxyTracer=e},846:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ProxyTracerProvider=void 0;let d=c(125),e=new(c(124)).NoopTracerProvider;class f{getTracer(a,b,c){var e;return null!=(e=this.getDelegateTracer(a,b,c))?e:new d.ProxyTracer(this,a,b,c)}getDelegate(){var a;return null!=(a=this._delegate)?a:e}setDelegate(a){this._delegate=a}getDelegateTracer(a,b,c){var d;return null==(d=this._delegate)?void 0:d.getTracer(a,b,c)}}b.ProxyTracerProvider=f},996:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SamplingDecision=void 0,function(a){a[a.NOT_RECORD=0]="NOT_RECORD",a[a.RECORD=1]="RECORD",a[a.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(b.SamplingDecision||(b.SamplingDecision={}))},607:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.getSpanContext=b.setSpanContext=b.deleteSpan=b.setSpan=b.getActiveSpan=b.getSpan=void 0;let d=c(780),e=c(403),f=c(491),g=(0,d.createContextKey)("OpenTelemetry Context Key SPAN");function h(a){return a.getValue(g)||void 0}function i(a,b){return a.setValue(g,b)}b.getSpan=h,b.getActiveSpan=function(){return h(f.ContextAPI.getInstance().active())},b.setSpan=i,b.deleteSpan=function(a){return a.deleteValue(g)},b.setSpanContext=function(a,b){return i(a,new e.NonRecordingSpan(b))},b.getSpanContext=function(a){var b;return null==(b=h(a))?void 0:b.spanContext()}},325:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceStateImpl=void 0;let d=c(564);class e{constructor(a){this._internalState=new Map,a&&this._parse(a)}set(a,b){let c=this._clone();return c._internalState.has(a)&&c._internalState.delete(a),c._internalState.set(a,b),c}unset(a){let b=this._clone();return b._internalState.delete(a),b}get(a){return this._internalState.get(a)}serialize(){return this._keys().reduce((a,b)=>(a.push(b+"="+this.get(b)),a),[]).join(",")}_parse(a){!(a.length>512)&&(this._internalState=a.split(",").reverse().reduce((a,b)=>{let c=b.trim(),e=c.indexOf("=");if(-1!==e){let f=c.slice(0,e),g=c.slice(e+1,b.length);(0,d.validateKey)(f)&&(0,d.validateValue)(g)&&a.set(f,g)}return a},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let a=new e;return a._internalState=new Map(this._internalState),a}}b.TraceStateImpl=e},564:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.validateValue=b.validateKey=void 0;let c="[_0-9a-z-*/]",d=`[a-z]${c}{0,255}`,e=`[a-z0-9]${c}{0,240}@[a-z]${c}{0,13}`,f=RegExp(`^(?:${d}|${e})$`),g=/^[ -~]{0,255}[!-~]$/,h=/,|=/;b.validateKey=function(a){return f.test(a)},b.validateValue=function(a){return g.test(a)&&!h.test(a)}},98:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createTraceState=void 0;let d=c(325);b.createTraceState=function(a){return new d.TraceStateImpl(a)}},476:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.INVALID_SPAN_CONTEXT=b.INVALID_TRACEID=b.INVALID_SPANID=void 0;let d=c(475);b.INVALID_SPANID="0000000000000000",b.INVALID_TRACEID="00000000000000000000000000000000",b.INVALID_SPAN_CONTEXT={traceId:b.INVALID_TRACEID,spanId:b.INVALID_SPANID,traceFlags:d.TraceFlags.NONE}},357:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SpanKind=void 0,function(a){a[a.INTERNAL=0]="INTERNAL",a[a.SERVER=1]="SERVER",a[a.CLIENT=2]="CLIENT",a[a.PRODUCER=3]="PRODUCER",a[a.CONSUMER=4]="CONSUMER"}(b.SpanKind||(b.SpanKind={}))},139:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.wrapSpanContext=b.isSpanContextValid=b.isValidSpanId=b.isValidTraceId=void 0;let d=c(476),e=c(403),f=/^([0-9a-f]{32})$/i,g=/^[0-9a-f]{16}$/i;function h(a){return f.test(a)&&a!==d.INVALID_TRACEID}function i(a){return g.test(a)&&a!==d.INVALID_SPANID}b.isValidTraceId=h,b.isValidSpanId=i,b.isSpanContextValid=function(a){return h(a.traceId)&&i(a.spanId)},b.wrapSpanContext=function(a){return new e.NonRecordingSpan(a)}},847:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SpanStatusCode=void 0,function(a){a[a.UNSET=0]="UNSET",a[a.OK=1]="OK",a[a.ERROR=2]="ERROR"}(b.SpanStatusCode||(b.SpanStatusCode={}))},475:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceFlags=void 0,function(a){a[a.NONE=0]="NONE",a[a.SAMPLED=1]="SAMPLED"}(b.TraceFlags||(b.TraceFlags={}))},521:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.VERSION=void 0,b.VERSION="1.6.0"}},d={};function e(a){var c=d[a];if(void 0!==c)return c.exports;var f=d[a]={exports:{}},g=!0;try{b[a].call(f.exports,f,f.exports,e),g=!1}finally{g&&delete d[a]}return f.exports}e.ab="//";var f={};(()=>{Object.defineProperty(f,"__esModule",{value:!0}),f.trace=f.propagation=f.metrics=f.diag=f.context=f.INVALID_SPAN_CONTEXT=f.INVALID_TRACEID=f.INVALID_SPANID=f.isValidSpanId=f.isValidTraceId=f.isSpanContextValid=f.createTraceState=f.TraceFlags=f.SpanStatusCode=f.SpanKind=f.SamplingDecision=f.ProxyTracerProvider=f.ProxyTracer=f.defaultTextMapSetter=f.defaultTextMapGetter=f.ValueType=f.createNoopMeter=f.DiagLogLevel=f.DiagConsoleLogger=f.ROOT_CONTEXT=f.createContextKey=f.baggageEntryMetadataFromString=void 0;var a=e(369);Object.defineProperty(f,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return a.baggageEntryMetadataFromString}});var b=e(780);Object.defineProperty(f,"createContextKey",{enumerable:!0,get:function(){return b.createContextKey}}),Object.defineProperty(f,"ROOT_CONTEXT",{enumerable:!0,get:function(){return b.ROOT_CONTEXT}});var c=e(972);Object.defineProperty(f,"DiagConsoleLogger",{enumerable:!0,get:function(){return c.DiagConsoleLogger}});var d=e(957);Object.defineProperty(f,"DiagLogLevel",{enumerable:!0,get:function(){return d.DiagLogLevel}});var g=e(102);Object.defineProperty(f,"createNoopMeter",{enumerable:!0,get:function(){return g.createNoopMeter}});var h=e(901);Object.defineProperty(f,"ValueType",{enumerable:!0,get:function(){return h.ValueType}});var i=e(194);Object.defineProperty(f,"defaultTextMapGetter",{enumerable:!0,get:function(){return i.defaultTextMapGetter}}),Object.defineProperty(f,"defaultTextMapSetter",{enumerable:!0,get:function(){return i.defaultTextMapSetter}});var j=e(125);Object.defineProperty(f,"ProxyTracer",{enumerable:!0,get:function(){return j.ProxyTracer}});var k=e(846);Object.defineProperty(f,"ProxyTracerProvider",{enumerable:!0,get:function(){return k.ProxyTracerProvider}});var l=e(996);Object.defineProperty(f,"SamplingDecision",{enumerable:!0,get:function(){return l.SamplingDecision}});var m=e(357);Object.defineProperty(f,"SpanKind",{enumerable:!0,get:function(){return m.SpanKind}});var n=e(847);Object.defineProperty(f,"SpanStatusCode",{enumerable:!0,get:function(){return n.SpanStatusCode}});var o=e(475);Object.defineProperty(f,"TraceFlags",{enumerable:!0,get:function(){return o.TraceFlags}});var p=e(98);Object.defineProperty(f,"createTraceState",{enumerable:!0,get:function(){return p.createTraceState}});var q=e(139);Object.defineProperty(f,"isSpanContextValid",{enumerable:!0,get:function(){return q.isSpanContextValid}}),Object.defineProperty(f,"isValidTraceId",{enumerable:!0,get:function(){return q.isValidTraceId}}),Object.defineProperty(f,"isValidSpanId",{enumerable:!0,get:function(){return q.isValidSpanId}});var r=e(476);Object.defineProperty(f,"INVALID_SPANID",{enumerable:!0,get:function(){return r.INVALID_SPANID}}),Object.defineProperty(f,"INVALID_TRACEID",{enumerable:!0,get:function(){return r.INVALID_TRACEID}}),Object.defineProperty(f,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return r.INVALID_SPAN_CONTEXT}});let s=e(67);Object.defineProperty(f,"context",{enumerable:!0,get:function(){return s.context}});let t=e(506);Object.defineProperty(f,"diag",{enumerable:!0,get:function(){return t.diag}});let u=e(886);Object.defineProperty(f,"metrics",{enumerable:!0,get:function(){return u.metrics}});let v=e(939);Object.defineProperty(f,"propagation",{enumerable:!0,get:function(){return v.propagation}});let w=e(845);Object.defineProperty(f,"trace",{enumerable:!0,get:function(){return w.trace}}),f.default={context:s.context,diag:t.diag,metrics:u.metrics,propagation:v.propagation,trace:w.trace}})(),a.exports=f})()}},a=>{var b=a(a.s=305);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=b}]);
//# sourceMappingURL=middleware.js.map