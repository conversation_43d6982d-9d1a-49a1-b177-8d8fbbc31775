{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/scanner/qr-scanner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useRef, useState, useCallback } from \"react\"\nimport { BrowserMultiFormatReader, NotFoundException } from \"@zxing/library\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { Camera, CameraOff, RotateCcw, Settings } from \"lucide-react\"\nimport { CameraDevice, ScanResult } from \"@/lib/types/scanner\"\n\ninterface QRScannerProps {\n  onScanResult: (result: ScanResult) => void\n  isActive: boolean\n  onCameraError?: (error: string) => void\n}\n\nexport function QRScanner({ onScanResult, isActive, onCameraError }: QRScannerProps) {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const canvasRef = useRef<HTMLCanvasElement>(null)\n  const readerRef = useRef<BrowserMultiFormatReader | null>(null)\n  const [isScanning, setIsScanning] = useState(false)\n  const [availableCameras, setAvailableCameras] = useState<CameraDevice[]>([])\n  const [selectedCamera, setSelectedCamera] = useState<string>(\"\")\n  const [error, setError] = useState<string>(\"\")\n  const [hasPermission, setHasPermission] = useState<boolean | null>(null)\n\n  // Initialize camera reader\n  useEffect(() => {\n    readerRef.current = new BrowserMultiFormatReader()\n    \n    return () => {\n      if (readerRef.current) {\n        readerRef.current.reset()\n      }\n    }\n  }, [])\n\n  // Get available cameras\n  const getAvailableCameras = useCallback(async () => {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices()\n      const videoDevices = devices\n        .filter(device => device.kind === 'videoinput')\n        .map(device => ({\n          deviceId: device.deviceId,\n          label: device.label || `Camera ${device.deviceId.slice(0, 8)}`,\n          kind: 'videoinput' as const\n        }))\n      \n      setAvailableCameras(videoDevices)\n      \n      // Select rear camera by default if available\n      const rearCamera = videoDevices.find(camera => \n        camera.label.toLowerCase().includes('back') || \n        camera.label.toLowerCase().includes('rear')\n      )\n      setSelectedCamera(rearCamera?.deviceId || videoDevices[0]?.deviceId || \"\")\n    } catch (err) {\n      console.error(\"Error getting cameras:\", err)\n      setError(\"Failed to access cameras\")\n      onCameraError?.(\"Failed to access cameras\")\n    }\n  }, [onCameraError])\n\n  // Request camera permission\n  const requestCameraPermission = useCallback(async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ video: true })\n      stream.getTracks().forEach(track => track.stop()) // Stop the test stream\n      setHasPermission(true)\n      await getAvailableCameras()\n    } catch (err) {\n      console.error(\"Camera permission denied:\", err)\n      setHasPermission(false)\n      setError(\"Camera permission denied\")\n      onCameraError?.(\"Camera permission denied\")\n    }\n  }, [getAvailableCameras, onCameraError])\n\n  // Start scanning\n  const startScanning = useCallback(async () => {\n    if (!readerRef.current || !selectedCamera || !videoRef.current) return\n\n    try {\n      setIsScanning(true)\n      setError(\"\")\n\n      await readerRef.current.decodeFromVideoDevice(\n        selectedCamera,\n        videoRef.current,\n        (result, error) => {\n          if (result) {\n            const scanResult: ScanResult = {\n              success: true,\n              data: result.getText(),\n              timestamp: new Date()\n            }\n            onScanResult(scanResult)\n            \n            // Brief pause between scans to prevent rapid duplicate scans\n            setTimeout(() => {\n              if (isActive && isScanning) {\n                // Continue scanning\n              }\n            }, 1000)\n          }\n          \n          if (error && !(error instanceof NotFoundException)) {\n            console.error(\"Scan error:\", error)\n          }\n        }\n      )\n    } catch (err) {\n      console.error(\"Failed to start scanning:\", err)\n      setError(\"Failed to start camera\")\n      setIsScanning(false)\n      onCameraError?.(\"Failed to start camera\")\n    }\n  }, [selectedCamera, onScanResult, isActive, isScanning, onCameraError])\n\n  // Stop scanning\n  const stopScanning = useCallback(() => {\n    if (readerRef.current) {\n      readerRef.current.reset()\n    }\n    setIsScanning(false)\n  }, [])\n\n  // Handle camera switch\n  const switchCamera = useCallback(() => {\n    const currentIndex = availableCameras.findIndex(cam => cam.deviceId === selectedCamera)\n    const nextIndex = (currentIndex + 1) % availableCameras.length\n    setSelectedCamera(availableCameras[nextIndex]?.deviceId || \"\")\n  }, [availableCameras, selectedCamera])\n\n  // Effect to start/stop scanning based on isActive prop\n  useEffect(() => {\n    if (isActive && hasPermission && selectedCamera && !isScanning) {\n      startScanning()\n    } else if (!isActive && isScanning) {\n      stopScanning()\n    }\n  }, [isActive, hasPermission, selectedCamera, isScanning, startScanning, stopScanning])\n\n  // Effect to restart scanning when camera changes\n  useEffect(() => {\n    if (isScanning && selectedCamera) {\n      stopScanning()\n      setTimeout(() => {\n        if (isActive) {\n          startScanning()\n        }\n      }, 100)\n    }\n  }, [selectedCamera, isScanning, isActive, startScanning, stopScanning])\n\n  // Initial permission request\n  useEffect(() => {\n    if (hasPermission === null) {\n      requestCameraPermission()\n    }\n  }, [hasPermission, requestCameraPermission])\n\n  if (hasPermission === false) {\n    return (\n      <Card className=\"w-full max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <CameraOff className=\"h-16 w-16 mx-auto mb-4 text-muted-foreground\" />\n          <h3 className=\"text-lg font-semibold mb-2\">Camera Permission Required</h3>\n          <p className=\"text-muted-foreground mb-4\">\n            Please allow camera access to use the QR scanner\n          </p>\n          <Button onClick={requestCameraPermission}>\n            <Camera className=\"mr-2 h-4 w-4\" />\n            Grant Camera Access\n          </Button>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      {/* Camera Controls */}\n      <div className=\"flex justify-between items-center mb-4\">\n        <div className=\"flex gap-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={switchCamera}\n            disabled={availableCameras.length <= 1}\n          >\n            <RotateCcw className=\"h-4 w-4 mr-2\" />\n            Switch Camera\n          </Button>\n        </div>\n        \n        <div className=\"flex items-center gap-2\">\n          <div className={`w-3 h-3 rounded-full ${isScanning ? 'bg-green-500' : 'bg-red-500'}`} />\n          <span className=\"text-sm text-muted-foreground\">\n            {isScanning ? 'Scanning' : 'Stopped'}\n          </span>\n        </div>\n      </div>\n\n      {/* Scanner Interface */}\n      <Card className=\"relative overflow-hidden\">\n        <CardContent className=\"p-0\">\n          <div className=\"relative aspect-square max-w-2xl mx-auto bg-black\">\n            {/* Video Element */}\n            <video\n              ref={videoRef}\n              className=\"w-full h-full object-cover\"\n              playsInline\n              muted\n            />\n            \n            {/* Scanning Overlay */}\n            <div className=\"absolute inset-0 pointer-events-none\">\n              {/* Scanning Reticle */}\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"relative w-64 h-64 border-2 border-white/50 rounded-lg\">\n                  {/* Corner indicators */}\n                  <div className=\"absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-primary rounded-tl-lg\" />\n                  <div className=\"absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-primary rounded-tr-lg\" />\n                  <div className=\"absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-primary rounded-bl-lg\" />\n                  <div className=\"absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-primary rounded-br-lg\" />\n                  \n                  {/* Scanning line animation */}\n                  {isScanning && (\n                    <div className=\"absolute inset-x-0 top-0 h-1 bg-primary animate-pulse\" \n                         style={{\n                           animation: 'scan 2s linear infinite',\n                         }} />\n                  )}\n                </div>\n              </div>\n              \n              {/* Instructions */}\n              <div className=\"absolute bottom-4 left-0 right-0 text-center\">\n                <p className=\"text-white text-lg font-medium bg-black/50 px-4 py-2 rounded-lg inline-block\">\n                  Position QR code within the frame\n                </p>\n              </div>\n            </div>\n            \n            {/* Error Overlay */}\n            {error && (\n              <div className=\"absolute inset-0 bg-black/80 flex items-center justify-center\">\n                <div className=\"text-center text-white\">\n                  <CameraOff className=\"h-16 w-16 mx-auto mb-4\" />\n                  <p className=\"text-lg font-medium mb-2\">Camera Error</p>\n                  <p className=\"text-sm opacity-75 mb-4\">{error}</p>\n                  <Button variant=\"secondary\" onClick={requestCameraPermission}>\n                    <Camera className=\"mr-2 h-4 w-4\" />\n                    Retry\n                  </Button>\n                </div>\n              </div>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Hidden canvas for image processing */}\n      <canvas ref={canvasRef} className=\"hidden\" />\n      \n      <style jsx>{`\n        @keyframes scan {\n          0% { top: 0; }\n          50% { top: calc(100% - 4px); }\n          100% { top: 0; }\n        }\n      `}</style>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;AANA;;;;;;;AAeO,SAAS,UAAU,KAAyD;QAAzD,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAkB,GAAzD;;IACxB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmC;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,UAAU,OAAO,GAAG,IAAI,mLAAA,CAAA,2BAAwB;YAEhD;uCAAO;oBACL,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,OAAO,CAAC,KAAK;oBACzB;gBACF;;QACF;8BAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACtC,IAAI;oBAiBwC;gBAhB1C,MAAM,UAAU,MAAM,UAAU,YAAY,CAAC,gBAAgB;gBAC7D,MAAM,eAAe,QAClB,MAAM;+EAAC,CAAA,SAAU,OAAO,IAAI,KAAK;8EACjC,GAAG;+EAAC,CAAA,SAAU,CAAC;4BACd,UAAU,OAAO,QAAQ;4BACzB,OAAO,OAAO,KAAK,IAAI,AAAC,UAAqC,OAA5B,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG;4BAC1D,MAAM;wBACR,CAAC;;gBAEH,oBAAoB;gBAEpB,6CAA6C;gBAC7C,MAAM,aAAa,aAAa,IAAI;6EAAC,CAAA,SACnC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WACpC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;;gBAEtC,kBAAkB,CAAA,uBAAA,iCAAA,WAAY,QAAQ,OAAI,iBAAA,YAAY,CAAC,EAAE,cAAf,qCAAA,eAAiB,QAAQ,KAAI;YACzE,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SAAS;gBACT,0BAAA,oCAAA,cAAgB;YAClB;QACF;qDAAG;QAAC;KAAc;IAElB,4BAA4B;IAC5B,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YAC1C,IAAI;gBACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;oBAAE,OAAO;gBAAK;gBACvE,OAAO,SAAS,GAAG,OAAO;sEAAC,CAAA,QAAS,MAAM,IAAI;sEAAI,uBAAuB;gBACzE,iBAAiB;gBACjB,MAAM;YACR,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,iBAAiB;gBACjB,SAAS;gBACT,0BAAA,oCAAA,cAAgB;YAClB;QACF;yDAAG;QAAC;QAAqB;KAAc;IAEvC,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAChC,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,OAAO,EAAE;YAEhE,IAAI;gBACF,cAAc;gBACd,SAAS;gBAET,MAAM,UAAU,OAAO,CAAC,qBAAqB,CAC3C,gBACA,SAAS,OAAO;4DAChB,CAAC,QAAQ;wBACP,IAAI,QAAQ;4BACV,MAAM,aAAyB;gCAC7B,SAAS;gCACT,MAAM,OAAO,OAAO;gCACpB,WAAW,IAAI;4BACjB;4BACA,aAAa;4BAEb,6DAA6D;4BAC7D;wEAAW;oCACT,IAAI,YAAY,YAAY;oCAC1B,oBAAoB;oCACtB;gCACF;uEAAG;wBACL;wBAEA,IAAI,SAAS,CAAC,CAAC,iBAAiB,yNAAA,CAAA,oBAAiB,GAAG;4BAClD,QAAQ,KAAK,CAAC,eAAe;wBAC/B;oBACF;;YAEJ,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,SAAS;gBACT,cAAc;gBACd,0BAAA,oCAAA,cAAgB;YAClB;QACF;+CAAG;QAAC;QAAgB;QAAc;QAAU;QAAY;KAAc;IAEtE,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC/B,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,KAAK;YACzB;YACA,cAAc;QAChB;8CAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;gBAGb;YAFlB,MAAM,eAAe,iBAAiB,SAAS;oEAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;;YACxE,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,iBAAiB,MAAM;YAC9D,kBAAkB,EAAA,8BAAA,gBAAgB,CAAC,UAAU,cAA3B,kDAAA,4BAA6B,QAAQ,KAAI;QAC7D;8CAAG;QAAC;QAAkB;KAAe;IAErC,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAY,iBAAiB,kBAAkB,CAAC,YAAY;gBAC9D;YACF,OAAO,IAAI,CAAC,YAAY,YAAY;gBAClC;YACF;QACF;8BAAG;QAAC;QAAU;QAAe;QAAgB;QAAY;QAAe;KAAa;IAErF,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,cAAc,gBAAgB;gBAChC;gBACA;2CAAW;wBACT,IAAI,UAAU;4BACZ;wBACF;oBACF;0CAAG;YACL;QACF;8BAAG;QAAC;QAAgB;QAAY;QAAU;QAAe;KAAa;IAEtE,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,kBAAkB,MAAM;gBAC1B;YACF;QACF;8BAAG;QAAC;QAAe;KAAwB;IAE3C,IAAI,kBAAkB,OAAO;QAC3B,qBACE,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAS;;0CACf,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,6LAAC;kDAAc;;0BAEb,6LAAC;0DAAc;;kCACb,6LAAC;kEAAc;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,iBAAiB,MAAM,IAAI;;8CAErC,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAK1C,6LAAC;kEAAc;;0CACb,6LAAC;0EAAe,AAAC,wBAAkE,OAA3C,aAAa,iBAAiB;;;;;;0CACtE,6LAAC;0EAAe;0CACb,aAAa,aAAa;;;;;;;;;;;;;;;;;;0BAMjC,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;kEAAc;;0CAEb,6LAAC;gCACC,KAAK;gCAEL,WAAW;gCACX,KAAK;0EAFK;;;;;;0CAMZ,6LAAC;0EAAc;;kDAEb,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAc;;8DAEb,6LAAC;8FAAc;;;;;;8DACf,6LAAC;8FAAc;;;;;;8DACf,6LAAC;8FAAc;;;;;;8DACf,6LAAC;8FAAc;;;;;;gDAGd,4BACC,6LAAC;oDACI,OAAO;wDACL,WAAW;oDACb;8FAHU;;;;;;;;;;;;;;;;;kDASrB,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAY;sDAA+E;;;;;;;;;;;;;;;;;4BAO/F,uBACC,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sFAAY;sDAA2B;;;;;;sDACxC,6LAAC;sFAAY;sDAA2B;;;;;;sDACxC,6LAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAY,SAAS;;8DACnC,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjD,6LAAC;gBAAO,KAAK;0DAAqB;;;;;;;;;;;;;;;;AAWxC;GApQgB;KAAA", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,6JAAA,CAAA,aAAgB,OAGrC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/scanner/student-info-card.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Student, AttendanceRecord } from \"@/lib/types/scanner\"\nimport { User, GraduationCap, Clock, CheckCircle, XCircle, AlertCircle } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface StudentInfoCardProps {\n  student: Student\n  attendanceRecord?: AttendanceRecord\n  className?: string\n  showAttendanceHistory?: boolean\n}\n\nexport function StudentInfoCard({ \n  student, \n  attendanceRecord, \n  className,\n  showAttendanceHistory = false \n}: StudentInfoCardProps) {\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'Present':\n        return <CheckCircle className=\"h-4 w-4\" />\n      case 'Late':\n        return <AlertCircle className=\"h-4 w-4\" />\n      case 'Absent':\n        return <XCircle className=\"h-4 w-4\" />\n      default:\n        return <Clock className=\"h-4 w-4\" />\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'Present':\n        return 'bg-green-500'\n      case 'Late':\n        return 'bg-yellow-500'\n      case 'Absent':\n        return 'bg-red-500'\n      default:\n        return 'bg-gray-500'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  return (\n    <Card className={cn(\"w-full max-w-md mx-auto\", className)}>\n      <CardHeader className=\"pb-4\">\n        <CardTitle className=\"flex items-center gap-2 text-lg\">\n          <User className=\"h-5 w-5\" />\n          Student Information\n        </CardTitle>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-6\">\n        {/* Student Photo and Basic Info */}\n        <div className=\"flex items-center gap-4\">\n          <Avatar className=\"h-20 w-20 border-2 border-border\">\n            <AvatarImage \n              src={student.photo} \n              alt={student.name}\n              className=\"object-cover\"\n            />\n            <AvatarFallback className=\"text-lg font-semibold bg-primary/10\">\n              {getInitials(student.name)}\n            </AvatarFallback>\n          </Avatar>\n          \n          <div className=\"flex-1 space-y-1\">\n            <h3 className=\"text-xl font-bold leading-tight\">{student.name}</h3>\n            <p className=\"text-sm text-muted-foreground\">{student.email}</p>\n            <Badge \n              variant={student.status === 'Active' ? 'default' : 'secondary'}\n              className=\"text-xs\"\n            >\n              {student.status}\n            </Badge>\n          </div>\n        </div>\n\n        {/* Academic Information */}\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div className=\"space-y-1\">\n            <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n              Student ID\n            </p>\n            <p className=\"text-sm font-mono font-semibold\">{student.id}</p>\n          </div>\n          \n          <div className=\"space-y-1\">\n            <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n              Year Level\n            </p>\n            <p className=\"text-sm font-semibold\">{student.year}</p>\n          </div>\n          \n          <div className=\"space-y-1 col-span-2\">\n            <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n              Course\n            </p>\n            <div className=\"flex items-center gap-2\">\n              <GraduationCap className=\"h-4 w-4 text-muted-foreground\" />\n              <p className=\"text-sm font-semibold\">{student.course}</p>\n            </div>\n          </div>\n\n          {student.section && (\n            <div className=\"space-y-1\">\n              <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n                Section\n              </p>\n              <p className=\"text-sm font-semibold\">{student.section}</p>\n            </div>\n          )}\n\n          {student.grade && (\n            <div className=\"space-y-1\">\n              <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n                Grade\n              </p>\n              <p className=\"text-sm font-semibold\">{student.grade}</p>\n            </div>\n          )}\n        </div>\n\n        {/* Current Attendance Status */}\n        {attendanceRecord && (\n          <div className=\"border-t pt-4\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h4 className=\"text-sm font-semibold\">Today's Attendance</h4>\n              <div className=\"flex items-center gap-2\">\n                <div className={cn(\"w-2 h-2 rounded-full\", getStatusColor(attendanceRecord.status))} />\n                <span className=\"text-xs text-muted-foreground\">\n                  {new Date(attendanceRecord.timestamp).toLocaleTimeString()}\n                </span>\n              </div>\n            </div>\n            \n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              {attendanceRecord.checkIn && (\n                <div className=\"space-y-1\">\n                  <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n                    Check In\n                  </p>\n                  <p className=\"font-semibold\">{attendanceRecord.checkIn}</p>\n                </div>\n              )}\n              \n              {attendanceRecord.checkOut && (\n                <div className=\"space-y-1\">\n                  <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n                    Check Out\n                  </p>\n                  <p className=\"font-semibold\">{attendanceRecord.checkOut}</p>\n                </div>\n              )}\n              \n              <div className=\"space-y-1 col-span-2\">\n                <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n                  Status\n                </p>\n                <div className=\"flex items-center gap-2\">\n                  {getStatusIcon(attendanceRecord.status)}\n                  <Badge \n                    variant={\n                      attendanceRecord.status === 'Present' ? 'default' :\n                      attendanceRecord.status === 'Late' ? 'secondary' : 'destructive'\n                    }\n                  >\n                    {attendanceRecord.status}\n                  </Badge>\n                </div>\n              </div>\n\n              {attendanceRecord.subject && (\n                <div className=\"space-y-1 col-span-2\">\n                  <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n                    Subject\n                  </p>\n                  <p className=\"text-sm font-semibold\">{attendanceRecord.subject}</p>\n                </div>\n              )}\n\n              {attendanceRecord.period && (\n                <div className=\"space-y-1\">\n                  <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n                    Period\n                  </p>\n                  <p className=\"text-sm font-semibold\">{attendanceRecord.period}</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* QR Code Info */}\n        {student.qrCode && (\n          <div className=\"border-t pt-4\">\n            <div className=\"space-y-1\">\n              <p className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\n                QR Code\n              </p>\n              <p className=\"text-xs font-mono bg-muted px-2 py-1 rounded\">\n                {student.qrCode}\n              </p>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;AAgBO,SAAS,gBAAgB,KAKT;QALS,EAC9B,OAAO,EACP,gBAAgB,EAChB,SAAS,EACT,wBAAwB,KAAK,EACR,GALS;IAM9B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC7C,6LAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,4HAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAKhC,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,6LAAC,8HAAA,CAAA,cAAW;wCACV,KAAK,QAAQ,KAAK;wCAClB,KAAK,QAAQ,IAAI;wCACjB,WAAU;;;;;;kDAEZ,6LAAC,8HAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,YAAY,QAAQ,IAAI;;;;;;;;;;;;0CAI7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC,QAAQ,IAAI;;;;;;kDAC7D,6LAAC;wCAAE,WAAU;kDAAiC,QAAQ,KAAK;;;;;;kDAC3D,6LAAC,6HAAA,CAAA,QAAK;wCACJ,SAAS,QAAQ,MAAM,KAAK,WAAW,YAAY;wCACnD,WAAU;kDAET,QAAQ,MAAM;;;;;;;;;;;;;;;;;;kCAMrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoE;;;;;;kDAGjF,6LAAC;wCAAE,WAAU;kDAAmC,QAAQ,EAAE;;;;;;;;;;;;0CAG5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoE;;;;;;kDAGjF,6LAAC;wCAAE,WAAU;kDAAyB,QAAQ,IAAI;;;;;;;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoE;;;;;;kDAGjF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAE,WAAU;0DAAyB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;4BAIvD,QAAQ,OAAO,kBACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoE;;;;;;kDAGjF,6LAAC;wCAAE,WAAU;kDAAyB,QAAQ,OAAO;;;;;;;;;;;;4BAIxD,QAAQ,KAAK,kBACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoE;;;;;;kDAGjF,6LAAC;wCAAE,WAAU;kDAAyB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;oBAMxD,kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB,eAAe,iBAAiB,MAAM;;;;;;0DACjF,6LAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,iBAAiB,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;0CAK9D,6LAAC;gCAAI,WAAU;;oCACZ,iBAAiB,OAAO,kBACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoE;;;;;;0DAGjF,6LAAC;gDAAE,WAAU;0DAAiB,iBAAiB,OAAO;;;;;;;;;;;;oCAIzD,iBAAiB,QAAQ,kBACxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoE;;;;;;0DAGjF,6LAAC;gDAAE,WAAU;0DAAiB,iBAAiB,QAAQ;;;;;;;;;;;;kDAI3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoE;;;;;;0DAGjF,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,iBAAiB,MAAM;kEACtC,6LAAC,6HAAA,CAAA,QAAK;wDACJ,SACE,iBAAiB,MAAM,KAAK,YAAY,YACxC,iBAAiB,MAAM,KAAK,SAAS,cAAc;kEAGpD,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;oCAK7B,iBAAiB,OAAO,kBACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoE;;;;;;0DAGjF,6LAAC;gDAAE,WAAU;0DAAyB,iBAAiB,OAAO;;;;;;;;;;;;oCAIjE,iBAAiB,MAAM,kBACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoE;;;;;;0DAGjF,6LAAC;gDAAE,WAAU;0DAAyB,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;oBAQtE,QAAQ,MAAM,kBACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAoE;;;;;;8CAGjF,6LAAC;oCAAE,WAAU;8CACV,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;KA/MgB", "debugId": null}}, {"offset": {"line": 1452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/scanner/attendance-marking.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Student, AttendanceAction, ScanMode, Subject, TimePeriod } from \"@/lib/types/scanner\"\nimport { \n  LogIn, \n  LogOut, \n  CheckCircle, \n  Clock, \n  XCircle, \n  AlertCircle,\n  BookOpen,\n  Timer,\n  User\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface AttendanceMarkingProps {\n  student: Student\n  mode: ScanMode\n  onMarkAttendance: (action: AttendanceAction, options?: {\n    subject?: string\n    period?: string\n    reason?: string\n  }) => void\n  isLoading?: boolean\n  subjects?: Subject[]\n  periods?: TimePeriod[]\n  selectedSubject?: string\n  selectedPeriod?: string\n  onSubjectChange?: (subject: string) => void\n  onPeriodChange?: (period: string) => void\n}\n\nexport function AttendanceMarking({\n  student,\n  mode,\n  onMarkAttendance,\n  isLoading = false,\n  subjects = [],\n  periods = [],\n  selectedSubject,\n  selectedPeriod,\n  onSubjectChange,\n  onPeriodChange\n}: AttendanceMarkingProps) {\n  const [selectedAction, setSelectedAction] = useState<AttendanceAction | null>(null)\n\n  const handleMarkAttendance = (action: AttendanceAction) => {\n    setSelectedAction(action)\n    \n    const options: any = {}\n    if (mode === 'subject') {\n      options.subject = selectedSubject\n      options.period = selectedPeriod\n    }\n    \n    onMarkAttendance(action, options)\n    \n    // Reset selection after a brief delay\n    setTimeout(() => setSelectedAction(null), 1000)\n  }\n\n  const getActionIcon = (action: AttendanceAction) => {\n    switch (action) {\n      case 'check-in':\n        return <LogIn className=\"h-5 w-5\" />\n      case 'check-out':\n        return <LogOut className=\"h-5 w-5\" />\n      case 'present':\n        return <CheckCircle className=\"h-5 w-5\" />\n      case 'late':\n        return <Clock className=\"h-5 w-5\" />\n      case 'absent':\n        return <XCircle className=\"h-5 w-5\" />\n      default:\n        return <AlertCircle className=\"h-5 w-5\" />\n    }\n  }\n\n  const getActionColor = (action: AttendanceAction) => {\n    switch (action) {\n      case 'check-in':\n      case 'present':\n        return 'bg-green-500 hover:bg-green-600'\n      case 'late':\n        return 'bg-yellow-500 hover:bg-yellow-600'\n      case 'check-out':\n        return 'bg-blue-500 hover:bg-blue-600'\n      case 'absent':\n        return 'bg-red-500 hover:bg-red-600'\n      default:\n        return 'bg-gray-500 hover:bg-gray-600'\n    }\n  }\n\n  const gateActions: AttendanceAction[] = ['check-in', 'check-out']\n  const subjectActions: AttendanceAction[] = ['present', 'late', 'absent']\n\n  const actions = mode === 'gate' ? gateActions : subjectActions\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\">\n      <CardHeader className=\"pb-4\">\n        <CardTitle className=\"flex items-center gap-2\">\n          <User className=\"h-5 w-5\" />\n          Mark Attendance\n        </CardTitle>\n        <div className=\"flex items-center gap-2\">\n          <Badge variant=\"outline\" className=\"text-xs\">\n            {mode === 'gate' ? 'Gate Mode' : 'Subject Mode'}\n          </Badge>\n          <Badge variant=\"secondary\" className=\"text-xs\">\n            {student.name}\n          </Badge>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-6\">\n        {/* Subject and Period Selection for Subject Mode */}\n        {mode === 'subject' && (\n          <div className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium flex items-center gap-2\">\n                <BookOpen className=\"h-4 w-4\" />\n                Subject\n              </label>\n              <Select value={selectedSubject} onValueChange={onSubjectChange}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select subject\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {subjects.map((subject) => (\n                    <SelectItem key={subject.id} value={subject.id}>\n                      <div className=\"flex flex-col\">\n                        <span>{subject.name}</span>\n                        <span className=\"text-xs text-muted-foreground\">{subject.code}</span>\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium flex items-center gap-2\">\n                <Timer className=\"h-4 w-4\" />\n                Time Period\n              </label>\n              <Select value={selectedPeriod} onValueChange={onPeriodChange}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select period\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {periods.map((period) => (\n                    <SelectItem key={period.id} value={period.id}>\n                      <div className=\"flex flex-col\">\n                        <span>{period.name}</span>\n                        <span className=\"text-xs text-muted-foreground\">\n                          {period.startTime} - {period.endTime}\n                        </span>\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <Separator />\n          </div>\n        )}\n\n        {/* Attendance Action Buttons */}\n        <div className=\"space-y-3\">\n          <h4 className=\"text-sm font-medium text-muted-foreground\">\n            {mode === 'gate' ? 'Gate Actions' : 'Attendance Status'}\n          </h4>\n          \n          <div className=\"grid gap-3\">\n            {actions.map((action) => (\n              <Button\n                key={action}\n                onClick={() => handleMarkAttendance(action)}\n                disabled={\n                  isLoading || \n                  (mode === 'subject' && (!selectedSubject || !selectedPeriod))\n                }\n                className={cn(\n                  \"h-16 text-white font-semibold text-lg transition-all duration-200\",\n                  getActionColor(action),\n                  selectedAction === action && \"scale-95 opacity-75\"\n                )}\n                size=\"lg\"\n              >\n                <div className=\"flex items-center gap-3\">\n                  {getActionIcon(action)}\n                  <div className=\"flex flex-col items-start\">\n                    <span className=\"capitalize\">{action.replace('-', ' ')}</span>\n                    <span className=\"text-xs opacity-75\">\n                      {mode === 'gate' \n                        ? action === 'check-in' ? 'Enter building' : 'Exit building'\n                        : action === 'present' ? 'Student is present' : \n                          action === 'late' ? 'Student is late' : 'Mark as absent'\n                      }\n                    </span>\n                  </div>\n                </div>\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* Manual Override Section */}\n        <div className=\"border-t pt-4\">\n          <h4 className=\"text-sm font-medium text-muted-foreground mb-3\">\n            Manual Override\n          </h4>\n          \n          <div className=\"grid grid-cols-2 gap-2\">\n            {(mode === 'gate' ? subjectActions : gateActions).map((action) => (\n              <Button\n                key={action}\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleMarkAttendance(action)}\n                disabled={isLoading}\n                className=\"h-12 text-xs\"\n              >\n                <div className=\"flex flex-col items-center gap-1\">\n                  {getActionIcon(action)}\n                  <span className=\"capitalize\">{action.replace('-', ' ')}</span>\n                </div>\n              </Button>\n            ))}\n          </div>\n          \n          <p className=\"text-xs text-muted-foreground mt-2\">\n            Use manual override for special cases or corrections\n          </p>\n        </div>\n\n        {/* Current Selection Info */}\n        {mode === 'subject' && (selectedSubject || selectedPeriod) && (\n          <div className=\"bg-muted/50 p-3 rounded-lg\">\n            <h5 className=\"text-xs font-medium text-muted-foreground mb-2\">\n              Current Selection\n            </h5>\n            <div className=\"space-y-1 text-sm\">\n              {selectedSubject && (\n                <div className=\"flex justify-between\">\n                  <span>Subject:</span>\n                  <span className=\"font-medium\">\n                    {subjects.find(s => s.id === selectedSubject)?.name || selectedSubject}\n                  </span>\n                </div>\n              )}\n              {selectedPeriod && (\n                <div className=\"flex justify-between\">\n                  <span>Period:</span>\n                  <span className=\"font-medium\">\n                    {periods.find(p => p.id === selectedPeriod)?.name || selectedPeriod}\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AApBA;;;;;;;;;AAuCO,SAAS,kBAAkB,KAWT;QAXS,EAChC,OAAO,EACP,IAAI,EACJ,gBAAgB,EAChB,YAAY,KAAK,EACjB,WAAW,EAAE,EACb,UAAU,EAAE,EACZ,eAAe,EACf,cAAc,EACd,eAAe,EACf,cAAc,EACS,GAXS;QA0Nb,gBAQA;;IAtNnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAE9E,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;QAElB,MAAM,UAAe,CAAC;QACtB,IAAI,SAAS,WAAW;YACtB,QAAQ,OAAO,GAAG;YAClB,QAAQ,MAAM,GAAG;QACnB;QAEA,iBAAiB,QAAQ;QAEzB,sCAAsC;QACtC,WAAW,IAAM,kBAAkB,OAAO;IAC5C;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAkC;QAAC;QAAY;KAAY;IACjE,MAAM,iBAAqC;QAAC;QAAW;QAAQ;KAAS;IAExE,MAAM,UAAU,SAAS,SAAS,cAAc;IAEhD,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,4HAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAChC,SAAS,SAAS,cAAc;;;;;;0CAEnC,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;0BAKnB,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,SAAS,2BACR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;;0DACf,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAiB,eAAe;;0DAC7C,6LAAC,8HAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;0DACX,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,8HAAA,CAAA,aAAU;wDAAkB,OAAO,QAAQ,EAAE;kEAC5C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAM,QAAQ,IAAI;;;;;;8EACnB,6LAAC;oEAAK,WAAU;8EAAiC,QAAQ,IAAI;;;;;;;;;;;;uDAHhD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAWnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;;0DACf,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAgB,eAAe;;0DAC5C,6LAAC,8HAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;0DACX,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,8HAAA,CAAA,aAAU;wDAAiB,OAAO,OAAO,EAAE;kEAC1C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAM,OAAO,IAAI;;;;;;8EAClB,6LAAC;oEAAK,WAAU;;wEACb,OAAO,SAAS;wEAAC;wEAAI,OAAO,OAAO;;;;;;;;;;;;;uDAJzB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAalC,6LAAC,iIAAA,CAAA,YAAS;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,SAAS,SAAS,iBAAiB;;;;;;0CAGtC,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,8HAAA,CAAA,SAAM;wCAEL,SAAS,IAAM,qBAAqB;wCACpC,UACE,aACC,SAAS,aAAa,CAAC,CAAC,mBAAmB,CAAC,cAAc;wCAE7D,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qEACA,eAAe,SACf,mBAAmB,UAAU;wCAE/B,MAAK;kDAEL,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,cAAc;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAc,OAAO,OAAO,CAAC,KAAK;;;;;;sEAClD,6LAAC;4DAAK,WAAU;sEACb,SAAS,SACN,WAAW,aAAa,mBAAmB,kBAC3C,WAAW,YAAY,uBACvB,WAAW,SAAS,oBAAoB;;;;;;;;;;;;;;;;;;uCArB7C;;;;;;;;;;;;;;;;kCAgCb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAI/D,6LAAC;gCAAI,WAAU;0CACZ,CAAC,SAAS,SAAS,iBAAiB,WAAW,EAAE,GAAG,CAAC,CAAC,uBACrD,6LAAC,8HAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,qBAAqB;wCACpC,UAAU;wCACV,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,cAAc;8DACf,6LAAC;oDAAK,WAAU;8DAAc,OAAO,OAAO,CAAC,KAAK;;;;;;;;;;;;uCAT/C;;;;;;;;;;0CAeX,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;oBAMnD,SAAS,aAAa,CAAC,mBAAmB,cAAc,mBACvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,6LAAC;gCAAI,WAAU;;oCACZ,iCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DACb,EAAA,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,8BAA5B,qCAAA,eAA8C,IAAI,KAAI;;;;;;;;;;;;oCAI5D,gCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DACb,EAAA,gBAAA,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,6BAA3B,oCAAA,cAA4C,IAAI,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzE;GA5OgB;KAAA", "debugId": null}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/scanner/feedback-system.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useRef, useState } from \"react\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { ScanNotification } from \"@/lib/types/scanner\"\nimport { CheckCircle, XCircle, AlertTriangle, Info, X, Volume2, VolumeX } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface FeedbackSystemProps {\n  notifications: ScanNotification[]\n  onDismissNotification: (id: string) => void\n  audioEnabled: boolean\n  onToggleAudio: () => void\n}\n\nexport function FeedbackSystem({\n  notifications,\n  onDismissNotification,\n  audioEnabled,\n  onToggleAudio\n}: FeedbackSystemProps) {\n  const audioContextRef = useRef<AudioContext | null>(null)\n  const [isAudioInitialized, setIsAudioInitialized] = useState(false)\n\n  // Initialize audio context\n  useEffect(() => {\n    const initAudio = () => {\n      if (!audioContextRef.current && audioEnabled) {\n        try {\n          audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()\n          setIsAudioInitialized(true)\n        } catch (error) {\n          console.error(\"Failed to initialize audio context:\", error)\n        }\n      }\n    }\n\n    // Initialize on user interaction\n    const handleUserInteraction = () => {\n      initAudio()\n      document.removeEventListener('click', handleUserInteraction)\n      document.removeEventListener('touchstart', handleUserInteraction)\n    }\n\n    document.addEventListener('click', handleUserInteraction)\n    document.addEventListener('touchstart', handleUserInteraction)\n\n    return () => {\n      document.removeEventListener('click', handleUserInteraction)\n      document.removeEventListener('touchstart', handleUserInteraction)\n    }\n  }, [audioEnabled])\n\n  // Play sound notification\n  const playSound = (type: 'success' | 'error' | 'warning' | 'info') => {\n    if (!audioEnabled || !audioContextRef.current || !isAudioInitialized) return\n\n    try {\n      const ctx = audioContextRef.current\n      const oscillator = ctx.createOscillator()\n      const gainNode = ctx.createGain()\n\n      oscillator.connect(gainNode)\n      gainNode.connect(ctx.destination)\n\n      // Different frequencies for different notification types\n      const frequencies = {\n        success: [523.25, 659.25, 783.99], // C5, E5, G5 (major chord)\n        error: [220, 185, 165], // A3, F#3, E3 (descending)\n        warning: [440, 554.37], // A4, C#5\n        info: [523.25] // C5\n      }\n\n      const freq = frequencies[type]\n      const duration = type === 'success' ? 0.3 : type === 'error' ? 0.5 : 0.2\n\n      freq.forEach((f, index) => {\n        const osc = ctx.createOscillator()\n        const gain = ctx.createGain()\n        \n        osc.connect(gain)\n        gain.connect(ctx.destination)\n        \n        osc.frequency.setValueAtTime(f, ctx.currentTime + index * 0.1)\n        osc.type = type === 'error' ? 'sawtooth' : 'sine'\n        \n        gain.gain.setValueAtTime(0, ctx.currentTime + index * 0.1)\n        gain.gain.linearRampToValueAtTime(0.1, ctx.currentTime + index * 0.1 + 0.05)\n        gain.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + index * 0.1 + duration)\n        \n        osc.start(ctx.currentTime + index * 0.1)\n        osc.stop(ctx.currentTime + index * 0.1 + duration)\n      })\n\n      // Vibration for mobile devices\n      if ('vibrator' in navigator || 'vibrate' in navigator) {\n        const vibrationPatterns = {\n          success: [100],\n          error: [200, 100, 200],\n          warning: [150],\n          info: [50]\n        }\n        navigator.vibrate?.(vibrationPatterns[type])\n      }\n    } catch (error) {\n      console.error(\"Failed to play sound:\", error)\n    }\n  }\n\n  // Play sound when new notifications arrive\n  useEffect(() => {\n    const latestNotification = notifications[notifications.length - 1]\n    if (latestNotification && audioEnabled) {\n      playSound(latestNotification.type)\n    }\n  }, [notifications, audioEnabled])\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-600\" />\n      case 'error':\n        return <XCircle className=\"h-5 w-5 text-red-600\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-600\" />\n      default:\n        return <Info className=\"h-5 w-5 text-gray-600\" />\n    }\n  }\n\n  const getNotificationStyle = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950'\n      case 'error':\n        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950'\n      case 'warning':\n        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950'\n      case 'info':\n        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950'\n      default:\n        return 'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-950'\n    }\n  }\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {/* Audio Control */}\n      <div className=\"flex justify-end\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={onToggleAudio}\n          className=\"bg-background/80 backdrop-blur-sm\"\n        >\n          {audioEnabled ? (\n            <Volume2 className=\"h-4 w-4\" />\n          ) : (\n            <VolumeX className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* Notifications */}\n      {notifications.map((notification) => (\n        <NotificationCard\n          key={notification.id}\n          notification={notification}\n          onDismiss={() => onDismissNotification(notification.id)}\n          icon={getNotificationIcon(notification.type)}\n          className={getNotificationStyle(notification.type)}\n        />\n      ))}\n    </div>\n  )\n}\n\ninterface NotificationCardProps {\n  notification: ScanNotification\n  onDismiss: () => void\n  icon: React.ReactNode\n  className: string\n}\n\nfunction NotificationCard({ notification, onDismiss, icon, className }: NotificationCardProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [isExiting, setIsExiting] = useState(false)\n\n  useEffect(() => {\n    // Animate in\n    const timer = setTimeout(() => setIsVisible(true), 10)\n    \n    // Auto dismiss\n    if (notification.duration) {\n      const dismissTimer = setTimeout(() => {\n        handleDismiss()\n      }, notification.duration)\n      \n      return () => {\n        clearTimeout(timer)\n        clearTimeout(dismissTimer)\n      }\n    }\n    \n    return () => clearTimeout(timer)\n  }, [notification.duration])\n\n  const handleDismiss = () => {\n    setIsExiting(true)\n    setTimeout(() => {\n      onDismiss()\n    }, 200)\n  }\n\n  return (\n    <Card \n      className={cn(\n        \"border transition-all duration-200 transform\",\n        className,\n        isVisible && !isExiting ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\",\n        isExiting && \"translate-x-full opacity-0\"\n      )}\n    >\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-start gap-3\">\n          <div className=\"flex-shrink-0 mt-0.5\">\n            {icon}\n          </div>\n          \n          <div className=\"flex-1 min-w-0\">\n            <h4 className=\"text-sm font-semibold leading-tight\">\n              {notification.title}\n            </h4>\n            <p className=\"text-sm text-muted-foreground mt-1\">\n              {notification.message}\n            </p>\n            \n            {notification.action && (\n              <Button\n                variant=\"link\"\n                size=\"sm\"\n                onClick={notification.action.handler}\n                className=\"p-0 h-auto mt-2 text-xs\"\n              >\n                {notification.action.label}\n              </Button>\n            )}\n          </div>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleDismiss}\n            className=\"flex-shrink-0 h-6 w-6 p-0\"\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n        \n        <div className=\"flex justify-between items-center mt-2 text-xs text-muted-foreground\">\n          <span>{notification.timestamp.toLocaleTimeString()}</span>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;AAgBO,SAAS,eAAe,KAKT;QALS,EAC7B,aAAa,EACb,qBAAqB,EACrB,YAAY,EACZ,aAAa,EACO,GALS;;IAM7B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IACpD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI,CAAC,gBAAgB,OAAO,IAAI,cAAc;wBAC5C,IAAI;4BACF,gBAAgB,OAAO,GAAG,IAAI,CAAC,OAAO,YAAY,IAAI,AAAC,OAAe,kBAAkB;4BACxF,sBAAsB;wBACxB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,uCAAuC;wBACvD;oBACF;gBACF;;YAEA,iCAAiC;YACjC,MAAM;kEAAwB;oBAC5B;oBACA,SAAS,mBAAmB,CAAC,SAAS;oBACtC,SAAS,mBAAmB,CAAC,cAAc;gBAC7C;;YAEA,SAAS,gBAAgB,CAAC,SAAS;YACnC,SAAS,gBAAgB,CAAC,cAAc;YAExC;4CAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS;oBACtC,SAAS,mBAAmB,CAAC,cAAc;gBAC7C;;QACF;mCAAG;QAAC;KAAa;IAEjB,0BAA0B;IAC1B,MAAM,YAAY,CAAC;QACjB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,OAAO,IAAI,CAAC,oBAAoB;QAEtE,IAAI;YACF,MAAM,MAAM,gBAAgB,OAAO;YACnC,MAAM,aAAa,IAAI,gBAAgB;YACvC,MAAM,WAAW,IAAI,UAAU;YAE/B,WAAW,OAAO,CAAC;YACnB,SAAS,OAAO,CAAC,IAAI,WAAW;YAEhC,yDAAyD;YACzD,MAAM,cAAc;gBAClB,SAAS;oBAAC;oBAAQ;oBAAQ;iBAAO;gBACjC,OAAO;oBAAC;oBAAK;oBAAK;iBAAI;gBACtB,SAAS;oBAAC;oBAAK;iBAAO;gBACtB,MAAM;oBAAC;iBAAO,CAAC,KAAK;YACtB;YAEA,MAAM,OAAO,WAAW,CAAC,KAAK;YAC9B,MAAM,WAAW,SAAS,YAAY,MAAM,SAAS,UAAU,MAAM;YAErE,KAAK,OAAO,CAAC,CAAC,GAAG;gBACf,MAAM,MAAM,IAAI,gBAAgB;gBAChC,MAAM,OAAO,IAAI,UAAU;gBAE3B,IAAI,OAAO,CAAC;gBACZ,KAAK,OAAO,CAAC,IAAI,WAAW;gBAE5B,IAAI,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI,WAAW,GAAG,QAAQ;gBAC1D,IAAI,IAAI,GAAG,SAAS,UAAU,aAAa;gBAE3C,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI,WAAW,GAAG,QAAQ;gBACtD,KAAK,IAAI,CAAC,uBAAuB,CAAC,KAAK,IAAI,WAAW,GAAG,QAAQ,MAAM;gBACvE,KAAK,IAAI,CAAC,4BAA4B,CAAC,MAAM,IAAI,WAAW,GAAG,QAAQ,MAAM;gBAE7E,IAAI,KAAK,CAAC,IAAI,WAAW,GAAG,QAAQ;gBACpC,IAAI,IAAI,CAAC,IAAI,WAAW,GAAG,QAAQ,MAAM;YAC3C;YAEA,+BAA+B;YAC/B,IAAI,cAAc,aAAa,aAAa,WAAW;oBAOrD,oBAAA;gBANA,MAAM,oBAAoB;oBACxB,SAAS;wBAAC;qBAAI;oBACd,OAAO;wBAAC;wBAAK;wBAAK;qBAAI;oBACtB,SAAS;wBAAC;qBAAI;oBACd,MAAM;wBAAC;qBAAG;gBACZ;iBACA,qBAAA,CAAA,aAAA,WAAU,OAAO,cAAjB,yCAAA,wBAAA,YAAoB,iBAAiB,CAAC,KAAK;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,qBAAqB,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;YAClE,IAAI,sBAAsB,cAAc;gBACtC,UAAU,mBAAmB,IAAI;YACnC;QACF;mCAAG;QAAC;QAAe;KAAa;IAEhC,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;8BAET,6BACC,6LAAC,+MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;6CAEnB,6LAAC,+MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;;;;;;;;;;;YAMxB,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;oBAEC,cAAc;oBACd,WAAW,IAAM,sBAAsB,aAAa,EAAE;oBACtD,MAAM,oBAAoB,aAAa,IAAI;oBAC3C,WAAW,qBAAqB,aAAa,IAAI;mBAJ5C,aAAa,EAAE;;;;;;;;;;;AAS9B;GAlKgB;KAAA;AA2KhB,SAAS,iBAAiB,KAAmE;QAAnE,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAyB,GAAnE;;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,aAAa;YACb,MAAM,QAAQ;oDAAW,IAAM,aAAa;mDAAO;YAEnD,eAAe;YACf,IAAI,aAAa,QAAQ,EAAE;gBACzB,MAAM,eAAe;+DAAW;wBAC9B;oBACF;8DAAG,aAAa,QAAQ;gBAExB;kDAAO;wBACL,aAAa;wBACb,aAAa;oBACf;;YACF;YAEA;8CAAO,IAAM,aAAa;;QAC5B;qCAAG;QAAC,aAAa,QAAQ;KAAC;IAE1B,MAAM,gBAAgB;QACpB,aAAa;QACb,WAAW;YACT;QACF,GAAG;IACL;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gDACA,WACA,aAAa,CAAC,YAAY,8BAA8B,8BACxD,aAAa;kBAGf,cAAA,6LAAC,4HAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAGH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,aAAa,KAAK;;;;;;8CAErB,6LAAC;oCAAE,WAAU;8CACV,aAAa,OAAO;;;;;;gCAGtB,aAAa,MAAM,kBAClB,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,aAAa,MAAM,CAAC,OAAO;oCACpC,WAAU;8CAET,aAAa,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAKhC,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAM,aAAa,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;AAK1D;IAjFS;MAAA", "debugId": null}}, {"offset": {"line": 2758, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2966, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2999, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/scanner/manual-entry-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useMemo } from \"react\"\nimport { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Student, AttendanceAction, ManualEntry } from \"@/lib/types/scanner\"\nimport { Search, User, KeyboardIcon, CheckCircle, AlertCircle } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ManualEntryDialogProps {\n  isOpen: boolean\n  onOpenChange: (open: boolean) => void\n  students: Student[]\n  onSubmitEntry: (entry: ManualEntry) => void\n  isLoading?: boolean\n}\n\nexport function ManualEntryDialog({\n  isOpen,\n  onOpenChange,\n  students,\n  onSubmitEntry,\n  isLoading = false\n}: ManualEntryDialogProps) {\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)\n  const [selectedAction, setSelectedAction] = useState<AttendanceAction>('present')\n  const [reason, setReason] = useState(\"\")\n\n  // Filter students based on search query\n  const filteredStudents = useMemo(() => {\n    if (!searchQuery.trim()) return students.slice(0, 10) // Show first 10 by default\n    \n    const query = searchQuery.toLowerCase()\n    return students.filter(student => \n      student.id.toLowerCase().includes(query) ||\n      student.name.toLowerCase().includes(query) ||\n      student.email.toLowerCase().includes(query) ||\n      student.course.toLowerCase().includes(query)\n    ).slice(0, 20) // Limit to 20 results\n  }, [searchQuery, students])\n\n  // Reset form when dialog opens/closes\n  useEffect(() => {\n    if (!isOpen) {\n      setSearchQuery(\"\")\n      setSelectedStudent(null)\n      setSelectedAction('present')\n      setReason(\"\")\n    }\n  }, [isOpen])\n\n  const handleSubmit = () => {\n    if (!selectedStudent) return\n\n    const entry: ManualEntry = {\n      studentId: selectedStudent.id,\n      action: selectedAction,\n      timestamp: new Date(),\n      reason: reason.trim() || undefined\n    }\n\n    onSubmitEntry(entry)\n    onOpenChange(false)\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const actionOptions: { value: AttendanceAction; label: string; description: string }[] = [\n    { value: 'present', label: 'Present', description: 'Mark student as present' },\n    { value: 'late', label: 'Late', description: 'Mark student as late arrival' },\n    { value: 'absent', label: 'Absent', description: 'Mark student as absent' },\n    { value: 'check-in', label: 'Check In', description: 'Manual check-in entry' },\n    { value: 'check-out', label: 'Check Out', description: 'Manual check-out entry' }\n  ]\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <KeyboardIcon className=\"h-5 w-5\" />\n            Manual Entry\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"flex-1 overflow-hidden flex flex-col space-y-6\">\n          {/* Student Search */}\n          <div className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"search\">Search Student</Label>\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  id=\"search\"\n                  placeholder=\"Enter student ID, name, or email...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                  autoFocus\n                />\n              </div>\n            </div>\n\n            {/* Student Results */}\n            <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n              {filteredStudents.length > 0 ? (\n                filteredStudents.map((student) => (\n                  <Card\n                    key={student.id}\n                    className={cn(\n                      \"cursor-pointer transition-colors hover:bg-muted/50\",\n                      selectedStudent?.id === student.id && \"ring-2 ring-primary bg-muted/50\"\n                    )}\n                    onClick={() => setSelectedStudent(student)}\n                  >\n                    <CardContent className=\"p-3\">\n                      <div className=\"flex items-center gap-3\">\n                        <Avatar className=\"h-10 w-10\">\n                          <AvatarImage src={student.photo} alt={student.name} />\n                          <AvatarFallback className=\"text-sm\">\n                            {getInitials(student.name)}\n                          </AvatarFallback>\n                        </Avatar>\n                        \n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center gap-2\">\n                            <h4 className=\"font-medium truncate\">{student.name}</h4>\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {student.id}\n                            </Badge>\n                          </div>\n                          <p className=\"text-sm text-muted-foreground truncate\">\n                            {student.course} • {student.year}\n                          </p>\n                        </div>\n                        \n                        {selectedStudent?.id === student.id && (\n                          <CheckCircle className=\"h-5 w-5 text-primary\" />\n                        )}\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))\n              ) : searchQuery.trim() ? (\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  <User className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\n                  <p>No students found matching \"{searchQuery}\"</p>\n                </div>\n              ) : (\n                <div className=\"text-center py-4 text-muted-foreground\">\n                  <p className=\"text-sm\">Start typing to search for students</p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Selected Student Info */}\n          {selectedStudent && (\n            <Card className=\"border-primary/20 bg-primary/5\">\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center gap-3\">\n                  <Avatar className=\"h-12 w-12\">\n                    <AvatarImage src={selectedStudent.photo} alt={selectedStudent.name} />\n                    <AvatarFallback>\n                      {getInitials(selectedStudent.name)}\n                    </AvatarFallback>\n                  </Avatar>\n                  \n                  <div className=\"flex-1\">\n                    <h3 className=\"font-semibold\">{selectedStudent.name}</h3>\n                    <p className=\"text-sm text-muted-foreground\">\n                      {selectedStudent.id} • {selectedStudent.course} • {selectedStudent.year}\n                    </p>\n                  </div>\n                  \n                  <Badge variant={selectedStudent.status === 'Active' ? 'default' : 'secondary'}>\n                    {selectedStudent.status}\n                  </Badge>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Action Selection */}\n          {selectedStudent && (\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>Attendance Action</Label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {actionOptions.map((option) => (\n                    <Button\n                      key={option.value}\n                      variant={selectedAction === option.value ? \"default\" : \"outline\"}\n                      onClick={() => setSelectedAction(option.value)}\n                      className=\"h-auto p-3 flex flex-col items-start\"\n                    >\n                      <span className=\"font-medium\">{option.label}</span>\n                      <span className=\"text-xs opacity-75\">{option.description}</span>\n                    </Button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Reason/Notes */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"reason\">Reason (Optional)</Label>\n                <Input\n                  id=\"reason\"\n                  placeholder=\"Enter reason for manual entry...\"\n                  value={reason}\n                  onChange={(e) => setReason(e.target.value)}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex justify-end gap-2 pt-4 border-t\">\n          <Button variant=\"outline\" onClick={() => onOpenChange(false)}>\n            Cancel\n          </Button>\n          <Button \n            onClick={handleSubmit}\n            disabled={!selectedStudent || isLoading}\n          >\n            {isLoading ? (\n              <>\n                <AlertCircle className=\"mr-2 h-4 w-4 animate-spin\" />\n                Processing...\n              </>\n            ) : (\n              <>\n                <CheckCircle className=\"mr-2 h-4 w-4\" />\n                Submit Entry\n              </>\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAZA;;;;;;;;;;;AAsBO,SAAS,kBAAkB,KAMT;QANS,EAChC,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,YAAY,KAAK,EACM,GANS;;IAOhC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,wCAAwC;IACxC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE;YAC/B,IAAI,CAAC,YAAY,IAAI,IAAI,OAAO,SAAS,KAAK,CAAC,GAAG,IAAI,2BAA2B;;YAEjF,MAAM,QAAQ,YAAY,WAAW;YACrC,OAAO,SAAS,MAAM;+DAAC,CAAA,UACrB,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,UAClC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACpC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,QAAQ,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC;8DACtC,KAAK,CAAC,GAAG,IAAI,sBAAsB;;QACvC;sDAAG;QAAC;QAAa;KAAS;IAE1B,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,QAAQ;gBACX,eAAe;gBACf,mBAAmB;gBACnB,kBAAkB;gBAClB,UAAU;YACZ;QACF;sCAAG;QAAC;KAAO;IAEX,MAAM,eAAe;QACnB,IAAI,CAAC,iBAAiB;QAEtB,MAAM,QAAqB;YACzB,WAAW,gBAAgB,EAAE;YAC7B,QAAQ;YACR,WAAW,IAAI;YACf,QAAQ,OAAO,IAAI,MAAM;QAC3B;QAEA,cAAc;QACd,aAAa;IACf;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAmF;QACvF;YAAE,OAAO;YAAW,OAAO;YAAW,aAAa;QAA0B;QAC7E;YAAE,OAAO;YAAQ,OAAO;YAAQ,aAAa;QAA+B;QAC5E;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAAyB;QAC1E;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAAwB;QAC7E;YAAE,OAAO;YAAa,OAAO;YAAa,aAAa;QAAyB;KACjF;IAED,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,iNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAKxC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAS;;;;;;sDACxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC,6HAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,SAAS;;;;;;;;;;;;;;;;;;8CAMf,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,MAAM,GAAG,IACzB,iBAAiB,GAAG,CAAC,CAAC,wBACpB,6LAAC,4HAAA,CAAA,OAAI;4CAEH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA,CAAA,4BAAA,sCAAA,gBAAiB,EAAE,MAAK,QAAQ,EAAE,IAAI;4CAExC,SAAS,IAAM,mBAAmB;sDAElC,cAAA,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,8HAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,6LAAC,8HAAA,CAAA,cAAW;oEAAC,KAAK,QAAQ,KAAK;oEAAE,KAAK,QAAQ,IAAI;;;;;;8EAClD,6LAAC,8HAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,YAAY,QAAQ,IAAI;;;;;;;;;;;;sEAI7B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAwB,QAAQ,IAAI;;;;;;sFAClD,6LAAC,6HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAChC,QAAQ,EAAE;;;;;;;;;;;;8EAGf,6LAAC;oEAAE,WAAU;;wEACV,QAAQ,MAAM;wEAAC;wEAAI,QAAQ,IAAI;;;;;;;;;;;;;wDAInC,CAAA,4BAAA,sCAAA,gBAAiB,EAAE,MAAK,QAAQ,EAAE,kBACjC,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;2CA7BxB,QAAQ,EAAE;;;;oDAmCjB,YAAY,IAAI,mBAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;;oDAAE;oDAA6B;oDAAY;;;;;;;;;;;;6DAG9C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;wBAO9B,iCACC,6LAAC,4HAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,8HAAA,CAAA,cAAW;oDAAC,KAAK,gBAAgB,KAAK;oDAAE,KAAK,gBAAgB,IAAI;;;;;;8DAClE,6LAAC,8HAAA,CAAA,iBAAc;8DACZ,YAAY,gBAAgB,IAAI;;;;;;;;;;;;sDAIrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiB,gBAAgB,IAAI;;;;;;8DACnD,6LAAC;oDAAE,WAAU;;wDACV,gBAAgB,EAAE;wDAAC;wDAAI,gBAAgB,MAAM;wDAAC;wDAAI,gBAAgB,IAAI;;;;;;;;;;;;;sDAI3E,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAS,gBAAgB,MAAM,KAAK,WAAW,YAAY;sDAC/D,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;wBAQhC,iCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,8HAAA,CAAA,SAAM;oDAEL,SAAS,mBAAmB,OAAO,KAAK,GAAG,YAAY;oDACvD,SAAS,IAAM,kBAAkB,OAAO,KAAK;oDAC7C,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAe,OAAO,KAAK;;;;;;sEAC3C,6LAAC;4DAAK,WAAU;sEAAsB,OAAO,WAAW;;;;;;;mDANnD,OAAO,KAAK;;;;;;;;;;;;;;;;8CAazB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAS;;;;;;sDACxB,6LAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,aAAa;sCAAQ;;;;;;sCAG9D,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,mBAAmB;sCAE7B,0BACC;;kDACE,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA8B;;6DAIvD;;kDACE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GAzOgB;KAAA", "debugId": null}}, {"offset": {"line": 3629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,6JAAA,CAAA,aAAgB,MAGjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,6JAAA,CAAA,aAAgB,CAGhC,QAAoD;QAAnD,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO;yBAClD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/scanner/batch-scanner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport { BatchSession, BatchScanItem, Student, AttendanceAction } from \"@/lib/types/scanner\"\nimport { \n  Users, \n  CheckCircle, \n  Clock, \n  XCircle, \n  Play, \n  Pause, \n  RotateCcw,\n  Download,\n  Filter\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BatchScannerProps {\n  session: BatchSession | null\n  onStartSession: (students: Student[], subject?: string, period?: string) => void\n  onPauseSession: () => void\n  onResumeSession: () => void\n  onResetSession: () => void\n  onMarkStudent: (studentId: string, action: AttendanceAction) => void\n  onExportResults: () => void\n  isScanning: boolean\n  currentStudent?: Student\n}\n\nexport function BatchScanner({\n  session,\n  onStartSession,\n  onPauseSession,\n  onResumeSession,\n  onResetSession,\n  onMarkStudent,\n  onExportResults,\n  isScanning,\n  currentStudent\n}: BatchScannerProps) {\n  const [filter, setFilter] = useState<'all' | 'scanned' | 'pending'>('all')\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(n => n[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const getStatusIcon = (item: BatchScanItem) => {\n    if (!item.scanned) return <Clock className=\"h-4 w-4 text-muted-foreground\" />\n    \n    switch (item.status) {\n      case 'present':\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n      case 'late':\n        return <Clock className=\"h-4 w-4 text-yellow-600\" />\n      case 'absent':\n        return <XCircle className=\"h-4 w-4 text-red-600\" />\n      default:\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n    }\n  }\n\n  const getStatusColor = (item: BatchScanItem) => {\n    if (!item.scanned) return 'bg-muted'\n    \n    switch (item.status) {\n      case 'present':\n        return 'bg-green-100 border-green-200 dark:bg-green-950 dark:border-green-800'\n      case 'late':\n        return 'bg-yellow-100 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800'\n      case 'absent':\n        return 'bg-red-100 border-red-200 dark:bg-red-950 dark:border-red-800'\n      default:\n        return 'bg-green-100 border-green-200 dark:bg-green-950 dark:border-green-800'\n    }\n  }\n\n  const filteredStudents = session?.students.filter(item => {\n    switch (filter) {\n      case 'scanned':\n        return item.scanned\n      case 'pending':\n        return !item.scanned\n      default:\n        return true\n    }\n  }) || []\n\n  const progress = session ? (session.completedCount / session.totalCount) * 100 : 0\n\n  if (!session) {\n    return (\n      <Card className=\"w-full max-w-md mx-auto\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Batch Scanner\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"text-center py-8\">\n          <Users className=\"h-16 w-16 mx-auto mb-4 text-muted-foreground\" />\n          <h3 className=\"text-lg font-semibold mb-2\">No Active Session</h3>\n          <p className=\"text-muted-foreground mb-4\">\n            Start a batch scanning session to process multiple students\n          </p>\n          <Button onClick={() => onStartSession([])}>\n            <Play className=\"mr-2 h-4 w-4\" />\n            Start Batch Session\n          </Button>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className=\"w-full max-w-2xl mx-auto space-y-4\">\n      {/* Session Header */}\n      <Card>\n        <CardHeader className=\"pb-4\">\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"flex items-center gap-2\">\n              <Users className=\"h-5 w-5\" />\n              {session.name}\n            </CardTitle>\n            <Badge variant=\"outline\">\n              {session.completedCount} / {session.totalCount}\n            </Badge>\n          </div>\n          \n          {(session.subject || session.period) && (\n            <div className=\"flex gap-2 text-sm text-muted-foreground\">\n              {session.subject && <span>Subject: {session.subject}</span>}\n              {session.period && <span>Period: {session.period}</span>}\n            </div>\n          )}\n        </CardHeader>\n        \n        <CardContent className=\"space-y-4\">\n          {/* Progress */}\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm\">\n              <span>Progress</span>\n              <span>{Math.round(progress)}%</span>\n            </div>\n            <Progress value={progress} className=\"h-2\" />\n          </div>\n\n          {/* Controls */}\n          <div className=\"flex gap-2\">\n            {isScanning ? (\n              <Button onClick={onPauseSession} variant=\"outline\">\n                <Pause className=\"mr-2 h-4 w-4\" />\n                Pause\n              </Button>\n            ) : (\n              <Button onClick={onResumeSession}>\n                <Play className=\"mr-2 h-4 w-4\" />\n                Resume\n              </Button>\n            )}\n            \n            <Button onClick={onResetSession} variant=\"outline\">\n              <RotateCcw className=\"mr-2 h-4 w-4\" />\n              Reset\n            </Button>\n            \n            <Button onClick={onExportResults} variant=\"outline\">\n              <Download className=\"mr-2 h-4 w-4\" />\n              Export\n            </Button>\n          </div>\n\n          {/* Filter */}\n          <div className=\"flex gap-2\">\n            <Button\n              variant={filter === 'all' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setFilter('all')}\n            >\n              All ({session.totalCount})\n            </Button>\n            <Button\n              variant={filter === 'scanned' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setFilter('scanned')}\n            >\n              Scanned ({session.completedCount})\n            </Button>\n            <Button\n              variant={filter === 'pending' ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setFilter('pending')}\n            >\n              Pending ({session.totalCount - session.completedCount})\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Student List */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Filter className=\"h-4 w-4\" />\n            Students ({filteredStudents.length})\n          </CardTitle>\n        </CardHeader>\n        \n        <CardContent className=\"p-0\">\n          <ScrollArea className=\"h-96\">\n            <div className=\"space-y-2 p-4\">\n              {filteredStudents.map((item, index) => (\n                <Card\n                  key={item.student.id}\n                  className={cn(\n                    \"transition-all duration-200\",\n                    getStatusColor(item),\n                    currentStudent?.id === item.student.id && \"ring-2 ring-primary scale-[1.02]\"\n                  )}\n                >\n                  <CardContent className=\"p-3\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"text-sm font-mono text-muted-foreground w-6\">\n                          {index + 1}\n                        </span>\n                        {getStatusIcon(item)}\n                      </div>\n                      \n                      <Avatar className=\"h-10 w-10\">\n                        <AvatarImage src={item.student.photo} alt={item.student.name} />\n                        <AvatarFallback className=\"text-sm\">\n                          {getInitials(item.student.name)}\n                        </AvatarFallback>\n                      </Avatar>\n                      \n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center gap-2\">\n                          <h4 className=\"font-medium truncate\">{item.student.name}</h4>\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {item.student.id}\n                          </Badge>\n                        </div>\n                        <p className=\"text-sm text-muted-foreground truncate\">\n                          {item.student.course} • {item.student.year}\n                        </p>\n                      </div>\n                      \n                      <div className=\"flex flex-col items-end gap-1\">\n                        {item.scanned && item.timestamp && (\n                          <span className=\"text-xs text-muted-foreground\">\n                            {item.timestamp.toLocaleTimeString()}\n                          </span>\n                        )}\n                        \n                        {item.scanned && item.status && (\n                          <Badge \n                            variant={\n                              item.status === 'present' ? 'default' :\n                              item.status === 'late' ? 'secondary' : 'destructive'\n                            }\n                            className=\"text-xs\"\n                          >\n                            {item.status}\n                          </Badge>\n                        )}\n                        \n                        {!item.scanned && (\n                          <div className=\"flex gap-1\">\n                            <Button\n                              size=\"sm\"\n                              variant=\"outline\"\n                              onClick={() => onMarkStudent(item.student.id, 'present')}\n                              className=\"h-6 px-2 text-xs\"\n                            >\n                              Present\n                            </Button>\n                            <Button\n                              size=\"sm\"\n                              variant=\"outline\"\n                              onClick={() => onMarkStudent(item.student.id, 'absent')}\n                              className=\"h-6 px-2 text-xs\"\n                            >\n                              Absent\n                            </Button>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n              \n              {filteredStudents.length === 0 && (\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  <Users className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\n                  <p>No students match the current filter</p>\n                </div>\n              )}\n            </div>\n          </ScrollArea>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AArBA;;;;;;;;;;AAmCO,SAAS,aAAa,KAUT;QAVS,EAC3B,OAAO,EACP,cAAc,EACd,cAAc,EACd,eAAe,EACf,cAAc,EACd,aAAa,EACb,eAAe,EACf,UAAU,EACV,cAAc,EACI,GAVS;;IAW3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAEpE,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,KAAK,OAAO,EAAE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAE3C,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO;QAE1B,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAA,oBAAA,8BAAA,QAAS,QAAQ,CAAC,MAAM,CAAC,CAAA;QAChD,OAAQ;YACN,KAAK;gBACH,OAAO,KAAK,OAAO;YACrB,KAAK;gBACH,OAAO,CAAC,KAAK,OAAO;YACtB;gBACE,OAAO;QACX;IACF,OAAM,EAAE;IAER,MAAM,WAAW,UAAU,AAAC,QAAQ,cAAc,GAAG,QAAQ,UAAU,GAAI,MAAM;IAEjF,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,4HAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIjC,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,eAAe,EAAE;;8CACtC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;IAM3C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,QAAQ,IAAI;;;;;;;kDAEf,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;;4CACZ,QAAQ,cAAc;4CAAC;4CAAI,QAAQ,UAAU;;;;;;;;;;;;;4BAIjD,CAAC,QAAQ,OAAO,IAAI,QAAQ,MAAM,mBACjC,6LAAC;gCAAI,WAAU;;oCACZ,QAAQ,OAAO,kBAAI,6LAAC;;4CAAK;4CAAU,QAAQ,OAAO;;;;;;;oCAClD,QAAQ,MAAM,kBAAI,6LAAC;;4CAAK;4CAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;kCAKtD,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAM,KAAK,KAAK,CAAC;oDAAU;;;;;;;;;;;;;kDAE9B,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,OAAO;wCAAU,WAAU;;;;;;;;;;;;0CAIvC,6LAAC;gCAAI,WAAU;;oCACZ,2BACC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAgB,SAAQ;;0DACvC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;6DAIpC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKrC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAgB,SAAQ;;0DACvC,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIxC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAiB,SAAQ;;0DACxC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAMzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS,WAAW,QAAQ,YAAY;wCACxC,MAAK;wCACL,SAAS,IAAM,UAAU;;4CAC1B;4CACO,QAAQ,UAAU;4CAAC;;;;;;;kDAE3B,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS,WAAW,YAAY,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,UAAU;;4CAC1B;4CACW,QAAQ,cAAc;4CAAC;;;;;;;kDAEnC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS,WAAW,YAAY,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,UAAU;;4CAC1B;4CACW,QAAQ,UAAU,GAAG,QAAQ,cAAc;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCACnB,iBAAiB,MAAM;gCAAC;;;;;;;;;;;;kCAIvC,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,sIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC,4HAAA,CAAA,OAAI;4CAEH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+BACA,eAAe,OACf,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,KAAK,OAAO,CAAC,EAAE,IAAI;sDAG5C,cAAA,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,QAAQ;;;;;;gEAEV,cAAc;;;;;;;sEAGjB,6LAAC,8HAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,6LAAC,8HAAA,CAAA,cAAW;oEAAC,KAAK,KAAK,OAAO,CAAC,KAAK;oEAAE,KAAK,KAAK,OAAO,CAAC,IAAI;;;;;;8EAC5D,6LAAC,8HAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,YAAY,KAAK,OAAO,CAAC,IAAI;;;;;;;;;;;;sEAIlC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAwB,KAAK,OAAO,CAAC,IAAI;;;;;;sFACvD,6LAAC,6HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAChC,KAAK,OAAO,CAAC,EAAE;;;;;;;;;;;;8EAGpB,6LAAC;oEAAE,WAAU;;wEACV,KAAK,OAAO,CAAC,MAAM;wEAAC;wEAAI,KAAK,OAAO,CAAC,IAAI;;;;;;;;;;;;;sEAI9C,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,OAAO,IAAI,KAAK,SAAS,kBAC7B,6LAAC;oEAAK,WAAU;8EACb,KAAK,SAAS,CAAC,kBAAkB;;;;;;gEAIrC,KAAK,OAAO,IAAI,KAAK,MAAM,kBAC1B,6LAAC,6HAAA,CAAA,QAAK;oEACJ,SACE,KAAK,MAAM,KAAK,YAAY,YAC5B,KAAK,MAAM,KAAK,SAAS,cAAc;oEAEzC,WAAU;8EAET,KAAK,MAAM;;;;;;gEAIf,CAAC,KAAK,OAAO,kBACZ,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,8HAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,cAAc,KAAK,OAAO,CAAC,EAAE,EAAE;4EAC9C,WAAU;sFACX;;;;;;sFAGD,6LAAC,8HAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,cAAc,KAAK,OAAO,CAAC,EAAE,EAAE;4EAC9C,WAAU;sFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CArEN,KAAK,OAAO,CAAC,EAAE;;;;;oCAgFvB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GAxRgB;KAAA", "debugId": null}}, {"offset": {"line": 4489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/scanner/settings-panel.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Switch } from \"@/components/ui/switch\"\nimport { <PERSON>lider } from \"@/components/ui/slider\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  ScannerSettings, \n  CameraDevice, \n  Subject, \n  TimePeriod, \n  ScanMode \n} from \"@/lib/types/scanner\"\nimport { \n  Settings, \n  Camera, \n  Volume2, \n  VolumeX, \n  Vibrate, \n  Clock, \n  BookOpen, \n  Timer,\n  Wifi,\n  WifiOff,\n  RotateCcw\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SettingsPanelProps {\n  settings: ScannerSettings\n  onUpdateSettings: (settings: <PERSON><PERSON><ScannerSettings>) => void\n  availableCameras: <PERSON><PERSON><PERSON><PERSON>[]\n  subjects: Subject[]\n  periods: TimePeriod[]\n  selectedSubject?: string\n  selectedPeriod?: string\n  onSubjectChange: (subject: string) => void\n  onPeriodChange: (period: string) => void\n  scanMode: ScanMode\n  onScanModeChange: (mode: ScanMode) => void\n  isOnline: boolean\n  className?: string\n}\n\nexport function SettingsPanel({\n  settings,\n  onUpdateSettings,\n  availableCameras,\n  subjects,\n  periods,\n  selectedSubject,\n  selectedPeriod,\n  onSubjectChange,\n  onPeriodChange,\n  scanMode,\n  onScanModeChange,\n  isOnline,\n  className\n}: SettingsPanelProps) {\n  const [isExpanded, setIsExpanded] = useState(false)\n\n  const handleSettingChange = (key: keyof ScannerSettings, value: any) => {\n    onUpdateSettings({ [key]: value })\n  }\n\n  const resetToDefaults = () => {\n    const defaultSettings: ScannerSettings = {\n      audioEnabled: true,\n      vibrationEnabled: true,\n      scanDelay: 1000,\n      autoAdvance: false,\n      offlineMode: false,\n      syncInterval: 5\n    }\n    onUpdateSettings(defaultSettings)\n  }\n\n  return (\n    <Card className={cn(\"w-full max-w-md mx-auto\", className)}>\n      <CardHeader className=\"pb-4\">\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <Settings className=\"h-5 w-5\" />\n            Scanner Settings\n          </CardTitle>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setIsExpanded(!isExpanded)}\n          >\n            {isExpanded ? 'Collapse' : 'Expand'}\n          </Button>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-6\">\n        {/* Scan Mode Selection */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Scan Mode</Label>\n          <div className=\"grid grid-cols-3 gap-2\">\n            {(['gate', 'subject', 'batch'] as ScanMode[]).map((mode) => (\n              <Button\n                key={mode}\n                variant={scanMode === mode ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => onScanModeChange(mode)}\n                className=\"capitalize\"\n              >\n                {mode}\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* Subject and Period Selection (for subject mode) */}\n        {scanMode === 'subject' && (\n          <div className=\"space-y-4\">\n            <Separator />\n            \n            <div className=\"space-y-2\">\n              <Label className=\"flex items-center gap-2\">\n                <BookOpen className=\"h-4 w-4\" />\n                Subject\n              </Label>\n              <Select value={selectedSubject} onValueChange={onSubjectChange}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select subject\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {subjects.map((subject) => (\n                    <SelectItem key={subject.id} value={subject.id}>\n                      <div className=\"flex flex-col\">\n                        <span>{subject.name}</span>\n                        <span className=\"text-xs text-muted-foreground\">{subject.code}</span>\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label className=\"flex items-center gap-2\">\n                <Timer className=\"h-4 w-4\" />\n                Time Period\n              </Label>\n              <Select value={selectedPeriod} onValueChange={onPeriodChange}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select period\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {periods.map((period) => (\n                    <SelectItem key={period.id} value={period.id}>\n                      <div className=\"flex flex-col\">\n                        <span>{period.name}</span>\n                        <span className=\"text-xs text-muted-foreground\">\n                          {period.startTime} - {period.endTime}\n                        </span>\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        )}\n\n        {isExpanded && (\n          <>\n            <Separator />\n\n            {/* Camera Settings */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-sm font-medium flex items-center gap-2\">\n                <Camera className=\"h-4 w-4\" />\n                Camera Settings\n              </h4>\n              \n              <div className=\"space-y-2\">\n                <Label>Camera Device</Label>\n                <Select \n                  value={settings.cameraDeviceId} \n                  onValueChange={(value) => handleSettingChange('cameraDeviceId', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select camera\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {availableCameras.map((camera) => (\n                      <SelectItem key={camera.deviceId} value={camera.deviceId}>\n                        {camera.label}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label>Scan Delay (ms)</Label>\n                <div className=\"px-3\">\n                  <Slider\n                    value={[settings.scanDelay]}\n                    onValueChange={([value]) => handleSettingChange('scanDelay', value)}\n                    max={5000}\n                    min={500}\n                    step={250}\n                    className=\"w-full\"\n                  />\n                  <div className=\"flex justify-between text-xs text-muted-foreground mt-1\">\n                    <span>500ms</span>\n                    <span>{settings.scanDelay}ms</span>\n                    <span>5000ms</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <Separator />\n\n            {/* Audio & Feedback Settings */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-sm font-medium\">Audio & Feedback</h4>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  {settings.audioEnabled ? (\n                    <Volume2 className=\"h-4 w-4\" />\n                  ) : (\n                    <VolumeX className=\"h-4 w-4\" />\n                  )}\n                  <Label>Sound Notifications</Label>\n                </div>\n                <Switch\n                  checked={settings.audioEnabled}\n                  onCheckedChange={(checked) => handleSettingChange('audioEnabled', checked)}\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <Vibrate className=\"h-4 w-4\" />\n                  <Label>Vibration</Label>\n                </div>\n                <Switch\n                  checked={settings.vibrationEnabled}\n                  onCheckedChange={(checked) => handleSettingChange('vibrationEnabled', checked)}\n                />\n              </div>\n            </div>\n\n            <Separator />\n\n            {/* Scanning Behavior */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-sm font-medium\">Scanning Behavior</h4>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-2\">\n                  <Clock className=\"h-4 w-4\" />\n                  <Label>Auto Advance (Batch Mode)</Label>\n                </div>\n                <Switch\n                  checked={settings.autoAdvance}\n                  onCheckedChange={(checked) => handleSettingChange('autoAdvance', checked)}\n                />\n              </div>\n            </div>\n\n            <Separator />\n\n            {/* Offline & Sync Settings */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-sm font-medium flex items-center gap-2\">\n                {isOnline ? (\n                  <Wifi className=\"h-4 w-4 text-green-600\" />\n                ) : (\n                  <WifiOff className=\"h-4 w-4 text-red-600\" />\n                )}\n                Offline & Sync\n              </h4>\n              \n              <div className=\"flex items-center justify-between\">\n                <Label>Offline Mode</Label>\n                <Switch\n                  checked={settings.offlineMode}\n                  onCheckedChange={(checked) => handleSettingChange('offlineMode', checked)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label>Sync Interval (minutes)</Label>\n                <div className=\"px-3\">\n                  <Slider\n                    value={[settings.syncInterval]}\n                    onValueChange={([value]) => handleSettingChange('syncInterval', value)}\n                    max={60}\n                    min={1}\n                    step={1}\n                    className=\"w-full\"\n                  />\n                  <div className=\"flex justify-between text-xs text-muted-foreground mt-1\">\n                    <span>1 min</span>\n                    <span>{settings.syncInterval} min</span>\n                    <span>60 min</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex items-center gap-2\">\n                <Badge variant={isOnline ? 'default' : 'destructive'} className=\"text-xs\">\n                  {isOnline ? 'Online' : 'Offline'}\n                </Badge>\n                {!isOnline && (\n                  <span className=\"text-xs text-muted-foreground\">\n                    Data will sync when connection is restored\n                  </span>\n                )}\n              </div>\n            </div>\n\n            <Separator />\n\n            {/* Reset Settings */}\n            <div className=\"flex justify-center\">\n              <Button variant=\"outline\" onClick={resetToDefaults} size=\"sm\">\n                <RotateCcw className=\"mr-2 h-4 w-4\" />\n                Reset to Defaults\n              </Button>\n            </div>\n          </>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AA/BA;;;;;;;;;;;;AAiDO,SAAS,cAAc,KAcT;QAdS,EAC5B,QAAQ,EACR,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,OAAO,EACP,eAAe,EACf,cAAc,EACd,eAAe,EACf,cAAc,EACd,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACR,SAAS,EACU,GAdS;;IAe5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,sBAAsB,CAAC,KAA4B;QACvD,iBAAiB;YAAE,CAAC,IAAI,EAAE;QAAM;IAClC;IAEA,MAAM,kBAAkB;QACtB,MAAM,kBAAmC;YACvC,cAAc;YACd,kBAAkB;YAClB,WAAW;YACX,aAAa;YACb,aAAa;YACb,cAAc;QAChB;QACA,iBAAiB;IACnB;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC7C,6LAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,cAAc,CAAC;sCAE7B,aAAa,aAAa;;;;;;;;;;;;;;;;;0BAKjC,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,6LAAC;gCAAI,WAAU;0CACZ,AAAC;oCAAC;oCAAQ;oCAAW;iCAAQ,CAAgB,GAAG,CAAC,CAAC,qBACjD,6LAAC,8HAAA,CAAA,SAAM;wCAEL,SAAS,aAAa,OAAO,YAAY;wCACzC,MAAK;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDAET;uCANI;;;;;;;;;;;;;;;;oBAaZ,aAAa,2BACZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iIAAA,CAAA,YAAS;;;;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAiB,eAAe;;0DAC7C,6LAAC,8HAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;0DACX,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,8HAAA,CAAA,aAAU;wDAAkB,OAAO,QAAQ,EAAE;kEAC5C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAM,QAAQ,IAAI;;;;;;8EACnB,6LAAC;oEAAK,WAAU;8EAAiC,QAAQ,IAAI;;;;;;;;;;;;uDAHhD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAWnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAgB,eAAe;;0DAC5C,6LAAC,8HAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;0DACX,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,8HAAA,CAAA,aAAU;wDAAiB,OAAO,OAAO,EAAE;kEAC1C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAM,OAAO,IAAI;;;;;;8EAClB,6LAAC;oEAAK,WAAU;;wEACb,OAAO,SAAS;wEAAC;wEAAI,OAAO,OAAO;;;;;;;;;;;;;uDAJzB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAerC,4BACC;;0CACE,6LAAC,iIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,8HAAA,CAAA,SAAM;gDACL,OAAO,SAAS,cAAc;gDAC9B,eAAe,CAAC,QAAU,oBAAoB,kBAAkB;;kEAEhE,6LAAC,8HAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,8HAAA,CAAA,gBAAa;kEACX,iBAAiB,GAAG,CAAC,CAAC,uBACrB,6LAAC,8HAAA,CAAA,aAAU;gEAAuB,OAAO,OAAO,QAAQ;0EACrD,OAAO,KAAK;+DADE,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;kDAQxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8HAAA,CAAA,SAAM;wDACL,OAAO;4DAAC,SAAS,SAAS;yDAAC;wDAC3B,eAAe;gEAAC,CAAC,MAAM;mEAAK,oBAAoB,aAAa;;wDAC7D,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM,SAAS,SAAS;oEAAC;;;;;;;0EAC1B,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMd,6LAAC,iIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,SAAS,YAAY,iBACpB,6LAAC,+MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,6LAAC,+MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEAErB,6LAAC,6HAAA,CAAA,QAAK;kEAAC;;;;;;;;;;;;0DAET,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAS,SAAS,YAAY;gDAC9B,iBAAiB,CAAC,UAAY,oBAAoB,gBAAgB;;;;;;;;;;;;kDAItE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC,6HAAA,CAAA,QAAK;kEAAC;;;;;;;;;;;;0DAET,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAS,SAAS,gBAAgB;gDAClC,iBAAiB,CAAC,UAAY,oBAAoB,oBAAoB;;;;;;;;;;;;;;;;;;0CAK5E,6LAAC,iIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC,6HAAA,CAAA,QAAK;kEAAC;;;;;;;;;;;;0DAET,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAS,SAAS,WAAW;gDAC7B,iBAAiB,CAAC,UAAY,oBAAoB,eAAe;;;;;;;;;;;;;;;;;;0CAKvE,6LAAC,iIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CACX,yBACC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAEhB,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CACnB;;;;;;;kDAIJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAS,SAAS,WAAW;gDAC7B,iBAAiB,CAAC,UAAY,oBAAoB,eAAe;;;;;;;;;;;;kDAIrE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8HAAA,CAAA,SAAM;wDACL,OAAO;4DAAC,SAAS,YAAY;yDAAC;wDAC9B,eAAe;gEAAC,CAAC,MAAM;mEAAK,oBAAoB,gBAAgB;;wDAChE,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM,SAAS,YAAY;oEAAC;;;;;;;0EAC7B,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAKZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAS,WAAW,YAAY;gDAAe,WAAU;0DAC7D,WAAW,WAAW;;;;;;4CAExB,CAAC,0BACA,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAOtD,6LAAC,iIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;oCAAiB,MAAK;;sDACvD,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD;GAlSgB;KAAA", "debugId": null}}, {"offset": {"line": 5486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils/offline-manager.ts"], "sourcesContent": ["\"use client\"\n\nimport { OfflineQueueI<PERSON>, <PERSON>ync<PERSON>tatus, <PERSON><PERSON><PERSON><PERSON>, ScanR<PERSON>ult } from \"@/lib/types/scanner\"\n\nexport class OfflineManager {\n  private static instance: OfflineManager\n  private syncQueue: OfflineQueueItem[] = []\n  private isOnline: boolean = true\n  private syncInProgress: boolean = false\n  private listeners: ((status: SyncStatus) => void)[] = []\n  private syncInterval: NodeJS.Timeout | null = null\n\n  private constructor() {\n    this.initializeOnlineDetection()\n    this.loadQueueFromStorage()\n    this.startPeriodicSync()\n  }\n\n  static getInstance(): OfflineManager {\n    if (!OfflineManager.instance) {\n      OfflineManager.instance = new OfflineManager()\n    }\n    return OfflineManager.instance\n  }\n\n  // Initialize online/offline detection\n  private initializeOnlineDetection() {\n    if (typeof window !== 'undefined') {\n      this.isOnline = navigator.onLine\n      \n      window.addEventListener('online', () => {\n        this.isOnline = true\n        this.notifyListeners()\n        this.syncQueue()\n      })\n      \n      window.addEventListener('offline', () => {\n        this.isOnline = false\n        this.notifyListeners()\n      })\n    }\n  }\n\n  // Load queue from localStorage\n  private loadQueueFromStorage() {\n    if (typeof window !== 'undefined') {\n      try {\n        const stored = localStorage.getItem('qrsams_offline_queue')\n        if (stored) {\n          const parsed = JSON.parse(stored)\n          this.syncQueue = parsed.map((item: any) => ({\n            ...item,\n            timestamp: new Date(item.timestamp),\n            lastRetry: item.lastRetry ? new Date(item.lastRetry) : undefined\n          }))\n        }\n      } catch (error) {\n        console.error('Failed to load offline queue:', error)\n        this.syncQueue = []\n      }\n    }\n  }\n\n  // Save queue to localStorage\n  private saveQueueToStorage() {\n    if (typeof window !== 'undefined') {\n      try {\n        localStorage.setItem('qrsams_offline_queue', JSON.stringify(this.syncQueue))\n      } catch (error) {\n        console.error('Failed to save offline queue:', error)\n      }\n    }\n  }\n\n  // Add item to sync queue\n  addToQueue(type: 'attendance' | 'scan', data: AttendanceRecord | ScanResult) {\n    const item: OfflineQueueItem = {\n      id: this.generateId(),\n      type,\n      data,\n      timestamp: new Date(),\n      retryCount: 0\n    }\n\n    this.syncQueue.push(item)\n    this.saveQueueToStorage()\n    this.notifyListeners()\n\n    // Try to sync immediately if online\n    if (this.isOnline) {\n      this.syncQueue()\n    }\n  }\n\n  // Sync queue with server\n  private async syncQueue() {\n    if (this.syncInProgress || !this.isOnline || this.syncQueue.length === 0) {\n      return\n    }\n\n    this.syncInProgress = true\n    this.notifyListeners()\n\n    const itemsToSync = [...this.syncQueue]\n    const successfulItems: string[] = []\n\n    for (const item of itemsToSync) {\n      try {\n        const success = await this.syncItem(item)\n        if (success) {\n          successfulItems.push(item.id)\n        } else {\n          // Increment retry count\n          item.retryCount++\n          item.lastRetry = new Date()\n          \n          // Remove items that have failed too many times\n          if (item.retryCount >= 5) {\n            console.warn(`Removing item ${item.id} after 5 failed attempts`)\n            successfulItems.push(item.id) // Remove it from queue\n          }\n        }\n      } catch (error) {\n        console.error(`Failed to sync item ${item.id}:`, error)\n        item.retryCount++\n        item.lastRetry = new Date()\n      }\n    }\n\n    // Remove successfully synced items\n    this.syncQueue = this.syncQueue.filter(item => !successfulItems.includes(item.id))\n    this.saveQueueToStorage()\n\n    this.syncInProgress = false\n    this.notifyListeners()\n  }\n\n  // Sync individual item\n  private async syncItem(item: OfflineQueueItem): Promise<boolean> {\n    try {\n      const endpoint = item.type === 'attendance' ? '/api/attendance' : '/api/scans'\n      \n      const response = await fetch(endpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...item.data,\n          offline: true,\n          originalTimestamp: item.timestamp\n        })\n      })\n\n      return response.ok\n    } catch (error) {\n      console.error('Sync request failed:', error)\n      return false\n    }\n  }\n\n  // Start periodic sync\n  private startPeriodicSync(intervalMinutes: number = 5) {\n    if (this.syncInterval) {\n      clearInterval(this.syncInterval)\n    }\n\n    this.syncInterval = setInterval(() => {\n      if (this.isOnline) {\n        this.syncQueue()\n      }\n    }, intervalMinutes * 60 * 1000)\n  }\n\n  // Update sync interval\n  updateSyncInterval(intervalMinutes: number) {\n    this.startPeriodicSync(intervalMinutes)\n  }\n\n  // Get current sync status\n  getSyncStatus(): SyncStatus {\n    return {\n      isOnline: this.isOnline,\n      lastSync: this.getLastSyncTime(),\n      pendingItems: this.syncQueue.length,\n      isSyncing: this.syncInProgress,\n      syncError: this.getLastSyncError()\n    }\n  }\n\n  // Get last sync time\n  private getLastSyncTime(): Date | undefined {\n    if (typeof window !== 'undefined') {\n      const stored = localStorage.getItem('qrsams_last_sync')\n      return stored ? new Date(stored) : undefined\n    }\n    return undefined\n  }\n\n  // Set last sync time\n  private setLastSyncTime() {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('qrsams_last_sync', new Date().toISOString())\n    }\n  }\n\n  // Get last sync error\n  private getLastSyncError(): string | undefined {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('qrsams_sync_error') || undefined\n    }\n    return undefined\n  }\n\n  // Set sync error\n  private setSyncError(error: string) {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('qrsams_sync_error', error)\n    }\n  }\n\n  // Clear sync error\n  private clearSyncError() {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('qrsams_sync_error')\n    }\n  }\n\n  // Add status listener\n  addStatusListener(listener: (status: SyncStatus) => void) {\n    this.listeners.push(listener)\n    // Immediately notify with current status\n    listener(this.getSyncStatus())\n  }\n\n  // Remove status listener\n  removeStatusListener(listener: (status: SyncStatus) => void) {\n    this.listeners = this.listeners.filter(l => l !== listener)\n  }\n\n  // Notify all listeners\n  private notifyListeners() {\n    const status = this.getSyncStatus()\n    this.listeners.forEach(listener => listener(status))\n  }\n\n  // Generate unique ID\n  private generateId(): string {\n    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n  }\n\n  // Force sync\n  async forceSync(): Promise<boolean> {\n    if (!this.isOnline) {\n      throw new Error('Cannot sync while offline')\n    }\n\n    await this.syncQueue()\n    return this.syncQueue.length === 0\n  }\n\n  // Clear queue (for testing/reset)\n  clearQueue() {\n    this.syncQueue = []\n    this.saveQueueToStorage()\n    this.notifyListeners()\n  }\n\n  // Get queue items (for debugging)\n  getQueueItems(): OfflineQueueItem[] {\n    return [...this.syncQueue]\n  }\n\n  // Cleanup\n  destroy() {\n    if (this.syncInterval) {\n      clearInterval(this.syncInterval)\n    }\n    this.listeners = []\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM;IAcX,OAAO,cAA8B;QACnC,IAAI,CAAC,eAAe,QAAQ,EAAE;YAC5B,eAAe,QAAQ,GAAG,IAAI;QAChC;QACA,OAAO,eAAe,QAAQ;IAChC;IAEA,sCAAsC;IAC9B,4BAA4B;QAClC,wCAAmC;YACjC,IAAI,CAAC,QAAQ,GAAG,UAAU,MAAM;YAEhC,OAAO,gBAAgB,CAAC,UAAU;gBAChC,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,eAAe;gBACpB,IAAI,CAAC,SAAS;YAChB;YAEA,OAAO,gBAAgB,CAAC,WAAW;gBACjC,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,eAAe;YACtB;QACF;IACF;IAEA,+BAA+B;IACvB,uBAAuB;QAC7B,wCAAmC;YACjC,IAAI;gBACF,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,IAAI,QAAQ;oBACV,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,IAAI,CAAC,SAAS,GAAG,OAAO,GAAG,CAAC,CAAC,OAAc,CAAC;4BAC1C,GAAG,IAAI;4BACP,WAAW,IAAI,KAAK,KAAK,SAAS;4BAClC,WAAW,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,IAAI;wBACzD,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,IAAI,CAAC,SAAS,GAAG,EAAE;YACrB;QACF;IACF;IAEA,6BAA6B;IACrB,qBAAqB;QAC3B,wCAAmC;YACjC,IAAI;gBACF,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC,IAAI,CAAC,SAAS;YAC5E,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;IACF;IAEA,yBAAyB;IACzB,WAAW,IAA2B,EAAE,IAAmC,EAAE;QAC3E,MAAM,OAAyB;YAC7B,IAAI,IAAI,CAAC,UAAU;YACnB;YACA;YACA,WAAW,IAAI;YACf,YAAY;QACd;QAEA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACpB,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,eAAe;QAEpB,oCAAoC;QACpC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,SAAS;QAChB;IACF;IAEA,yBAAyB;IACzB,MAAc,YAAY;QACxB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;YACxE;QACF;QAEA,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,eAAe;QAEpB,MAAM,cAAc;eAAI,IAAI,CAAC,SAAS;SAAC;QACvC,MAAM,kBAA4B,EAAE;QAEpC,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI;gBACF,MAAM,UAAU,MAAM,IAAI,CAAC,QAAQ,CAAC;gBACpC,IAAI,SAAS;oBACX,gBAAgB,IAAI,CAAC,KAAK,EAAE;gBAC9B,OAAO;oBACL,wBAAwB;oBACxB,KAAK,UAAU;oBACf,KAAK,SAAS,GAAG,IAAI;oBAErB,+CAA+C;oBAC/C,IAAI,KAAK,UAAU,IAAI,GAAG;wBACxB,QAAQ,IAAI,CAAC,AAAC,iBAAwB,OAAR,KAAK,EAAE,EAAC;wBACtC,gBAAgB,IAAI,CAAC,KAAK,EAAE,GAAE,uBAAuB;oBACvD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,AAAC,uBAA8B,OAAR,KAAK,EAAE,EAAC,MAAI;gBACjD,KAAK,UAAU;gBACf,KAAK,SAAS,GAAG,IAAI;YACvB;QACF;QAEA,mCAAmC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,gBAAgB,QAAQ,CAAC,KAAK,EAAE;QAChF,IAAI,CAAC,kBAAkB;QAEvB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,eAAe;IACtB;IAEA,uBAAuB;IACvB,MAAc,SAAS,IAAsB,EAAoB;QAC/D,IAAI;YACF,MAAM,WAAW,KAAK,IAAI,KAAK,eAAe,oBAAoB;YAElE,MAAM,WAAW,MAAM,MAAM,UAAU;gBACrC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,KAAK,IAAI;oBACZ,SAAS;oBACT,mBAAmB,KAAK,SAAS;gBACnC;YACF;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;IACF;IAEA,sBAAsB;IACd,oBAA+C;YAA7B,kBAAA,iEAA0B;QAClD,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,cAAc,IAAI,CAAC,YAAY;QACjC;QAEA,IAAI,CAAC,YAAY,GAAG,YAAY;YAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,SAAS;YAChB;QACF,GAAG,kBAAkB,KAAK;IAC5B;IAEA,uBAAuB;IACvB,mBAAmB,eAAuB,EAAE;QAC1C,IAAI,CAAC,iBAAiB,CAAC;IACzB;IAEA,0BAA0B;IAC1B,gBAA4B;QAC1B,OAAO;YACL,UAAU,IAAI,CAAC,QAAQ;YACvB,UAAU,IAAI,CAAC,eAAe;YAC9B,cAAc,IAAI,CAAC,SAAS,CAAC,MAAM;YACnC,WAAW,IAAI,CAAC,cAAc;YAC9B,WAAW,IAAI,CAAC,gBAAgB;QAClC;IACF;IAEA,qBAAqB;IACb,kBAAoC;QAC1C,wCAAmC;YACjC,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,OAAO,SAAS,IAAI,KAAK,UAAU;QACrC;;;IAEF;IAEA,qBAAqB;IACb,kBAAkB;QACxB,wCAAmC;YACjC,aAAa,OAAO,CAAC,oBAAoB,IAAI,OAAO,WAAW;QACjE;IACF;IAEA,sBAAsB;IACd,mBAAuC;QAC7C,wCAAmC;YACjC,OAAO,aAAa,OAAO,CAAC,wBAAwB;QACtD;;;IAEF;IAEA,iBAAiB;IACT,aAAa,KAAa,EAAE;QAClC,wCAAmC;YACjC,aAAa,OAAO,CAAC,qBAAqB;QAC5C;IACF;IAEA,mBAAmB;IACX,iBAAiB;QACvB,wCAAmC;YACjC,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,sBAAsB;IACtB,kBAAkB,QAAsC,EAAE;QACxD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACpB,yCAAyC;QACzC,SAAS,IAAI,CAAC,aAAa;IAC7B;IAEA,yBAAyB;IACzB,qBAAqB,QAAsC,EAAE;QAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;IACpD;IAEA,uBAAuB;IACf,kBAAkB;QACxB,MAAM,SAAS,IAAI,CAAC,aAAa;QACjC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA,WAAY,SAAS;IAC9C;IAEA,qBAAqB;IACb,aAAqB;QAC3B,OAAO,AAAC,GAAgB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAC/D;IAEA,aAAa;IACb,MAAM,YAA8B;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,IAAI,CAAC,SAAS;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK;IACnC;IAEA,kCAAkC;IAClC,aAAa;QACX,IAAI,CAAC,SAAS,GAAG,EAAE;QACnB,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,eAAe;IACtB;IAEA,kCAAkC;IAClC,gBAAoC;QAClC,OAAO;eAAI,IAAI,CAAC,SAAS;SAAC;IAC5B;IAEA,UAAU;IACV,UAAU;QACR,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,cAAc,IAAI,CAAC,YAAY;QACjC;QACA,IAAI,CAAC,SAAS,GAAG,EAAE;IACrB;IA3QA,aAAsB;QANtB,+KAAQ,aAAgC,EAAE;QAC1C,+KAAQ,YAAoB;QAC5B,+KAAQ,kBAA0B;QAClC,+KAAQ,aAA8C,EAAE;QACxD,+KAAQ,gBAAsC;QAG5C,IAAI,CAAC,yBAAyB;QAC9B,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,iBAAiB;IACxB;AAwQF;AAnRE,yKADW,gBACI,YAAf,KAAA", "debugId": null}}, {"offset": {"line": 5747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/scanner/sync-status-indicator.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { SyncStatus } from \"@/lib/types/scanner\"\nimport { OfflineManager } from \"@/lib/utils/offline-manager\"\nimport { \n  Wifi, \n  WifiOff, \n  RefreshCw, \n  CheckCircle, \n  AlertCircle, \n  Clock,\n  Database\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SyncStatusIndicatorProps {\n  className?: string\n  showDetails?: boolean\n}\n\nexport function SyncStatusIndicator({ \n  className, \n  showDetails = false \n}: SyncStatusIndicatorProps) {\n  const [syncStatus, setSyncStatus] = useState<SyncStatus>({\n    isOnline: true,\n    pendingItems: 0,\n    isSyncing: false\n  })\n  const [isExpanded, setIsExpanded] = useState(false)\n\n  useEffect(() => {\n    const offlineManager = OfflineManager.getInstance()\n    \n    const handleStatusUpdate = (status: SyncStatus) => {\n      setSyncStatus(status)\n    }\n\n    offlineManager.addStatusListener(handleStatusUpdate)\n\n    return () => {\n      offlineManager.removeStatusListener(handleStatusUpdate)\n    }\n  }, [])\n\n  const handleForceSync = async () => {\n    try {\n      const offlineManager = OfflineManager.getInstance()\n      await offlineManager.forceSync()\n    } catch (error) {\n      console.error('Force sync failed:', error)\n    }\n  }\n\n  const getStatusColor = () => {\n    if (!syncStatus.isOnline) return 'text-red-600'\n    if (syncStatus.isSyncing) return 'text-blue-600'\n    if (syncStatus.pendingItems > 0) return 'text-yellow-600'\n    return 'text-green-600'\n  }\n\n  const getStatusIcon = () => {\n    if (!syncStatus.isOnline) return <WifiOff className=\"h-4 w-4\" />\n    if (syncStatus.isSyncing) return <RefreshCw className=\"h-4 w-4 animate-spin\" />\n    if (syncStatus.pendingItems > 0) return <AlertCircle className=\"h-4 w-4\" />\n    return <CheckCircle className=\"h-4 w-4\" />\n  }\n\n  const getStatusText = () => {\n    if (!syncStatus.isOnline) return 'Offline'\n    if (syncStatus.isSyncing) return 'Syncing...'\n    if (syncStatus.pendingItems > 0) return `${syncStatus.pendingItems} pending`\n    return 'Synced'\n  }\n\n  if (!showDetails) {\n    // Compact indicator\n    return (\n      <div className={cn(\"flex items-center gap-2\", className)}>\n        <div className={cn(\"flex items-center gap-1\", getStatusColor())}>\n          {getStatusIcon()}\n          <span className=\"text-sm font-medium\">{getStatusText()}</span>\n        </div>\n        \n        {syncStatus.pendingItems > 0 && syncStatus.isOnline && (\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleForceSync}\n            disabled={syncStatus.isSyncing}\n            className=\"h-6 px-2 text-xs\"\n          >\n            Sync Now\n          </Button>\n        )}\n      </div>\n    )\n  }\n\n  // Detailed card view\n  return (\n    <Card className={cn(\"w-full max-w-sm\", className)}>\n      <CardContent className=\"p-4\">\n        <div className=\"space-y-4\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              <Database className=\"h-4 w-4 text-muted-foreground\" />\n              <span className=\"text-sm font-medium\">Sync Status</span>\n            </div>\n            \n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsExpanded(!isExpanded)}\n              className=\"h-6 px-2 text-xs\"\n            >\n              {isExpanded ? 'Less' : 'More'}\n            </Button>\n          </div>\n\n          {/* Status Overview */}\n          <div className=\"flex items-center justify-between\">\n            <div className={cn(\"flex items-center gap-2\", getStatusColor())}>\n              {getStatusIcon()}\n              <span className=\"text-sm font-medium\">{getStatusText()}</span>\n            </div>\n            \n            <div className=\"flex items-center gap-2\">\n              <Badge variant={syncStatus.isOnline ? 'default' : 'destructive'} className=\"text-xs\">\n                {syncStatus.isOnline ? 'Online' : 'Offline'}\n              </Badge>\n              \n              {syncStatus.pendingItems > 0 && (\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  {syncStatus.pendingItems} pending\n                </Badge>\n              )}\n            </div>\n          </div>\n\n          {/* Expanded Details */}\n          {isExpanded && (\n            <div className=\"space-y-3 border-t pt-3\">\n              {/* Last Sync */}\n              {syncStatus.lastSync && (\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">Last Sync:</span>\n                  <span className=\"font-medium\">\n                    {syncStatus.lastSync.toLocaleTimeString()}\n                  </span>\n                </div>\n              )}\n\n              {/* Pending Items */}\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-muted-foreground\">Pending Items:</span>\n                <span className=\"font-medium\">{syncStatus.pendingItems}</span>\n              </div>\n\n              {/* Sync Error */}\n              {syncStatus.syncError && (\n                <div className=\"p-2 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded\">\n                  <div className=\"flex items-center gap-2 text-red-600 dark:text-red-400\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    <span className=\"text-xs font-medium\">Sync Error</span>\n                  </div>\n                  <p className=\"text-xs text-red-600 dark:text-red-400 mt-1\">\n                    {syncStatus.syncError}\n                  </p>\n                </div>\n              )}\n\n              {/* Actions */}\n              <div className=\"flex gap-2\">\n                {syncStatus.pendingItems > 0 && syncStatus.isOnline && (\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleForceSync}\n                    disabled={syncStatus.isSyncing}\n                    className=\"flex-1\"\n                  >\n                    {syncStatus.isSyncing ? (\n                      <>\n                        <RefreshCw className=\"mr-2 h-3 w-3 animate-spin\" />\n                        Syncing...\n                      </>\n                    ) : (\n                      <>\n                        <RefreshCw className=\"mr-2 h-3 w-3\" />\n                        Sync Now\n                      </>\n                    )}\n                  </Button>\n                )}\n              </div>\n\n              {/* Offline Message */}\n              {!syncStatus.isOnline && (\n                <div className=\"p-2 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded\">\n                  <div className=\"flex items-center gap-2 text-yellow-600 dark:text-yellow-400\">\n                    <Clock className=\"h-4 w-4\" />\n                    <span className=\"text-xs font-medium\">Offline Mode</span>\n                  </div>\n                  <p className=\"text-xs text-yellow-600 dark:text-yellow-400 mt-1\">\n                    Data will sync automatically when connection is restored\n                  </p>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAjBA;;;;;;;;AAwBO,SAAS,oBAAoB,KAGT;QAHS,EAClC,SAAS,EACT,cAAc,KAAK,EACM,GAHS;;IAIlC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,UAAU;QACV,cAAc;QACd,WAAW;IACb;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,iBAAiB,qIAAA,CAAA,iBAAc,CAAC,WAAW;YAEjD,MAAM;oEAAqB,CAAC;oBAC1B,cAAc;gBAChB;;YAEA,eAAe,iBAAiB,CAAC;YAEjC;iDAAO;oBACL,eAAe,oBAAoB,CAAC;gBACtC;;QACF;wCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,iBAAiB,qIAAA,CAAA,iBAAc,CAAC,WAAW;YACjD,MAAM,eAAe,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,WAAW,QAAQ,EAAE,OAAO;QACjC,IAAI,WAAW,SAAS,EAAE,OAAO;QACjC,IAAI,WAAW,YAAY,GAAG,GAAG,OAAO;QACxC,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW,QAAQ,EAAE,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACpD,IAAI,WAAW,SAAS,EAAE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACtD,IAAI,WAAW,YAAY,GAAG,GAAG,qBAAO,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC/D,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW,QAAQ,EAAE,OAAO;QACjC,IAAI,WAAW,SAAS,EAAE,OAAO;QACjC,IAAI,WAAW,YAAY,GAAG,GAAG,OAAO,AAAC,GAA0B,OAAxB,WAAW,YAAY,EAAC;QACnE,OAAO;IACT;IAEA,IAAI,CAAC,aAAa;QAChB,oBAAoB;QACpB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;8BAC5C,6LAAC;oBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;wBAC3C;sCACD,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;gBAGxC,WAAW,YAAY,GAAG,KAAK,WAAW,QAAQ,kBACjD,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,UAAU,WAAW,SAAS;oBAC9B,WAAU;8BACX;;;;;;;;;;;;IAMT;IAEA,qBAAqB;IACrB,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;kBACrC,cAAA,6LAAC,4HAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,aAAa,SAAS;;;;;;;;;;;;kCAK3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;oCAC3C;kDACD,6LAAC;wCAAK,WAAU;kDAAuB;;;;;;;;;;;;0CAGzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAS,WAAW,QAAQ,GAAG,YAAY;wCAAe,WAAU;kDACxE,WAAW,QAAQ,GAAG,WAAW;;;;;;oCAGnC,WAAW,YAAY,GAAG,mBACzB,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;4CAClC,WAAW,YAAY;4CAAC;;;;;;;;;;;;;;;;;;;oBAOhC,4BACC,6LAAC;wBAAI,WAAU;;4BAEZ,WAAW,QAAQ,kBAClB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;kDACb,WAAW,QAAQ,CAAC,kBAAkB;;;;;;;;;;;;0CAM7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;kDAAe,WAAW,YAAY;;;;;;;;;;;;4BAIvD,WAAW,SAAS,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,6LAAC;wCAAE,WAAU;kDACV,WAAW,SAAS;;;;;;;;;;;;0CAM3B,6LAAC;gCAAI,WAAU;0CACZ,WAAW,YAAY,GAAG,KAAK,WAAW,QAAQ,kBACjD,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,WAAW,SAAS;oCAC9B,WAAU;8CAET,WAAW,SAAS,iBACnB;;0DACE,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA8B;;qEAIrD;;0DACE,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAS/C,CAAC,WAAW,QAAQ,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnF;GApMgB;KAAA", "debugId": null}}, {"offset": {"line": 6211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/data/mock-data.ts"], "sourcesContent": ["import { Student, Subject, TimePeriod, AttendanceRecord } from \"@/lib/types/scanner\"\n\n// Enhanced mock student data with Philippine names and Grade 7-12 structure\nexport const mockStudents: Student[] = [\n  // Grade 7 Students\n  {\n    id: \"STU001\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-A\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU001_2025\"\n  },\n  {\n    id: \"STU002\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-B\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU002_2025\"\n  },\n  {\n    id: \"STU003\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-A\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU003_2025\"\n  },\n  // Grade 8 Students\n  {\n    id: \"STU004\",\n    name: \"Jose Miguel Rodriguez\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"2nd Year\",\n    section: \"Grade 8-A\",\n    grade: \"8\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU004_2025\"\n  },\n  {\n    id: \"STU005\",\n    name: \"Princess Mae Garcia\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"2nd Year\",\n    section: \"Grade 8-B\",\n    grade: \"8\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU005_2025\"\n  },\n  // Grade 9 Students\n  {\n    id: \"STU006\",\n    name: \"Mark Anthony Villanueva\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"3rd Year\",\n    section: \"Grade 9-A\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU006_2025\"\n  },\n  {\n    id: \"STU007\",\n    name: \"Angelica Mae Torres\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"3rd Year\",\n    section: \"Grade 9-B\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU007_2025\"\n  },\n  // Grade 10 Students\n  {\n    id: \"STU008\",\n    name: \"Christian Paul Mendoza\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"4th Year\",\n    section: \"Grade 10-A\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU008_2025\"\n  },\n  {\n    id: \"STU009\",\n    name: \"Kimberly Rose Flores\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"4th Year\",\n    section: \"Grade 10-B\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU009_2025\"\n  },\n  // Grade 11 Students (Senior High School)\n  {\n    id: \"STU010\",\n    name: \"John Michael Cruz\",\n    email: \"<EMAIL>\",\n    course: \"Information and Communications Technology\",\n    year: \"1st Year Senior High\",\n    section: \"ICT 11-A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU010_2025\"\n  },\n  {\n    id: \"STU011\",\n    name: \"Mary Grace Aquino\",\n    email: \"<EMAIL>\",\n    course: \"Accountancy, Business and Management\",\n    year: \"1st Year Senior High\",\n    section: \"ABM 11-A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU011_2025\"\n  },\n  // Grade 12 Students (Senior High School)\n  {\n    id: \"STU012\",\n    name: \"Ryan James Bautista\",\n    email: \"<EMAIL>\",\n    course: \"Information and Communications Technology\",\n    year: \"2nd Year Senior High\",\n    section: \"ICT 12-A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU012_2025\"\n  },\n  {\n    id: \"STU013\",\n    name: \"Sarah Jane Morales\",\n    email: \"<EMAIL>\",\n    course: \"Humanities and Social Sciences\",\n    year: \"2nd Year Senior High\",\n    section: \"HUMSS 12-A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU013_2025\"\n  }\n]\n\n// Mock subjects\nexport const mockSubjects: Subject[] = [\n  {\n    id: \"SUBJ001\",\n    name: \"Programming Fundamentals\",\n    code: \"IT101\",\n    instructor: \"Prof. Martinez\",\n    schedule: [\n      { day: \"Monday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Wednesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Friday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ002\",\n    name: \"Database Management\",\n    code: \"IT201\",\n    instructor: \"Prof. Rodriguez\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"10:00\", endTime: \"12:00\" },\n      { day: \"Thursday\", startTime: \"10:00\", endTime: \"12:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ003\",\n    name: \"Web Development\",\n    code: \"IT301\",\n    instructor: \"Prof. Santos\",\n    schedule: [\n      { day: \"Monday\", startTime: \"13:00\", endTime: \"15:00\" },\n      { day: \"Wednesday\", startTime: \"13:00\", endTime: \"15:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ004\",\n    name: \"Data Structures\",\n    code: \"CS201\",\n    instructor: \"Prof. Reyes\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Thursday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ005\",\n    name: \"Software Engineering\",\n    code: \"CS301\",\n    instructor: \"Prof. Cruz\",\n    schedule: [\n      { day: \"Monday\", startTime: \"15:00\", endTime: \"17:00\" },\n      { day: \"Friday\", startTime: \"15:00\", endTime: \"17:00\" }\n    ]\n  }\n]\n\n// Mock time periods\nexport const mockTimePeriods: TimePeriod[] = [\n  {\n    id: \"PERIOD001\",\n    name: \"1st Period\",\n    startTime: \"08:00\",\n    endTime: \"10:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD002\",\n    name: \"2nd Period\",\n    startTime: \"10:00\",\n    endTime: \"12:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD003\",\n    name: \"3rd Period\",\n    startTime: \"13:00\",\n    endTime: \"15:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD004\",\n    name: \"4th Period\",\n    startTime: \"15:00\",\n    endTime: \"17:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD005\",\n    name: \"Evening Class\",\n    startTime: \"18:00\",\n    endTime: \"20:00\",\n    type: \"evening\"\n  }\n]\n\n// Enhanced mock attendance records with realistic patterns\nexport const mockAttendanceRecords: AttendanceRecord[] = [\n  // Today's attendance records\n  {\n    id: \"ATT001\",\n    studentId: \"STU001\",\n    studentName: \"Maria Cristina Santos\",\n    course: \"Junior High School\",\n    checkIn: \"7:45 AM\",\n    checkOut: \"4:30 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 45, 0))\n  },\n  {\n    id: \"ATT002\",\n    studentId: \"STU002\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    course: \"Junior High School\",\n    checkIn: \"7:50 AM\",\n    checkOut: \"4:25 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 50, 0))\n  },\n  {\n    id: \"ATT003\",\n    studentId: \"STU003\",\n    studentName: \"Ana Marie Reyes\",\n    course: \"Junior High School\",\n    checkIn: \"8:15 AM\",\n    checkOut: \"4:35 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(8, 15, 0))\n  },\n  {\n    id: \"ATT004\",\n    studentId: \"STU004\",\n    studentName: \"Jose Miguel Rodriguez\",\n    course: \"Junior High School\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Absent\",\n    type: \"subject\",\n    subject: \"Mathematics\",\n    period: \"1st Period\",\n    timestamp: new Date(new Date().setHours(8, 0, 0))\n  },\n  {\n    id: \"ATT005\",\n    studentId: \"STU005\",\n    studentName: \"Princess Mae Garcia\",\n    course: \"Junior High School\",\n    checkIn: \"7:55 AM\",\n    checkOut: \"4:20 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 55, 0))\n  },\n  {\n    id: \"ATT006\",\n    studentId: \"STU010\",\n    studentName: \"John Michael Cruz\",\n    course: \"Information and Communications Technology\",\n    checkIn: \"7:40 AM\",\n    checkOut: \"5:00 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 40, 0))\n  },\n  {\n    id: \"ATT007\",\n    studentId: \"STU012\",\n    studentName: \"Ryan James Bautista\",\n    course: \"Information and Communications Technology\",\n    checkIn: \"8:10 AM\",\n    checkOut: \"5:05 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(8, 10, 0))\n  }\n]\n\n// Dashboard statistics data\nexport const mockDashboardStats = {\n  totalStudents: 1234,\n  presentToday: 1105,\n  lateToday: 23,\n  absentToday: 106,\n  attendanceRate: 89.5,\n  weeklyTrend: [\n    { day: 'Mon', present: 1150, late: 15, absent: 69 },\n    { day: 'Tue', present: 1120, late: 28, absent: 86 },\n    { day: 'Wed', present: 1105, late: 23, absent: 106 },\n    { day: 'Thu', present: 1140, late: 18, absent: 76 },\n    { day: 'Fri', present: 1095, late: 35, absent: 104 }\n  ],\n  gradeBreakdown: [\n    { grade: '7', total: 180, present: 165, late: 3, absent: 12 },\n    { grade: '8', total: 175, present: 158, late: 4, absent: 13 },\n    { grade: '9', total: 170, present: 155, late: 2, absent: 13 },\n    { grade: '10', total: 165, present: 148, late: 5, absent: 12 },\n    { grade: '11', total: 272, present: 245, late: 6, absent: 21 },\n    { grade: '12', total: 272, present: 234, late: 3, absent: 35 }\n  ]\n}\n\n// Recent activity feed\nexport const mockRecentActivity = [\n  {\n    id: \"ACT001\",\n    type: \"scan\",\n    studentName: \"Maria Cristina Santos\",\n    action: \"Check In\",\n    time: \"2 minutes ago\",\n    status: \"success\"\n  },\n  {\n    id: \"ACT002\",\n    type: \"scan\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    action: \"Check Out\",\n    time: \"5 minutes ago\",\n    status: \"success\"\n  },\n  {\n    id: \"ACT003\",\n    type: \"alert\",\n    studentName: \"Jose Miguel Rodriguez\",\n    action: \"Marked Absent\",\n    time: \"15 minutes ago\",\n    status: \"warning\"\n  },\n  {\n    id: \"ACT004\",\n    type: \"scan\",\n    studentName: \"Princess Mae Garcia\",\n    action: \"Late Arrival\",\n    time: \"25 minutes ago\",\n    status: \"warning\"\n  }\n]\n\n// Helper functions\nexport function findStudentById(id: string): Student | undefined {\n  return mockStudents.find(student => student.id === id)\n}\n\nexport function findStudentByQRCode(qrCode: string): Student | undefined {\n  return mockStudents.find(student => student.qrCode === qrCode)\n}\n\nexport function findSubjectById(id: string): Subject | undefined {\n  return mockSubjects.find(subject => subject.id === id)\n}\n\nexport function findPeriodById(id: string): TimePeriod | undefined {\n  return mockTimePeriods.find(period => period.id === id)\n}\n\nexport function getStudentAttendanceRecords(studentId: string): AttendanceRecord[] {\n  return mockAttendanceRecords.filter(record => record.studentId === studentId)\n}\n\nexport function getTodayAttendanceRecord(studentId: string): AttendanceRecord | undefined {\n  const today = new Date().toISOString().split('T')[0]\n  return mockAttendanceRecords.find(record => \n    record.studentId === studentId && record.date === today\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGO,MAAM,eAA0B;IACrC,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;YACzD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;SAC1D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;CACD;AAGM,MAAM,kBAAgC;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;CACD;AAGM,MAAM,wBAA4C;IACvD,6BAA6B;IAC7B;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,SAAS;QACT,QAAQ;QACR,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,GAAG;IAChD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;CACD;AAGM,MAAM,qBAAqB;IAChC,eAAe;IACf,cAAc;IACd,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,aAAa;QACX;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAI;QACnD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAI;KACpD;IACD,gBAAgB;QACd;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC7D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC7D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;KAC9D;AACH;AAGO,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;CACD;AAGM,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,oBAAoB,MAAc;IAChD,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;AACzD;AAEO,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AACtD;AAEO,SAAS,4BAA4B,SAAiB;IAC3D,OAAO,sBAAsB,MAAM,CAAC,CAAA,SAAU,OAAO,SAAS,KAAK;AACrE;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACpD,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAChC,OAAO,SAAS,KAAK,aAAa,OAAO,IAAI,KAAK;AAEtD", "debugId": null}}, {"offset": {"line": 6755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/scanner/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useCallback } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { QRScanner } from \"@/components/scanner/qr-scanner\"\nimport { StudentInfoCard } from \"@/components/scanner/student-info-card\"\nimport { AttendanceMarking } from \"@/components/scanner/attendance-marking\"\nimport { FeedbackSystem } from \"@/components/scanner/feedback-system\"\nimport { ManualEntryDialog } from \"@/components/scanner/manual-entry-dialog\"\nimport { BatchScanner } from \"@/components/scanner/batch-scanner\"\nimport { SettingsPanel } from \"@/components/scanner/settings-panel\"\nimport { SyncStatusIndicator } from \"@/components/scanner/sync-status-indicator\"\nimport { OfflineManager } from \"@/lib/utils/offline-manager\"\nimport {\n  <PERSON>,\n  ScanResult,\n  AttendanceAction,\n  ScanMode,\n  ScannerSettings,\n  ScanNotification,\n  ManualEntry,\n  BatchSession,\n  CameraDevice,\n  AttendanceRecord\n} from \"@/lib/types/scanner\"\nimport {\n  mockStudents,\n  mockSubjects,\n  mockTimePeriods,\n  findStudentByQRCode,\n  getTodayAttendanceRecord\n} from \"@/lib/data/mock-data\"\nimport { QrCode, Camera, Settings, KeyboardIcon, Users, Wifi } from \"lucide-react\"\n\n\n\nexport default function ScannerPage() {\n  // Core scanner state\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanMode, setScanMode] = useState<ScanMode>('gate')\n  const [currentStudent, setCurrentStudent] = useState<Student | null>(null)\n  const [attendanceRecord, setAttendanceRecord] = useState<AttendanceRecord | null>(null)\n\n  // Settings and configuration\n  const [settings, setSettings] = useState<ScannerSettings>({\n    audioEnabled: true,\n    vibrationEnabled: true,\n    scanDelay: 1000,\n    autoAdvance: false,\n    offlineMode: false,\n    syncInterval: 5\n  })\n  const [selectedSubject, setSelectedSubject] = useState<string>(\"\")\n  const [selectedPeriod, setSelectedPeriod] = useState<string>(\"\")\n  const [availableCameras, setAvailableCameras] = useState<CameraDevice[]>([])\n\n  // UI state\n  const [notifications, setNotifications] = useState<ScanNotification[]>([])\n  const [showManualEntry, setShowManualEntry] = useState(false)\n  const [showSettings, setShowSettings] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n\n  // Batch scanning state\n  const [batchSession, setBatchSession] = useState<BatchSession | null>(null)\n\n  // Initialize offline manager\n  useEffect(() => {\n    const offlineManager = OfflineManager.getInstance()\n    offlineManager.updateSyncInterval(settings.syncInterval)\n  }, [settings.syncInterval])\n\n  // Add notification helper\n  const addNotification = useCallback((notification: Omit<ScanNotification, 'id' | 'timestamp'>) => {\n    const newNotification: ScanNotification = {\n      ...notification,\n      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      timestamp: new Date()\n    }\n    setNotifications(prev => [...prev, newNotification])\n  }, [])\n\n  // Remove notification\n  const removeNotification = useCallback((id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id))\n  }, [])\n\n  // Handle QR scan result\n  const handleScanResult = useCallback(async (result: ScanResult) => {\n    if (!result.success || !result.data) {\n      addNotification({\n        type: 'error',\n        title: 'Scan Failed',\n        message: result.error || 'Invalid QR code',\n        duration: 3000\n      })\n      return\n    }\n\n    setIsLoading(true)\n\n    try {\n      // Look up student by QR code\n      const response = await fetch('/api/scanner/lookup', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ qrCode: result.data })\n      })\n\n      const data = await response.json()\n\n      if (data.success && data.data) {\n        const student = data.data as Student\n        setCurrentStudent(student)\n\n        // Get today's attendance record\n        const todayRecord = getTodayAttendanceRecord(student.id)\n        setAttendanceRecord(todayRecord || null)\n\n        addNotification({\n          type: 'success',\n          title: 'Student Found',\n          message: `${student.name} (${student.id})`,\n          duration: 2000\n        })\n      } else {\n        addNotification({\n          type: 'error',\n          title: 'Student Not Found',\n          message: data.error || 'QR code not recognized',\n          duration: 3000\n        })\n      }\n    } catch (error) {\n      console.error('Lookup error:', error)\n      addNotification({\n        type: 'error',\n        title: 'Lookup Failed',\n        message: 'Failed to look up student',\n        duration: 3000\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }, [addNotification])\n\n  // Handle attendance marking\n  const handleMarkAttendance = useCallback(async (action: AttendanceAction, options?: any) => {\n    if (!currentStudent) return\n\n    setIsLoading(true)\n\n    try {\n      const attendanceData = {\n        studentId: currentStudent.id,\n        action,\n        subject: options?.subject || selectedSubject,\n        period: options?.period || selectedPeriod,\n        reason: options?.reason\n      }\n\n      const response = await fetch('/api/scanner/attendance', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(attendanceData)\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        addNotification({\n          type: 'success',\n          title: 'Attendance Recorded',\n          message: `${currentStudent.name} marked as ${action}`,\n          duration: 2000\n        })\n\n        // Update attendance record\n        setAttendanceRecord(data.data)\n\n        // Clear current student after a delay\n        setTimeout(() => {\n          setCurrentStudent(null)\n          setAttendanceRecord(null)\n        }, 2000)\n      } else {\n        throw new Error(data.error)\n      }\n    } catch (error) {\n      console.error('Attendance error:', error)\n\n      // Add to offline queue\n      const offlineManager = OfflineManager.getInstance()\n      const attendanceRecord: AttendanceRecord = {\n        id: `ATT_${Date.now()}`,\n        studentId: currentStudent.id,\n        studentName: currentStudent.name,\n        course: currentStudent.course,\n        date: new Date().toISOString().split('T')[0],\n        status: action === 'present' ? 'Present' : action === 'late' ? 'Late' : 'Absent',\n        type: scanMode === 'gate' ? 'gate' : 'subject',\n        timestamp: new Date(),\n        subject: selectedSubject,\n        period: selectedPeriod\n      }\n\n      offlineManager.addToQueue('attendance', attendanceRecord)\n\n      addNotification({\n        type: 'warning',\n        title: 'Saved Offline',\n        message: 'Attendance saved locally and will sync when online',\n        duration: 3000\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }, [currentStudent, selectedSubject, selectedPeriod, scanMode, addNotification])\n\n  // Handle manual entry\n  const handleManualEntry = useCallback(async (entry: ManualEntry) => {\n    setIsLoading(true)\n\n    try {\n      const response = await fetch('/api/scanner/attendance', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          studentId: entry.studentId,\n          action: entry.action,\n          reason: entry.reason\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        addNotification({\n          type: 'success',\n          title: 'Manual Entry Recorded',\n          message: `Attendance recorded for ${entry.studentId}`,\n          duration: 2000\n        })\n      } else {\n        throw new Error(data.error)\n      }\n    } catch (error) {\n      console.error('Manual entry error:', error)\n      addNotification({\n        type: 'error',\n        title: 'Entry Failed',\n        message: 'Failed to record manual entry',\n        duration: 3000\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }, [addNotification])\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <div className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"container flex h-16 items-center justify-between px-4\">\n          <div className=\"flex items-center gap-4\">\n            <h1 className=\"text-xl font-semibold\">QR Scanner</h1>\n            <Badge variant=\"outline\" className=\"capitalize\">\n              {scanMode} Mode\n            </Badge>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <SyncStatusIndicator />\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setShowSettings(!showSettings)}\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setShowManualEntry(true)}\n            >\n              <KeyboardIcon className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto p-4 space-y-6\">\n        <div className=\"grid gap-6 lg:grid-cols-3\">\n          {/* Left Column - Scanner */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Mode Selection */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Scanner Mode</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-3 gap-2\">\n                  <Button\n                    variant={scanMode === 'gate' ? 'default' : 'outline'}\n                    onClick={() => setScanMode('gate')}\n                    className=\"h-16 flex-col\"\n                  >\n                    <QrCode className=\"h-6 w-6 mb-1\" />\n                    <span className=\"text-sm\">Gate</span>\n                  </Button>\n                  <Button\n                    variant={scanMode === 'subject' ? 'default' : 'outline'}\n                    onClick={() => setScanMode('subject')}\n                    className=\"h-16 flex-col\"\n                  >\n                    <Camera className=\"h-6 w-6 mb-1\" />\n                    <span className=\"text-sm\">Subject</span>\n                  </Button>\n                  <Button\n                    variant={scanMode === 'batch' ? 'default' : 'outline'}\n                    onClick={() => setScanMode('batch')}\n                    className=\"h-16 flex-col\"\n                  >\n                    <Users className=\"h-6 w-6 mb-1\" />\n                    <span className=\"text-sm\">Batch</span>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Scanner Interface */}\n            {scanMode !== 'batch' && (\n              <QRScanner\n                onScanResult={handleScanResult}\n                isActive={isScanning}\n                onCameraError={(error) => addNotification({\n                  type: 'error',\n                  title: 'Camera Error',\n                  message: error,\n                  duration: 5000\n                })}\n              />\n            )}\n\n            {/* Batch Scanner */}\n            {scanMode === 'batch' && (\n              <BatchScanner\n                session={batchSession}\n                onStartSession={(students, subject, period) => {\n                  setBatchSession({\n                    id: `batch_${Date.now()}`,\n                    name: `Batch Session ${new Date().toLocaleTimeString()}`,\n                    students: students.map(student => ({\n                      student,\n                      scanned: false\n                    })),\n                    subject,\n                    period,\n                    startTime: new Date(),\n                    completedCount: 0,\n                    totalCount: students.length\n                  })\n                }}\n                onPauseSession={() => setIsScanning(false)}\n                onResumeSession={() => setIsScanning(true)}\n                onResetSession={() => setBatchSession(null)}\n                onMarkStudent={(studentId, action) => {\n                  // Handle batch student marking\n                  console.log('Mark student:', studentId, action)\n                }}\n                onExportResults={() => {\n                  // Handle export\n                  console.log('Export results')\n                }}\n                isScanning={isScanning}\n                currentStudent={currentStudent}\n              />\n            )}\n          </div>\n\n          {/* Right Column - Student Info & Controls */}\n          <div className=\"space-y-6\">\n            {/* Student Information */}\n            {currentStudent && (\n              <StudentInfoCard\n                student={currentStudent}\n                attendanceRecord={attendanceRecord}\n              />\n            )}\n\n            {/* Attendance Marking */}\n            {currentStudent && scanMode !== 'batch' && (\n              <AttendanceMarking\n                student={currentStudent}\n                mode={scanMode}\n                onMarkAttendance={handleMarkAttendance}\n                isLoading={isLoading}\n                subjects={mockSubjects}\n                periods={mockTimePeriods}\n                selectedSubject={selectedSubject}\n                selectedPeriod={selectedPeriod}\n                onSubjectChange={setSelectedSubject}\n                onPeriodChange={setSelectedPeriod}\n              />\n            )}\n\n            {/* Settings Panel */}\n            {showSettings && (\n              <SettingsPanel\n                settings={settings}\n                onUpdateSettings={(newSettings) => setSettings(prev => ({ ...prev, ...newSettings }))}\n                availableCameras={availableCameras}\n                subjects={mockSubjects}\n                periods={mockTimePeriods}\n                selectedSubject={selectedSubject}\n                selectedPeriod={selectedPeriod}\n                onSubjectChange={setSelectedSubject}\n                onPeriodChange={setSelectedPeriod}\n                scanMode={scanMode}\n                onScanModeChange={setScanMode}\n                isOnline={true}\n              />\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Feedback System */}\n      <FeedbackSystem\n        notifications={notifications}\n        onDismissNotification={removeNotification}\n        audioEnabled={settings.audioEnabled}\n        onToggleAudio={() => setSettings(prev => ({ ...prev, audioEnabled: !prev.audioEnabled }))}\n      />\n\n      {/* Manual Entry Dialog */}\n      <ManualEntryDialog\n        isOpen={showManualEntry}\n        onOpenChange={setShowManualEntry}\n        students={mockStudents}\n        onSubmitEntry={handleManualEntry}\n        isLoading={isLoading}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAaA;AAOA;AAAA;AAAA;AAAA;AAAA;;;AAlCA;;;;;;;;;;;;;;;;AAsCe,SAAS;;IACtB,qBAAqB;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAElF,6BAA6B;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,cAAc;QACd,kBAAkB;QAClB,WAAW;QACX,aAAa;QACb,aAAa;QACb,cAAc;IAChB;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAE3E,WAAW;IACX,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uBAAuB;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEtE,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,iBAAiB,qIAAA,CAAA,iBAAc,CAAC,WAAW;YACjD,eAAe,kBAAkB,CAAC,SAAS,YAAY;QACzD;gCAAG;QAAC,SAAS,YAAY;KAAC;IAE1B,0BAA0B;IAC1B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACnC,MAAM,kBAAoC;gBACxC,GAAG,YAAY;gBACf,IAAI,AAAC,SAAsB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;gBAChE,WAAW,IAAI;YACjB;YACA;4DAAiB,CAAA,OAAQ;2BAAI;wBAAM;qBAAgB;;QACrD;mDAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACtC;+DAAiB,CAAA,OAAQ,KAAK,MAAM;uEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;;QACrD;sDAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YAC1C,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE;gBACnC,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS,OAAO,KAAK,IAAI;oBACzB,UAAU;gBACZ;gBACA;YACF;YAEA,aAAa;YAEb,IAAI;gBACF,6BAA6B;gBAC7B,MAAM,WAAW,MAAM,MAAM,uBAAuB;oBAClD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,QAAQ,OAAO,IAAI;oBAAC;gBAC7C;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;oBAC7B,MAAM,UAAU,KAAK,IAAI;oBACzB,kBAAkB;oBAElB,gCAAgC;oBAChC,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ,EAAE;oBACvD,oBAAoB,eAAe;oBAEnC,gBAAgB;wBACd,MAAM;wBACN,OAAO;wBACP,SAAS,AAAC,GAAmB,OAAjB,QAAQ,IAAI,EAAC,MAAe,OAAX,QAAQ,EAAE,EAAC;wBACxC,UAAU;oBACZ;gBACF,OAAO;oBACL,gBAAgB;wBACd,MAAM;wBACN,OAAO;wBACP,SAAS,KAAK,KAAK,IAAI;wBACvB,UAAU;oBACZ;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,UAAU;gBACZ;YACF,SAAU;gBACR,aAAa;YACf;QACF;oDAAG;QAAC;KAAgB;IAEpB,4BAA4B;IAC5B,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO,QAA0B;YACxE,IAAI,CAAC,gBAAgB;YAErB,aAAa;YAEb,IAAI;gBACF,MAAM,iBAAiB;oBACrB,WAAW,eAAe,EAAE;oBAC5B;oBACA,SAAS,CAAA,oBAAA,8BAAA,QAAS,OAAO,KAAI;oBAC7B,QAAQ,CAAA,oBAAA,8BAAA,QAAS,MAAM,KAAI;oBAC3B,MAAM,EAAE,oBAAA,8BAAA,QAAS,MAAM;gBACzB;gBAEA,MAAM,WAAW,MAAM,MAAM,2BAA2B;oBACtD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB;wBACd,MAAM;wBACN,OAAO;wBACP,SAAS,AAAC,GAAmC,OAAjC,eAAe,IAAI,EAAC,eAAoB,OAAP;wBAC7C,UAAU;oBACZ;oBAEA,2BAA2B;oBAC3B,oBAAoB,KAAK,IAAI;oBAE7B,sCAAsC;oBACtC;yEAAW;4BACT,kBAAkB;4BAClB,oBAAoB;wBACtB;wEAAG;gBACL,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,KAAK;gBAC5B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;gBAEnC,uBAAuB;gBACvB,MAAM,iBAAiB,qIAAA,CAAA,iBAAc,CAAC,WAAW;gBACjD,MAAM,mBAAqC;oBACzC,IAAI,AAAC,OAAiB,OAAX,KAAK,GAAG;oBACnB,WAAW,eAAe,EAAE;oBAC5B,aAAa,eAAe,IAAI;oBAChC,QAAQ,eAAe,MAAM;oBAC7B,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5C,QAAQ,WAAW,YAAY,YAAY,WAAW,SAAS,SAAS;oBACxE,MAAM,aAAa,SAAS,SAAS;oBACrC,WAAW,IAAI;oBACf,SAAS;oBACT,QAAQ;gBACV;gBAEA,eAAe,UAAU,CAAC,cAAc;gBAExC,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,UAAU;gBACZ;YACF,SAAU;gBACR,aAAa;YACf;QACF;wDAAG;QAAC;QAAgB;QAAiB;QAAgB;QAAU;KAAgB;IAE/E,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO;YAC3C,aAAa;YAEb,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;oBACtD,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB,WAAW,MAAM,SAAS;wBAC1B,QAAQ,MAAM,MAAM;wBACpB,QAAQ,MAAM,MAAM;oBACtB;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB;wBACd,MAAM;wBACN,OAAO;wBACP,SAAS,AAAC,2BAA0C,OAAhB,MAAM,SAAS;wBACnD,UAAU;oBACZ;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,KAAK;gBAC5B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,UAAU;gBACZ;YACF,SAAU;gBACR,aAAa;YACf;QACF;qDAAG;QAAC;KAAgB;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAChC;wCAAS;;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,wJAAA,CAAA,sBAAmB;;;;;8CACpB,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,gBAAgB,CAAC;8CAEhC,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,mBAAmB;8CAElC,cAAA,6LAAC,iNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,4HAAA,CAAA,OAAI;;sDACH,6LAAC,4HAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,6LAAC,4HAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8HAAA,CAAA,SAAM;wDACL,SAAS,aAAa,SAAS,YAAY;wDAC3C,SAAS,IAAM,YAAY;wDAC3B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC,8HAAA,CAAA,SAAM;wDACL,SAAS,aAAa,YAAY,YAAY;wDAC9C,SAAS,IAAM,YAAY;wDAC3B,WAAU;;0EAEV,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC,8HAAA,CAAA,SAAM;wDACL,SAAS,aAAa,UAAU,YAAY;wDAC5C,SAAS,IAAM,YAAY;wDAC3B,WAAU;;0EAEV,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAOjC,aAAa,yBACZ,6LAAC,0IAAA,CAAA,YAAS;oCACR,cAAc;oCACd,UAAU;oCACV,eAAe,CAAC,QAAU,gBAAgB;4CACxC,MAAM;4CACN,OAAO;4CACP,SAAS;4CACT,UAAU;wCACZ;;;;;;gCAKH,aAAa,yBACZ,6LAAC,6IAAA,CAAA,eAAY;oCACX,SAAS;oCACT,gBAAgB,CAAC,UAAU,SAAS;wCAClC,gBAAgB;4CACd,IAAI,AAAC,SAAmB,OAAX,KAAK,GAAG;4CACrB,MAAM,AAAC,iBAAgD,OAAhC,IAAI,OAAO,kBAAkB;4CACpD,UAAU,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;oDACjC;oDACA,SAAS;gDACX,CAAC;4CACD;4CACA;4CACA,WAAW,IAAI;4CACf,gBAAgB;4CAChB,YAAY,SAAS,MAAM;wCAC7B;oCACF;oCACA,gBAAgB,IAAM,cAAc;oCACpC,iBAAiB,IAAM,cAAc;oCACrC,gBAAgB,IAAM,gBAAgB;oCACtC,eAAe,CAAC,WAAW;wCACzB,+BAA+B;wCAC/B,QAAQ,GAAG,CAAC,iBAAiB,WAAW;oCAC1C;oCACA,iBAAiB;wCACf,gBAAgB;wCAChB,QAAQ,GAAG,CAAC;oCACd;oCACA,YAAY;oCACZ,gBAAgB;;;;;;;;;;;;sCAMtB,6LAAC;4BAAI,WAAU;;gCAEZ,gCACC,6LAAC,oJAAA,CAAA,kBAAe;oCACd,SAAS;oCACT,kBAAkB;;;;;;gCAKrB,kBAAkB,aAAa,yBAC9B,6LAAC,kJAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,MAAM;oCACN,kBAAkB;oCAClB,WAAW;oCACX,UAAU,8HAAA,CAAA,eAAY;oCACtB,SAAS,8HAAA,CAAA,kBAAe;oCACxB,iBAAiB;oCACjB,gBAAgB;oCAChB,iBAAiB;oCACjB,gBAAgB;;;;;;gCAKnB,8BACC,6LAAC,8IAAA,CAAA,gBAAa;oCACZ,UAAU;oCACV,kBAAkB,CAAC,cAAgB,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,GAAG,WAAW;4CAAC,CAAC;oCACnF,kBAAkB;oCAClB,UAAU,8HAAA,CAAA,eAAY;oCACtB,SAAS,8HAAA,CAAA,kBAAe;oCACxB,iBAAiB;oCACjB,gBAAgB;oCAChB,iBAAiB;oCACjB,gBAAgB;oCAChB,UAAU;oCACV,kBAAkB;oCAClB,UAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC,+IAAA,CAAA,iBAAc;gBACb,eAAe;gBACf,uBAAuB;gBACvB,cAAc,SAAS,YAAY;gBACnC,eAAe,IAAM,YAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,cAAc,CAAC,KAAK,YAAY;wBAAC,CAAC;;;;;;0BAIzF,6LAAC,sJAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,cAAc;gBACd,UAAU,8HAAA,CAAA,eAAY;gBACtB,eAAe;gBACf,WAAW;;;;;;;;;;;;AAInB;GAzZwB;KAAA", "debugId": null}}]}