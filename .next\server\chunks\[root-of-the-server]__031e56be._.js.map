{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/auth/config.ts"], "sourcesContent": ["import NextAuth from \"next-auth\"\nimport Credentials from \"next-auth/providers/credentials\"\n\n// Mock user data - in production, this would come from a database\nconst users = [\n  {\n    id: \"1\",\n    email: \"<EMAIL>\",\n    password: \"admin123\",\n    name: \"Administrator\",\n    role: \"admin\"\n  },\n  {\n    id: \"2\", \n    email: \"<EMAIL>\",\n    password: \"teacher123\",\n    name: \"Teacher\",\n    role: \"teacher\"\n  }\n]\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n  providers: [\n    Credentials({\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = users.find(\n          (user) => user.email === credentials.email && user.password === credentials.password\n        )\n\n        if (user) {\n          return {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role\n          }\n        }\n\n        return null\n      }\n    })\n  ],\n  pages: {\n    signIn: \"/login\"\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  session: {\n    strategy: \"jwt\"\n  }\n})\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;;;AAEA,kEAAkE;AAClE,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,MAAM;IACR;CACD;AAEM,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,UAAQ,AAAD,EAAE;IAC1D,WAAW;QACT,CAAA,GAAA,4JAAA,CAAA,UAAW,AAAD,EAAE;YACV,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,IAAI,CACrB,CAAC,OAAS,KAAK,KAAK,KAAK,YAAY,KAAK,IAAI,KAAK,QAAQ,KAAK,YAAY,QAAQ;gBAGtF,IAAI,MAAM;oBACR,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;oBACjB;gBACF;gBAEA,OAAO;YACT;QACF;KACD;IACD,OAAO;QACL,QAAQ;IACV;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import { handlers } from \"@/lib/auth/config\"\n\nexport const { GET, POST } = handlers\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,uHAAA,CAAA,WAAQ", "debugId": null}}]}