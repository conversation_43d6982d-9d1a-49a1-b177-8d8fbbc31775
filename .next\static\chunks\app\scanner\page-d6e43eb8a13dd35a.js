(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[307],{16409:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ex});var a=t(95155),n=t(12115),r=t(88482),l=t(97168),i=t(88145),c=t(11518),d=t.n(c),o=t(76142),m=t(27755),u=t(84355),x=t(40133);function h(e){let{onScanResult:s,isActive:t,onCameraError:i}=e,c=(0,n.useRef)(null),h=(0,n.useRef)(null),j=(0,n.useRef)(null),[p,f]=(0,n.useState)(!1),[g,b]=(0,n.useState)([]),[v,N]=(0,n.useState)(""),[y,w]=(0,n.useState)(""),[S,k]=(0,n.useState)(null);(0,n.useEffect)(()=>(j.current=new o.BrowserMultiFormatReader,()=>{j.current&&j.current.reset()}),[]);let C=(0,n.useCallback)(async()=>{try{var e;let s=(await navigator.mediaDevices.enumerateDevices()).filter(e=>"videoinput"===e.kind).map(e=>({deviceId:e.deviceId,label:e.label||"Camera ".concat(e.deviceId.slice(0,8)),kind:"videoinput"}));b(s);let t=s.find(e=>e.label.toLowerCase().includes("back")||e.label.toLowerCase().includes("rear"));N((null==t?void 0:t.deviceId)||(null==(e=s[0])?void 0:e.deviceId)||"")}catch(e){console.error("Error getting cameras:",e),w("Failed to access cameras"),null==i||i("Failed to access cameras")}},[i]),A=(0,n.useCallback)(async()=>{try{(await navigator.mediaDevices.getUserMedia({video:!0})).getTracks().forEach(e=>e.stop()),k(!0),await C()}catch(e){console.error("Camera permission denied:",e),k(!1),w("Camera permission denied"),null==i||i("Camera permission denied")}},[C,i]),I=(0,n.useCallback)(async()=>{if(j.current&&v&&c.current)try{f(!0),w(""),await j.current.decodeFromVideoDevice(v,c.current,(e,t)=>{if(e){let t={success:!0,data:e.getText(),timestamp:new Date};s(t),setTimeout(()=>{},1e3)}!t||t instanceof o.NotFoundException||console.error("Scan error:",t)})}catch(e){console.error("Failed to start scanning:",e),w("Failed to start camera"),f(!1),null==i||i("Failed to start camera")}},[v,s,t,p,i]),E=(0,n.useCallback)(()=>{j.current&&j.current.reset(),f(!1)},[]),L=(0,n.useCallback)(()=>{var e;let s=(g.findIndex(e=>e.deviceId===v)+1)%g.length;N((null==(e=g[s])?void 0:e.deviceId)||"")},[g,v]);return((0,n.useEffect)(()=>{t&&S&&v&&!p?I():!t&&p&&E()},[t,S,v,p,I,E]),(0,n.useEffect)(()=>{p&&v&&(E(),setTimeout(()=>{t&&I()},100))},[v,p,t,I,E]),(0,n.useEffect)(()=>{null===S&&A()},[S,A]),!1===S)?(0,a.jsx)(r.Zp,{className:"w-full max-w-2xl mx-auto",children:(0,a.jsxs)(r.Wu,{className:"p-8 text-center",children:[(0,a.jsx)(m.A,{className:"h-16 w-16 mx-auto mb-4 text-muted-foreground"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Camera Permission Required"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please allow camera access to use the QR scanner"}),(0,a.jsxs)(l.$,{onClick:A,children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Grant Camera Access"]})]})}):(0,a.jsxs)("div",{className:"jsx-24ecc4b6f9ee0901 w-full max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"jsx-24ecc4b6f9ee0901 flex justify-between items-center mb-4",children:[(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 flex gap-2",children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:L,disabled:g.length<=1,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Switch Camera"]})}),(0,a.jsxs)("div",{className:"jsx-24ecc4b6f9ee0901 flex items-center gap-2",children:[(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 "+"w-3 h-3 rounded-full ".concat(p?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{className:"jsx-24ecc4b6f9ee0901 text-sm text-muted-foreground",children:p?"Scanning":"Stopped"})]})]}),(0,a.jsx)(r.Zp,{className:"relative overflow-hidden",children:(0,a.jsx)(r.Wu,{className:"p-0",children:(0,a.jsxs)("div",{className:"jsx-24ecc4b6f9ee0901 relative aspect-square max-w-2xl mx-auto bg-black",children:[(0,a.jsx)("video",{ref:c,playsInline:!0,muted:!0,className:"jsx-24ecc4b6f9ee0901 w-full h-full object-cover"}),(0,a.jsxs)("div",{className:"jsx-24ecc4b6f9ee0901 absolute inset-0 pointer-events-none",children:[(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"jsx-24ecc4b6f9ee0901 relative w-64 h-64 border-2 border-white/50 rounded-lg",children:[(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-primary rounded-tl-lg"}),(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-primary rounded-tr-lg"}),(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-primary rounded-bl-lg"}),(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-primary rounded-br-lg"}),p&&(0,a.jsx)("div",{style:{animation:"scan 2s linear infinite"},className:"jsx-24ecc4b6f9ee0901 absolute inset-x-0 top-0 h-1 bg-primary animate-pulse"})]})}),(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 absolute bottom-4 left-0 right-0 text-center",children:(0,a.jsx)("p",{className:"jsx-24ecc4b6f9ee0901 text-white text-lg font-medium bg-black/50 px-4 py-2 rounded-lg inline-block",children:"Position QR code within the frame"})})]}),y&&(0,a.jsx)("div",{className:"jsx-24ecc4b6f9ee0901 absolute inset-0 bg-black/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"jsx-24ecc4b6f9ee0901 text-center text-white",children:[(0,a.jsx)(m.A,{className:"h-16 w-16 mx-auto mb-4"}),(0,a.jsx)("p",{className:"jsx-24ecc4b6f9ee0901 text-lg font-medium mb-2",children:"Camera Error"}),(0,a.jsx)("p",{className:"jsx-24ecc4b6f9ee0901 text-sm opacity-75 mb-4",children:y}),(0,a.jsxs)(l.$,{variant:"secondary",onClick:A,children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Retry"]})]})})]})})}),(0,a.jsx)("canvas",{ref:h,className:"jsx-24ecc4b6f9ee0901 hidden"}),(0,a.jsx)(d(),{id:"24ecc4b6f9ee0901",children:"@keyframes scan{0%{top:0}50%{top:calc(100% - 4px)}100%{top:0}}"})]})}var j=t(69663),p=t(40646),f=t(85339),g=t(54861),b=t(14186),v=t(71007),N=t(87949),y=t(53999);function w(e){let{student:s,attendanceRecord:t,className:n,showAttendanceHistory:l=!1}=e;return(0,a.jsxs)(r.Zp,{className:(0,y.cn)("w-full max-w-md mx-auto",n),children:[(0,a.jsx)(r.aR,{className:"pb-4",children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),"Student Information"]})}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(j.eu,{className:"h-20 w-20 border-2 border-border",children:[(0,a.jsx)(j.BK,{src:s.photo,alt:s.name,className:"object-cover"}),(0,a.jsx)(j.q5,{className:"text-lg font-semibold bg-primary/10",children:s.name.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]}),(0,a.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,a.jsx)("h3",{className:"text-xl font-bold leading-tight",children:s.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:s.email}),(0,a.jsx)(i.E,{variant:"Active"===s.status?"default":"secondary",className:"text-xs",children:s.status})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Student ID"}),(0,a.jsx)("p",{className:"text-sm font-mono font-semibold",children:s.id})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Year Level"}),(0,a.jsx)("p",{className:"text-sm font-semibold",children:s.year})]}),(0,a.jsxs)("div",{className:"space-y-1 col-span-2",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Course"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("p",{className:"text-sm font-semibold",children:s.course})]})]}),s.section&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Section"}),(0,a.jsx)("p",{className:"text-sm font-semibold",children:s.section})]}),s.grade&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Grade"}),(0,a.jsx)("p",{className:"text-sm font-semibold",children:s.grade})]})]}),t&&(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold",children:"Today's Attendance"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,y.cn)("w-2 h-2 rounded-full",(e=>{switch(e){case"Present":return"bg-green-500";case"Late":return"bg-yellow-500";case"Absent":return"bg-red-500";default:return"bg-gray-500"}})(t.status))}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:new Date(t.timestamp).toLocaleTimeString()})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[t.checkIn&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Check In"}),(0,a.jsx)("p",{className:"font-semibold",children:t.checkIn})]}),t.checkOut&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Check Out"}),(0,a.jsx)("p",{className:"font-semibold",children:t.checkOut})]}),(0,a.jsxs)("div",{className:"space-y-1 col-span-2",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Status"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(e=>{switch(e){case"Present":return(0,a.jsx)(p.A,{className:"h-4 w-4"});case"Late":return(0,a.jsx)(f.A,{className:"h-4 w-4"});case"Absent":return(0,a.jsx)(g.A,{className:"h-4 w-4"});default:return(0,a.jsx)(b.A,{className:"h-4 w-4"})}})(t.status),(0,a.jsx)(i.E,{variant:"Present"===t.status?"default":"Late"===t.status?"secondary":"destructive",children:t.status})]})]}),t.subject&&(0,a.jsxs)("div",{className:"space-y-1 col-span-2",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Subject"}),(0,a.jsx)("p",{className:"text-sm font-semibold",children:t.subject})]}),t.period&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Period"}),(0,a.jsx)("p",{className:"text-sm font-semibold",children:t.period})]})]})]}),s.qrCode&&(0,a.jsx)("div",{className:"border-t pt-4",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"QR Code"}),(0,a.jsx)("p",{className:"text-xs font-mono bg-muted px-2 py-1 rounded",children:s.qrCode})]})})]})]})}var S=t(95784),k=t(76037),C=t(70306),A=t(34835),I=t(5040),E=t(46561);function L(e){var s,t;let{student:c,mode:d,onMarkAttendance:o,isLoading:m=!1,subjects:u=[],periods:x=[],selectedSubject:h,selectedPeriod:j,onSubjectChange:N,onPeriodChange:w}=e,[L,T]=(0,n.useState)(null),O=e=>{T(e);let s={};"subject"===d&&(s.subject=h,s.period=j),o(e,s),setTimeout(()=>T(null),1e3)},z=e=>{switch(e){case"check-in":return(0,a.jsx)(C.A,{className:"h-5 w-5"});case"check-out":return(0,a.jsx)(A.A,{className:"h-5 w-5"});case"present":return(0,a.jsx)(p.A,{className:"h-5 w-5"});case"late":return(0,a.jsx)(b.A,{className:"h-5 w-5"});case"absent":return(0,a.jsx)(g.A,{className:"h-5 w-5"});default:return(0,a.jsx)(f.A,{className:"h-5 w-5"})}},R=["check-in","check-out"],D=["present","late","absent"],M="gate"===d?R:D;return(0,a.jsxs)(r.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsxs)(r.aR,{className:"pb-4",children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),"Mark Attendance"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.E,{variant:"outline",className:"text-xs",children:"gate"===d?"Gate Mode":"Subject Mode"}),(0,a.jsx)(i.E,{variant:"secondary",className:"text-xs",children:c.name})]})]}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:["subject"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium flex items-center gap-2",children:[(0,a.jsx)(I.A,{className:"h-4 w-4"}),"Subject"]}),(0,a.jsxs)(S.l6,{value:h,onValueChange:N,children:[(0,a.jsx)(S.bq,{children:(0,a.jsx)(S.yv,{placeholder:"Select subject"})}),(0,a.jsx)(S.gC,{children:u.map(e=>(0,a.jsx)(S.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.code})]})},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium flex items-center gap-2",children:[(0,a.jsx)(E.A,{className:"h-4 w-4"}),"Time Period"]}),(0,a.jsxs)(S.l6,{value:j,onValueChange:w,children:[(0,a.jsx)(S.bq,{children:(0,a.jsx)(S.yv,{placeholder:"Select period"})}),(0,a.jsx)(S.gC,{children:x.map(e=>(0,a.jsx)(S.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.startTime," - ",e.endTime]})]})},e.id))})]})]}),(0,a.jsx)(k.w,{})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-muted-foreground",children:"gate"===d?"Gate Actions":"Attendance Status"}),(0,a.jsx)("div",{className:"grid gap-3",children:M.map(e=>(0,a.jsx)(l.$,{onClick:()=>O(e),disabled:m||"subject"===d&&(!h||!j),className:(0,y.cn)("h-16 text-white font-semibold text-lg transition-all duration-200",(e=>{switch(e){case"check-in":case"present":return"bg-green-500 hover:bg-green-600";case"late":return"bg-yellow-500 hover:bg-yellow-600";case"check-out":return"bg-blue-500 hover:bg-blue-600";case"absent":return"bg-red-500 hover:bg-red-600";default:return"bg-gray-500 hover:bg-gray-600"}})(e),L===e&&"scale-95 opacity-75"),size:"lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[z(e),(0,a.jsxs)("div",{className:"flex flex-col items-start",children:[(0,a.jsx)("span",{className:"capitalize",children:e.replace("-"," ")}),(0,a.jsx)("span",{className:"text-xs opacity-75",children:"gate"===d?"check-in"===e?"Enter building":"Exit building":"present"===e?"Student is present":"late"===e?"Student is late":"Mark as absent"})]})]})},e))})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-muted-foreground mb-3",children:"Manual Override"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:("gate"===d?D:R).map(e=>(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>O(e),disabled:m,className:"h-12 text-xs",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-1",children:[z(e),(0,a.jsx)("span",{className:"capitalize",children:e.replace("-"," ")})]})},e))}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:"Use manual override for special cases or corrections"})]}),"subject"===d&&(h||j)&&(0,a.jsxs)("div",{className:"bg-muted/50 p-3 rounded-lg",children:[(0,a.jsx)("h5",{className:"text-xs font-medium text-muted-foreground mb-2",children:"Current Selection"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm",children:[h&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Subject:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(s=u.find(e=>e.id===h))?void 0:s.name)||h})]}),j&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Period:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(t=x.find(e=>e.id===j))?void 0:t.name)||j})]})]})]})]})]})}var T=t(1243),O=t(81284),z=t(15273),R=t(9771),D=t(54416);function M(e){let{notifications:s,onDismissNotification:t,audioEnabled:r,onToggleAudio:i}=e,c=(0,n.useRef)(null),[d,o]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e=()=>{if(!c.current&&r)try{c.current=new(window.AudioContext||window.webkitAudioContext),o(!0)}catch(e){console.error("Failed to initialize audio context:",e)}document.removeEventListener("click",e),document.removeEventListener("touchstart",e)};return document.addEventListener("click",e),document.addEventListener("touchstart",e),()=>{document.removeEventListener("click",e),document.removeEventListener("touchstart",e)}},[r]),(0,n.useEffect)(()=>{let e=s[s.length-1];e&&r&&(e=>{if(r&&c.current&&d)try{let a=c.current,n=a.createOscillator(),r=a.createGain();n.connect(r),r.connect(a.destination);let l="success"===e?.3:"error"===e?.5:.2;if(({success:[523.25,659.25,783.99],error:[220,185,165],warning:[440,554.37],info:[523.25]})[e].forEach((s,t)=>{let n=a.createOscillator(),r=a.createGain();n.connect(r),r.connect(a.destination),n.frequency.setValueAtTime(s,a.currentTime+.1*t),n.type="error"===e?"sawtooth":"sine",r.gain.setValueAtTime(0,a.currentTime+.1*t),r.gain.linearRampToValueAtTime(.1,a.currentTime+.1*t+.05),r.gain.exponentialRampToValueAtTime(.01,a.currentTime+.1*t+l),n.start(a.currentTime+.1*t),n.stop(a.currentTime+.1*t+l)}),"vibrator"in navigator||"vibrate"in navigator){var s,t;null==(s=(t=navigator).vibrate)||s.call(t,{success:[100],error:[200,100,200],warning:[150],info:[50]}[e])}}catch(e){console.error("Failed to play sound:",e)}})(e.type)},[s,r]),(0,a.jsxs)("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:i,className:"bg-background/80 backdrop-blur-sm",children:r?(0,a.jsx)(z.A,{className:"h-4 w-4"}):(0,a.jsx)(R.A,{className:"h-4 w-4"})})}),s.map(e=>(0,a.jsx)(P,{notification:e,onDismiss:()=>t(e.id),icon:(e=>{switch(e){case"success":return(0,a.jsx)(p.A,{className:"h-5 w-5 text-green-600"});case"error":return(0,a.jsx)(g.A,{className:"h-5 w-5 text-red-600"});case"warning":return(0,a.jsx)(T.A,{className:"h-5 w-5 text-yellow-600"});case"info":return(0,a.jsx)(O.A,{className:"h-5 w-5 text-blue-600"});default:return(0,a.jsx)(O.A,{className:"h-5 w-5 text-gray-600"})}})(e.type),className:(e=>{switch(e){case"success":return"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950";case"error":return"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950";case"warning":return"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950";case"info":return"border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950";default:return"border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-950"}})(e.type)},e.id))]})}function P(e){let{notification:s,onDismiss:t,icon:i,className:c}=e,[d,o]=(0,n.useState)(!1),[m,u]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=setTimeout(()=>o(!0),10);if(s.duration){let t=setTimeout(()=>{x()},s.duration);return()=>{clearTimeout(e),clearTimeout(t)}}return()=>clearTimeout(e)},[s.duration]);let x=()=>{u(!0),setTimeout(()=>{t()},200)};return(0,a.jsx)(r.Zp,{className:(0,y.cn)("border transition-all duration-200 transform",c,d&&!m?"translate-x-0 opacity-100":"translate-x-full opacity-0",m&&"translate-x-full opacity-0"),children:(0,a.jsxs)(r.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:i}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold leading-tight",children:s.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:s.message}),s.action&&(0,a.jsx)(l.$,{variant:"link",size:"sm",onClick:s.action.handler,className:"p-0 h-auto mt-2 text-xs",children:s.action.label})]}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:x,className:"flex-shrink-0 h-6 w-6 p-0",children:(0,a.jsx)(D.A,{className:"h-3 w-3"})})]}),(0,a.jsx)("div",{className:"flex justify-between items-center mt-2 text-xs text-muted-foreground",children:(0,a.jsx)("span",{children:s.timestamp.toLocaleTimeString()})})]})})}var _=t(99840),F=t(89852),Q=t(82714),$=t(55133),q=t(47924);function Z(e){let{isOpen:s,onOpenChange:t,students:c,onSubmitEntry:d,isLoading:o=!1}=e,[m,u]=(0,n.useState)(""),[x,h]=(0,n.useState)(null),[g,b]=(0,n.useState)("present"),[N,w]=(0,n.useState)(""),S=(0,n.useMemo)(()=>{if(!m.trim())return c.slice(0,10);let e=m.toLowerCase();return c.filter(s=>s.id.toLowerCase().includes(e)||s.name.toLowerCase().includes(e)||s.email.toLowerCase().includes(e)||s.course.toLowerCase().includes(e)).slice(0,20)},[m,c]);(0,n.useEffect)(()=>{s||(u(""),h(null),b("present"),w(""))},[s]);let k=e=>e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2);return(0,a.jsx)(_.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(_.Cf,{className:"max-w-2xl max-h-[90vh] overflow-hidden flex flex-col",children:[(0,a.jsx)(_.c7,{children:(0,a.jsxs)(_.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)($.A,{className:"h-5 w-5"}),"Manual Entry"]})}),(0,a.jsxs)("div",{className:"flex-1 overflow-hidden flex flex-col space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(Q.J,{htmlFor:"search",children:"Search Student"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(q.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(F.p,{id:"search",placeholder:"Enter student ID, name, or email...",value:m,onChange:e=>u(e.target.value),className:"pl-10",autoFocus:!0})]})]}),(0,a.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:S.length>0?S.map(e=>(0,a.jsx)(r.Zp,{className:(0,y.cn)("cursor-pointer transition-colors hover:bg-muted/50",(null==x?void 0:x.id)===e.id&&"ring-2 ring-primary bg-muted/50"),onClick:()=>h(e),children:(0,a.jsx)(r.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(j.eu,{className:"h-10 w-10",children:[(0,a.jsx)(j.BK,{src:e.photo,alt:e.name}),(0,a.jsx)(j.q5,{className:"text-sm",children:k(e.name)})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h4",{className:"font-medium truncate",children:e.name}),(0,a.jsx)(i.E,{variant:"outline",className:"text-xs",children:e.id})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground truncate",children:[e.course," • ",e.year]})]}),(null==x?void 0:x.id)===e.id&&(0,a.jsx)(p.A,{className:"h-5 w-5 text-primary"})]})})},e.id)):m.trim()?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(v.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsxs)("p",{children:['No students found matching "',m,'"']})]}):(0,a.jsx)("div",{className:"text-center py-4 text-muted-foreground",children:(0,a.jsx)("p",{className:"text-sm",children:"Start typing to search for students"})})})]}),x&&(0,a.jsx)(r.Zp,{className:"border-primary/20 bg-primary/5",children:(0,a.jsx)(r.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(j.eu,{className:"h-12 w-12",children:[(0,a.jsx)(j.BK,{src:x.photo,alt:x.name}),(0,a.jsx)(j.q5,{children:k(x.name)})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold",children:x.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[x.id," • ",x.course," • ",x.year]})]}),(0,a.jsx)(i.E,{variant:"Active"===x.status?"default":"secondary",children:x.status})]})})}),x&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(Q.J,{children:"Attendance Action"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{value:"present",label:"Present",description:"Mark student as present"},{value:"late",label:"Late",description:"Mark student as late arrival"},{value:"absent",label:"Absent",description:"Mark student as absent"},{value:"check-in",label:"Check In",description:"Manual check-in entry"},{value:"check-out",label:"Check Out",description:"Manual check-out entry"}].map(e=>(0,a.jsxs)(l.$,{variant:g===e.value?"default":"outline",onClick:()=>b(e.value),className:"h-auto p-3 flex flex-col items-start",children:[(0,a.jsx)("span",{className:"font-medium",children:e.label}),(0,a.jsx)("span",{className:"text-xs opacity-75",children:e.description})]},e.value))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(Q.J,{htmlFor:"reason",children:"Reason (Optional)"}),(0,a.jsx)(F.p,{id:"reason",placeholder:"Enter reason for manual entry...",value:N,onChange:e=>w(e.target.value)})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>t(!1),children:"Cancel"}),(0,a.jsx)(l.$,{onClick:()=>{x&&(d({studentId:x.id,action:g,timestamp:new Date,reason:N.trim()||void 0}),t(!1))},disabled:!x||o,children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Submit Entry"]})})]})]})})}var J=t(41397),B=t(16891),V=t(17580),W=t(85690),G=t(82178),K=t(91788),U=t(66932);function Y(e){let{session:s,onStartSession:t,onPauseSession:c,onResumeSession:d,onResetSession:o,onMarkStudent:m,onExportResults:u,isScanning:h,currentStudent:f}=e,[v,N]=(0,n.useState)("all"),w=(null==s?void 0:s.students.filter(e=>{switch(v){case"scanned":return e.scanned;case"pending":return!e.scanned;default:return!0}}))||[],S=s?s.completedCount/s.totalCount*100:0;return s?(0,a.jsxs)("div",{className:"w-full max-w-2xl mx-auto space-y-4",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(V.A,{className:"h-5 w-5"}),s.name]}),(0,a.jsxs)(i.E,{variant:"outline",children:[s.completedCount," / ",s.totalCount]})]}),(s.subject||s.period)&&(0,a.jsxs)("div",{className:"flex gap-2 text-sm text-muted-foreground",children:[s.subject&&(0,a.jsxs)("span",{children:["Subject: ",s.subject]}),s.period&&(0,a.jsxs)("span",{children:["Period: ",s.period]})]})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[Math.round(S),"%"]})]}),(0,a.jsx)(J.k,{value:S,className:"h-2"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[h?(0,a.jsxs)(l.$,{onClick:c,variant:"outline",children:[(0,a.jsx)(G.A,{className:"mr-2 h-4 w-4"}),"Pause"]}):(0,a.jsxs)(l.$,{onClick:d,children:[(0,a.jsx)(W.A,{className:"mr-2 h-4 w-4"}),"Resume"]}),(0,a.jsxs)(l.$,{onClick:o,variant:"outline",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Reset"]}),(0,a.jsxs)(l.$,{onClick:u,variant:"outline",children:[(0,a.jsx)(K.A,{className:"mr-2 h-4 w-4"}),"Export"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{variant:"all"===v?"default":"outline",size:"sm",onClick:()=>N("all"),children:["All (",s.totalCount,")"]}),(0,a.jsxs)(l.$,{variant:"scanned"===v?"default":"outline",size:"sm",onClick:()=>N("scanned"),children:["Scanned (",s.completedCount,")"]}),(0,a.jsxs)(l.$,{variant:"pending"===v?"default":"outline",size:"sm",onClick:()=>N("pending"),children:["Pending (",s.totalCount-s.completedCount,")"]})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(U.A,{className:"h-4 w-4"}),"Students (",w.length,")"]})}),(0,a.jsx)(r.Wu,{className:"p-0",children:(0,a.jsx)(B.F,{className:"h-96",children:(0,a.jsxs)("div",{className:"space-y-2 p-4",children:[w.map((e,s)=>(0,a.jsx)(r.Zp,{className:(0,y.cn)("transition-all duration-200",(e=>{if(!e.scanned)return"bg-muted";switch(e.status){case"present":default:return"bg-green-100 border-green-200 dark:bg-green-950 dark:border-green-800";case"late":return"bg-yellow-100 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800";case"absent":return"bg-red-100 border-red-200 dark:bg-red-950 dark:border-red-800"}})(e),(null==f?void 0:f.id)===e.student.id&&"ring-2 ring-primary scale-[1.02]"),children:(0,a.jsx)(r.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-mono text-muted-foreground w-6",children:s+1}),(e=>{if(!e.scanned)return(0,a.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"});switch(e.status){case"present":default:return(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600"});case"late":return(0,a.jsx)(b.A,{className:"h-4 w-4 text-yellow-600"});case"absent":return(0,a.jsx)(g.A,{className:"h-4 w-4 text-red-600"})}})(e)]}),(0,a.jsxs)(j.eu,{className:"h-10 w-10",children:[(0,a.jsx)(j.BK,{src:e.student.photo,alt:e.student.name}),(0,a.jsx)(j.q5,{className:"text-sm",children:e.student.name.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h4",{className:"font-medium truncate",children:e.student.name}),(0,a.jsx)(i.E,{variant:"outline",className:"text-xs",children:e.student.id})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground truncate",children:[e.student.course," • ",e.student.year]})]}),(0,a.jsxs)("div",{className:"flex flex-col items-end gap-1",children:[e.scanned&&e.timestamp&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.timestamp.toLocaleTimeString()}),e.scanned&&e.status&&(0,a.jsx)(i.E,{variant:"present"===e.status?"default":"late"===e.status?"secondary":"destructive",className:"text-xs",children:e.status}),!e.scanned&&(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>m(e.student.id,"present"),className:"h-6 px-2 text-xs",children:"Present"}),(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>m(e.student.id,"absent"),className:"h-6 px-2 text-xs",children:"Absent"})]})]})]})})},e.student.id)),0===w.length&&(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(V.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No students match the current filter"})]})]})})})]})]}):(0,a.jsxs)(r.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(V.A,{className:"h-5 w-5"}),"Batch Scanner"]})}),(0,a.jsxs)(r.Wu,{className:"text-center py-8",children:[(0,a.jsx)(V.A,{className:"h-16 w-16 mx-auto mb-4 text-muted-foreground"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No Active Session"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Start a batch scanning session to process multiple students"}),(0,a.jsxs)(l.$,{onClick:()=>t([]),children:[(0,a.jsx)(W.A,{className:"mr-2 h-4 w-4"}),"Start Batch Session"]})]})]})}var H=t(4884);let X=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(H.bL,{className:(0,y.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...n,ref:s,children:(0,a.jsx)(H.zi,{className:(0,y.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});X.displayName=H.bL.displayName;var ee=t(54073);let es=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsxs)(ee.bL,{ref:s,className:(0,y.cn)("relative flex w-full touch-none select-none items-center",t),...n,children:[(0,a.jsx)(ee.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,a.jsx)(ee.Q6,{className:"absolute h-full bg-primary"})}),(0,a.jsx)(ee.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});es.displayName=ee.bL.displayName;var et=t(381),ea=t(19735),en=t(76517),er=t(94449);function el(e){let{settings:s,onUpdateSettings:t,availableCameras:c,subjects:d,periods:o,selectedSubject:m,selectedPeriod:h,onSubjectChange:j,onPeriodChange:p,scanMode:f,onScanModeChange:g,isOnline:v,className:N}=e,[w,C]=(0,n.useState)(!1),A=(e,s)=>{t({[e]:s})};return(0,a.jsxs)(r.Zp,{className:(0,y.cn)("w-full max-w-md mx-auto",N),children:[(0,a.jsx)(r.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-5 w-5"}),"Scanner Settings"]}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>C(!w),children:w?"Collapse":"Expand"})]})}),(0,a.jsxs)(r.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(Q.J,{className:"text-sm font-medium",children:"Scan Mode"}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-2",children:["gate","subject","batch"].map(e=>(0,a.jsx)(l.$,{variant:f===e?"default":"outline",size:"sm",onClick:()=>g(e),className:"capitalize",children:e},e))})]}),"subject"===f&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(k.w,{}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(Q.J,{className:"flex items-center gap-2",children:[(0,a.jsx)(I.A,{className:"h-4 w-4"}),"Subject"]}),(0,a.jsxs)(S.l6,{value:m,onValueChange:j,children:[(0,a.jsx)(S.bq,{children:(0,a.jsx)(S.yv,{placeholder:"Select subject"})}),(0,a.jsx)(S.gC,{children:d.map(e=>(0,a.jsx)(S.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.code})]})},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(Q.J,{className:"flex items-center gap-2",children:[(0,a.jsx)(E.A,{className:"h-4 w-4"}),"Time Period"]}),(0,a.jsxs)(S.l6,{value:h,onValueChange:p,children:[(0,a.jsx)(S.bq,{children:(0,a.jsx)(S.yv,{placeholder:"Select period"})}),(0,a.jsx)(S.gC,{children:o.map(e=>(0,a.jsx)(S.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.startTime," - ",e.endTime]})]})},e.id))})]})]})]}),w&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.w,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Camera Settings"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(Q.J,{children:"Camera Device"}),(0,a.jsxs)(S.l6,{value:s.cameraDeviceId,onValueChange:e=>A("cameraDeviceId",e),children:[(0,a.jsx)(S.bq,{children:(0,a.jsx)(S.yv,{placeholder:"Select camera"})}),(0,a.jsx)(S.gC,{children:c.map(e=>(0,a.jsx)(S.eb,{value:e.deviceId,children:e.label},e.deviceId))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(Q.J,{children:"Scan Delay (ms)"}),(0,a.jsxs)("div",{className:"px-3",children:[(0,a.jsx)(es,{value:[s.scanDelay],onValueChange:e=>{let[s]=e;return A("scanDelay",s)},max:5e3,min:500,step:250,className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mt-1",children:[(0,a.jsx)("span",{children:"500ms"}),(0,a.jsxs)("span",{children:[s.scanDelay,"ms"]}),(0,a.jsx)("span",{children:"5000ms"})]})]})]})]}),(0,a.jsx)(k.w,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Audio & Feedback"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[s.audioEnabled?(0,a.jsx)(z.A,{className:"h-4 w-4"}):(0,a.jsx)(R.A,{className:"h-4 w-4"}),(0,a.jsx)(Q.J,{children:"Sound Notifications"})]}),(0,a.jsx)(X,{checked:s.audioEnabled,onCheckedChange:e=>A("audioEnabled",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-4 w-4"}),(0,a.jsx)(Q.J,{children:"Vibration"})]}),(0,a.jsx)(X,{checked:s.vibrationEnabled,onCheckedChange:e=>A("vibrationEnabled",e)})]})]}),(0,a.jsx)(k.w,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Scanning Behavior"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)(Q.J,{children:"Auto Advance (Batch Mode)"})]}),(0,a.jsx)(X,{checked:s.autoAdvance,onCheckedChange:e=>A("autoAdvance",e)})]})]}),(0,a.jsx)(k.w,{}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium flex items-center gap-2",children:[v?(0,a.jsx)(en.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(er.A,{className:"h-4 w-4 text-red-600"}),"Offline & Sync"]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(Q.J,{children:"Offline Mode"}),(0,a.jsx)(X,{checked:s.offlineMode,onCheckedChange:e=>A("offlineMode",e)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(Q.J,{children:"Sync Interval (minutes)"}),(0,a.jsxs)("div",{className:"px-3",children:[(0,a.jsx)(es,{value:[s.syncInterval],onValueChange:e=>{let[s]=e;return A("syncInterval",s)},max:60,min:1,step:1,className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mt-1",children:[(0,a.jsx)("span",{children:"1 min"}),(0,a.jsxs)("span",{children:[s.syncInterval," min"]}),(0,a.jsx)("span",{children:"60 min"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.E,{variant:v?"default":"destructive",className:"text-xs",children:v?"Online":"Offline"}),!v&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Data will sync when connection is restored"})]})]}),(0,a.jsx)(k.w,{}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>{t({audioEnabled:!0,vibrationEnabled:!0,scanDelay:1e3,autoAdvance:!1,offlineMode:!1,syncInterval:5})},size:"sm",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Reset to Defaults"]})})]})]})]})}class ei{static getInstance(){return ei.instance||(ei.instance=new ei),ei.instance}initializeOnlineDetection(){this.isOnline=navigator.onLine,window.addEventListener("online",()=>{this.isOnline=!0,this.notifyListeners(),this.syncQueue()}),window.addEventListener("offline",()=>{this.isOnline=!1,this.notifyListeners()})}loadQueueFromStorage(){try{let e=localStorage.getItem("qrsams_offline_queue");if(e){let s=JSON.parse(e);this.syncQueue=s.map(e=>({...e,timestamp:new Date(e.timestamp),lastRetry:e.lastRetry?new Date(e.lastRetry):void 0}))}}catch(e){console.error("Failed to load offline queue:",e),this.syncQueue=[]}}saveQueueToStorage(){try{localStorage.setItem("qrsams_offline_queue",JSON.stringify(this.syncQueue))}catch(e){console.error("Failed to save offline queue:",e)}}addToQueue(e,s){let t={id:this.generateId(),type:e,data:s,timestamp:new Date,retryCount:0};this.syncQueue.push(t),this.saveQueueToStorage(),this.notifyListeners(),this.isOnline&&this.syncQueue()}async syncQueue(){if(this.syncInProgress||!this.isOnline||0===this.syncQueue.length)return;this.syncInProgress=!0,this.notifyListeners();let e=[...this.syncQueue],s=[];for(let t of e)try{await this.syncItem(t)?s.push(t.id):(t.retryCount++,t.lastRetry=new Date,t.retryCount>=5&&(console.warn("Removing item ".concat(t.id," after 5 failed attempts")),s.push(t.id)))}catch(e){console.error("Failed to sync item ".concat(t.id,":"),e),t.retryCount++,t.lastRetry=new Date}this.syncQueue=this.syncQueue.filter(e=>!s.includes(e.id)),this.saveQueueToStorage(),this.syncInProgress=!1,this.notifyListeners()}async syncItem(e){try{let s="attendance"===e.type?"/api/attendance":"/api/scans";return(await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e.data,offline:!0,originalTimestamp:e.timestamp})})).ok}catch(e){return console.error("Sync request failed:",e),!1}}startPeriodicSync(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;this.syncInterval&&clearInterval(this.syncInterval),this.syncInterval=setInterval(()=>{this.isOnline&&this.syncQueue()},60*e*1e3)}updateSyncInterval(e){this.startPeriodicSync(e)}getSyncStatus(){return{isOnline:this.isOnline,lastSync:this.getLastSyncTime(),pendingItems:this.syncQueue.length,isSyncing:this.syncInProgress,syncError:this.getLastSyncError()}}getLastSyncTime(){{let e=localStorage.getItem("qrsams_last_sync");return e?new Date(e):void 0}}setLastSyncTime(){localStorage.setItem("qrsams_last_sync",new Date().toISOString())}getLastSyncError(){return localStorage.getItem("qrsams_sync_error")||void 0}setSyncError(e){localStorage.setItem("qrsams_sync_error",e)}clearSyncError(){localStorage.removeItem("qrsams_sync_error")}addStatusListener(e){this.listeners.push(e),e(this.getSyncStatus())}removeStatusListener(e){this.listeners=this.listeners.filter(s=>s!==e)}notifyListeners(){let e=this.getSyncStatus();this.listeners.forEach(s=>s(e))}generateId(){return"".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9))}async forceSync(){if(!this.isOnline)throw Error("Cannot sync while offline");return await this.syncQueue(),0===this.syncQueue.length}clearQueue(){this.syncQueue=[],this.saveQueueToStorage(),this.notifyListeners()}getQueueItems(){return[...this.syncQueue]}destroy(){this.syncInterval&&clearInterval(this.syncInterval),this.listeners=[]}constructor(){this.syncQueue=[],this.isOnline=!0,this.syncInProgress=!1,this.listeners=[],this.syncInterval=null,this.initializeOnlineDetection(),this.loadQueueFromStorage(),this.startPeriodicSync()}}var ec=t(53904),ed=t(54213);function eo(e){let{className:s,showDetails:t=!1}=e,[c,d]=(0,n.useState)({isOnline:!0,pendingItems:0,isSyncing:!1}),[o,m]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=ei.getInstance(),s=e=>{d(e)};return e.addStatusListener(s),()=>{e.removeStatusListener(s)}},[]);let u=async()=>{try{let e=ei.getInstance();await e.forceSync()}catch(e){console.error("Force sync failed:",e)}},x=()=>c.isOnline?c.isSyncing?"text-blue-600":c.pendingItems>0?"text-yellow-600":"text-green-600":"text-red-600",h=()=>c.isOnline?c.isSyncing?(0,a.jsx)(ec.A,{className:"h-4 w-4 animate-spin"}):c.pendingItems>0?(0,a.jsx)(f.A,{className:"h-4 w-4"}):(0,a.jsx)(p.A,{className:"h-4 w-4"}):(0,a.jsx)(er.A,{className:"h-4 w-4"}),j=()=>c.isOnline?c.isSyncing?"Syncing...":c.pendingItems>0?"".concat(c.pendingItems," pending"):"Synced":"Offline";return t?(0,a.jsx)(r.Zp,{className:(0,y.cn)("w-full max-w-sm",s),children:(0,a.jsx)(r.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ed.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Sync Status"})]}),(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>m(!o),className:"h-6 px-2 text-xs",children:o?"Less":"More"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:(0,y.cn)("flex items-center gap-2",x()),children:[h(),(0,a.jsx)("span",{className:"text-sm font-medium",children:j()})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.E,{variant:c.isOnline?"default":"destructive",className:"text-xs",children:c.isOnline?"Online":"Offline"}),c.pendingItems>0&&(0,a.jsxs)(i.E,{variant:"secondary",className:"text-xs",children:[c.pendingItems," pending"]})]})]}),o&&(0,a.jsxs)("div",{className:"space-y-3 border-t pt-3",children:[c.lastSync&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Last Sync:"}),(0,a.jsx)("span",{className:"font-medium",children:c.lastSync.toLocaleTimeString()})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Pending Items:"}),(0,a.jsx)("span",{className:"font-medium",children:c.pendingItems})]}),c.syncError&&(0,a.jsxs)("div",{className:"p-2 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-600 dark:text-red-400",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Sync Error"})]}),(0,a.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400 mt-1",children:c.syncError})]}),(0,a.jsx)("div",{className:"flex gap-2",children:c.pendingItems>0&&c.isOnline&&(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:u,disabled:c.isSyncing,className:"flex-1",children:c.isSyncing?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec.A,{className:"mr-2 h-3 w-3 animate-spin"}),"Syncing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec.A,{className:"mr-2 h-3 w-3"}),"Sync Now"]})})}),!c.isOnline&&(0,a.jsxs)("div",{className:"p-2 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-yellow-600 dark:text-yellow-400",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Offline Mode"})]}),(0,a.jsx)("p",{className:"text-xs text-yellow-600 dark:text-yellow-400 mt-1",children:"Data will sync automatically when connection is restored"})]})]})]})})}):(0,a.jsxs)("div",{className:(0,y.cn)("flex items-center gap-2",s),children:[(0,a.jsxs)("div",{className:(0,y.cn)("flex items-center gap-1",x()),children:[h(),(0,a.jsx)("span",{className:"text-sm font-medium",children:j()})]}),c.pendingItems>0&&c.isOnline&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:u,disabled:c.isSyncing,className:"h-6 px-2 text-xs",children:"Sync Now"})]})}var em=t(74324),eu=t(97939);function ex(){let[e,s]=(0,n.useState)(!1),[t,c]=(0,n.useState)("gate"),[d,o]=(0,n.useState)(null),[m,x]=(0,n.useState)(null),[j,p]=(0,n.useState)({audioEnabled:!0,vibrationEnabled:!0,scanDelay:1e3,autoAdvance:!1,offlineMode:!1,syncInterval:5}),[f,g]=(0,n.useState)(""),[b,v]=(0,n.useState)(""),[N,y]=(0,n.useState)([]),[S,k]=(0,n.useState)([]),[C,A]=(0,n.useState)(!1),[I,E]=(0,n.useState)(!1),[T,O]=(0,n.useState)(!1),[z,R]=(0,n.useState)(null);(0,n.useEffect)(()=>{ei.getInstance().updateSyncInterval(j.syncInterval)},[j.syncInterval]);let D=(0,n.useCallback)(e=>{let s={...e,id:"notif_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),timestamp:new Date};k(e=>[...e,s])},[]),P=(0,n.useCallback)(e=>{k(s=>s.filter(s=>s.id!==e))},[]),_=(0,n.useCallback)(async e=>{if(!e.success||!e.data)return void D({type:"error",title:"Scan Failed",message:e.error||"Invalid QR code",duration:3e3});O(!0);try{let s=await fetch("/api/scanner/lookup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({qrCode:e.data})}),t=await s.json();if(t.success&&t.data){let e=t.data;o(e);let s=(0,em.o)(e.id);x(s||null),D({type:"success",title:"Student Found",message:"".concat(e.name," (").concat(e.id,")"),duration:2e3})}else D({type:"error",title:"Student Not Found",message:t.error||"QR code not recognized",duration:3e3})}catch(e){console.error("Lookup error:",e),D({type:"error",title:"Lookup Failed",message:"Failed to look up student",duration:3e3})}finally{O(!1)}},[D]),F=(0,n.useCallback)(async(e,s)=>{if(d){O(!0);try{let t={studentId:d.id,action:e,subject:(null==s?void 0:s.subject)||f,period:(null==s?void 0:s.period)||b,reason:null==s?void 0:s.reason},a=await fetch("/api/scanner/attendance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),n=await a.json();if(n.success)D({type:"success",title:"Attendance Recorded",message:"".concat(d.name," marked as ").concat(e),duration:2e3}),x(n.data),setTimeout(()=>{o(null),x(null)},2e3);else throw Error(n.error)}catch(n){console.error("Attendance error:",n);let s=ei.getInstance(),a={id:"ATT_".concat(Date.now()),studentId:d.id,studentName:d.name,course:d.course,date:new Date().toISOString().split("T")[0],status:"present"===e?"Present":"late"===e?"Late":"Absent",type:"gate"===t?"gate":"subject",timestamp:new Date,subject:f,period:b};s.addToQueue("attendance",a),D({type:"warning",title:"Saved Offline",message:"Attendance saved locally and will sync when online",duration:3e3})}finally{O(!1)}}},[d,f,b,t,D]),Q=(0,n.useCallback)(async e=>{O(!0);try{let s=await fetch("/api/scanner/attendance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:e.studentId,action:e.action,reason:e.reason})}),t=await s.json();if(t.success)D({type:"success",title:"Manual Entry Recorded",message:"Attendance recorded for ".concat(e.studentId),duration:2e3});else throw Error(t.error)}catch(e){console.error("Manual entry error:",e),D({type:"error",title:"Entry Failed",message:"Failed to record manual entry",duration:3e3})}finally{O(!1)}},[D]);return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,a.jsxs)("div",{className:"container flex h-16 items-center justify-between px-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("h1",{className:"text-xl font-semibold",children:"QR Scanner"}),(0,a.jsxs)(i.E,{variant:"outline",className:"capitalize",children:[t," Mode"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eo,{}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>E(!I),children:(0,a.jsx)(et.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>A(!0),children:(0,a.jsx)($.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsx)("div",{className:"container mx-auto p-4 space-y-6",children:(0,a.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Scanner Mode"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,a.jsxs)(l.$,{variant:"gate"===t?"default":"outline",onClick:()=>c("gate"),className:"h-16 flex-col",children:[(0,a.jsx)(eu.A,{className:"h-6 w-6 mb-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Gate"})]}),(0,a.jsxs)(l.$,{variant:"subject"===t?"default":"outline",onClick:()=>c("subject"),className:"h-16 flex-col",children:[(0,a.jsx)(u.A,{className:"h-6 w-6 mb-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Subject"})]}),(0,a.jsxs)(l.$,{variant:"batch"===t?"default":"outline",onClick:()=>c("batch"),className:"h-16 flex-col",children:[(0,a.jsx)(V.A,{className:"h-6 w-6 mb-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Batch"})]})]})})]}),"batch"!==t&&(0,a.jsx)(h,{onScanResult:_,isActive:e,onCameraError:e=>D({type:"error",title:"Camera Error",message:e,duration:5e3})}),"batch"===t&&(0,a.jsx)(Y,{session:z,onStartSession:(e,s,t)=>{R({id:"batch_".concat(Date.now()),name:"Batch Session ".concat(new Date().toLocaleTimeString()),students:e.map(e=>({student:e,scanned:!1})),subject:s,period:t,startTime:new Date,completedCount:0,totalCount:e.length})},onPauseSession:()=>s(!1),onResumeSession:()=>s(!0),onResetSession:()=>R(null),onMarkStudent:(e,s)=>{console.log("Mark student:",e,s)},onExportResults:()=>{console.log("Export results")},isScanning:e,currentStudent:d})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[d&&(0,a.jsx)(w,{student:d,attendanceRecord:m}),d&&"batch"!==t&&(0,a.jsx)(L,{student:d,mode:t,onMarkAttendance:F,isLoading:T,subjects:em.IK,periods:em.Y1,selectedSubject:f,selectedPeriod:b,onSubjectChange:g,onPeriodChange:v}),I&&(0,a.jsx)(el,{settings:j,onUpdateSettings:e=>p(s=>({...s,...e})),availableCameras:N,subjects:em.IK,periods:em.Y1,selectedSubject:f,selectedPeriod:b,onSubjectChange:g,onPeriodChange:v,scanMode:t,onScanModeChange:c,isOnline:!0})]})]})}),(0,a.jsx)(M,{notifications:S,onDismissNotification:P,audioEnabled:j.audioEnabled,onToggleAudio:()=>p(e=>({...e,audioEnabled:!e.audioEnabled}))}),(0,a.jsx)(Z,{isOpen:C,onOpenChange:A,students:em.Z9,onSubmitEntry:Q,isLoading:T})]})}},16891:(e,s,t)=>{"use strict";t.d(s,{F:()=>i});var a=t(95155),n=t(12115),r=t(47655),l=t(53999);let i=n.forwardRef((e,s)=>{let{className:t,children:n,...i}=e;return(0,a.jsxs)(r.bL,{ref:s,className:(0,l.cn)("relative overflow-hidden",t),...i,children:[(0,a.jsx)(r.LM,{className:"h-full w-full rounded-[inherit]",children:n}),(0,a.jsx)(c,{}),(0,a.jsx)(r.OK,{})]})});i.displayName=r.bL.displayName;let c=n.forwardRef((e,s)=>{let{className:t,orientation:n="vertical",...i}=e;return(0,a.jsx)(r.VM,{ref:s,orientation:n,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===n&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===n&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...i,children:(0,a.jsx)(r.lr,{className:"relative flex-1 rounded-full bg-border"})})});c.displayName=r.VM.displayName},69663:(e,s,t)=>{"use strict";t.d(s,{BK:()=>c,eu:()=>i,q5:()=>d});var a=t(95155),n=t(12115),r=t(54011),l=t(53999);let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.bL,{ref:s,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...n})});i.displayName=r.bL.displayName;let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r._V,{ref:s,className:(0,l.cn)("aspect-square h-full w-full",t),...n})});c.displayName=r._V.displayName;let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,a.jsx)(r.H4,{ref:s,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...n})});d.displayName=r.H4.displayName},76037:(e,s,t)=>{"use strict";t.d(s,{w:()=>l});var a=t(95155);t(12115);var n=t(87489),r=t(53999);function l(e){let{className:s,orientation:t="horizontal",decorative:l=!0,...i}=e;return(0,a.jsx)(n.b,{"data-slot":"separator",decorative:l,orientation:t,className:(0,r.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",s),...i})}},82714:(e,s,t)=>{"use strict";t.d(s,{J:()=>l});var a=t(95155);t(12115);var n=t(40968),r=t(53999);function l(e){let{className:s,...t}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},89852:(e,s,t)=>{"use strict";t.d(s,{p:()=>r});var a=t(95155);t(12115);var n=t(53999);function r(e){let{className:s,type:t,...r}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...r})}},95784:(e,s,t)=>{"use strict";t.d(s,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>d,yv:()=>o});var a=t(95155);t(12115);var n=t(22918),r=t(66474),l=t(5196),i=t(47863),c=t(53999);function d(e){let{...s}=e;return(0,a.jsx)(n.bL,{"data-slot":"select",...s})}function o(e){let{...s}=e;return(0,a.jsx)(n.WT,{"data-slot":"select-value",...s})}function m(e){let{className:s,size:t="default",children:l,...i}=e;return(0,a.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":t,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...i,children:[l,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:s,children:t,position:r="popper",...l}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:r,...l,children:[(0,a.jsx)(h,{}),(0,a.jsx)(n.LM,{className:(0,c.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(j,{})]})})}function x(e){let{className:s,children:t,...r}=e;return(0,a.jsxs)(n.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"size-4"})})}),(0,a.jsx)(n.p4,{children:t})]})}function h(e){let{className:s,...t}=e;return(0,a.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}function j(e){let{className:s,...t}=e;return(0,a.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(r.A,{className:"size-4"})})}},97248:(e,s,t)=>{Promise.resolve().then(t.bind(t,16409))},99840:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>m,L3:()=>x,c7:()=>u,lG:()=>i,rr:()=>h,zM:()=>c});var a=t(95155);t(12115);var n=t(15452),r=t(54416),l=t(53999);function i(e){let{...s}=e;return(0,a.jsx)(n.bL,{"data-slot":"dialog",...s})}function c(e){let{...s}=e;return(0,a.jsx)(n.l9,{"data-slot":"dialog-trigger",...s})}function d(e){let{...s}=e;return(0,a.jsx)(n.ZL,{"data-slot":"dialog-portal",...s})}function o(e){let{className:s,...t}=e;return(0,a.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...t})}function m(e){let{className:s,children:t,showCloseButton:i=!0,...c}=e;return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(o,{}),(0,a.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...c,children:[t,i&&(0,a.jsxs)(n.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(r.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",s),...t})}function x(e){let{className:s,...t}=e;return(0,a.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",s),...t})}function h(e){let{className:s,...t}=e;return(0,a.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",s),...t})}}},e=>{e.O(0,[803,550,986,679,685,750,902,441,964,358],()=>e(e.s=97248)),_N_E=e.O()}]);