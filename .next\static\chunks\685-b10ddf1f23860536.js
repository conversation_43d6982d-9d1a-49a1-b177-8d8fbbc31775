"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[685],{34835:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(19946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},47655:(e,r,t)=>{t.d(r,{LM:()=>q,OK:()=>K,VM:()=>C,bL:()=>G,lr:()=>H});var o=t(12115),n=t(63655),l=t(28905),i=t(46081),a=t(6101),s=t(39033),c=t(94315),d=t(52712),u=t(89367),p=t(85185),f=t(95155),h="ScrollArea",[v,w]=(0,i.A)(h),[g,b]=v(h),m=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:l="hover",dir:i,scrollHideDelay:s=600,...d}=e,[u,p]=o.useState(null),[h,v]=o.useState(null),[w,b]=o.useState(null),[m,S]=o.useState(null),[y,E]=o.useState(null),[C,x]=o.useState(0),[T,R]=o.useState(0),[L,P]=o.useState(!1),[j,_]=o.useState(!1),A=(0,a.s)(r,e=>p(e)),D=(0,c.jH)(i);return(0,f.jsx)(g,{scope:t,type:l,dir:D,scrollHideDelay:s,scrollArea:u,viewport:h,onViewportChange:v,content:w,onContentChange:b,scrollbarX:m,onScrollbarXChange:S,scrollbarXEnabled:L,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:E,scrollbarYEnabled:j,onScrollbarYEnabledChange:_,onCornerWidthChange:x,onCornerHeightChange:R,children:(0,f.jsx)(n.sG.div,{dir:D,...d,ref:A,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});m.displayName=h;var S="ScrollAreaViewport",y=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:l,nonce:i,...s}=e,c=b(S,t),d=o.useRef(null),u=(0,a.s)(r,d,c.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,f.jsx)(n.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:u,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});y.displayName=S;var E="ScrollAreaScrollbar",C=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=b(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return o.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,f.jsx)(x,{...n,ref:r,forceMount:t}):"scroll"===l.type?(0,f.jsx)(T,{...n,ref:r,forceMount:t}):"auto"===l.type?(0,f.jsx)(R,{...n,ref:r,forceMount:t}):"always"===l.type?(0,f.jsx)(L,{...n,ref:r}):null});C.displayName=E;var x=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,i=b(E,e.__scopeScrollArea),[a,s]=o.useState(!1);return o.useEffect(()=>{let e=i.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),s(!0)},o=()=>{r=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",o)}}},[i.scrollArea,i.scrollHideDelay]),(0,f.jsx)(l.C,{present:t||a,children:(0,f.jsx)(R,{"data-state":a?"visible":"hidden",...n,ref:r})})}),T=o.forwardRef((e,r)=>{var t;let{forceMount:n,...i}=e,a=b(E,e.__scopeScrollArea),s="horizontal"===e.orientation,c=F(()=>u("SCROLL_END"),100),[d,u]=(t={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,r)=>{let o=t[e][r];return null!=o?o:e},"hidden"));return o.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>u("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,a.scrollHideDelay,u]),o.useEffect(()=>{let e=a.viewport,r=s?"scrollLeft":"scrollTop";if(e){let t=e[r],o=()=>{let o=e[r];t!==o&&(u("SCROLL"),c()),t=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[a.viewport,s,u,c]),(0,f.jsx)(l.C,{present:n||"hidden"!==d,children:(0,f.jsx)(L,{"data-state":"hidden"===d?"hidden":"visible",...i,ref:r,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>u("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>u("POINTER_LEAVE"))})})}),R=o.forwardRef((e,r)=>{let t=b(E,e.__scopeScrollArea),{forceMount:n,...i}=e,[a,s]=o.useState(!1),c="horizontal"===e.orientation,d=F(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;s(c?e:r)}},10);return B(t.viewport,d),B(t.content,d),(0,f.jsx)(l.C,{present:n||a,children:(0,f.jsx)(L,{"data-state":a?"visible":"hidden",...i,ref:r})})}),L=o.forwardRef((e,r)=>{let{orientation:t="vertical",...n}=e,l=b(E,e.__scopeScrollArea),i=o.useRef(null),a=o.useRef(0),[s,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=M(s.viewport,s.content),u={...n,sizes:s,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,r){return function(e,r,t){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",n=X(t),l=r||n/2,i=t.scrollbar.paddingStart+l,a=t.scrollbar.size-t.scrollbar.paddingEnd-(n-l),s=t.content-t.viewport;return U([i,a],"ltr"===o?[0,s]:[-1*s,0])(e)}(e,a.current,s,r)}return"horizontal"===t?(0,f.jsx)(P,{...u,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===t?(0,f.jsx)(j,{...u,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),P=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...l}=e,i=b(E,e.__scopeScrollArea),[s,c]=o.useState(),d=o.useRef(null),u=(0,a.s)(r,d,i.onScrollbarXChange);return o.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,f.jsx)(D,{"data-orientation":"horizontal",...l,ref:u,sizes:t,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":X(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollLeft+r.deltaX;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&n({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:I(s.paddingLeft),paddingEnd:I(s.paddingRight)}})}})}),j=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...l}=e,i=b(E,e.__scopeScrollArea),[s,c]=o.useState(),d=o.useRef(null),u=(0,a.s)(r,d,i.onScrollbarYChange);return o.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,f.jsx)(D,{"data-orientation":"vertical",...l,ref:u,sizes:t,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":X(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollTop+r.deltaY;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&n({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:I(s.paddingTop),paddingEnd:I(s.paddingBottom)}})}})}),[_,A]=v(E),D=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:l,hasThumb:i,onThumbChange:c,onThumbPointerUp:d,onThumbPointerDown:u,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:w,onResize:g,...m}=e,S=b(E,t),[y,C]=o.useState(null),x=(0,a.s)(r,e=>C(e)),T=o.useRef(null),R=o.useRef(""),L=S.viewport,P=l.content-l.viewport,j=(0,s.c)(w),A=(0,s.c)(h),D=F(g,10);function N(e){T.current&&v({x:e.clientX-T.current.left,y:e.clientY-T.current.top})}return o.useEffect(()=>{let e=e=>{let r=e.target;(null==y?void 0:y.contains(r))&&j(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[L,y,P,j]),o.useEffect(A,[l,A]),B(y,D),B(S.content,D),(0,f.jsx)(_,{scope:t,scrollbar:y,hasThumb:i,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(d),onThumbPositionChange:A,onThumbPointerDown:(0,s.c)(u),children:(0,f.jsx)(n.sG.div,{...m,ref:x,style:{position:"absolute",...m.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),T.current=y.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),N(e))}),onPointerMove:(0,p.m)(e.onPointerMove,N),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,S.viewport&&(S.viewport.style.scrollBehavior=""),T.current=null})})})}),N="ScrollAreaThumb",H=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,n=A(N,e.__scopeScrollArea);return(0,f.jsx)(l.C,{present:t||n.hasThumb,children:(0,f.jsx)(k,{ref:r,...o})})}),k=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:l,...i}=e,s=b(N,t),c=A(N,t),{onThumbPositionChange:d}=c,u=(0,a.s)(r,e=>c.onThumbChange(e)),h=o.useRef(void 0),v=F(()=>{h.current&&(h.current(),h.current=void 0)},100);return o.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{v(),h.current||(h.current=V(e,d),d())};return d(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,v,d]),(0,f.jsx)(n.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...i,ref:u,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,o=e.clientY-r.top;c.onThumbPointerDown({x:t,y:o})}),onPointerUp:(0,p.m)(e.onPointerUp,c.onThumbPointerUp)})});H.displayName=N;var z="ScrollAreaCorner",W=o.forwardRef((e,r)=>{let t=b(z,e.__scopeScrollArea),o=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&o?(0,f.jsx)(O,{...e,ref:r}):null});W.displayName=z;var O=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,...l}=e,i=b(z,t),[a,s]=o.useState(0),[c,d]=o.useState(0),u=!!(a&&c);return B(i.scrollbarX,()=>{var e;let r=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(r),d(r)}),B(i.scrollbarY,()=>{var e;let r=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(r),s(r)}),u?(0,f.jsx)(n.sG.div,{...l,ref:r,style:{width:a,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function I(e){return e?parseInt(e,10):0}function M(e,r){let t=e/r;return isNaN(t)?0:t}function X(e){let r=M(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function Y(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",o=X(r),n=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-n,i=r.content-r.viewport,a=(0,u.q)(e,"ltr"===t?[0,i]:[-1*i,0]);return U([0,i],[0,l-o])(a)}function U(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let o=(r[1]-r[0])/(e[1]-e[0]);return r[0]+o*(t-e[0])}}var V=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},t={left:e.scrollLeft,top:e.scrollTop},o=0;return!function n(){let l={left:e.scrollLeft,top:e.scrollTop},i=t.left!==l.left,a=t.top!==l.top;(i||a)&&r(),t=l,o=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(o)};function F(e,r){let t=(0,s.c)(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function B(e,r){let t=(0,s.c)(r);(0,d.N)(()=>{let r=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,t])}var G=m,q=y,K=W},87949:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(19946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])}}]);