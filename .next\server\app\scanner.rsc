1:"$Sreact.fragment"
2:I[61321,["493","static/chunks/493-b2ac3368cd8d8b23.js","671","static/chunks/671-a5f23158e8e0d2d7.js","177","static/chunks/app/layout-0d3580470ff5d038.js"],"ThemeProvider"]
3:I[17236,["493","static/chunks/493-b2ac3368cd8d8b23.js","671","static/chunks/671-a5f23158e8e0d2d7.js","177","static/chunks/app/layout-0d3580470ff5d038.js"],"AuthProvider"]
4:I[87555,[],""]
5:I[31295,[],""]
6:I[56671,["493","static/chunks/493-b2ac3368cd8d8b23.js","671","static/chunks/671-a5f23158e8e0d2d7.js","177","static/chunks/app/layout-0d3580470ff5d038.js"],"Toaster"]
7:I[90894,[],"ClientPageRoot"]
8:I[16409,["803","static/chunks/803-ef5b787ac76ce444.js","550","static/chunks/550-42eebaaa1104251b.js","986","static/chunks/986-4c4782d01f8d38ae.js","679","static/chunks/679-635c38f840cced7c.js","685","static/chunks/685-b10ddf1f23860536.js","750","static/chunks/750-52f014ad0227ad4a.js","902","static/chunks/902-6f82cc6120d3b9ed.js","307","static/chunks/app/scanner/page-d6e43eb8a13dd35a.js"],"default"]
b:I[59665,[],"OutletBoundary"]
d:I[74911,[],"AsyncMetadataOutlet"]
f:I[59665,[],"ViewportBoundary"]
11:I[59665,[],"MetadataBoundary"]
12:"$Sreact.suspense"
14:I[28393,[],""]
:HL["/_next/static/css/d7e9274c71f93616.css","style"]
0:{"P":null,"b":"ZNYS9Gx8MySfyZaXqd9LQ","p":"","c":["","scanner"],"i":false,"f":[[["",{"children":["scanner",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/d7e9274c71f93616.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":[["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L6",null,{}]]}]}]}]]}],{"children":["scanner",["$","$1","c",{"children":[null,["$","div",null,{"className":"min-h-screen bg-background","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":{},"promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Lf",null,{"children":"$L10"}],null],["$","$L11",null,{"children":["$","div",null,{"hidden":true,"children":["$","$12",null,{"fallback":null,"children":"$L13"}]}]}]]}],false]],"m":"$undefined","G":["$14",[]],"s":false,"S":true}
9:{}
a:"$0:f:0:1:2:children:2:children:1:props:children:0:props:params"
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
15:I[38175,[],"IconMark"]
e:{"metadata":[["$","title","0",{"children":"QR Scanner - QRSAMS"}],["$","meta","1",{"name":"description","content":"QR Code Scanner for Student Attendance"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L15","3",{}]],"error":null,"digest":"$undefined"}
13:"$e:metadata"
