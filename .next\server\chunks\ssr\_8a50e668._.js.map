{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/attendance/attendance-filters.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Calendar } from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface AttendanceFiltersProps {\n  onFiltersChange: (filters: AttendanceFilters) => void\n  className?: string\n}\n\nexport interface AttendanceFilters {\n  dateRange: {\n    from: Date\n    to: Date\n  }\n  grade: string\n  section: string\n  course: string\n  status: string\n  searchQuery: string\n}\n\nexport function AttendanceFilters({ onFiltersChange, className }: AttendanceFiltersProps) {\n  const [filters, setFilters] = useState<AttendanceFilters>({\n    dateRange: {\n      from: new Date(),\n      to: new Date()\n    },\n    grade: \"all\",\n    section: \"all\",\n    course: \"all\",\n    status: \"all\",\n    searchQuery: \"\"\n  })\n\n  const handleFilterChange = (key: keyof AttendanceFilters, value: any) => {\n    const newFilters = { ...filters, [key]: value }\n    setFilters(newFilters)\n    onFiltersChange(newFilters)\n  }\n\n  const clearFilters = () => {\n    const defaultFilters: AttendanceFilters = {\n      dateRange: {\n        from: new Date(),\n        to: new Date()\n      },\n      grade: \"all\",\n      section: \"all\",\n      course: \"all\",\n      status: \"all\",\n      searchQuery: \"\"\n    }\n    setFilters(defaultFilters)\n    onFiltersChange(defaultFilters)\n  }\n\n  const grades = [\n    { value: \"all\", label: \"All Grades\" },\n    { value: \"7\", label: \"Grade 7\" },\n    { value: \"8\", label: \"Grade 8\" },\n    { value: \"9\", label: \"Grade 9\" },\n    { value: \"10\", label: \"Grade 10\" },\n    { value: \"11\", label: \"Grade 11\" },\n    { value: \"12\", label: \"Grade 12\" }\n  ]\n\n  const courses = [\n    { value: \"all\", label: \"All Courses\" },\n    { value: \"junior-high\", label: \"Junior High School\" },\n    { value: \"ict\", label: \"Information and Communications Technology\" },\n    { value: \"abm\", label: \"Accountancy, Business and Management\" },\n    { value: \"humss\", label: \"Humanities and Social Sciences\" },\n    { value: \"stem\", label: \"Science, Technology, Engineering and Mathematics\" }\n  ]\n\n  const statuses = [\n    { value: \"all\", label: \"All Status\" },\n    { value: \"present\", label: \"Present\" },\n    { value: \"late\", label: \"Late\" },\n    { value: \"absent\", label: \"Absent\" }\n  ]\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle className=\"text-lg\">Filters</CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Search */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"search\">Search Student</Label>\n          <Input\n            id=\"search\"\n            placeholder=\"Search by name or ID...\"\n            value={filters.searchQuery}\n            onChange={(e) => handleFilterChange(\"searchQuery\", e.target.value)}\n          />\n        </div>\n\n        {/* Date Range */}\n        <div className=\"space-y-2\">\n          <Label>Date Range</Label>\n          <div className=\"flex items-center space-x-2\">\n            <Button variant=\"outline\" size=\"sm\" className=\"justify-start text-left font-normal\">\n              <Calendar className=\"mr-2 h-4 w-4\" />\n              {format(filters.dateRange.from, \"MMM dd, yyyy\")}\n            </Button>\n            <span className=\"text-sm text-muted-foreground\">to</span>\n            <Button variant=\"outline\" size=\"sm\" className=\"justify-start text-left font-normal\">\n              <Calendar className=\"mr-2 h-4 w-4\" />\n              {format(filters.dateRange.to, \"MMM dd, yyyy\")}\n            </Button>\n          </div>\n        </div>\n\n        {/* Grade Filter */}\n        <div className=\"space-y-2\">\n          <Label>Grade Level</Label>\n          <Select value={filters.grade} onValueChange={(value) => handleFilterChange(\"grade\", value)}>\n            <SelectTrigger>\n              <SelectValue placeholder=\"Select grade\" />\n            </SelectTrigger>\n            <SelectContent>\n              {grades.map((grade) => (\n                <SelectItem key={grade.value} value={grade.value}>\n                  {grade.label}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* Course Filter */}\n        <div className=\"space-y-2\">\n          <Label>Course/Track</Label>\n          <Select value={filters.course} onValueChange={(value) => handleFilterChange(\"course\", value)}>\n            <SelectTrigger>\n              <SelectValue placeholder=\"Select course\" />\n            </SelectTrigger>\n            <SelectContent>\n              {courses.map((course) => (\n                <SelectItem key={course.value} value={course.value}>\n                  {course.label}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* Status Filter */}\n        <div className=\"space-y-2\">\n          <Label>Attendance Status</Label>\n          <Select value={filters.status} onValueChange={(value) => handleFilterChange(\"status\", value)}>\n            <SelectTrigger>\n              <SelectValue placeholder=\"Select status\" />\n            </SelectTrigger>\n            <SelectContent>\n              {statuses.map((status) => (\n                <SelectItem key={status.value} value={status.value}>\n                  {status.label}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* Active Filters */}\n        <div className=\"space-y-2\">\n          <Label>Active Filters</Label>\n          <div className=\"flex flex-wrap gap-2\">\n            {filters.grade !== \"all\" && (\n              <Badge variant=\"secondary\">\n                Grade: {grades.find(g => g.value === filters.grade)?.label}\n              </Badge>\n            )}\n            {filters.course !== \"all\" && (\n              <Badge variant=\"secondary\">\n                Course: {courses.find(c => c.value === filters.course)?.label}\n              </Badge>\n            )}\n            {filters.status !== \"all\" && (\n              <Badge variant=\"secondary\">\n                Status: {statuses.find(s => s.value === filters.status)?.label}\n              </Badge>\n            )}\n            {filters.searchQuery && (\n              <Badge variant=\"secondary\">\n                Search: {filters.searchQuery}\n              </Badge>\n            )}\n          </div>\n        </div>\n\n        {/* Clear Filters */}\n        <Button variant=\"outline\" onClick={clearFilters} className=\"w-full\">\n          Clear All Filters\n        </Button>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AA6BO,SAAS,kBAAkB,EAAE,eAAe,EAAE,SAAS,EAA0B;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QACxD,WAAW;YACT,MAAM,IAAI;YACV,IAAI,IAAI;QACV;QACA,OAAO;QACP,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,aAAa;IACf;IAEA,MAAM,qBAAqB,CAAC,KAA8B;QACxD,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;QAC9C,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,MAAM,iBAAoC;YACxC,WAAW;gBACT,MAAM,IAAI;gBACV,IAAI,IAAI;YACV;YACA,OAAO;YACP,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;QACA,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,SAAS;QACb;YAAE,OAAO;YAAO,OAAO;QAAa;QACpC;YAAE,OAAO;YAAK,OAAO;QAAU;QAC/B;YAAE,OAAO;YAAK,OAAO;QAAU;QAC/B;YAAE,OAAO;YAAK,OAAO;QAAU;QAC/B;YAAE,OAAO;YAAM,OAAO;QAAW;QACjC;YAAE,OAAO;YAAM,OAAO;QAAW;QACjC;YAAE,OAAO;YAAM,OAAO;QAAW;KAClC;IAED,MAAM,UAAU;QACd;YAAE,OAAO;YAAO,OAAO;QAAc;QACrC;YAAE,OAAO;YAAe,OAAO;QAAqB;QACpD;YAAE,OAAO;YAAO,OAAO;QAA4C;QACnE;YAAE,OAAO;YAAO,OAAO;QAAuC;QAC9D;YAAE,OAAO;YAAS,OAAO;QAAiC;QAC1D;YAAE,OAAO;YAAQ,OAAO;QAAmD;KAC5E;IAED,MAAM,WAAW;QACf;YAAE,OAAO;YAAO,OAAO;QAAa;QACpC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAU,OAAO;QAAS;KACpC;IAED,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;8BAAU;;;;;;;;;;;0BAEjC,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAS;;;;;;0CACxB,8OAAC,0HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,QAAQ,WAAW;gCAC1B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kCAKrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;;0DAC5C,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,SAAS,CAAC,IAAI,EAAE;;;;;;;kDAElC,8OAAC;wCAAK,WAAU;kDAAgC;;;;;;kDAChD,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;;0DAC5C,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,SAAS,CAAC,EAAE,EAAE;;;;;;;;;;;;;;;;;;;kCAMpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO,QAAQ,KAAK;gCAAE,eAAe,CAAC,QAAU,mBAAmB,SAAS;;kDAClF,8OAAC,2HAAA,CAAA,gBAAa;kDACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,2HAAA,CAAA,gBAAa;kDACX,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,2HAAA,CAAA,aAAU;gDAAmB,OAAO,MAAM,KAAK;0DAC7C,MAAM,KAAK;+CADG,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;kCASpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO,QAAQ,MAAM;gCAAE,eAAe,CAAC,QAAU,mBAAmB,UAAU;;kDACpF,8OAAC,2HAAA,CAAA,gBAAa;kDACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,2HAAA,CAAA,gBAAa;kDACX,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,2HAAA,CAAA,aAAU;gDAAoB,OAAO,OAAO,KAAK;0DAC/C,OAAO,KAAK;+CADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;kCASrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO,QAAQ,MAAM;gCAAE,eAAe,CAAC,QAAU,mBAAmB,UAAU;;kDACpF,8OAAC,2HAAA,CAAA,gBAAa;kDACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,2HAAA,CAAA,gBAAa;kDACX,SAAS,GAAG,CAAC,CAAC,uBACb,8OAAC,2HAAA,CAAA,aAAU;gDAAoB,OAAO,OAAO,KAAK;0DAC/C,OAAO,KAAK;+CADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;kCASrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC;gCAAI,WAAU;;oCACZ,QAAQ,KAAK,KAAK,uBACjB,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAY;4CACjB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,KAAK,GAAG;;;;;;;oCAGxD,QAAQ,MAAM,KAAK,uBAClB,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAY;4CAChB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,MAAM,GAAG;;;;;;;oCAG3D,QAAQ,MAAM,KAAK,uBAClB,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAY;4CAChB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,MAAM,GAAG;;;;;;;oCAG5D,QAAQ,WAAW,kBAClB,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAY;4CAChB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;kCAOpC,8OAAC,2HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAc,WAAU;kCAAS;;;;;;;;;;;;;;;;;;AAM5E", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,qMAAA,CAAA,aAAgB,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/attendance/attendance-grid.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { \n  Edit, \n  Clock, \n  CheckCircle, \n  XCircle, \n  AlertCircle,\n  User,\n  Calendar,\n  MessageSquare\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface AttendanceRecord {\n  id: string\n  studentId: string\n  studentName: string\n  course: string\n  grade: string\n  section: string\n  checkIn?: string\n  checkOut?: string\n  date: string\n  status: \"Present\" | \"Late\" | \"Absent\"\n  type: \"gate\" | \"subject\"\n  subject?: string\n  period?: string\n  reason?: string\n  photo?: string\n}\n\ninterface AttendanceGridProps {\n  records: AttendanceRecord[]\n  onUpdateRecord: (recordId: string, updates: Partial<AttendanceRecord>) => void\n  className?: string\n}\n\nexport function AttendanceGrid({ records, onUpdateRecord, className }: AttendanceGridProps) {\n  const [editingRecord, setEditingRecord] = useState<AttendanceRecord | null>(null)\n  const [editForm, setEditForm] = useState({\n    status: \"\",\n    checkIn: \"\",\n    checkOut: \"\",\n    reason: \"\"\n  })\n\n  const handleEditRecord = (record: AttendanceRecord) => {\n    setEditingRecord(record)\n    setEditForm({\n      status: record.status,\n      checkIn: record.checkIn || \"\",\n      checkOut: record.checkOut || \"\",\n      reason: record.reason || \"\"\n    })\n  }\n\n  const handleSaveEdit = () => {\n    if (!editingRecord) return\n\n    onUpdateRecord(editingRecord.id, {\n      status: editForm.status as \"Present\" | \"Late\" | \"Absent\",\n      checkIn: editForm.checkIn || undefined,\n      checkOut: editForm.checkOut || undefined,\n      reason: editForm.reason || undefined\n    })\n\n    setEditingRecord(null)\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case \"Present\":\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case \"Late\":\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />\n      case \"Absent\":\n        return <XCircle className=\"h-4 w-4 text-red-500\" />\n      default:\n        return <AlertCircle className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getStatusBadge = (status: string) => {\n    const variant = status === \"Present\" ? \"default\" : \n                   status === \"Late\" ? \"secondary\" : \"destructive\"\n    return <Badge variant={variant}>{status}</Badge>\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center justify-between\">\n          <span>Attendance Records</span>\n          <Badge variant=\"outline\">{records.length} records</Badge>\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        <div className=\"rounded-md border\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Student</TableHead>\n                <TableHead>Grade/Section</TableHead>\n                <TableHead>Check In</TableHead>\n                <TableHead>Check Out</TableHead>\n                <TableHead>Status</TableHead>\n                <TableHead>Subject/Period</TableHead>\n                <TableHead className=\"text-right\">Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {records.map((record) => (\n                <TableRow key={record.id}>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-3\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarImage src={record.photo} alt={record.studentName} />\n                        <AvatarFallback>\n                          <User className=\"h-4 w-4\" />\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <div className=\"font-medium\">{record.studentName}</div>\n                        <div className=\"text-sm text-muted-foreground\">{record.studentId}</div>\n                      </div>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <div>\n                      <div className=\"font-medium\">Grade {record.grade}</div>\n                      <div className=\"text-sm text-muted-foreground\">{record.section}</div>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-2\">\n                      {record.checkIn ? (\n                        <>\n                          <Clock className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>{record.checkIn}</span>\n                        </>\n                      ) : (\n                        <span className=\"text-muted-foreground\">-</span>\n                      )}\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-2\">\n                      {record.checkOut ? (\n                        <>\n                          <Clock className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>{record.checkOut}</span>\n                        </>\n                      ) : (\n                        <span className=\"text-muted-foreground\">-</span>\n                      )}\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-2\">\n                      {getStatusIcon(record.status)}\n                      {getStatusBadge(record.status)}\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    {record.subject ? (\n                      <div>\n                        <div className=\"font-medium\">{record.subject}</div>\n                        <div className=\"text-sm text-muted-foreground\">{record.period}</div>\n                      </div>\n                    ) : (\n                      <Badge variant=\"outline\">Gate Entry</Badge>\n                    )}\n                  </TableCell>\n                  <TableCell className=\"text-right\">\n                    <Dialog>\n                      <DialogTrigger asChild>\n                        <Button \n                          variant=\"ghost\" \n                          size=\"sm\"\n                          onClick={() => handleEditRecord(record)}\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                      </DialogTrigger>\n                      <DialogContent>\n                        <DialogHeader>\n                          <DialogTitle>Edit Attendance Record</DialogTitle>\n                          <DialogDescription>\n                            Update attendance information for {record.studentName}\n                          </DialogDescription>\n                        </DialogHeader>\n                        <div className=\"space-y-4\">\n                          <div className=\"grid grid-cols-2 gap-4\">\n                            <div className=\"space-y-2\">\n                              <Label>Status</Label>\n                              <Select \n                                value={editForm.status} \n                                onValueChange={(value) => setEditForm({...editForm, status: value})}\n                              >\n                                <SelectTrigger>\n                                  <SelectValue />\n                                </SelectTrigger>\n                                <SelectContent>\n                                  <SelectItem value=\"Present\">Present</SelectItem>\n                                  <SelectItem value=\"Late\">Late</SelectItem>\n                                  <SelectItem value=\"Absent\">Absent</SelectItem>\n                                </SelectContent>\n                              </Select>\n                            </div>\n                            <div className=\"space-y-2\">\n                              <Label>Check In Time</Label>\n                              <Input\n                                type=\"time\"\n                                value={editForm.checkIn}\n                                onChange={(e) => setEditForm({...editForm, checkIn: e.target.value})}\n                              />\n                            </div>\n                          </div>\n                          <div className=\"space-y-2\">\n                            <Label>Check Out Time</Label>\n                            <Input\n                              type=\"time\"\n                              value={editForm.checkOut}\n                              onChange={(e) => setEditForm({...editForm, checkOut: e.target.value})}\n                            />\n                          </div>\n                          <div className=\"space-y-2\">\n                            <Label>Reason/Notes</Label>\n                            <Textarea\n                              placeholder=\"Enter reason for absence or late arrival...\"\n                              value={editForm.reason}\n                              onChange={(e) => setEditForm({...editForm, reason: e.target.value})}\n                            />\n                          </div>\n                          <div className=\"flex justify-end space-x-2\">\n                            <Button variant=\"outline\" onClick={() => setEditingRecord(null)}>\n                              Cancel\n                            </Button>\n                            <Button onClick={handleSaveEdit}>\n                              Save Changes\n                            </Button>\n                          </div>\n                        </div>\n                      </DialogContent>\n                    </Dialog>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;;;;;AAiDO,SAAS,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAuB;IACxF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,QAAQ;QACR,SAAS;QACT,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;QACjB,YAAY;YACV,QAAQ,OAAO,MAAM;YACrB,SAAS,OAAO,OAAO,IAAI;YAC3B,UAAU,OAAO,QAAQ,IAAI;YAC7B,QAAQ,OAAO,MAAM,IAAI;QAC3B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,eAAe;QAEpB,eAAe,cAAc,EAAE,EAAE;YAC/B,QAAQ,SAAS,MAAM;YACvB,SAAS,SAAS,OAAO,IAAI;YAC7B,UAAU,SAAS,QAAQ,IAAI;YAC/B,QAAQ,SAAS,MAAM,IAAI;QAC7B;QAEA,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,WAAW,YAAY,YACxB,WAAW,SAAS,cAAc;QACjD,qBAAO,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAS;sBAAU;;;;;;IACnC;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC;sCAAK;;;;;;sCACN,8OAAC,0HAAA,CAAA,QAAK;4BAAC,SAAQ;;gCAAW,QAAQ,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAG7C,8OAAC,yHAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;0CACJ,8OAAC,0HAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;;sDACP,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,0HAAA,CAAA,YAAS;4CAAC,WAAU;sDAAa;;;;;;;;;;;;;;;;;0CAGtC,8OAAC,0HAAA,CAAA,YAAS;0CACP,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,0HAAA,CAAA,WAAQ;;0DACP,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2HAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,8OAAC,2HAAA,CAAA,cAAW;oEAAC,KAAK,OAAO,KAAK;oEAAE,KAAK,OAAO,WAAW;;;;;;8EACvD,8OAAC,2HAAA,CAAA,iBAAc;8EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGpB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAe,OAAO,WAAW;;;;;;8EAChD,8OAAC;oEAAI,WAAU;8EAAiC,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;0DAItE,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;gEAAc;gEAAO,OAAO,KAAK;;;;;;;sEAChD,8OAAC;4DAAI,WAAU;sEAAiC,OAAO,OAAO;;;;;;;;;;;;;;;;;0DAGlE,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC;oDAAI,WAAU;8DACZ,OAAO,OAAO,iBACb;;0EACE,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,OAAO,OAAO;;;;;;;qFAGvB,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;0DAI9C,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC;oDAAI,WAAU;8DACZ,OAAO,QAAQ,iBACd;;0EACE,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,OAAO,QAAQ;;;;;;;qFAGxB,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;0DAI9C,8OAAC,0HAAA,CAAA,YAAS;0DACR,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,cAAc,OAAO,MAAM;wDAC3B,eAAe,OAAO,MAAM;;;;;;;;;;;;0DAGjC,8OAAC,0HAAA,CAAA,YAAS;0DACP,OAAO,OAAO,iBACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAe,OAAO,OAAO;;;;;;sEAC5C,8OAAC;4DAAI,WAAU;sEAAiC,OAAO,MAAM;;;;;;;;;;;yEAG/D,8OAAC,0HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;0DAG7B,8OAAC,0HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,8OAAC,2HAAA,CAAA,SAAM;;sEACL,8OAAC,2HAAA,CAAA,gBAAa;4DAAC,OAAO;sEACpB,cAAA,8OAAC,2HAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,iBAAiB;0EAEhC,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAGpB,8OAAC,2HAAA,CAAA,gBAAa;;8EACZ,8OAAC,2HAAA,CAAA,eAAY;;sFACX,8OAAC,2HAAA,CAAA,cAAW;sFAAC;;;;;;sFACb,8OAAC,2HAAA,CAAA,oBAAiB;;gFAAC;gFACkB,OAAO,WAAW;;;;;;;;;;;;;8EAGzD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,0HAAA,CAAA,QAAK;sGAAC;;;;;;sGACP,8OAAC,2HAAA,CAAA,SAAM;4FACL,OAAO,SAAS,MAAM;4FACtB,eAAe,CAAC,QAAU,YAAY;oGAAC,GAAG,QAAQ;oGAAE,QAAQ;gGAAK;;8GAEjE,8OAAC,2HAAA,CAAA,gBAAa;8GACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;8GAEd,8OAAC,2HAAA,CAAA,gBAAa;;sHACZ,8OAAC,2HAAA,CAAA,aAAU;4GAAC,OAAM;sHAAU;;;;;;sHAC5B,8OAAC,2HAAA,CAAA,aAAU;4GAAC,OAAM;sHAAO;;;;;;sHACzB,8OAAC,2HAAA,CAAA,aAAU;4GAAC,OAAM;sHAAS;;;;;;;;;;;;;;;;;;;;;;;;8FAIjC,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,0HAAA,CAAA,QAAK;sGAAC;;;;;;sGACP,8OAAC,0HAAA,CAAA,QAAK;4FACJ,MAAK;4FACL,OAAO,SAAS,OAAO;4FACvB,UAAU,CAAC,IAAM,YAAY;oGAAC,GAAG,QAAQ;oGAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gGAAA;;;;;;;;;;;;;;;;;;sFAIxE,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0HAAA,CAAA,QAAK;8FAAC;;;;;;8FACP,8OAAC,0HAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,OAAO,SAAS,QAAQ;oFACxB,UAAU,CAAC,IAAM,YAAY;4FAAC,GAAG,QAAQ;4FAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wFAAA;;;;;;;;;;;;sFAGvE,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0HAAA,CAAA,QAAK;8FAAC;;;;;;8FACP,8OAAC,6HAAA,CAAA,WAAQ;oFACP,aAAY;oFACZ,OAAO,SAAS,MAAM;oFACtB,UAAU,CAAC,IAAM,YAAY;4FAAC,GAAG,QAAQ;4FAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wFAAA;;;;;;;;;;;;sFAGrE,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,2HAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAU,SAAS,IAAM,iBAAiB;8FAAO;;;;;;8FAGjE,8OAAC,2HAAA,CAAA,SAAM;oFAAC,SAAS;8FAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA9H9B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8IxC", "debugId": null}}, {"offset": {"line": 1964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/dashboard/attendance-stats.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Users, Clock, Calendar, TrendingUp, TrendingDown } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface AttendanceStatsProps {\n  totalStudents: number\n  presentToday: number\n  lateToday: number\n  absentToday: number\n  attendanceRate: number\n  className?: string\n}\n\nexport function AttendanceStats({\n  totalStudents,\n  presentToday,\n  lateToday,\n  absentToday,\n  attendanceRate,\n  className\n}: AttendanceStatsProps) {\n  const stats = [\n    {\n      title: \"Total Students\",\n      value: totalStudents.toLocaleString(),\n      icon: Users,\n      description: \"Enrolled students\",\n      color: \"text-blue-600\",\n      bgColor: \"bg-blue-50\",\n      change: \"+2.5%\",\n      trend: \"up\"\n    },\n    {\n      title: \"Present Today\",\n      value: presentToday.toLocaleString(),\n      icon: Calendar,\n      description: `${attendanceRate}% attendance rate`,\n      color: \"text-green-600\",\n      bgColor: \"bg-green-50\",\n      change: \"+1.2%\",\n      trend: \"up\"\n    },\n    {\n      title: \"Late Arrivals\",\n      value: lateToday.toString(),\n      icon: Clock,\n      description: \"Students arrived late\",\n      color: \"text-yellow-600\",\n      bgColor: \"bg-yellow-50\",\n      change: \"-0.8%\",\n      trend: \"down\"\n    },\n    {\n      title: \"Absent Today\",\n      value: absentToday.toString(),\n      icon: Users,\n      description: `${((absentToday / totalStudents) * 100).toFixed(1)}% of total`,\n      color: \"text-red-600\",\n      bgColor: \"bg-red-50\",\n      change: \"+0.3%\",\n      trend: \"up\"\n    }\n  ]\n\n  return (\n    <div className={cn(\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\", className)}>\n      {stats.map((stat, index) => {\n        const Icon = stat.icon\n        const TrendIcon = stat.trend === \"up\" ? TrendingUp : TrendingDown\n        \n        return (\n          <Card key={index} className=\"relative overflow-hidden\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                {stat.title}\n              </CardTitle>\n              <div className={cn(\"p-2 rounded-lg\", stat.bgColor)}>\n                <Icon className={cn(\"h-4 w-4\", stat.color)} />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"text-2xl font-bold\">{stat.value}</div>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    {stat.description}\n                  </p>\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  <TrendIcon \n                    className={cn(\n                      \"h-3 w-3\",\n                      stat.trend === \"up\" ? \"text-green-500\" : \"text-red-500\"\n                    )} \n                  />\n                  <span \n                    className={cn(\n                      \"text-xs font-medium\",\n                      stat.trend === \"up\" ? \"text-green-500\" : \"text-red-500\"\n                    )}\n                  >\n                    {stat.change}\n                  </span>\n                </div>\n              </div>\n              \n              {/* Progress bar for attendance rate */}\n              {index === 1 && (\n                <div className=\"mt-3\">\n                  <Progress value={attendanceRate} className=\"h-2\" />\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        )\n      })}\n    </div>\n  )\n}\n\n// Progress Ring Component\ninterface ProgressRingProps {\n  value: number\n  max: number\n  size?: number\n  strokeWidth?: number\n  className?: string\n  children?: React.ReactNode\n}\n\nexport function ProgressRing({\n  value,\n  max,\n  size = 120,\n  strokeWidth = 8,\n  className,\n  children\n}: ProgressRingProps) {\n  const radius = (size - strokeWidth) / 2\n  const circumference = radius * 2 * Math.PI\n  const percentage = (value / max) * 100\n  const strokeDasharray = circumference\n  const strokeDashoffset = circumference - (percentage / 100) * circumference\n\n  return (\n    <div className={cn(\"relative inline-flex items-center justify-center\", className)}>\n      <svg\n        width={size}\n        height={size}\n        className=\"transform -rotate-90\"\n      >\n        {/* Background circle */}\n        <circle\n          cx={size / 2}\n          cy={size / 2}\n          r={radius}\n          stroke=\"currentColor\"\n          strokeWidth={strokeWidth}\n          fill=\"none\"\n          className=\"text-muted-foreground/20\"\n        />\n        {/* Progress circle */}\n        <circle\n          cx={size / 2}\n          cy={size / 2}\n          r={radius}\n          stroke=\"currentColor\"\n          strokeWidth={strokeWidth}\n          fill=\"none\"\n          strokeDasharray={strokeDasharray}\n          strokeDashoffset={strokeDashoffset}\n          className=\"text-primary transition-all duration-300 ease-in-out\"\n          strokeLinecap=\"round\"\n        />\n      </svg>\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        {children || (\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold\">{percentage.toFixed(0)}%</div>\n            <div className=\"text-xs text-muted-foreground\">\n              {value}/{max}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\n// Real-time Counter Component\ninterface RealTimeCounterProps {\n  value: number\n  label: string\n  trend?: \"up\" | \"down\" | \"stable\"\n  className?: string\n}\n\nexport function RealTimeCounter({\n  value,\n  label,\n  trend = \"stable\",\n  className\n}: RealTimeCounterProps) {\n  return (\n    <div className={cn(\"text-center\", className)}>\n      <div className=\"text-3xl font-bold tabular-nums\">\n        {value.toLocaleString()}\n      </div>\n      <div className=\"text-sm text-muted-foreground\">{label}</div>\n      {trend !== \"stable\" && (\n        <div className=\"flex items-center justify-center mt-1\">\n          {trend === \"up\" ? (\n            <TrendingUp className=\"h-3 w-3 text-green-500 mr-1\" />\n          ) : (\n            <TrendingDown className=\"h-3 w-3 text-red-500 mr-1\" />\n          )}\n          <Badge \n            variant={trend === \"up\" ? \"default\" : \"destructive\"}\n            className=\"text-xs\"\n          >\n            {trend === \"up\" ? \"↑\" : \"↓\"}\n          </Badge>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAiBO,SAAS,gBAAgB,EAC9B,aAAa,EACb,YAAY,EACZ,SAAS,EACT,WAAW,EACX,cAAc,EACd,SAAS,EACY;IACrB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,cAAc,cAAc;YACnC,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;YACb,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,aAAa,cAAc;YAClC,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa,GAAG,eAAe,iBAAiB,CAAC;YACjD,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,UAAU,QAAQ;YACzB,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;YACb,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,YAAY,QAAQ;YAC3B,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa,GAAG,CAAC,AAAC,cAAc,gBAAiB,GAAG,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC;YAC5E,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;kBAC5D,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,OAAO,KAAK,IAAI;YACtB,MAAM,YAAY,KAAK,KAAK,KAAK,OAAO,kNAAA,CAAA,aAAU,GAAG,sNAAA,CAAA,eAAY;YAEjE,qBACE,8OAAC,yHAAA,CAAA,OAAI;gBAAa,WAAU;;kCAC1B,8OAAC,yHAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;0CAClB,KAAK,KAAK;;;;;;0CAEb,8OAAC;gCAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB,KAAK,OAAO;0CAC/C,cAAA,8OAAC;oCAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,KAAK,KAAK;;;;;;;;;;;;;;;;;kCAG7C,8OAAC,yHAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAsB,KAAK,KAAK;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;kDAGrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,WACA,KAAK,KAAK,KAAK,OAAO,mBAAmB;;;;;;0DAG7C,8OAAC;gDACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uBACA,KAAK,KAAK,KAAK,OAAO,mBAAmB;0DAG1C,KAAK,MAAM;;;;;;;;;;;;;;;;;;4BAMjB,UAAU,mBACT,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,OAAO;oCAAgB,WAAU;;;;;;;;;;;;;;;;;;eAtCxC;;;;;QA4Cf;;;;;;AAGN;AAYO,SAAS,aAAa,EAC3B,KAAK,EACL,GAAG,EACH,OAAO,GAAG,EACV,cAAc,CAAC,EACf,SAAS,EACT,QAAQ,EACU;IAClB,MAAM,SAAS,CAAC,OAAO,WAAW,IAAI;IACtC,MAAM,gBAAgB,SAAS,IAAI,KAAK,EAAE;IAC1C,MAAM,aAAa,AAAC,QAAQ,MAAO;IACnC,MAAM,kBAAkB;IACxB,MAAM,mBAAmB,gBAAgB,AAAC,aAAa,MAAO;IAE9D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;;0BACrE,8OAAC;gBACC,OAAO;gBACP,QAAQ;gBACR,WAAU;;kCAGV,8OAAC;wBACC,IAAI,OAAO;wBACX,IAAI,OAAO;wBACX,GAAG;wBACH,QAAO;wBACP,aAAa;wBACb,MAAK;wBACL,WAAU;;;;;;kCAGZ,8OAAC;wBACC,IAAI,OAAO;wBACX,IAAI,OAAO;wBACX,GAAG;wBACH,QAAO;wBACP,aAAa;wBACb,MAAK;wBACL,iBAAiB;wBACjB,kBAAkB;wBAClB,WAAU;wBACV,eAAc;;;;;;;;;;;;0BAGlB,8OAAC;gBAAI,WAAU;0BACZ,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAAsB,WAAW,OAAO,CAAC;gCAAG;;;;;;;sCAC3D,8OAAC;4BAAI,WAAU;;gCACZ;gCAAM;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;AAUO,SAAS,gBAAgB,EAC9B,KAAK,EACL,KAAK,EACL,QAAQ,QAAQ,EAChB,SAAS,EACY;IACrB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;;0BAChC,8OAAC;gBAAI,WAAU;0BACZ,MAAM,cAAc;;;;;;0BAEvB,8OAAC;gBAAI,WAAU;0BAAiC;;;;;;YAC/C,UAAU,0BACT,8OAAC;gBAAI,WAAU;;oBACZ,UAAU,qBACT,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;6CAEtB,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCAE1B,8OAAC,0HAAA,CAAA,QAAK;wBACJ,SAAS,UAAU,OAAO,YAAY;wBACtC,WAAU;kCAET,UAAU,OAAO,MAAM;;;;;;;;;;;;;;;;;;AAMpC", "debugId": null}}, {"offset": {"line": 2355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/dashboard/attendance-charts.tsx"], "sourcesContent": ["\"use client\"\n\nimport {\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  LineChart,\n  Line,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  <PERSON>lt<PERSON>,\n  Legend,\n  ResponsiveContainer\n} from \"recharts\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\n\n// Weekly trend chart\ninterface WeeklyTrendChartProps {\n  data: Array<{\n    day: string\n    present: number\n    late: number\n    absent: number\n  }>\n}\n\nexport function WeeklyTrendChart({ data }: WeeklyTrendChartProps) {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Weekly Attendance Trend</CardTitle>\n        <CardDescription>Daily attendance patterns for this week</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <ResponsiveContainer width=\"100%\" height={300}>\n          <AreaChart data={data}>\n            <CartesianGrid strokeDasharray=\"3 3\" />\n            <XAxis dataKey=\"day\" />\n            <YAxis />\n            <Tooltip />\n            <Legend />\n            <Area\n              type=\"monotone\"\n              dataKey=\"present\"\n              stackId=\"1\"\n              stroke=\"#22c55e\"\n              fill=\"#22c55e\"\n              fillOpacity={0.8}\n            />\n            <Area\n              type=\"monotone\"\n              dataKey=\"late\"\n              stackId=\"1\"\n              stroke=\"#f59e0b\"\n              fill=\"#f59e0b\"\n              fillOpacity={0.8}\n            />\n            <Area\n              type=\"monotone\"\n              dataKey=\"absent\"\n              stackId=\"1\"\n              stroke=\"#ef4444\"\n              fill=\"#ef4444\"\n              fillOpacity={0.8}\n            />\n          </AreaChart>\n        </ResponsiveContainer>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Grade level breakdown chart\ninterface GradeBreakdownChartProps {\n  data: Array<{\n    grade: string\n    total: number\n    present: number\n    late: number\n    absent: number\n  }>\n}\n\nexport function GradeBreakdownChart({ data }: GradeBreakdownChartProps) {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Grade Level Breakdown</CardTitle>\n        <CardDescription>Attendance by grade level</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <ResponsiveContainer width=\"100%\" height={300}>\n          <BarChart data={data}>\n            <CartesianGrid strokeDasharray=\"3 3\" />\n            <XAxis dataKey=\"grade\" />\n            <YAxis />\n            <Tooltip />\n            <Legend />\n            <Bar dataKey=\"present\" fill=\"#22c55e\" name=\"Present\" />\n            <Bar dataKey=\"late\" fill=\"#f59e0b\" name=\"Late\" />\n            <Bar dataKey=\"absent\" fill=\"#ef4444\" name=\"Absent\" />\n          </BarChart>\n        </ResponsiveContainer>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Attendance rate pie chart\ninterface AttendanceRatePieChartProps {\n  present: number\n  late: number\n  absent: number\n}\n\nexport function AttendanceRatePieChart({ present, late, absent }: AttendanceRatePieChartProps) {\n  const data = [\n    { name: \"Present\", value: present, color: \"#22c55e\" },\n    { name: \"Late\", value: late, color: \"#f59e0b\" },\n    { name: \"Absent\", value: absent, color: \"#ef4444\" }\n  ]\n\n  const total = present + late + absent\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Today&apos;s Attendance Distribution</CardTitle>\n        <CardDescription>Current attendance breakdown</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"flex items-center justify-between\">\n          <ResponsiveContainer width=\"60%\" height={200}>\n            <PieChart>\n              <Pie\n                data={data}\n                cx=\"50%\"\n                cy=\"50%\"\n                innerRadius={40}\n                outerRadius={80}\n                paddingAngle={5}\n                dataKey=\"value\"\n              >\n                {data.map((entry, index) => (\n                  <Cell key={`cell-${index}`} fill={entry.color} />\n                ))}\n              </Pie>\n              <Tooltip />\n            </PieChart>\n          </ResponsiveContainer>\n          <div className=\"space-y-2\">\n            {data.map((entry, index) => (\n              <div key={index} className=\"flex items-center space-x-2\">\n                <div\n                  className=\"w-3 h-3 rounded-full\"\n                  style={{ backgroundColor: entry.color }}\n                />\n                <div className=\"text-sm\">\n                  <div className=\"font-medium\">{entry.name}</div>\n                  <div className=\"text-muted-foreground\">\n                    {entry.value} ({((entry.value / total) * 100).toFixed(1)}%)\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Monthly trend line chart\ninterface MonthlyTrendChartProps {\n  data: Array<{\n    date: string\n    attendanceRate: number\n    totalStudents: number\n  }>\n}\n\nexport function MonthlyTrendChart({ data }: MonthlyTrendChartProps) {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Monthly Attendance Trend</CardTitle>\n        <CardDescription>Attendance rate over the past month</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <ResponsiveContainer width=\"100%\" height={300}>\n          <LineChart data={data}>\n            <CartesianGrid strokeDasharray=\"3 3\" />\n            <XAxis dataKey=\"date\" />\n            <YAxis domain={[80, 100]} />\n            <Tooltip />\n            <Line\n              type=\"monotone\"\n              dataKey=\"attendanceRate\"\n              stroke=\"#3b82f6\"\n              strokeWidth={2}\n              dot={{ fill: \"#3b82f6\", strokeWidth: 2, r: 4 }}\n            />\n          </LineChart>\n        </ResponsiveContainer>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Attendance heat map component\ninterface AttendanceHeatMapProps {\n  data: Array<{\n    day: string\n    hour: number\n    count: number\n  }>\n}\n\nexport function AttendanceHeatMap({ data }: AttendanceHeatMapProps) {\n  const days = [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\"]\n  const hours = Array.from({ length: 10 }, (_, i) => i + 7) // 7 AM to 4 PM\n\n  const getIntensity = (day: string, hour: number) => {\n    const entry = data.find(d => d.day === day && d.hour === hour)\n    return entry ? entry.count : 0\n  }\n\n  const maxCount = Math.max(...data.map(d => d.count))\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Attendance Heat Map</CardTitle>\n        <CardDescription>Student check-in patterns by day and hour</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-2\">\n          <div className=\"flex space-x-1\">\n            <div className=\"w-12\"></div>\n            {hours.map(hour => (\n              <div key={hour} className=\"w-8 text-xs text-center text-muted-foreground\">\n                {hour}\n              </div>\n            ))}\n          </div>\n          {days.map(day => (\n            <div key={day} className=\"flex items-center space-x-1\">\n              <div className=\"w-12 text-xs text-muted-foreground\">{day}</div>\n              {hours.map(hour => {\n                const count = getIntensity(day, hour)\n                const intensity = count / maxCount\n                return (\n                  <div\n                    key={`${day}-${hour}`}\n                    className=\"w-8 h-6 rounded-sm border border-border\"\n                    style={{\n                      backgroundColor: `rgba(34, 197, 94, ${intensity})`,\n                    }}\n                    title={`${day} ${hour}:00 - ${count} students`}\n                  />\n                )\n              })}\n            </div>\n          ))}\n        </div>\n        <div className=\"flex items-center justify-between mt-4 text-xs text-muted-foreground\">\n          <span>Less</span>\n          <div className=\"flex space-x-1\">\n            {[0, 0.2, 0.4, 0.6, 0.8, 1].map(intensity => (\n              <div\n                key={intensity}\n                className=\"w-3 h-3 rounded-sm border border-border\"\n                style={{\n                  backgroundColor: `rgba(34, 197, 94, ${intensity})`,\n                }}\n              />\n            ))}\n          </div>\n          <span>More</span>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAnBA;;;;AAgCO,SAAS,iBAAiB,EAAE,IAAI,EAAyB;IAC9D,qBACE,8OAAC,yHAAA,CAAA,OAAI;;0BACH,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,yHAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;;;;;;0CAC/B,8OAAC,qJAAA,CAAA,QAAK;gCAAC,SAAQ;;;;;;0CACf,8OAAC,qJAAA,CAAA,QAAK;;;;;0CACN,8OAAC,uJAAA,CAAA,UAAO;;;;;0CACR,8OAAC,sJAAA,CAAA,SAAM;;;;;0CACP,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,SAAQ;gCACR,QAAO;gCACP,MAAK;gCACL,aAAa;;;;;;0CAEf,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,SAAQ;gCACR,QAAO;gCACP,MAAK;gCACL,aAAa;;;;;;0CAEf,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,SAAQ;gCACR,QAAO;gCACP,MAAK;gCACL,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;AAaO,SAAS,oBAAoB,EAAE,IAAI,EAA4B;IACpE,qBACE,8OAAC,yHAAA,CAAA,OAAI;;0BACH,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,yHAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;wBAAC,MAAM;;0CACd,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;;;;;;0CAC/B,8OAAC,qJAAA,CAAA,QAAK;gCAAC,SAAQ;;;;;;0CACf,8OAAC,qJAAA,CAAA,QAAK;;;;;0CACN,8OAAC,uJAAA,CAAA,UAAO;;;;;0CACR,8OAAC,sJAAA,CAAA,SAAM;;;;;0CACP,8OAAC,mJAAA,CAAA,MAAG;gCAAC,SAAQ;gCAAU,MAAK;gCAAU,MAAK;;;;;;0CAC3C,8OAAC,mJAAA,CAAA,MAAG;gCAAC,SAAQ;gCAAO,MAAK;gCAAU,MAAK;;;;;;0CACxC,8OAAC,mJAAA,CAAA,MAAG;gCAAC,SAAQ;gCAAS,MAAK;gCAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtD;AASO,SAAS,uBAAuB,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAA+B;IAC3F,MAAM,OAAO;QACX;YAAE,MAAM;YAAW,OAAO;YAAS,OAAO;QAAU;QACpD;YAAE,MAAM;YAAQ,OAAO;YAAM,OAAO;QAAU;QAC9C;YAAE,MAAM;YAAU,OAAO;YAAQ,OAAO;QAAU;KACnD;IAED,MAAM,QAAQ,UAAU,OAAO;IAE/B,qBACE,8OAAC,yHAAA,CAAA,OAAI;;0BACH,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,yHAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,mKAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAM,QAAQ;sCACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;kDACP,8OAAC,+IAAA,CAAA,MAAG;wCACF,MAAM;wCACN,IAAG;wCACH,IAAG;wCACH,aAAa;wCACb,aAAa;wCACb,cAAc;wCACd,SAAQ;kDAEP,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,8OAAC,oJAAA,CAAA,OAAI;gDAAuB,MAAM,MAAM,KAAK;+CAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kDAG9B,8OAAC,uJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;sCAGZ,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,MAAM,KAAK;4CAAC;;;;;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAe,MAAM,IAAI;;;;;;8DACxC,8OAAC;oDAAI,WAAU;;wDACZ,MAAM,KAAK;wDAAC;wDAAG,CAAC,AAAC,MAAM,KAAK,GAAG,QAAS,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;mCARrD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBxB;AAWO,SAAS,kBAAkB,EAAE,IAAI,EAA0B;IAChE,qBACE,8OAAC,yHAAA,CAAA,OAAI;;0BACH,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,yHAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;;;;;;0CAC/B,8OAAC,qJAAA,CAAA,QAAK;gCAAC,SAAQ;;;;;;0CACf,8OAAC,qJAAA,CAAA,QAAK;gCAAC,QAAQ;oCAAC;oCAAI;iCAAI;;;;;;0CACxB,8OAAC,uJAAA,CAAA,UAAO;;;;;0CACR,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3D;AAWO,SAAS,kBAAkB,EAAE,IAAI,EAA0B;IAChE,MAAM,OAAO;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IAChD,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,eAAe;;IAEzE,MAAM,eAAe,CAAC,KAAa;QACjC,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,OAAO,EAAE,IAAI,KAAK;QACzD,OAAO,QAAQ,MAAM,KAAK,GAAG;IAC/B;IAEA,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IAElD,qBACE,8OAAC,yHAAA,CAAA,OAAI;;0BACH,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,yHAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;oCACd,MAAM,GAAG,CAAC,CAAA,qBACT,8OAAC;4CAAe,WAAU;sDACvB;2CADO;;;;;;;;;;;4BAKb,KAAK,GAAG,CAAC,CAAA,oBACR,8OAAC;oCAAc,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDAAsC;;;;;;wCACpD,MAAM,GAAG,CAAC,CAAA;4CACT,MAAM,QAAQ,aAAa,KAAK;4CAChC,MAAM,YAAY,QAAQ;4CAC1B,qBACE,8OAAC;gDAEC,WAAU;gDACV,OAAO;oDACL,iBAAiB,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;gDACpD;gDACA,OAAO,GAAG,IAAI,CAAC,EAAE,KAAK,MAAM,EAAE,MAAM,SAAS,CAAC;+CALzC,GAAG,IAAI,CAAC,EAAE,MAAM;;;;;wCAQ3B;;mCAfQ;;;;;;;;;;;kCAmBd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAK;oCAAK;oCAAK;oCAAK;iCAAE,CAAC,GAAG,CAAC,CAAA,0BAC9B,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,iBAAiB,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;wCACpD;uCAJK;;;;;;;;;;0CAQX,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 3047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/data/mock-data.ts"], "sourcesContent": ["import { Student, Subject, TimePeriod, AttendanceRecord } from \"@/lib/types/scanner\"\n\n// Enhanced mock student data with Philippine names and Grade 7-12 structure\nexport const mockStudents: Student[] = [\n  // Grade 7 Students\n  {\n    id: \"STU001\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-A\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU001_2025\"\n  },\n  {\n    id: \"STU002\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-B\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU002_2025\"\n  },\n  {\n    id: \"STU003\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-A\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU003_2025\"\n  },\n  // Grade 8 Students\n  {\n    id: \"STU004\",\n    name: \"Jose Miguel Rodriguez\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"2nd Year\",\n    section: \"Grade 8-A\",\n    grade: \"8\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU004_2025\"\n  },\n  {\n    id: \"STU005\",\n    name: \"Princess Mae Garcia\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"2nd Year\",\n    section: \"Grade 8-B\",\n    grade: \"8\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU005_2025\"\n  },\n  // Grade 9 Students\n  {\n    id: \"STU006\",\n    name: \"Mark Anthony Villanueva\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"3rd Year\",\n    section: \"Grade 9-A\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU006_2025\"\n  },\n  {\n    id: \"STU007\",\n    name: \"Angelica Mae Torres\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"3rd Year\",\n    section: \"Grade 9-B\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU007_2025\"\n  },\n  // Grade 10 Students\n  {\n    id: \"STU008\",\n    name: \"Christian Paul Mendoza\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"4th Year\",\n    section: \"Grade 10-A\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU008_2025\"\n  },\n  {\n    id: \"STU009\",\n    name: \"Kimberly Rose Flores\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"4th Year\",\n    section: \"Grade 10-B\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU009_2025\"\n  },\n  // Grade 11 Students (Senior High School)\n  {\n    id: \"STU010\",\n    name: \"John Michael Cruz\",\n    email: \"<EMAIL>\",\n    course: \"Information and Communications Technology\",\n    year: \"1st Year Senior High\",\n    section: \"ICT 11-A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU010_2025\"\n  },\n  {\n    id: \"STU011\",\n    name: \"Mary Grace Aquino\",\n    email: \"<EMAIL>\",\n    course: \"Accountancy, Business and Management\",\n    year: \"1st Year Senior High\",\n    section: \"ABM 11-A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU011_2025\"\n  },\n  // Grade 12 Students (Senior High School)\n  {\n    id: \"STU012\",\n    name: \"Ryan James Bautista\",\n    email: \"<EMAIL>\",\n    course: \"Information and Communications Technology\",\n    year: \"2nd Year Senior High\",\n    section: \"ICT 12-A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU012_2025\"\n  },\n  {\n    id: \"STU013\",\n    name: \"Sarah Jane Morales\",\n    email: \"<EMAIL>\",\n    course: \"Humanities and Social Sciences\",\n    year: \"2nd Year Senior High\",\n    section: \"HUMSS 12-A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU013_2025\"\n  }\n]\n\n// Mock subjects\nexport const mockSubjects: Subject[] = [\n  {\n    id: \"SUBJ001\",\n    name: \"Programming Fundamentals\",\n    code: \"IT101\",\n    instructor: \"Prof. Martinez\",\n    schedule: [\n      { day: \"Monday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Wednesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Friday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ002\",\n    name: \"Database Management\",\n    code: \"IT201\",\n    instructor: \"Prof. Rodriguez\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"10:00\", endTime: \"12:00\" },\n      { day: \"Thursday\", startTime: \"10:00\", endTime: \"12:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ003\",\n    name: \"Web Development\",\n    code: \"IT301\",\n    instructor: \"Prof. Santos\",\n    schedule: [\n      { day: \"Monday\", startTime: \"13:00\", endTime: \"15:00\" },\n      { day: \"Wednesday\", startTime: \"13:00\", endTime: \"15:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ004\",\n    name: \"Data Structures\",\n    code: \"CS201\",\n    instructor: \"Prof. Reyes\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Thursday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ005\",\n    name: \"Software Engineering\",\n    code: \"CS301\",\n    instructor: \"Prof. Cruz\",\n    schedule: [\n      { day: \"Monday\", startTime: \"15:00\", endTime: \"17:00\" },\n      { day: \"Friday\", startTime: \"15:00\", endTime: \"17:00\" }\n    ]\n  }\n]\n\n// Mock time periods\nexport const mockTimePeriods: TimePeriod[] = [\n  {\n    id: \"PERIOD001\",\n    name: \"1st Period\",\n    startTime: \"08:00\",\n    endTime: \"10:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD002\",\n    name: \"2nd Period\",\n    startTime: \"10:00\",\n    endTime: \"12:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD003\",\n    name: \"3rd Period\",\n    startTime: \"13:00\",\n    endTime: \"15:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD004\",\n    name: \"4th Period\",\n    startTime: \"15:00\",\n    endTime: \"17:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD005\",\n    name: \"Evening Class\",\n    startTime: \"18:00\",\n    endTime: \"20:00\",\n    type: \"evening\"\n  }\n]\n\n// Enhanced mock attendance records with realistic patterns\nexport const mockAttendanceRecords: AttendanceRecord[] = [\n  // Today's attendance records\n  {\n    id: \"ATT001\",\n    studentId: \"STU001\",\n    studentName: \"Maria Cristina Santos\",\n    course: \"Junior High School\",\n    checkIn: \"7:45 AM\",\n    checkOut: \"4:30 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 45, 0))\n  },\n  {\n    id: \"ATT002\",\n    studentId: \"STU002\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    course: \"Junior High School\",\n    checkIn: \"7:50 AM\",\n    checkOut: \"4:25 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 50, 0))\n  },\n  {\n    id: \"ATT003\",\n    studentId: \"STU003\",\n    studentName: \"Ana Marie Reyes\",\n    course: \"Junior High School\",\n    checkIn: \"8:15 AM\",\n    checkOut: \"4:35 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(8, 15, 0))\n  },\n  {\n    id: \"ATT004\",\n    studentId: \"STU004\",\n    studentName: \"Jose Miguel Rodriguez\",\n    course: \"Junior High School\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Absent\",\n    type: \"subject\",\n    subject: \"Mathematics\",\n    period: \"1st Period\",\n    timestamp: new Date(new Date().setHours(8, 0, 0))\n  },\n  {\n    id: \"ATT005\",\n    studentId: \"STU005\",\n    studentName: \"Princess Mae Garcia\",\n    course: \"Junior High School\",\n    checkIn: \"7:55 AM\",\n    checkOut: \"4:20 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 55, 0))\n  },\n  {\n    id: \"ATT006\",\n    studentId: \"STU010\",\n    studentName: \"John Michael Cruz\",\n    course: \"Information and Communications Technology\",\n    checkIn: \"7:40 AM\",\n    checkOut: \"5:00 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 40, 0))\n  },\n  {\n    id: \"ATT007\",\n    studentId: \"STU012\",\n    studentName: \"Ryan James Bautista\",\n    course: \"Information and Communications Technology\",\n    checkIn: \"8:10 AM\",\n    checkOut: \"5:05 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(8, 10, 0))\n  }\n]\n\n// Dashboard statistics data\nexport const mockDashboardStats = {\n  totalStudents: 1234,\n  presentToday: 1105,\n  lateToday: 23,\n  absentToday: 106,\n  attendanceRate: 89.5,\n  weeklyTrend: [\n    { day: 'Mon', present: 1150, late: 15, absent: 69 },\n    { day: 'Tue', present: 1120, late: 28, absent: 86 },\n    { day: 'Wed', present: 1105, late: 23, absent: 106 },\n    { day: 'Thu', present: 1140, late: 18, absent: 76 },\n    { day: 'Fri', present: 1095, late: 35, absent: 104 }\n  ],\n  gradeBreakdown: [\n    { grade: '7', total: 180, present: 165, late: 3, absent: 12 },\n    { grade: '8', total: 175, present: 158, late: 4, absent: 13 },\n    { grade: '9', total: 170, present: 155, late: 2, absent: 13 },\n    { grade: '10', total: 165, present: 148, late: 5, absent: 12 },\n    { grade: '11', total: 272, present: 245, late: 6, absent: 21 },\n    { grade: '12', total: 272, present: 234, late: 3, absent: 35 }\n  ]\n}\n\n// Recent activity feed\nexport const mockRecentActivity = [\n  {\n    id: \"ACT001\",\n    type: \"scan\",\n    studentName: \"Maria Cristina Santos\",\n    action: \"Check In\",\n    time: \"2 minutes ago\",\n    status: \"success\"\n  },\n  {\n    id: \"ACT002\",\n    type: \"scan\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    action: \"Check Out\",\n    time: \"5 minutes ago\",\n    status: \"success\"\n  },\n  {\n    id: \"ACT003\",\n    type: \"alert\",\n    studentName: \"Jose Miguel Rodriguez\",\n    action: \"Marked Absent\",\n    time: \"15 minutes ago\",\n    status: \"warning\"\n  },\n  {\n    id: \"ACT004\",\n    type: \"scan\",\n    studentName: \"Princess Mae Garcia\",\n    action: \"Late Arrival\",\n    time: \"25 minutes ago\",\n    status: \"warning\"\n  }\n]\n\n// Helper functions\nexport function findStudentById(id: string): Student | undefined {\n  return mockStudents.find(student => student.id === id)\n}\n\nexport function findStudentByQRCode(qrCode: string): Student | undefined {\n  return mockStudents.find(student => student.qrCode === qrCode)\n}\n\nexport function findSubjectById(id: string): Subject | undefined {\n  return mockSubjects.find(subject => subject.id === id)\n}\n\nexport function findPeriodById(id: string): TimePeriod | undefined {\n  return mockTimePeriods.find(period => period.id === id)\n}\n\nexport function getStudentAttendanceRecords(studentId: string): AttendanceRecord[] {\n  return mockAttendanceRecords.filter(record => record.studentId === studentId)\n}\n\nexport function getTodayAttendanceRecord(studentId: string): AttendanceRecord | undefined {\n  const today = new Date().toISOString().split('T')[0]\n  return mockAttendanceRecords.find(record => \n    record.studentId === studentId && record.date === today\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGO,MAAM,eAA0B;IACrC,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;YACzD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;SAC1D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;CACD;AAGM,MAAM,kBAAgC;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;CACD;AAGM,MAAM,wBAA4C;IACvD,6BAA6B;IAC7B;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,SAAS;QACT,QAAQ;QACR,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,GAAG;IAChD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;CACD;AAGM,MAAM,qBAAqB;IAChC,eAAe;IACf,cAAc;IACd,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,aAAa;QACX;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAI;QACnD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAI;KACpD;IACD,gBAAgB;QACd;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC7D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC7D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;KAC9D;AACH;AAGO,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;CACD;AAGM,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,oBAAoB,MAAc;IAChD,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;AACzD;AAEO,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AACtD;AAEO,SAAS,4BAA4B,SAAiB;IAC3D,OAAO,sBAAsB,MAAM,CAAC,CAAA,SAAU,OAAO,SAAS,KAAK;AACrE;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACpD,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAChC,OAAO,SAAS,KAAK,aAAa,OAAO,IAAI,KAAK;AAEtD", "debugId": null}}, {"offset": {"line": 3586, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils/export-utils.ts"], "sourcesContent": ["import { format } from \"date-fns\"\n\n// Types for export data\nexport interface AttendanceRecord {\n  id: string\n  studentId: string\n  studentName: string\n  course: string\n  grade: string\n  section: string\n  checkIn?: string\n  checkOut?: string\n  date: string\n  status: \"Present\" | \"Late\" | \"Absent\"\n  type: \"gate\" | \"subject\"\n  subject?: string\n  period?: string\n  reason?: string\n}\n\nexport interface ExportOptions {\n  format: \"csv\" | \"pdf\" | \"excel\"\n  dateRange?: {\n    from: Date\n    to: Date\n  }\n  includePhotos?: boolean\n  groupBy?: \"grade\" | \"section\" | \"course\" | \"none\"\n}\n\n// CSV Export Functions\nexport function exportToCSV(data: AttendanceRecord[], filename?: string): void {\n  const headers = [\n    \"Student ID\",\n    \"Student Name\", \n    \"Grade\",\n    \"Section\",\n    \"Course\",\n    \"Date\",\n    \"Check In\",\n    \"Check Out\",\n    \"Status\",\n    \"Type\",\n    \"Subject\",\n    \"Period\",\n    \"Reason\"\n  ]\n\n  const csvContent = [\n    headers.join(\",\"),\n    ...data.map(record => [\n      record.studentId,\n      `\"${record.studentName}\"`,\n      record.grade,\n      `\"${record.section}\"`,\n      `\"${record.course}\"`,\n      record.date,\n      record.checkIn || \"\",\n      record.checkOut || \"\",\n      record.status,\n      record.type,\n      record.subject || \"\",\n      record.period || \"\",\n      record.reason ? `\"${record.reason}\"` : \"\"\n    ].join(\",\"))\n  ].join(\"\\n\")\n\n  downloadFile(\n    csvContent,\n    filename || `attendance-report-${format(new Date(), \"yyyy-MM-dd\")}.csv`,\n    \"text/csv\"\n  )\n}\n\n// Generate daily report data\nexport function generateDailyReport(data: AttendanceRecord[], date: Date) {\n  const dateStr = format(date, \"yyyy-MM-dd\")\n  const dayData = data.filter(record => record.date === dateStr)\n  \n  const summary = {\n    date: format(date, \"MMMM d, yyyy\"),\n    totalStudents: dayData.length,\n    present: dayData.filter(r => r.status === \"Present\").length,\n    late: dayData.filter(r => r.status === \"Late\").length,\n    absent: dayData.filter(r => r.status === \"Absent\").length,\n    attendanceRate: 0\n  }\n\n  if (summary.totalStudents > 0) {\n    summary.attendanceRate = ((summary.present + summary.late) / summary.totalStudents) * 100\n  }\n\n  const gradeBreakdown = dayData.reduce((acc, record) => {\n    const grade = record.grade\n    if (!acc[grade]) {\n      acc[grade] = { total: 0, present: 0, late: 0, absent: 0 }\n    }\n    acc[grade].total++\n    acc[grade][record.status.toLowerCase() as keyof typeof acc[typeof grade]]++\n    return acc\n  }, {} as Record<string, { total: number; present: number; late: number; absent: number }>)\n\n  return {\n    summary,\n    gradeBreakdown,\n    records: dayData\n  }\n}\n\n// Print functionality\nexport function printDailyReport(data: AttendanceRecord[], date: Date): void {\n  const report = generateDailyReport(data, date)\n  \n  const printContent = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Daily Attendance Report - ${report.summary.date}</title>\n      <style>\n        body { \n          font-family: Arial, sans-serif; \n          margin: 20px; \n          color: #333;\n        }\n        .header { \n          text-align: center; \n          margin-bottom: 30px;\n          border-bottom: 2px solid #333;\n          padding-bottom: 20px;\n        }\n        .school-name {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        .report-title {\n          font-size: 18px;\n          color: #666;\n        }\n        .summary { \n          display: grid; \n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 20px;\n          margin-bottom: 30px;\n        }\n        .summary-card {\n          border: 1px solid #ddd;\n          padding: 15px;\n          border-radius: 8px;\n          text-align: center;\n        }\n        .summary-value {\n          font-size: 32px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        .summary-label {\n          color: #666;\n          font-size: 14px;\n        }\n        .present { color: #22c55e; }\n        .late { color: #f59e0b; }\n        .absent { color: #ef4444; }\n        .rate { color: #3b82f6; }\n        \n        .grade-breakdown {\n          margin-bottom: 30px;\n        }\n        .grade-breakdown h3 {\n          margin-bottom: 15px;\n          color: #333;\n        }\n        .grade-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n        }\n        .grade-table th,\n        .grade-table td {\n          border: 1px solid #ddd;\n          padding: 8px;\n          text-align: center;\n        }\n        .grade-table th {\n          background-color: #f5f5f5;\n          font-weight: bold;\n        }\n        \n        .records-table {\n          width: 100%;\n          border-collapse: collapse;\n          font-size: 12px;\n        }\n        .records-table th,\n        .records-table td {\n          border: 1px solid #ddd;\n          padding: 6px;\n          text-align: left;\n        }\n        .records-table th {\n          background-color: #f5f5f5;\n          font-weight: bold;\n        }\n        \n        .footer {\n          margin-top: 40px;\n          text-align: center;\n          color: #666;\n          font-size: 12px;\n          border-top: 1px solid #ddd;\n          padding-top: 20px;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <div class=\"school-name\">QRSAMS - Tanauan National High School</div>\n        <div class=\"report-title\">Daily Attendance Report</div>\n        <div style=\"margin-top: 10px; font-size: 16px;\">${report.summary.date}</div>\n      </div>\n      \n      <div class=\"summary\">\n        <div class=\"summary-card\">\n          <div class=\"summary-value\">${report.summary.totalStudents}</div>\n          <div class=\"summary-label\">Total Students</div>\n        </div>\n        <div class=\"summary-card\">\n          <div class=\"summary-value present\">${report.summary.present}</div>\n          <div class=\"summary-label\">Present</div>\n        </div>\n        <div class=\"summary-card\">\n          <div class=\"summary-value late\">${report.summary.late}</div>\n          <div class=\"summary-label\">Late</div>\n        </div>\n        <div class=\"summary-card\">\n          <div class=\"summary-value absent\">${report.summary.absent}</div>\n          <div class=\"summary-label\">Absent</div>\n        </div>\n        <div class=\"summary-card\">\n          <div class=\"summary-value rate\">${report.summary.attendanceRate.toFixed(1)}%</div>\n          <div class=\"summary-label\">Attendance Rate</div>\n        </div>\n      </div>\n      \n      <div class=\"grade-breakdown\">\n        <h3>Grade Level Breakdown</h3>\n        <table class=\"grade-table\">\n          <thead>\n            <tr>\n              <th>Grade</th>\n              <th>Total</th>\n              <th>Present</th>\n              <th>Late</th>\n              <th>Absent</th>\n              <th>Rate</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${Object.entries(report.gradeBreakdown).map(([grade, stats]) => `\n              <tr>\n                <td>Grade ${grade}</td>\n                <td>${stats.total}</td>\n                <td class=\"present\">${stats.present}</td>\n                <td class=\"late\">${stats.late}</td>\n                <td class=\"absent\">${stats.absent}</td>\n                <td>${((stats.present + stats.late) / stats.total * 100).toFixed(1)}%</td>\n              </tr>\n            `).join(\"\")}\n          </tbody>\n        </table>\n      </div>\n      \n      <div>\n        <h3>Detailed Records</h3>\n        <table class=\"records-table\">\n          <thead>\n            <tr>\n              <th>Student ID</th>\n              <th>Name</th>\n              <th>Grade</th>\n              <th>Section</th>\n              <th>Check In</th>\n              <th>Check Out</th>\n              <th>Status</th>\n              <th>Notes</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${report.records.map(record => `\n              <tr>\n                <td>${record.studentId}</td>\n                <td>${record.studentName}</td>\n                <td>${record.grade}</td>\n                <td>${record.section}</td>\n                <td>${record.checkIn || \"-\"}</td>\n                <td>${record.checkOut || \"-\"}</td>\n                <td class=\"${record.status.toLowerCase()}\">${record.status}</td>\n                <td>${record.reason || \"-\"}</td>\n              </tr>\n            `).join(\"\")}\n          </tbody>\n        </table>\n      </div>\n      \n      <div class=\"footer\">\n        Generated on ${format(new Date(), \"MMMM d, yyyy 'at' h:mm a\")} by QRSAMS\n      </div>\n    </body>\n    </html>\n  `\n\n  const printWindow = window.open(\"\", \"_blank\")\n  if (printWindow) {\n    printWindow.document.write(printContent)\n    printWindow.document.close()\n    printWindow.focus()\n    printWindow.print()\n    printWindow.close()\n  }\n}\n\n// Utility function to download files\nfunction downloadFile(content: string, filename: string, mimeType: string): void {\n  const blob = new Blob([content], { type: mimeType })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement(\"a\")\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Generate summary statistics\nexport function generateSummaryStats(data: AttendanceRecord[]) {\n  const total = data.length\n  const present = data.filter(r => r.status === \"Present\").length\n  const late = data.filter(r => r.status === \"Late\").length\n  const absent = data.filter(r => r.status === \"Absent\").length\n  const attendanceRate = total > 0 ? ((present + late) / total) * 100 : 0\n\n  return {\n    total,\n    present,\n    late,\n    absent,\n    attendanceRate\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AA+BO,SAAS,YAAY,IAAwB,EAAE,QAAiB;IACrE,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa;QACjB,QAAQ,IAAI,CAAC;WACV,KAAK,GAAG,CAAC,CAAA,SAAU;gBACpB,OAAO,SAAS;gBAChB,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC,CAAC,CAAC;gBACzB,OAAO,KAAK;gBACZ,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;gBACpB,OAAO,IAAI;gBACX,OAAO,OAAO,IAAI;gBAClB,OAAO,QAAQ,IAAI;gBACnB,OAAO,MAAM;gBACb,OAAO,IAAI;gBACX,OAAO,OAAO,IAAI;gBAClB,OAAO,MAAM,IAAI;gBACjB,OAAO,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG;aACxC,CAAC,IAAI,CAAC;KACR,CAAC,IAAI,CAAC;IAEP,aACE,YACA,YAAY,CAAC,kBAAkB,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,cAAc,IAAI,CAAC,EACvE;AAEJ;AAGO,SAAS,oBAAoB,IAAwB,EAAE,IAAU;IACtE,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IAC7B,MAAM,UAAU,KAAK,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;IAEtD,MAAM,UAAU;QACd,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACnB,eAAe,QAAQ,MAAM;QAC7B,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC3D,MAAM,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QACrD,QAAQ,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QACzD,gBAAgB;IAClB;IAEA,IAAI,QAAQ,aAAa,GAAG,GAAG;QAC7B,QAAQ,cAAc,GAAG,AAAC,CAAC,QAAQ,OAAO,GAAG,QAAQ,IAAI,IAAI,QAAQ,aAAa,GAAI;IACxF;IAEA,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAK;QAC1C,MAAM,QAAQ,OAAO,KAAK;QAC1B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACf,GAAG,CAAC,MAAM,GAAG;gBAAE,OAAO;gBAAG,SAAS;gBAAG,MAAM;gBAAG,QAAQ;YAAE;QAC1D;QACA,GAAG,CAAC,MAAM,CAAC,KAAK;QAChB,GAAG,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,WAAW,GAAqC;QACzE,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO;QACL;QACA;QACA,SAAS;IACX;AACF;AAGO,SAAS,iBAAiB,IAAwB,EAAE,IAAU;IACnE,MAAM,SAAS,oBAAoB,MAAM;IAEzC,MAAM,eAAe,CAAC;;;;uCAIe,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wDA0GL,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC;;;;;qCAKzC,EAAE,OAAO,OAAO,CAAC,aAAa,CAAC;;;;6CAIvB,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC;;;;0CAI5B,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC;;;;4CAIpB,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC;;;;0CAI1B,EAAE,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG;;;;;;;;;;;;;;;;;;;YAmBzE,EAAE,OAAO,OAAO,CAAC,OAAO,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,GAAK,CAAC;;0BAEnD,EAAE,MAAM;oBACd,EAAE,MAAM,KAAK,CAAC;oCACE,EAAE,MAAM,OAAO,CAAC;iCACnB,EAAE,MAAM,IAAI,CAAC;mCACX,EAAE,MAAM,MAAM,CAAC;oBAC9B,EAAE,CAAC,CAAC,MAAM,OAAO,GAAG,MAAM,IAAI,IAAI,MAAM,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG;;YAExE,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;YAqBZ,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;;oBAExB,EAAE,OAAO,SAAS,CAAC;oBACnB,EAAE,OAAO,WAAW,CAAC;oBACrB,EAAE,OAAO,KAAK,CAAC;oBACf,EAAE,OAAO,OAAO,CAAC;oBACjB,EAAE,OAAO,OAAO,IAAI,IAAI;oBACxB,EAAE,OAAO,QAAQ,IAAI,IAAI;2BAClB,EAAE,OAAO,MAAM,CAAC,WAAW,GAAG,EAAE,EAAE,OAAO,MAAM,CAAC;oBACvD,EAAE,OAAO,MAAM,IAAI,IAAI;;YAE/B,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;qBAMH,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,4BAA4B;;;;EAIpE,CAAC;IAED,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;IACpC,IAAI,aAAa;QACf,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAC1B,YAAY,KAAK;QACjB,YAAY,KAAK;QACjB,YAAY,KAAK;IACnB;AACF;AAEA,qCAAqC;AACrC,SAAS,aAAa,OAAe,EAAE,QAAgB,EAAE,QAAgB;IACvE,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE,MAAM;IAAS;IAClD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,qBAAqB,IAAwB;IAC3D,MAAM,QAAQ,KAAK,MAAM;IACzB,MAAM,UAAU,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;IAC/D,MAAM,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;IACzD,MAAM,SAAS,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IAC7D,MAAM,iBAAiB,QAAQ,IAAI,AAAC,CAAC,UAAU,IAAI,IAAI,QAAS,MAAM;IAEtE,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/%28dashboard%29/attendance/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from \"@/components/ui/tabs\"\nimport { AttendanceFilters, type AttendanceFilters as FilterType } from \"@/components/attendance/attendance-filters\"\nimport { AttendanceGrid } from \"@/components/attendance/attendance-grid\"\nimport { AttendanceStats } from \"@/components/dashboard/attendance-stats\"\nimport { WeeklyTrendChart, GradeBreakdownChart } from \"@/components/dashboard/attendance-charts\"\nimport { mockDashboardStats, mockAttendanceRecords } from \"@/lib/data/mock-data\"\nimport { exportToCSV, printDailyReport } from \"@/lib/utils/export-utils\"\nimport {\n  Calendar,\n  QrCode,\n  Download,\n  Printer,\n  Users,\n  <PERSON><PERSON><PERSON>,\n  Al<PERSON>Triangle\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\n// Enhanced attendance record type\ninterface AttendanceRecord {\n  id: string\n  studentId: string\n  studentName: string\n  course: string\n  grade: string\n  section: string\n  checkIn?: string\n  checkOut?: string\n  date: string\n  status: \"Present\" | \"Late\" | \"Absent\"\n  type: \"gate\" | \"subject\"\n  subject?: string\n  period?: string\n  reason?: string\n  photo?: string\n}\n\n// Mock enhanced attendance data\nconst mockEnhancedAttendance: AttendanceRecord[] = [\n  {\n    id: \"ATT001\",\n    studentId: \"STU001\",\n    studentName: \"Maria Cristina Santos\",\n    course: \"Junior High School\",\n    grade: \"7\",\n    section: \"Grade 7-A\",\n    checkIn: \"7:45 AM\",\n    checkOut: \"4:30 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\"\n  },\n  {\n    id: \"ATT002\",\n    studentId: \"STU002\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    course: \"Junior High School\",\n    grade: \"7\",\n    section: \"Grade 7-B\",\n    checkIn: \"7:50 AM\",\n    checkOut: \"4:25 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\"\n  },\n  {\n    id: \"ATT003\",\n    studentId: \"STU003\",\n    studentName: \"Ana Marie Reyes\",\n    course: \"Junior High School\",\n    grade: \"7\",\n    section: \"Grade 7-A\",\n    checkIn: \"8:15 AM\",\n    checkOut: \"4:35 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    reason: \"Traffic delay\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\"\n  },\n  {\n    id: \"ATT004\",\n    studentId: \"STU004\",\n    studentName: \"Jose Miguel Rodriguez\",\n    course: \"Junior High School\",\n    grade: \"8\",\n    section: \"Grade 8-A\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Absent\",\n    type: \"subject\",\n    subject: \"Mathematics\",\n    period: \"1st Period\",\n    reason: \"Sick leave\",\n    photo: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n  }\n]\n\nexport default function AttendancePage() {\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>(mockEnhancedAttendance)\n  const [filteredRecords, setFilteredRecords] = useState<AttendanceRecord[]>(mockEnhancedAttendance)\n  const [filters, setFilters] = useState<FilterType>({\n    dateRange: { from: new Date(), to: new Date() },\n    grade: \"all\",\n    section: \"all\",\n    course: \"all\",\n    status: \"all\",\n    searchQuery: \"\"\n  })\n\n  // Filter records based on current filters\n  useEffect(() => {\n    let filtered = attendanceRecords\n\n    // Apply search filter\n    if (filters.searchQuery) {\n      filtered = filtered.filter(record =>\n        record.studentName.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||\n        record.studentId.toLowerCase().includes(filters.searchQuery.toLowerCase())\n      )\n    }\n\n    // Apply grade filter\n    if (filters.grade !== \"all\") {\n      filtered = filtered.filter(record => record.grade === filters.grade)\n    }\n\n    // Apply status filter\n    if (filters.status !== \"all\") {\n      filtered = filtered.filter(record => record.status.toLowerCase() === filters.status)\n    }\n\n    setFilteredRecords(filtered)\n  }, [filters, attendanceRecords])\n\n  const handleFiltersChange = (newFilters: FilterType) => {\n    setFilters(newFilters)\n  }\n\n  const handleUpdateRecord = (recordId: string, updates: Partial<AttendanceRecord>) => {\n    setAttendanceRecords(prev =>\n      prev.map(record =>\n        record.id === recordId ? { ...record, ...updates } : record\n      )\n    )\n  }\n\n  const handleExportData = () => {\n    exportToCSV(filteredRecords, `attendance-report-${format(new Date(), \"yyyy-MM-dd\")}.csv`)\n  }\n\n  const handlePrintReport = () => {\n    printDailyReport(filteredRecords, new Date())\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header Section */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Attendance Management</h1>\n          <p className=\"text-muted-foreground\">\n            Monitor, track, and manage student attendance records\n          </p>\n        </div>\n        <div className=\"flex space-x-2\">\n          <Button variant=\"outline\" onClick={handleExportData}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            Export Data\n          </Button>\n          <Button variant=\"outline\" onClick={handlePrintReport}>\n            <Printer className=\"mr-2 h-4 w-4\" />\n            Print Report\n          </Button>\n          <Button>\n            <QrCode className=\"mr-2 h-4 w-4\" />\n            Open Scanner\n          </Button>\n        </div>\n      </div>\n\n      {/* Statistics Overview */}\n      <AttendanceStats\n        totalStudents={mockDashboardStats.totalStudents}\n        presentToday={mockDashboardStats.presentToday}\n        lateToday={mockDashboardStats.lateToday}\n        absentToday={mockDashboardStats.absentToday}\n        attendanceRate={mockDashboardStats.attendanceRate}\n      />\n\n      {/* Main Content */}\n      <div className=\"grid gap-6 lg:grid-cols-4\">\n        {/* Filters Sidebar */}\n        <div className=\"lg:col-span-1\">\n          <AttendanceFilters onFiltersChange={handleFiltersChange} />\n        </div>\n\n        {/* Main Content Area */}\n        <div className=\"lg:col-span-3 space-y-6\">\n          <Tabs defaultValue=\"daily\" className=\"space-y-4\">\n            <TabsList className=\"grid w-full grid-cols-4\">\n              <TabsTrigger value=\"daily\">Daily View</TabsTrigger>\n              <TabsTrigger value=\"subject\">By Subject</TabsTrigger>\n              <TabsTrigger value=\"analytics\">Analytics</TabsTrigger>\n              <TabsTrigger value=\"reports\">Reports</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"daily\" className=\"space-y-4\">\n              <AttendanceGrid\n                records={filteredRecords}\n                onUpdateRecord={handleUpdateRecord}\n              />\n            </TabsContent>\n\n            <TabsContent value=\"subject\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Subject-wise Attendance</CardTitle>\n                  <CardDescription>Track attendance by subject and period</CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n                    {[\"Mathematics\", \"Science\", \"English\", \"Filipino\", \"Social Studies\", \"PE\"].map((subject) => (\n                      <Card key={subject}>\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-base\">{subject}</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"space-y-2\">\n                            <div className=\"flex justify-between text-sm\">\n                              <span>Present:</span>\n                              <span className=\"font-medium text-green-600\">85%</span>\n                            </div>\n                            <div className=\"flex justify-between text-sm\">\n                              <span>Late:</span>\n                              <span className=\"font-medium text-yellow-600\">8%</span>\n                            </div>\n                            <div className=\"flex justify-between text-sm\">\n                              <span>Absent:</span>\n                              <span className=\"font-medium text-red-600\">7%</span>\n                            </div>\n                          </div>\n                        </CardContent>\n                      </Card>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"analytics\" className=\"space-y-4\">\n              <div className=\"grid gap-4 md:grid-cols-2\">\n                <WeeklyTrendChart data={mockDashboardStats.weeklyTrend} />\n                <GradeBreakdownChart data={mockDashboardStats.gradeBreakdown} />\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"reports\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Generate Reports</CardTitle>\n                  <CardDescription>Create and download attendance reports</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"grid gap-4 md:grid-cols-2\">\n                    <Button variant=\"outline\" className=\"h-20 flex-col\">\n                      <Calendar className=\"h-6 w-6 mb-2\" />\n                      Daily Report\n                    </Button>\n                    <Button variant=\"outline\" className=\"h-20 flex-col\">\n                      <Users className=\"h-6 w-6 mb-2\" />\n                      Student Summary\n                    </Button>\n                    <Button variant=\"outline\" className=\"h-20 flex-col\">\n                      <BookOpen className=\"h-6 w-6 mb-2\" />\n                      Subject Report\n                    </Button>\n                    <Button variant=\"outline\" className=\"h-20 flex-col\">\n                      <AlertTriangle className=\"h-6 w-6 mb-2\" />\n                      Absence Report\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAtBA;;;;;;;;;;;;;;AA2CA,gCAAgC;AAChC,MAAM,yBAA6C;IACjD;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,OAAO;QACP,SAAS;QACT,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,OAAO;QACP,SAAS;QACT,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,OAAO;QACP,SAAS;QACT,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACjD,WAAW;YAAE,MAAM,IAAI;YAAQ,IAAI,IAAI;QAAO;QAC9C,OAAO;QACP,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,aAAa;IACf;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,sBAAsB;QACtB,IAAI,QAAQ,WAAW,EAAE;YACvB,WAAW,SAAS,MAAM,CAAC,CAAA,SACzB,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,CAAC,WAAW,OACzE,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW,CAAC,WAAW;QAE3E;QAEA,qBAAqB;QACrB,IAAI,QAAQ,KAAK,KAAK,OAAO;YAC3B,WAAW,SAAS,MAAM,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,QAAQ,KAAK;QACrE;QAEA,sBAAsB;QACtB,IAAI,QAAQ,MAAM,KAAK,OAAO;YAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,WAAW,OAAO,QAAQ,MAAM;QACrF;QAEA,mBAAmB;IACrB,GAAG;QAAC;QAAS;KAAkB;IAE/B,MAAM,sBAAsB,CAAC;QAC3B,WAAW;IACb;IAEA,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,qBAAqB,CAAA,OACnB,KAAK,GAAG,CAAC,CAAA,SACP,OAAO,EAAE,KAAK,WAAW;oBAAE,GAAG,MAAM;oBAAE,GAAG,OAAO;gBAAC,IAAI;IAG3D;IAEA,MAAM,mBAAmB;QACvB,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB,CAAC,kBAAkB,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,cAAc,IAAI,CAAC;IAC1F;IAEA,MAAM,oBAAoB;QACxB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,IAAI;IACxC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGtC,8OAAC,2HAAA,CAAA,SAAM;;kDACL,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOzC,8OAAC,+IAAA,CAAA,kBAAe;gBACd,eAAe,2HAAA,CAAA,qBAAkB,CAAC,aAAa;gBAC/C,cAAc,2HAAA,CAAA,qBAAkB,CAAC,YAAY;gBAC7C,WAAW,2HAAA,CAAA,qBAAkB,CAAC,SAAS;gBACvC,aAAa,2HAAA,CAAA,qBAAkB,CAAC,WAAW;gBAC3C,gBAAgB,2HAAA,CAAA,qBAAkB,CAAC,cAAc;;;;;;0BAInD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kJAAA,CAAA,oBAAiB;4BAAC,iBAAiB;;;;;;;;;;;kCAItC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,yHAAA,CAAA,OAAI;4BAAC,cAAa;4BAAQ,WAAU;;8CACnC,8OAAC,yHAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,yHAAA,CAAA,cAAW;4CAAC,OAAM;sDAAQ;;;;;;sDAC3B,8OAAC,yHAAA,CAAA,cAAW;4CAAC,OAAM;sDAAU;;;;;;sDAC7B,8OAAC,yHAAA,CAAA,cAAW;4CAAC,OAAM;sDAAY;;;;;;sDAC/B,8OAAC,yHAAA,CAAA,cAAW;4CAAC,OAAM;sDAAU;;;;;;;;;;;;8CAG/B,8OAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;8CACnC,cAAA,8OAAC,+IAAA,CAAA,iBAAc;wCACb,SAAS;wCACT,gBAAgB;;;;;;;;;;;8CAIpB,8OAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC,yHAAA,CAAA,OAAI;;0DACH,8OAAC,yHAAA,CAAA,aAAU;;kEACT,8OAAC,yHAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,yHAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,8OAAC,yHAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAe;wDAAW;wDAAW;wDAAY;wDAAkB;qDAAK,CAAC,GAAG,CAAC,CAAC,wBAC9E,8OAAC,yHAAA,CAAA,OAAI;;8EACH,8OAAC,yHAAA,CAAA,aAAU;oEAAC,WAAU;8EACpB,cAAA,8OAAC,yHAAA,CAAA,YAAS;wEAAC,WAAU;kFAAa;;;;;;;;;;;8EAEpC,8OAAC,yHAAA,CAAA,cAAW;8EACV,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;kGAAK;;;;;;kGACN,8OAAC;wFAAK,WAAU;kGAA6B;;;;;;;;;;;;0FAE/C,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;kGAAK;;;;;;kGACN,8OAAC;wFAAK,WAAU;kGAA8B;;;;;;;;;;;;0FAEhD,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;kGAAK;;;;;;kGACN,8OAAC;wFAAK,WAAU;kGAA2B;;;;;;;;;;;;;;;;;;;;;;;;2DAhBxC;;;;;;;;;;;;;;;;;;;;;;;;;;8CA2BrB,8OAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;8CACvC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gJAAA,CAAA,mBAAgB;gDAAC,MAAM,2HAAA,CAAA,qBAAkB,CAAC,WAAW;;;;;;0DACtD,8OAAC,gJAAA,CAAA,sBAAmB;gDAAC,MAAM,2HAAA,CAAA,qBAAkB,CAAC,cAAc;;;;;;;;;;;;;;;;;8CAIhE,8OAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC,yHAAA,CAAA,OAAI;;0DACH,8OAAC,yHAAA,CAAA,aAAU;;kEACT,8OAAC,yHAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,yHAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;;8EAClC,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,8OAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;;8EAClC,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,8OAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;;8EAClC,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,8OAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;;8EAClC,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhE", "debugId": null}}]}